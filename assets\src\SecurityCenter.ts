import { showChooseWallet } from "./ChooseWallet";
import { ALL_APP_SOURCE_CONFIG } from "./Config";
import { DEEP_INDEXZ, E_CAPABILITY_TYPE, E_CHANEL_TYPE, E_FUND_TYPE, UI_PATH_DIC, VERIFY_CODE_TYPE } from "./GlobalConstant";
import Global from "./GlobalScript";
import { INGSME_TYPE, KycMgr } from "./KYC/KycMgr";
import { ShowSecurityRequirement } from "./SecurityRequirements";
import { PN_VERIFY_TYPE, showPhoneNumber } from "./SetPhoneNumber";
import { showVerifyCode } from "./VerifyCode";
import UICommon from "./component/UICommon";
import { uiManager } from "./mgr/UIManager";
import HttpUtils from "./net/HttpUtils";

const { ccclass, property } = cc._decorator;
enum KYC_STATE{
    KYC_NO = 0,//未验证
    KYC_COMPLETED,//完成验证kyc
    KYC_ING,//审核中
    KYC_REJECT,//拒绝
}
@ccclass
export default class SecurityCenter extends UICommon {
    @property(cc.Label)
    lbBonus: cc.Label = null;

    @property(cc.Node)
    ndBonus: cc.Node = null;

    @property(cc.Node)
    ndPhone: cc.Node = null;

    @property(cc.Node)
    ndPaymentPwd: cc.Node = null;

    @property(cc.Node)
    ndLoginPwd: cc.Node = null;


    @property(cc.Node)
    generalView: cc.Node = null;

    @property(cc.Node)
    linkView: cc.Node = null;

    @property(cc.Node)
    actionView: cc.Node = null;

    //logout prefab
    @property(cc.Prefab)
    logoutPrefab: cc.Prefab = null;

    @property(cc.Node)
    mainNode: cc.Node = null;

    //版本号 label
    @property(cc.Label)
    version_label: cc.Label = null

    kyc_state = 0;//kyc状态
    kyc_simple = 0;//kyc是否简化版

    defaultData: { account: any, fundType: E_FUND_TYPE } = null;
    cur_fund_type = null;
    passwordPrefab: cc.Prefab = null;


    onLoad() {
        this.version_label.string = ALL_APP_SOURCE_CONFIG.app_version;
        
        cc.director.on("phoneBindSuc", this.onPhoneBindSuc, this)
        cc.director.on("bind_callback", this.onPhoneBindSuc, this)

        cc.director.on("setPasswordSuc", this.onSetPasswordSuc, this)
        cc.director.on("setLoginPwd_suc", this.onSetLoginPwdSuc, this)

        KycMgr.instance.in_game_type = -1;//重置一下kyc 进入类型 防止不合理弹窗
        //注册个事件 kyc之后通知 一下 刷新一下状态
        cc.director.on("upload_KYC_success", this.getKycState, this)

        
        if (Global.getInstance().mayaMode || Global.getInstance().gcashMode) {
            this.ndLoginPwd.active = false;
        }
        this.getKycData();
        this.initWidget();
        this.refreshStatus()
        this.preLoadPrefab()
        if(ALL_APP_SOURCE_CONFIG.channel == E_CHANEL_TYPE.WEB){
            this.getKycState();
        }
    }

    start() {
        let page = this.node.getChildByName("page").getComponent(cc.PageView);
        page.node.on('page-turning', ()=>{
            let curIndex = page.getCurrentPageIndex();
            if (curIndex != 1) {
                this.node.destroy();
            }
        }, this);
        let tempnode = this.node.getChildByName("temp");
        page.insertPage(cc.instantiate(tempnode),0);
        page.scrollToPage(1,0.01);
    }

    //更改kyc 状态显示
    changeKycState(){
        let details = this.generalView.getChildByName("details");  
        let d_none = details.getChildByName('none')
        if(ALL_APP_SOURCE_CONFIG.channel == E_CHANEL_TYPE.WEB){
            switch (this.kyc_state) {
                case KYC_STATE.KYC_NO:
                    d_none.getComponent(cc.Label).string = 'Not Verified';
                    d_none.color = new cc.Color(174,21,67,255);
                    break;
                case KYC_STATE.KYC_ING:
                    d_none.getComponent(cc.Label).string = 'Reviewing';
                    d_none.color = new cc.Color(255,147,111,255);
                    break;
                case KYC_STATE.KYC_COMPLETED:
                    d_none.getComponent(cc.Label).string = 'Completed';
                    d_none.color = new cc.Color(26,216,122,255);
                    break;
                case KYC_STATE.KYC_REJECT:
                    d_none.getComponent(cc.Label).string = 'Rejected';
                    d_none.color = new cc.Color(174,21,67,255);
                    break;
                default:
                    break;
            }
        }
    }
    //获取kyc 状态
    getKycState(cb?){
        this.getKycData();
        let params: Object = {};
        params["token"] = Global.getInstance().token;
        HttpUtils.getInstance().get(3, 3, this, "/common/api/user/status", params,
            (response) => {
                // {"code":200,"msg":"success","data":{"status":1,"is_full":0}}
                // console.log('---------state:',JSON.stringify(response))
                if(response && response.data){
                    this.kyc_state = parseInt(response?.data?.status);
                    this.kyc_simple = response?.data?.is_full;
                    KycMgr.instance.kyc_state = this.kyc_state;
                    KycMgr.instance.kyc_simple = this.kyc_simple;
                    KycMgr.instance.in_game_type = INGSME_TYPE.myCenter;//标记一下为个人中心请求验证
                    this.changeKycState();
                }
                if (cb) {
                    cb();
                }
            }, (res) => {
                if (res && res.code) {
                    Global.getInstance().showSimpleTip(Global.getInstance().getLabel("php_code_" + res["code"]));
                } else {
                    Global.getInstance().showSimpleTip(res.msg);
                }
            });
    }
    //适配一下 scrollview 页面等
    initWidget() {
        if (Global.instance.is_mini_game()) {
            let scorllview = this.mainNode.getChildByName("scorllview").getComponent(cc.ScrollView);
            scorllview.content.height = scorllview.node.height;
            this.linkView.active = false;
            this.actionView.active = false;
            this.generalView.height = 420;
            this.generalView.getChildByName("general_setting").height = 420;
            let phone = this.generalView.getChildByName("phone");
            let password = this.generalView.getChildByName("password");
            let wallet = this.generalView.getChildByName("wallet");
            let details = this.generalView.getChildByName("details");
            let version = this.generalView.getChildByName("version");//maya添加版本号
            let version_l = version.getChildByName('version_l');

            let d_title = details.getChildByName('title')
            let d_none = details.getChildByName('none')
            d_title.y = 0;
            d_none.active = false;
            d_title.getComponent(cc.Label).string = 'Personal details';
            if (ALL_APP_SOURCE_CONFIG.channel == E_CHANEL_TYPE.G_CASH) {
                let hasSubmit = Global.getInstance().getStoreageData("HAVE_SUBMIT_KYC"+Global.getInstance().userdata.user_id,0);
                d_title.y = 26;
                d_none.active = true;
                d_title.getComponent(cc.Label).string = 'KYC Details';
                d_none.getComponent(cc.Label).string = '';
                // if (hasSubmit == 1) {
                //     d_none.getComponent(cc.Label).string = 'Completed';
                //     d_none.color = new cc.Color(17,190,107,255);
                // } else {
                //     d_none.getComponent(cc.Label).string = 'Pending';
                //     d_none.color = new cc.Color(232,166,0,255);
                // }
            }

            version.position = password.position;
            // wallet.position = password.position;
            details.position = phone.position;

            phone.active = false;
            password.active = false;
            wallet.active = false;
            
            version.active = true;
            version_l.getComponent(cc.Label).string = ALL_APP_SOURCE_CONFIG.app_version;
        }else if(ALL_APP_SOURCE_CONFIG.channel == E_CHANEL_TYPE.WEB){
            let scroolNode = this.mainNode.getChildByName("scorllview")
            let scorllview = scroolNode.getComponent(cc.ScrollView);
            
            
            let safeAreaRect = cc.sys.getSafeAreaRect();
            scroolNode.height = safeAreaRect.height - 220;
            scorllview.content.height = 2000;


            let details = this.generalView.getChildByName("details");
            let d_title = details.getChildByName('title')
            let d_none = details.getChildByName('none')
            d_title.y = 26;
            d_none.active = true;
            d_title.getComponent(cc.Label).string = 'KYC Details';
            d_none.getComponent(cc.Label).string = 'Not Verified';
        }
    }

    preLoadPrefab() {
        let prefabs = [
            // UI_PATH_DIC.PhoneBind,
            // UI_PATH_DIC.SetPayPassword,
            // UI_PATH_DIC.SetLoginPassword,
            // UI_PATH_DIC.VerifyCode,
            // UI_PATH_DIC.SecurityRequirement1,
            // UI_PATH_DIC.SecurityRequirement2,
            // UI_PATH_DIC.SecurityRequirement3,
            // UI_PATH_DIC.KYCVerification,//未认证的时候 会弹出这个认证页面
        ]
        cc.resources.preload(prefabs)
    }

    protected onDestroy(): void {
        cc.director.off("phoneBindSuc", this.onPhoneBindSuc, this)
        cc.director.off("bind_callback", this.onPhoneBindSuc, this)
        cc.director.off("setPasswordSuc", this.onSetPasswordSuc, this)
        cc.director.off("setLoginPwd_suc", this.onSetLoginPwdSuc, this)
        cc.director.off("upload_KYC_success", this.changeKycState, this)
    }
    //登出账号 提示框
    logoutBtnClick(){
        let cb = ()=>{
            let node = cc.instantiate(this.logoutPrefab);
            node.position = new cc.Vec3(0,-960);
            node.parent = this.node;
        }
        if (!this.logoutPrefab) {
            uiManager.instance.loadPrefabByLoading(UI_PATH_DIC.LogoutTips,cc.Prefab,(finish, total, item) => {
            }, (err, prefab: any) => {
                    if (err) {
                        return;
                    }
                    this.logoutPrefab = prefab;
                    cb();
            });
        } else {
            cb();
        }
    }
    refreshStatus() {
        let userData = Global.getInstance().userdata;
        let phoneNone = this.ndPhone.getChildByName("none")
        let phoneNum = this.ndPhone.getChildByName("num")
        if (userData.phone) {
            // this.ndBonus.active = false;
            phoneNum.active = true;
            phoneNone.active = false;
            phoneNum.getComponent(cc.Label).string = userData.phone.substring(0, 2) + "*****" + userData.phone.substring(6);
            // this.ndPhone.getComponent(cc.Button).interactable = false;
            // this.ndPhone.getChildByName("jt").active = false;
        } else {
            // this.ndBonus.active = true;
            phoneNum.active = false;
            phoneNone.active = true;
            // this.lbBonus.string = Global.getInstance().config.user_bind_phone_reward.amount;
            // this.ndPhone.getComponent(cc.Button).interactable = true;
            // this.ndPhone.getChildByName("jt").active = true;
        }

        if (this.ndPaymentPwd.active) {
            let payPwdNone = this.ndPaymentPwd.getChildByName("none")
            let payPwdNum = this.ndPaymentPwd.getChildByName("num")
            payPwdNone.active = !userData.withdraw_password
            payPwdNum.active = !!userData.withdraw_password
        }


        if (this.ndLoginPwd.active) {
            let loginPwdNone = this.ndLoginPwd.getChildByName("none")
            let loginPwdNum = this.ndLoginPwd.getChildByName("num")
            loginPwdNone.active = !userData.login_password
            loginPwdNum.active = !!userData.login_password
        }

    }
    //手机号修改
    phoneLoginBtnClick() {
        if (Global.getInstance().userdata.phone) {
            let isTest = false;
            for (const key in E_CAPABILITY_TYPE) {
                if (Object.prototype.hasOwnProperty.call(E_CAPABILITY_TYPE, key)) {
                    const element = E_CAPABILITY_TYPE[key];
                    if (element == Global.getInstance().userdata.type) {
                        isTest = true;
                        break;
                    }
                }
            }
            // if (isTest) return Global.getInstance().showSimpleTip("The mobile phone number cannot be modified for this account.")
            let data = {
                type: VERIFY_CODE_TYPE.ChangePhoneNum,
                verifyType: 15,//修改手机号 正确的type
                cb: () => {
                    //更改手机号
                    showPhoneNumber(PN_VERIFY_TYPE.ChangePhoneNumber, Global.getInstance().userdata.phone);
                }
            }
            showVerifyCode(data)
            return;
        }
        //设置手机号 默认
        showPhoneNumber()
    }
    //支付密码 修改
    walletPasswordBtnClick() {
        /*maya版本暂时不需要绑定
        if (!Global.getInstance().userdata.phone) {
            let data = {
                type: 1,
                verifyType: 14,
                title: 'For safety of your account, before set payment password, you must set phone number.'
            }
            ShowSecurityRequirement(data)
            return;
        }*/
        if (Global.getInstance().userdata.withdraw_password) {
            let data = {
                type: VERIFY_CODE_TYPE.ChangePaymentPwd,
                verifyType: 14,
                cb: () => {
                    this.showPasswordView();
                }
            }
            showVerifyCode(data)
        } else {
            this.showPasswordView();
        }
    }
    //登录密码修改
    loginPwdClick() {
        if (!Global.getInstance().userdata.phone) {
            let data = {
                type: 1,
                verifyType: 13,
                title: 'For safety of your account, before set login password, you must set phone number.'
            }
            ShowSecurityRequirement(data)
            return;
        }
        if(Global.getInstance().userdata.login_password){
            let data = {
                type: VERIFY_CODE_TYPE.ChangeLoginPwd,
                verifyType: 13,
                cb: () => {
                    uiManager.instance.showDialog(UI_PATH_DIC.SetLoginPassword,[{phone:Global.getInstance().userdata.phone}],null,DEEP_INDEXZ.PASSWORD);
                }
            }
            showVerifyCode(data)
        }else{
            //第一次 设置登陆密码的时候  geetest 验证先
            uiManager.instance.showDialog(UI_PATH_DIC.SetLoginPassword,[{phone:Global.getInstance().userdata.phone}],null,DEEP_INDEXZ.PASSWORD);
        }
    }
    onPhoneBindSuc(data: { bindType: string }) {
        this.refreshStatus()
        if (!data) return;
        if (data.bindType == "setPassword") {
            this.showPasswordView();
        }
    }
    onSetPasswordSuc() {
        this.refreshStatus()
    }
    onSetLoginPwdSuc() {
        this.refreshStatus()
    }
    closeAction() {
        this.hide()
    }
    
    //获取kyc 信息
    getKycData(cb?){ 
        HttpUtils.getInstance().post(3, 3, this, "/common/api/get/user/kyc", {
            token: Global.getInstance().token,
        }, (response) => {
            if (!this.node || !this.node.isValid) {
                return;
            }
            KycMgr.instance.kyc_data = response.data;
            let first_restriction = response.data.first_restriction;
            if (ALL_APP_SOURCE_CONFIG.channel == E_CHANEL_TYPE.G_CASH) {
                let details = this.generalView.getChildByName("details");
                let d_none = details.getChildByName('none')
                let d_title = details.getChildByName('title')
                d_title.y = 0;
                d_none.active = false;
                d_title.getComponent(cc.Label).string = 'Personal details';
                if (ALL_APP_SOURCE_CONFIG.channel == E_CHANEL_TYPE.G_CASH) {
                    d_title.y = 26;
                    d_none.active = true;
                    d_title.getComponent(cc.Label).string = 'KYC Details';
                    if (first_restriction) {
                        d_none.getComponent(cc.Label).string = 'Completed';
                        d_none.color = new cc.Color(17,190,107,255);
                    } else {
                        d_none.getComponent(cc.Label).string = 'Pending';
                        d_none.color = new cc.Color(232,166,0,255);
                    }
                }
            }
            if(cb){
                cb();
            }
        });
    }

    showKycDetials(){ 
        if (!KycMgr.instance.kyc_data) {
            this.getKycData(()=>{
                this.showKycDetials();
            });
            return;
        }
        let payCodeData = {
            user_account_info:[KycMgr.instance.kyc_data],
            all_account_code: null
        }    
        showChooseWallet(payCodeData, this.cur_fund_type)
    }

    showKycVerification(){ 
        if (!KycMgr.instance.kyc_data) {
            this.getKycData(()=>{
                this.showKycVerification();
            });
            return;
        }
        uiManager.instance.showDialog(UI_PATH_DIC.KYCVerification,[{status:1}],null,DEEP_INDEXZ.KYC);
    }

    perDetailsBtnClick() {
        let channel = ALL_APP_SOURCE_CONFIG.channel;
        this.cur_fund_type = E_FUND_TYPE.Maya;
        if(ALL_APP_SOURCE_CONFIG.channel == E_CHANEL_TYPE.MAYA){
            this.showKycDetials();
        } else if( channel == E_CHANEL_TYPE.G_CASH) {
            if (!KycMgr.instance.kyc_data) {
                this.getKycData(()=>{
                    let first_restriction = KycMgr.instance.kyc_data.first_restriction;
                    if (!first_restriction) {
                        this.showKycVerification();
                    } else {
                        this.showKycDetials();
                    }
                }); 
            } else {
                let first_restriction = KycMgr.instance.kyc_data.first_restriction;
                if (!first_restriction) {
                    this.showKycVerification();
                } else {
                    this.showKycDetials();
                }
            }
        } else if(channel == E_CHANEL_TYPE.WEB){
            this.getKycState(()=>{
                console.log('---------state:',this.kyc_state)
                switch (this.kyc_state) {
                    case KYC_STATE.KYC_COMPLETED:
                        this.showKycDetials();
                        break;
                    case KYC_STATE.KYC_ING:
                        this.showKycDetials();
                        //显示提示信息
                        break;
                    case KYC_STATE.KYC_REJECT:
                        //清除本地缓存信息 然后重新认证
                        Global.getInstance().setStoreageData(Global.GLOBAL_STORAGE_KEY.KYC,'');
                        uiManager.instance.showDialog(UI_PATH_DIC.KYCVerification,[{status:1}],null,DEEP_INDEXZ.KYC);
                        break;
                    case KYC_STATE.KYC_NO:
                        //打开kyc开始认证
                        // uiManager.instance.showDialog(UI_PATH_DIC.KYCVerification,[{status:this.kyc_simple}]);
                        KycMgr.instance.getBindPhone();//跳过第一步 直接开始第二步
                        break;
                    default:
                        break;
                }
            });
        }
    }

    showPasswordView() {
        //修改密码之后会通知 kyc 这里关闭kyc下一步
        KycMgr.instance.in_game_type = -1;//非验证渠道进入的话不会接受通知
        let cb = ()=>{
            this.scheduleOnce(()=>{
                let node = cc.instantiate(this.passwordPrefab);
                // node.position = new cc.Vec3(0,-960);
                node.parent = this.node;
            },0.01)
        }
        if (!this.passwordPrefab) {
            uiManager.instance.loadPrefabByLoading(UI_PATH_DIC.SetPayPassword,cc.Prefab,(finish, total, item) => {
            }, (err, prefab: any) => {
                    if (err) {
                        return;
                    }
                    this.passwordPrefab = prefab;
                    cb();
            });
        } else {
            cb();
        }
    }
}
