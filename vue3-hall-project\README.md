# Vue3 Hall Project

## 📖 项目概述

这是一个基于 Vue3 + TypeScript + Pinia 的游戏大厅项目，从 Cocos Creator TypeScript 代码转换而来。项目实现了完整的游戏大厅功能，包括游戏展示、交易记录、活动系统、用户管理等核心模块。

## 🚀 技术栈

- **框架**: Vue 3.4+ (Composition API)
- **语言**: TypeScript 5.2+
- **状态管理**: Pinia 2.1+
- **路由**: Vue Router 4.2+
- **构建工具**: Vite 4.4+
- **样式**: SCSS + CSS3
- **动画**: Animate.css
- **工具库**: @vueuse/core, Axios

## 📁 项目结构

```
vue3-hall-project/
├── public/                 # 静态资源
├── src/
│   ├── api/               # API 接口
│   ├── assets/            # 资源文件
│   ├── components/        # 组件
│   │   ├── Common/        # 通用组件
│   │   ├── Hall/          # 大厅相关组件
│   │   └── Transactions/  # 交易相关组件
│   ├── router/            # 路由配置
│   ├── stores/            # Pinia 状态管理
│   ├── styles/            # 样式文件
│   ├── types/             # TypeScript 类型定义
│   ├── utils/             # 工具函数
│   ├── views/             # 页面组件
│   ├── App.vue            # 根组件
│   └── main.ts            # 入口文件
├── package.json
├── vite.config.ts
└── README.md
```

## 🎯 核心功能模块

### 1. 大厅主页 (Hall)
- **游戏分类导航**: 支持多种游戏类型切换
- **Banner轮播**: 活动和推广内容展示
- **游戏列表**: 网格布局展示游戏，支持搜索和筛选
- **用户余额**: 实时显示和更新用户余额
- **功能按钮**: 转盘、红包、活动等快捷入口

### 2. 游戏系统 (AllGameView)
- **游戏分类**: Casino、Slots、Poker、Bingo等
- **搜索筛选**: 支持游戏名称搜索
- **游戏标签**: HOT、NEW等标签展示
- **收藏功能**: 游戏收藏和历史记录
- **特殊游戏**: 百家乐、轮盘、21点专区

### 3. 交易系统 (Transactions)
- **记录分类**: 充值、提现、奖励三大类型
- **状态管理**: 详细的交易状态跟踪
- **时间筛选**: 今天、昨天、最近3天、最近7天
- **详情查看**: 交易详情弹窗展示

### 4. Banner轮播 (BannerCarousel)
- **自动播放**: 可配置的自动轮播
- **手势支持**: 触摸滑动切换
- **分页指示**: 圆点指示器
- **导航按钮**: 左右切换按钮
- **响应式**: 适配不同屏幕尺寸

### 5. 状态管理 (Pinia Store)
- **用户状态**: 登录状态、余额信息
- **游戏数据**: 游戏列表、分类、搜索
- **交易记录**: 交易数据管理和筛选
- **活动数据**: 活动列表和有效性验证

## 🛠️ 开发指南

### 环境要求
- Node.js >= 18.0.0
- npm >= 8.0.0

### 安装依赖
```bash
npm install
```

### 开发模式
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

### 类型检查
```bash
npm run type-check
```

### 代码格式化
```bash
npm run format
```

### 代码检查
```bash
npm run lint
```

## 🎨 组件设计原则

### 1. 组件职责单一
每个组件只负责一个特定的功能，保持组件的简洁和可维护性。

### 2. Props 和 Emits 明确
使用 TypeScript 严格定义组件的输入和输出接口。

### 3. 响应式设计
所有组件都支持移动端和桌面端的响应式布局。

### 4. 性能优化
- 使用 `v-memo` 优化列表渲染
- 懒加载图片和组件
- 合理使用 `computed` 和 `watch`

## 📱 响应式设计

项目采用移动优先的响应式设计策略：

- **移动端**: < 768px
- **平板端**: 768px - 1024px  
- **桌面端**: > 1024px

## 🔧 配置说明

### Vite 配置
- 路径别名配置
- 代理服务器配置
- 构建优化配置

### 路由配置
- 路由守卫
- 页面过渡动画
- 权限控制

### 状态管理
- Pinia store 模块化
- 持久化存储
- 类型安全

## 🚀 部署指南

### 构建
```bash
npm run build
```

### 预览
```bash
npm run preview
```

### 部署到服务器
将 `dist` 目录上传到服务器即可。

## 🔄 从 Cocos Creator 转换说明

### 主要转换内容

1. **组件系统**: Cocos Creator 的 `@ccclass` 组件转换为 Vue 3 组件
2. **状态管理**: 全局状态从 Global 单例转换为 Pinia store
3. **事件系统**: Cocos 的事件系统转换为 Vue 的 emit/props 机制
4. **UI布局**: 从 Cocos 的节点树转换为 HTML/CSS 布局
5. **动画系统**: 从 Cocos 动画转换为 CSS 动画和 Animate.css

### 保留的业务逻辑

- 游戏分类和筛选逻辑
- 交易记录处理逻辑
- 用户状态管理逻辑
- Banner 轮播逻辑
- 活动系统逻辑

### 新增的功能

- 响应式设计
- 路由导航
- 更好的用户体验
- 现代化的 UI 设计
- 更好的性能优化

## 📝 开发注意事项

1. **类型安全**: 充分利用 TypeScript 的类型检查
2. **组件复用**: 提取通用组件，避免重复代码
3. **性能优化**: 注意大列表的虚拟滚动和图片懒加载
4. **错误处理**: 完善的错误边界和用户提示
5. **测试**: 编写单元测试和集成测试

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

MIT License

## 👥 开发团队

- 前端开发团队
- UI/UX 设计团队
- 后端开发团队

---

*最后更新: 2025-08-07*
