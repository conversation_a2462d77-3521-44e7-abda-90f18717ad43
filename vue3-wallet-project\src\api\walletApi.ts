import axios from 'axios'
import type { 
  WalletTaskInfo, 
  WalletTaskResponse, 
  WalletTaskRewardResponse 
} from '@/types/wallet'

// API基础配置
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || '/api'

// 创建axios实例
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    // 添加认证token
    const token = localStorage.getItem('auth_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    // 添加用户ID
    const userId = localStorage.getItem('user_id')
    if (userId) {
      config.headers['X-User-ID'] = userId
    }
    
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
apiClient.interceptors.response.use(
  (response) => {
    return response.data
  },
  (error) => {
    // 统一错误处理
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 401:
          // 未授权，清除token并跳转登录
          localStorage.removeItem('auth_token')
          localStorage.removeItem('user_id')
          window.location.href = '/login'
          break
        case 403:
          console.error('Access forbidden:', data.message)
          break
        case 404:
          console.error('API not found:', error.config.url)
          break
        case 500:
          console.error('Server error:', data.message)
          break
        default:
          console.error('API error:', data.message || error.message)
      }
      
      return Promise.reject(new Error(data.message || '请求失败'))
    } else if (error.request) {
      console.error('Network error:', error.message)
      return Promise.reject(new Error('网络连接失败'))
    } else {
      console.error('Request error:', error.message)
      return Promise.reject(error)
    }
  }
)

// 钱包任务API
export const walletApi = {
  /**
   * 获取钱包任务列表
   */
  async getTasks(): Promise<WalletTaskInfo[]> {
    try {
      const response: WalletTaskResponse = await apiClient.get('/wallet/tasks')
      
      if (response.code === 0) {
        return response.data || []
      } else {
        throw new Error(response.message || '获取任务列表失败')
      }
    } catch (error) {
      console.error('Failed to get wallet tasks:', error)
      throw error
    }
  },

  /**
   * 解锁钱包任务
   * @param taskId 任务ID
   */
  async unlockTask(taskId: string): Promise<void> {
    try {
      const response = await apiClient.post('/wallet/tasks/unlock', {
        task_id: taskId
      })
      
      if (response.code !== 0) {
        throw new Error(response.message || '解锁任务失败')
      }
    } catch (error) {
      console.error('Failed to unlock task:', error)
      throw error
    }
  },

  /**
   * 领取任务奖励
   * @param taskId 任务ID
   */
  async collectReward(taskId: string): Promise<{ rewardAmount: number; newBalance: number }> {
    try {
      const response: WalletTaskRewardResponse = await apiClient.post('/wallet/tasks/collect', {
        task_id: taskId
      })
      
      if (response.code === 0) {
        return {
          rewardAmount: response.data.reward_amount,
          newBalance: response.data.new_balance
        }
      } else {
        throw new Error(response.message || '领取奖励失败')
      }
    } catch (error) {
      console.error('Failed to collect reward:', error)
      throw error
    }
  },

  /**
   * 更新任务进度
   * @param taskId 任务ID
   * @param progress 进度数据
   */
  async updateProgress(taskId: string, progress: {
    bet_amount?: number
    recharge_done?: boolean
  }): Promise<void> {
    try {
      const response = await apiClient.post('/wallet/tasks/progress', {
        task_id: taskId,
        ...progress
      })
      
      if (response.code !== 0) {
        throw new Error(response.message || '更新进度失败')
      }
    } catch (error) {
      console.error('Failed to update progress:', error)
      throw error
    }
  },

  /**
   * 获取用户余额
   */
  async getBalance(): Promise<number> {
    try {
      const response = await apiClient.get('/user/balance')
      
      if (response.code === 0) {
        return response.data.balance || 0
      } else {
        throw new Error(response.message || '获取余额失败')
      }
    } catch (error) {
      console.error('Failed to get balance:', error)
      throw error
    }
  },

  /**
   * 获取任务统计信息
   */
  async getTaskStats(): Promise<{
    total: number
    completed: number
    totalRewards: number
  }> {
    try {
      const response = await apiClient.get('/wallet/tasks/stats')
      
      if (response.code === 0) {
        return response.data
      } else {
        throw new Error(response.message || '获取统计信息失败')
      }
    } catch (error) {
      console.error('Failed to get task stats:', error)
      throw error
    }
  },

  /**
   * 检查任务有效性
   * @param taskId 任务ID
   */
  async checkTaskValidity(taskId: string): Promise<boolean> {
    try {
      const response = await apiClient.get(`/wallet/tasks/${taskId}/validity`)
      
      if (response.code === 0) {
        return response.data.is_valid || false
      } else {
        return false
      }
    } catch (error) {
      console.error('Failed to check task validity:', error)
      return false
    }
  },

  /**
   * 获取任务详情
   * @param taskId 任务ID
   */
  async getTaskDetail(taskId: string): Promise<WalletTaskInfo | null> {
    try {
      const response = await apiClient.get(`/wallet/tasks/${taskId}`)
      
      if (response.code === 0) {
        return response.data
      } else {
        throw new Error(response.message || '获取任务详情失败')
      }
    } catch (error) {
      console.error('Failed to get task detail:', error)
      return null
    }
  },

  /**
   * 删除过期任务
   * @param taskId 任务ID
   */
  async removeExpiredTask(taskId: string): Promise<void> {
    try {
      const response = await apiClient.delete(`/wallet/tasks/${taskId}`)
      
      if (response.code !== 0) {
        throw new Error(response.message || '删除任务失败')
      }
    } catch (error) {
      console.error('Failed to remove expired task:', error)
      throw error
    }
  }
}

// 导出默认实例
export default walletApi

// 工具函数
export const walletApiUtils = {
  /**
   * 批量处理任务操作
   * @param taskIds 任务ID数组
   * @param operation 操作类型
   */
  async batchOperation(
    taskIds: string[], 
    operation: 'unlock' | 'collect' | 'remove'
  ): Promise<{ success: string[]; failed: string[] }> {
    const results = {
      success: [] as string[],
      failed: [] as string[]
    }

    for (const taskId of taskIds) {
      try {
        switch (operation) {
          case 'unlock':
            await walletApi.unlockTask(taskId)
            break
          case 'collect':
            await walletApi.collectReward(taskId)
            break
          case 'remove':
            await walletApi.removeExpiredTask(taskId)
            break
        }
        results.success.push(taskId)
      } catch (error) {
        console.error(`Failed to ${operation} task ${taskId}:`, error)
        results.failed.push(taskId)
      }
    }

    return results
  },

  /**
   * 轮询任务状态更新
   * @param taskId 任务ID
   * @param callback 状态更新回调
   * @param interval 轮询间隔（毫秒）
   */
  pollTaskStatus(
    taskId: string,
    callback: (task: WalletTaskInfo | null) => void,
    interval: number = 5000
  ): () => void {
    const poll = async () => {
      try {
        const task = await walletApi.getTaskDetail(taskId)
        callback(task)
      } catch (error) {
        console.error('Failed to poll task status:', error)
        callback(null)
      }
    }

    const timer = setInterval(poll, interval)
    
    // 立即执行一次
    poll()

    // 返回清理函数
    return () => clearInterval(timer)
  },

  /**
   * 格式化API错误信息
   * @param error 错误对象
   */
  formatError(error: any): string {
    if (typeof error === 'string') {
      return error
    }
    
    if (error?.message) {
      return error.message
    }
    
    if (error?.response?.data?.message) {
      return error.response.data.message
    }
    
    return '操作失败，请稍后重试'
  }
}
