import type { 
  WalletTaskInfo, 
  TaskProgress, 
  TaskCountdown,
  WalletTaskStats 
} from '@/types/wallet'
import { WalletTaskStatus } from '@/types/wallet'

/**
 * 钱包任务工具函数
 */
export const walletUtils = {
  /**
   * 格式化数字显示
   * @param num 数字
   * @param decimals 小数位数
   * @param showCommas 是否显示千分位分隔符
   */
  formatNumber(num: number | string, decimals: number = 2, showCommas: boolean = true): string {
    const value = typeof num === 'string' ? parseFloat(num) : num
    
    if (isNaN(value)) return '0.00'
    
    const options: Intl.NumberFormatOptions = {
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals
    }
    
    if (showCommas) {
      return value.toLocaleString('en-US', options)
    } else {
      return value.toFixed(decimals)
    }
  },

  /**
   * 格式化任务标题
   * @param title 原始标题
   * @param maxLength 最大长度
   */
  formatTaskTitle(title: string, maxLength: number = 30): string {
    if (!title) return ''
    
    if (title.length <= maxLength) {
      return title
    }
    
    return title.substring(0, maxLength - 3) + '...'
  },

  /**
   * 计算任务进度
   * @param task 任务信息
   */
  calculateTaskProgress(task: WalletTaskInfo): TaskProgress {
    const betCurrent = Number(task.bet_num) || 0
    const betTotal = task.bet_target_value || 0
    const rechargeTarget = Number(task.recharge_target_value) || 0
    
    // 计算投注进度
    const betProgress = betTotal > 0 ? Math.min(betCurrent / betTotal, 1) : 1
    
    // 计算充值进度
    const rechargeProgress = rechargeTarget > 0 ? (task.recharge_done ? 1 : 0) : 1
    
    // 总进度（投注和充值都需要完成）
    const totalProgress = Math.min(betProgress, rechargeProgress)
    
    return {
      current: betCurrent,
      total: betTotal,
      percentage: totalProgress * 100,
      isCompleted: betProgress >= 1 && rechargeProgress >= 1
    }
  },

  /**
   * 计算任务倒计时
   * @param task 任务信息
   */
  calculateTaskCountdown(task: WalletTaskInfo): TaskCountdown {
    if (task.expire_time === 0) {
      return {
        hours: 0,
        minutes: 0,
        seconds: 0,
        totalSeconds: 0,
        isExpired: false
      }
    }

    const now = Date.now() / 1000
    const createdTime = Number(task.created_at)
    const expireTimestamp = createdTime + (task.expire_time * 3600)
    const diffSeconds = Math.max(0, expireTimestamp - now)

    const hours = Math.floor(diffSeconds / 3600)
    const minutes = Math.floor((diffSeconds % 3600) / 60)
    const seconds = Math.floor(diffSeconds % 60)

    return {
      hours,
      minutes,
      seconds,
      totalSeconds: diffSeconds,
      isExpired: diffSeconds <= 0
    }
  },

  /**
   * 格式化倒计时显示
   * @param countdown 倒计时对象
   */
  formatCountdown(countdown: TaskCountdown): string {
    const { hours, minutes, seconds } = countdown
    
    if (countdown.isExpired) {
      return '已过期'
    }
    
    if (hours > 0) {
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
    } else {
      return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
    }
  },

  /**
   * 检查任务是否过期
   * @param task 任务信息
   */
  isTaskExpired(task: WalletTaskInfo): boolean {
    if (task.expire_time === 0) return false
    
    const countdown = this.calculateTaskCountdown(task)
    return countdown.isExpired
  },

  /**
   * 获取任务状态文本
   * @param status 任务状态
   */
  getTaskStatusText(status: WalletTaskStatus): string {
    const statusMap = {
      [WalletTaskStatus.LOCK]: '未解锁',
      [WalletTaskStatus.ONGOING]: '进行中',
      [WalletTaskStatus.FINISHED]: '已完成',
      [WalletTaskStatus.EXPIRED]: '已过期',
      [WalletTaskStatus.DELETE]: '已删除'
    }
    
    return statusMap[status] || '未知状态'
  },

  /**
   * 获取任务状态颜色
   * @param status 任务状态
   */
  getTaskStatusColor(status: WalletTaskStatus): string {
    const colorMap = {
      [WalletTaskStatus.LOCK]: '#999999',
      [WalletTaskStatus.ONGOING]: '#4CAF50',
      [WalletTaskStatus.FINISHED]: '#2196F3',
      [WalletTaskStatus.EXPIRED]: '#FF6B6B',
      [WalletTaskStatus.DELETE]: '#999999'
    }
    
    return colorMap[status] || '#999999'
  },

  /**
   * 过滤有效任务
   * @param tasks 任务列表
   */
  filterValidTasks(tasks: WalletTaskInfo[]): WalletTaskInfo[] {
    return tasks.filter(task => {
      // 过滤删除的任务
      if (task.task_status === WalletTaskStatus.DELETE) {
        return false
      }
      
      // 检查是否过期
      if (this.isTaskExpired(task)) {
        return false
      }
      
      return true
    })
  },

  /**
   * 按状态排序任务
   * @param tasks 任务列表
   */
  sortTasksByStatus(tasks: WalletTaskInfo[]): WalletTaskInfo[] {
    const statusPriority = {
      [WalletTaskStatus.FINISHED]: 4,
      [WalletTaskStatus.ONGOING]: 3,
      [WalletTaskStatus.LOCK]: 2,
      [WalletTaskStatus.EXPIRED]: 1,
      [WalletTaskStatus.DELETE]: 0
    }
    
    return [...tasks].sort((a, b) => {
      const aPriority = statusPriority[a.task_status] || 0
      const bPriority = statusPriority[b.task_status] || 0
      
      if (aPriority !== bPriority) {
        return bPriority - aPriority
      }
      
      // 相同状态按创建时间排序
      return Number(b.created_at) - Number(a.created_at)
    })
  },

  /**
   * 计算任务统计信息
   * @param tasks 任务列表
   */
  calculateTaskStats(tasks: WalletTaskInfo[]): WalletTaskStats {
    const stats: WalletTaskStats = {
      total: tasks.length,
      locked: 0,
      ongoing: 0,
      finished: 0,
      expired: 0,
      totalRewards: 0,
      completionRate: 0
    }

    tasks.forEach(task => {
      switch (task.task_status) {
        case WalletTaskStatus.LOCK:
          stats.locked++
          break
        case WalletTaskStatus.ONGOING:
          stats.ongoing++
          break
        case WalletTaskStatus.FINISHED:
          stats.finished++
          stats.totalRewards += Number(task.bonus) || 0
          break
        case WalletTaskStatus.EXPIRED:
          stats.expired++
          break
      }
    })

    if (stats.total > 0) {
      stats.completionRate = (stats.finished / stats.total) * 100
    }

    return stats
  },

  /**
   * 检查是否有进行中的任务
   * @param tasks 任务列表
   */
  hasOngoingTask(tasks: WalletTaskInfo[]): boolean {
    return tasks.some(task => task.task_status === WalletTaskStatus.ONGOING)
  },

  /**
   * 获取下一个可解锁的任务
   * @param tasks 任务列表
   */
  getNextUnlockableTask(tasks: WalletTaskInfo[]): WalletTaskInfo | null {
    const lockedTasks = tasks.filter(task => task.task_status === WalletTaskStatus.LOCK)
    
    if (lockedTasks.length === 0) return null
    
    // 按创建时间排序，返回最早的
    lockedTasks.sort((a, b) => Number(a.created_at) - Number(b.created_at))
    
    return lockedTasks[0]
  },

  /**
   * 验证任务数据完整性
   * @param task 任务信息
   */
  validateTaskData(task: WalletTaskInfo): boolean {
    if (!task.id || !task.task_name || !task.bonus) {
      return false
    }
    
    if (task.bet_target_value < 0 || Number(task.bonus) <= 0) {
      return false
    }
    
    if (task.expire_time < 0) {
      return false
    }
    
    return true
  },

  /**
   * 生成任务分享文本
   * @param task 任务信息
   */
  generateShareText(task: WalletTaskInfo): string {
    const reward = this.formatNumber(task.bonus)
    const title = this.formatTaskTitle(task.task_name, 20)
    
    return `🎯 ${title}\n💰 奖励：₱${reward}\n快来完成任务赢取奖励吧！`
  },

  /**
   * 解析游戏类型
   * @param gameTypes 游戏类型数组
   */
  parseGameTypes(gameTypes: string[]): string {
    if (!gameTypes || gameTypes.length === 0) {
      return '所有游戏'
    }
    
    const typeMap: Record<string, string> = {
      'casino': '娱乐场',
      'slots': '老虎机',
      'poker': '扑克',
      'bingo': '宾果',
      'arcade': '街机',
      'sports': '体育'
    }
    
    const translated = gameTypes.map(type => typeMap[type.toLowerCase()] || type)
    
    if (translated.length > 3) {
      return `${translated.slice(0, 3).join('、')}等`
    }
    
    return translated.join('、')
  },

  /**
   * 生成任务进度描述
   * @param task 任务信息
   */
  generateProgressDescription(task: WalletTaskInfo): string {
    const progress = this.calculateTaskProgress(task)
    const betTarget = task.bet_target_value
    const rechargeTarget = Number(task.recharge_target_value)
    
    const descriptions: string[] = []
    
    if (betTarget > 0) {
      const betCurrent = Number(task.bet_num)
      const betRemaining = Math.max(0, betTarget - betCurrent)
      
      if (betRemaining > 0) {
        descriptions.push(`还需投注 ₱${this.formatNumber(betRemaining)}`)
      } else {
        descriptions.push('投注要求已完成')
      }
    }
    
    if (rechargeTarget > 0) {
      if (task.recharge_done) {
        descriptions.push('充值要求已完成')
      } else {
        descriptions.push(`需充值 ₱${this.formatNumber(rechargeTarget)}`)
      }
    }
    
    return descriptions.join('，')
  }
}

export default walletUtils
