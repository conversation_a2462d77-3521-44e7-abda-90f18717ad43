import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { 
  WalletTaskInfo, 
  WalletTaskFilter, 
  WalletTaskStats,
  TaskProgress,
  TaskCountdown,
  WalletTaskConfig
} from '@/types/wallet'
import { 
  WalletTaskStatus, 
  DEFAULT_WALLET_CONFIG,
  WALLET_STORAGE_KEYS 
} from '@/types/wallet'

export const useWalletStore = defineStore('wallet', () => {
  // 状态
  const tasks = ref<WalletTaskInfo[]>([])
  const isLoading = ref(false)
  const showGuide = ref(false)
  const selectedTask = ref<WalletTaskInfo | null>(null)
  const filter = ref<WalletTaskFilter>({})
  const config = ref<WalletTaskConfig>(DEFAULT_WALLET_CONFIG)
  const userBalance = ref(0)
  const lastRefreshTime = ref<number>(0)

  // 计算属性
  const filteredTasks = computed(() => {
    let filtered = tasks.value

    // 按状态筛选
    if (filter.value.status && filter.value.status.length > 0) {
      filtered = filtered.filter(task => 
        filter.value.status!.includes(task.task_status as WalletTaskStatus)
      )
    }

    // 按时间限制筛选
    if (filter.value.hasTimeLimit !== undefined) {
      filtered = filtered.filter(task => 
        filter.value.hasTimeLimit ? task.expire_time > 0 : task.expire_time === 0
      )
    }

    // 排序
    if (filter.value.sortBy) {
      filtered.sort((a, b) => {
        const aValue = getTaskSortValue(a, filter.value.sortBy!)
        const bValue = getTaskSortValue(b, filter.value.sortBy!)
        
        if (filter.value.sortOrder === 'desc') {
          return bValue - aValue
        }
        return aValue - bValue
      })
    } else {
      // 默认排序：已完成 > 进行中 > 未解锁 > 已过期
      filtered.sort((a, b) => {
        const statusPriority = {
          [WalletTaskStatus.FINISHED]: 4,
          [WalletTaskStatus.ONGOING]: 3,
          [WalletTaskStatus.LOCK]: 2,
          [WalletTaskStatus.EXPIRED]: 1,
          [WalletTaskStatus.DELETE]: 0
        }
        return statusPriority[b.task_status] - statusPriority[a.task_status]
      })
    }

    return filtered
  })

  const taskStats = computed((): WalletTaskStats => {
    const stats = {
      total: tasks.value.length,
      locked: 0,
      ongoing: 0,
      finished: 0,
      expired: 0,
      totalRewards: 0,
      completionRate: 0
    }

    tasks.value.forEach(task => {
      switch (task.task_status) {
        case WalletTaskStatus.LOCK:
          stats.locked++
          break
        case WalletTaskStatus.ONGOING:
          stats.ongoing++
          break
        case WalletTaskStatus.FINISHED:
          stats.finished++
          stats.totalRewards += Number(task.bonus)
          break
        case WalletTaskStatus.EXPIRED:
          stats.expired++
          break
      }
    })

    if (stats.total > 0) {
      stats.completionRate = (stats.finished / stats.total) * 100
    }

    return stats
  })

  const hasOngoingTask = computed(() => {
    return tasks.value.some(task => task.task_status === WalletTaskStatus.ONGOING)
  })

  const availableTasks = computed(() => {
    return tasks.value.filter(task => 
      task.task_status !== WalletTaskStatus.DELETE &&
      task.task_status !== WalletTaskStatus.EXPIRED
    )
  })

  // 方法
  function getTaskSortValue(task: WalletTaskInfo, sortBy: string): number {
    switch (sortBy) {
      case 'created_at':
        return new Date(task.created_at).getTime()
      case 'bonus':
        return Number(task.bonus)
      case 'expire_time':
        return task.expire_time
      default:
        return 0
    }
  }

  function setTasks(newTasks: WalletTaskInfo[]) {
    // 过滤掉过期和删除的任务
    const validTasks = newTasks.filter(task => {
      if (task.task_status === WalletTaskStatus.DELETE) return false
      
      // 检查是否过期
      if (task.expire_time > 0) {
        const now = Date.now() / 1000
        const expireTimestamp = Number(task.created_at) + (task.expire_time * 3600)
        if (expireTimestamp <= now) {
          task.task_status = WalletTaskStatus.EXPIRED
          return false
        }
      }
      
      return true
    })

    tasks.value = validTasks
    lastRefreshTime.value = Date.now()
  }

  function addTask(task: WalletTaskInfo) {
    const existingIndex = tasks.value.findIndex(t => t.id === task.id)
    if (existingIndex >= 0) {
      tasks.value[existingIndex] = task
    } else {
      tasks.value.push(task)
    }
  }

  function removeTask(taskId: string) {
    const index = tasks.value.findIndex(task => task.id === taskId)
    if (index >= 0) {
      tasks.value.splice(index, 1)
    }
  }

  function updateTaskStatus(taskId: string, status: WalletTaskStatus) {
    const task = tasks.value.find(t => t.id === taskId)
    if (task) {
      task.task_status = status
      task.updated_at = new Date().toISOString()
    }
  }

  function updateTaskProgress(taskId: string, betNum: string, rechargeDone?: boolean) {
    const task = tasks.value.find(t => t.id === taskId)
    if (task) {
      task.bet_num = betNum
      if (rechargeDone !== undefined) {
        task.recharge_done = rechargeDone
      }
      
      // 检查是否完成
      const betProgress = Number(betNum) >= task.bet_target_value
      const rechargeProgress = task.recharge_target_value === '0' || task.recharge_done
      
      if (betProgress && rechargeProgress && task.task_status === WalletTaskStatus.ONGOING) {
        task.task_status = WalletTaskStatus.FINISHED
      }
    }
  }

  function getTaskProgress(task: WalletTaskInfo): TaskProgress {
    const betCurrent = Number(task.bet_num)
    const betTotal = task.bet_target_value
    const betProgress = betTotal > 0 ? Math.min(betCurrent / betTotal, 1) : 1

    return {
      current: betCurrent,
      total: betTotal,
      percentage: betProgress * 100,
      isCompleted: betProgress >= 1 && (task.recharge_target_value === '0' || task.recharge_done)
    }
  }

  function getTaskCountdown(task: WalletTaskInfo): TaskCountdown {
    if (task.expire_time === 0) {
      return {
        hours: 0,
        minutes: 0,
        seconds: 0,
        totalSeconds: 0,
        isExpired: false
      }
    }

    const now = Date.now() / 1000
    const expireTimestamp = Number(task.created_at) + (task.expire_time * 3600)
    const diffSeconds = Math.max(0, expireTimestamp - now)

    const hours = Math.floor(diffSeconds / 3600)
    const minutes = Math.floor((diffSeconds % 3600) / 60)
    const seconds = Math.floor(diffSeconds % 60)

    return {
      hours,
      minutes,
      seconds,
      totalSeconds: diffSeconds,
      isExpired: diffSeconds <= 0
    }
  }

  function setFilter(newFilter: Partial<WalletTaskFilter>) {
    filter.value = { ...filter.value, ...newFilter }
  }

  function clearFilter() {
    filter.value = {}
  }

  function setSelectedTask(task: WalletTaskInfo | null) {
    selectedTask.value = task
  }

  function setLoading(loading: boolean) {
    isLoading.value = loading
  }

  function setShowGuide(show: boolean) {
    showGuide.value = show
    if (show) {
      localStorage.setItem(WALLET_STORAGE_KEYS.GUIDE_SHOWN, 'false')
    } else {
      localStorage.setItem(WALLET_STORAGE_KEYS.GUIDE_SHOWN, 'true')
    }
  }

  function updateBalance(balance: number) {
    userBalance.value = balance
  }

  function setConfig(newConfig: Partial<WalletTaskConfig>) {
    config.value = { ...config.value, ...newConfig }
    localStorage.setItem(WALLET_STORAGE_KEYS.USER_PREFERENCES, JSON.stringify(config.value))
  }

  function loadConfig() {
    const saved = localStorage.getItem(WALLET_STORAGE_KEYS.USER_PREFERENCES)
    if (saved) {
      try {
        config.value = { ...DEFAULT_WALLET_CONFIG, ...JSON.parse(saved) }
      } catch (error) {
        console.error('Failed to load wallet config:', error)
      }
    }
  }

  function checkGuideStatus(): boolean {
    const guideShown = localStorage.getItem(WALLET_STORAGE_KEYS.GUIDE_SHOWN)
    return guideShown !== 'true' && tasks.value.length > 0
  }

  function formatTaskTitle(title: string): string {
    if (title.length > 30) {
      return title.substring(0, 27) + '...'
    }
    return title
  }

  function formatNumber(num: number | string): string {
    const value = typeof num === 'string' ? parseFloat(num) : num
    return value.toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    })
  }

  // 初始化
  function initialize() {
    loadConfig()
    showGuide.value = checkGuideStatus()
  }

  return {
    // 状态
    tasks,
    isLoading,
    showGuide,
    selectedTask,
    filter,
    config,
    userBalance,
    lastRefreshTime,

    // 计算属性
    filteredTasks,
    taskStats,
    hasOngoingTask,
    availableTasks,

    // 方法
    setTasks,
    addTask,
    removeTask,
    updateTaskStatus,
    updateTaskProgress,
    getTaskProgress,
    getTaskCountdown,
    setFilter,
    clearFilter,
    setSelectedTask,
    setLoading,
    setShowGuide,
    updateBalance,
    setConfig,
    loadConfig,
    checkGuideStatus,
    formatTaskTitle,
    formatNumber,
    initialize
  }
})
