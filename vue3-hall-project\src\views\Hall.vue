<template>
  <div class="hall-page">
    <!-- 顶部导航栏 -->
    <header class="hall-header">
      <div class="header-content">
        <div class="logo">
          <img src="/images/logo.png" alt="Logo" class="logo-image" />
        </div>
        
        <div class="header-actions">
          <button v-if="!hallStore.isLoggedIn" class="login-btn" @click="goToLogin">
            登录
          </button>
          <div v-else class="user-info">
            <div class="balance-display" @click="refreshBalance">
              <span class="balance-label">余额</span>
              <span class="balance-amount">₱{{ hallStore.formattedBalance }}</span>
            </div>
            <button class="profile-btn" @click="goToProfile">
              <i class="icon-user"></i>
            </button>
          </div>
        </div>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="hall-main">
      <HallMain />
    </main>

    <!-- 底部导航栏 -->
    <nav class="bottom-nav">
      <button 
        v-for="navItem in navItems" 
        :key="navItem.name"
        class="nav-item"
        :class="{ active: currentNav === navItem.name }"
        @click="navigateTo(navItem.route)"
      >
        <i :class="navItem.icon"></i>
        <span class="nav-label">{{ navItem.label }}</span>
        <span v-if="navItem.badge" class="nav-badge">{{ navItem.badge }}</span>
      </button>
    </nav>

    <!-- 浮动按钮 -->
    <div class="floating-buttons">
      <!-- 客服按钮 -->
      <button class="floating-btn service-btn" @click="openService">
        <i class="icon-headset"></i>
      </button>
      
      <!-- 返回顶部按钮 -->
      <button 
        v-show="showBackToTop"
        class="floating-btn back-to-top-btn" 
        @click="scrollToTop"
      >
        <i class="icon-arrow-up"></i>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useHallStore } from '@/stores/hallStore'
import HallMain from '@/components/Hall/HallMain.vue'

const router = useRouter()
const route = useRoute()
const hallStore = useHallStore()

// 响应式数据
const currentNav = ref('hall')
const showBackToTop = ref(false)

// 底部导航配置
const navItems = [
  {
    name: 'hall',
    label: '大厅',
    icon: 'icon-home',
    route: '/hall'
  },
  {
    name: 'games',
    label: '游戏',
    icon: 'icon-game-controller',
    route: '/games'
  },
  {
    name: 'activities',
    label: '活动',
    icon: 'icon-gift',
    route: '/activities',
    badge: computed(() => hallStore.validActivities.length || null)
  },
  {
    name: 'transactions',
    label: '记录',
    icon: 'icon-receipt',
    route: '/transactions'
  },
  {
    name: 'profile',
    label: '我的',
    icon: 'icon-user',
    route: '/profile'
  }
]

// 方法
function navigateTo(route: string) {
  if (route === '/profile' && !hallStore.isLoggedIn) {
    goToLogin()
    return
  }
  
  if (route === '/transactions' && !hallStore.isLoggedIn) {
    goToLogin()
    return
  }
  
  router.push(route)
  updateCurrentNav(route)
}

function updateCurrentNav(routePath: string) {
  const navItem = navItems.find(item => item.route === routePath)
  if (navItem) {
    currentNav.value = navItem.name
  }
}

function goToLogin() {
  router.push({ 
    name: 'Login',
    query: { redirect: route.fullPath }
  })
}

function goToProfile() {
  router.push('/profile')
}

function refreshBalance() {
  if (!hallStore.isLoggedIn) return
  
  // TODO: 实现余额刷新
  console.log('Refreshing balance...')
}

function openService() {
  // TODO: 打开客服
  console.log('Opening customer service...')
}

function scrollToTop() {
  window.scrollTo({
    top: 0,
    behavior: 'smooth'
  })
}

function handleScroll() {
  showBackToTop.value = window.scrollY > 300
}

// 生命周期
onMounted(() => {
  // 根据当前路由设置导航状态
  updateCurrentNav(route.path)
  
  // 添加滚动监听
  window.addEventListener('scroll', handleScroll)
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})
</script>

<style scoped>
.hall-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
}

.hall-header {
  position: sticky;
  top: 0;
  z-index: 100;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.logo {
  display: flex;
  align-items: center;
}

.logo-image {
  height: 32px;
  width: auto;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.login-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  background: #4CAF50;
  color: white;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
}

.login-btn:hover {
  background: #45A049;
  transform: translateY(-1px);
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.balance-display {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 6px;
  transition: background-color 0.3s ease;
}

.balance-display:hover {
  background: rgba(255, 255, 255, 0.1);
}

.balance-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
}

.balance-amount {
  font-size: 16px;
  font-weight: bold;
  color: white;
}

.profile-btn {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 18px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.profile-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
}

.hall-main {
  flex: 1;
  padding-bottom: 80px; /* 为底部导航留出空间 */
}

.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  padding: 8px 0;
  z-index: 100;
}

.nav-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 8px 4px;
  border: none;
  background: none;
  color: #666;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.nav-item:hover {
  color: #4CAF50;
}

.nav-item.active {
  color: #4CAF50;
  font-weight: bold;
}

.nav-item i {
  font-size: 20px;
}

.nav-label {
  font-size: 12px;
}

.nav-badge {
  position: absolute;
  top: 4px;
  right: 20%;
  background: #FF4444;
  color: white;
  border-radius: 10px;
  padding: 2px 6px;
  font-size: 10px;
  min-width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.floating-buttons {
  position: fixed;
  right: 20px;
  bottom: 100px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  z-index: 99;
}

.floating-btn {
  width: 48px;
  height: 48px;
  border: none;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  font-size: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.floating-btn:hover {
  background: rgba(0, 0, 0, 0.8);
  transform: scale(1.1);
}

.service-btn {
  background: linear-gradient(135deg, #4CAF50, #45A049);
}

.back-to-top-btn {
  background: linear-gradient(135deg, #2196F3, #1976D2);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    padding: 10px 16px;
  }
  
  .logo-image {
    height: 28px;
  }
  
  .balance-amount {
    font-size: 14px;
  }
  
  .floating-buttons {
    right: 16px;
    bottom: 90px;
  }
  
  .floating-btn {
    width: 44px;
    height: 44px;
    font-size: 18px;
  }
}

@media (max-width: 480px) {
  .header-content {
    padding: 8px 12px;
  }
  
  .user-info {
    gap: 8px;
  }
  
  .balance-display {
    padding: 2px 4px;
  }
  
  .balance-amount {
    font-size: 12px;
  }
  
  .profile-btn {
    width: 32px;
    height: 32px;
    font-size: 16px;
  }
}
</style>
