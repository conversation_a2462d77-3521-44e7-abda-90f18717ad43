import { showChooseBankCard } from "./ChooseBankCard";
import { showChooseWallet } from "./ChooseWallet";
import CompBubble from "./CompBubble";
import BackgroundAdapter, { FIT_POLICY } from "./component/BackgroundAdapter";
import UIPopIn from "./component/UIPopIn";
import { E_FUND_TYPE, E_TRANSACTION_TYPE, PASS_TYPE, PRODUCT_TYPE, UI_PATH_DIC, UI_POP_OUT_TIME } from "./GlobalConstant";
import Global from "./GlobalScript";
import { showPleaseRecharge } from "./hall/PleaseRecharge";
import { uiManager } from "./mgr/UIManager";
import HttpUtils from "./net/HttpUtils";
import NewShopItem from "./NewShopItem";
import ThirdPay from "./ThirdPay";
import utils from "./utils/utils";
import ZendeskManager from "./ZendeskManager";
const { ccclass, property } = cc._decorator;


const TOG_ANIM_TIME = 0.3
@ccclass
export default class NewShop extends UIPopIn {

    @property(cc.ToggleContainer)
    leftCheckboxs: cc.ToggleContainer = null;

    @property(cc.ScrollView)
    shopScv: cc.ScrollView = null;

    @property(cc.ScrollView)
    storeScv: cc.ScrollView = null;

    @property(cc.Node)
    shopContent: cc.Node = null;

    @property(cc.Node)
    storeContent: cc.Node = null;

    @property(cc.Node)
    ndThirdPay: cc.Node = null;

    @property(cc.Node)
    ndRedeemBubble: cc.Node = null;

    @property(cc.Prefab)
    m_shopItem_prefab: cc.Prefab = null;

    @property([cc.SpriteFrame])
    m_store_frame: cc.SpriteFrame[] = [];

    @property([cc.SpriteFrame])
    m_bg_frame: cc.SpriteFrame[] = [];

    @property(cc.Label)
    m_money: cc.Label = null;

    @property(cc.Node)
    redeemTog: cc.Node = null

    @property(cc.Node)
    ndGameRecord: cc.Node = null
    @property(cc.Node)
    ndAccount: cc.Node = null
    @property(cc.Node)
    ndRole: cc.Node = null

    @property(BackgroundAdapter)
    bgAdpter: BackgroundAdapter = null

    togType = 0;
    curProductId: any = "";
    isCLoseClicked = false  //是否已经点击过关闭
    shopOriginPos: cc.Vec2 = null;
    storeOriginPos: cc.Vec2 = null;

    record_data: any = {};
    cur_type: number = 0;
    curRedeemItem: any = null;
    initData: any = null;

    onLoad() {
        cc.director.on('goPayDirect', this.goPayDirect, this)
        let clickEventHandler = new cc.Component.EventHandler();
        clickEventHandler.target = this.node;
        clickEventHandler.component = "NewShopUI";
        clickEventHandler.handler = "checkBtnAction";
        this.leftCheckboxs.checkEvents = [clickEventHandler];
        if (Global.getInstance().isReview()) {
            this.leftCheckboxs.node.children[1].active = false
            // this.leftCheckboxs.node.children[2].active = false
            this.ndGameRecord.active = false
            this.ndAccount.active = false
        }
        if (Global.getInstance().mayaMode) {
            this.ndAccount.active = false
        }
        Global.getInstance().updateBalanceAfterGame()
        this.preLoadPrefab()
        setTimeout(() => {
            if (cc.sys.isBrowser && this.bgAdpter) {
                if (cc.winSize.width / cc.winSize.height >= 2) {
                    this.bgAdpter.setFitPolicy(FIT_POLICY.FIX)
                }
            }
        });
    }


    onEnable() {
        this.setRoomInfo()
        cc.director.on('CloseShop', this.closeAction, this)
        cc.director.on("purchase_suc", this.purchaseSuc, this);
        // cc.director.on("purchase_fail", this.purchaseFail, this);
        cc.director.on("update_gold", this.setRoomInfo, this);
        cc.director.on("password_suc", this.onPasswordSuc, this);
        cc.director.on("pay_event", this.onPayEvent, this);
        cc.director.on("closeBubble", this.onCloseBubble, this);
        cc.director.on("closeBubbleRoot", this.onCloseBubbleRoot, this);
    }
    onDisable() {
        cc.director.off('CloseShop', this.closeAction, this)
        // cc.director.off("purchase_fail", this.purchaseFail, this);
        cc.director.off("purchase_suc", this.purchaseSuc, this);
        cc.director.off("update_gold", this.setRoomInfo, this);
        cc.director.off("password_suc", this.onPasswordSuc, this);
        cc.director.off("pay_event", this.onPayEvent, this);
        cc.director.off("closeBubble", this.onCloseBubble, this);
        cc.director.off("closeBubbleRoot", this.onCloseBubbleRoot, this);
    }
    start() {
        // cc.tween(this.node).set({ scale: 0, opacity: 0 }).to(UI_POP_OUT_TIME, { scale: 1, opacity: 255 }).call(() => {
        //     if (cc.sys.os === cc.sys.OS_ANDROID && cc.sys.isNative && Global.getInstance().isReview()) {
        //         this.initRechargeInfoNative()
        //     } else {
        //         this.initRechargeInfo();
        //     }
        //     this.initStoreInfo();
        //     this.shopOriginPos = this.shopScv.node.getPosition()
        //     this.storeOriginPos = this.storeScv.node.getPosition();
        //     console.log("shopOriginPos ", this.shopOriginPos)
        //     console.log("storeOriginPos ", this.storeOriginPos)
        //     this.shopScv.node.active = false;
        //     this.shopScv.node.opacity = 255;
        //     cc.tween(this.storeScv.node).set({ position: cc.v3(2000, this.storeOriginPos.y, 0) }).to(TOG_ANIM_TIME, { position: cc.v3(this.storeOriginPos.x, this.storeOriginPos.y, 0), opacity: 255 }).start()
        //     this.togType = this.initData.type || 0;
        //     this.onTogSelect(this.togType)
        //     setTimeout(() => {
        //         this.execAutoOpen()
        //     }, 300);
        //     // let anim = this.node.getComponent(cc.Animation)
        //     // anim.on("finished", () => {
        //     //     this.togType = this.initData.type || 0;
        //     //     this.onTogSelect(this.togType)
        //     // })
        //     // anim.play();
        // }).start();
    }
    onDestroy() {
        cc.director.emit("DestroyQueuePopup")
        cc.director.off('goPayDirect', this.goPayDirect, this)
    }
    show(args?): void {
        super.show()
        if (args) {
            this.init(args)
        }
    }

    execAutoOpen(args?) {
        let extraData, payType
        if (args) {
            extraData = args;
            payType = args.payType
        }
        else {
            extraData = this.initData.extraData
            payType = this.initData.type
        }
        if (!extraData) return

        let amount = extraData.amount
        let payWay = extraData.payWay
        if (payType == E_TRANSACTION_TYPE.recharge) {
            let idx = this.getRechargeIndex(amount)
            let ndItem = this.storeContent.getChildByName("storeItem" + idx)
            if (!ndItem) return
            let newShopItem: NewShopItem = ndItem.getComponent(NewShopItem)
            if (!newShopItem) return
            newShopItem.doSelectEvent(payWay)
        } else {
            let idx = this.getStoreIndex(amount, payWay)
            let ndItem = this.shopContent.getChildByName("shopItem" + idx)
            if (!ndItem) return
            let newShopItem: NewShopItem = ndItem.getComponent(NewShopItem)
            if (!newShopItem) return
            newShopItem.doSelectEvent(payWay)
        }

    }

    preLoadPrefab() {
        let prefabs = [
            // UI_PATH_DIC.ShopItemDetail,
            // UI_PATH_DIC.ThirdPay,
            // UI_PATH_DIC.ExchangeSucceed,
            // UI_PATH_DIC.PleaseRecharge
        ]
        // for (let index = 0; index < prefabs.length; index++) {
        //     const prefabPath = prefabs[index];
        cc.resources.preload(prefabs)
        // }
    }
    onTogSelect(type) {
        this.leftCheckboxs.toggleItems.forEach((toggle: cc.Toggle, idx) => {
            toggle.isChecked = type == idx;
        });
    }


    init(data) {
        this.initData = data;

        if (this.initData.force != null) {
            this.onTogSelect(this.initData.type)
        }
    }

    fadeIn(nd: cc.Node, pos) {
        nd.stopAllActions()
        if (cc.sys.isBrowser) {
            //修改列表压胖子的bug
            let boardX = this.ndRole.width * 0.85 / 2 - 11
            if (!document.fullscreenElement && cc.sys.OS_ANDROID == cc.sys.os) {
                boardX = boardX * 1.3
            }
            pos.x = this.ndRole.x + boardX
        }
        cc.tween(nd).set({ position: cc.v3(2000, pos.y, 0), active: true }).to(TOG_ANIM_TIME, { position: cc.v3(pos.x, pos.y, 0), opacity: 255 }).call(() => {

        }).start()
    }
    fadeOut(nd) {
        if (nd.active) {
            nd.stopAllActions()
            cc.tween(nd).by(TOG_ANIM_TIME, { position: cc.v3(300, 0, 0), opacity: -255 }).set({ active: false }).start()
        }
    }
    changeSelectTab(index) {
        if (index == 0) {
            this.fadeIn(this.storeScv.node, this.storeOriginPos)
            this.fadeOut(this.shopScv.node)

        } else if (index == 1) {
            this.fadeIn(this.shopScv.node, this.shopOriginPos)
            this.fadeOut(this.storeScv.node)
            if (this.ndRedeemBubble && this.ndRedeemBubble.active) this.ndRedeemBubble.getComponentInChildren(CompBubble).closeBubble()
        }
    }
    checkBtnAction(sender) {
        let idx = sender.node.parent.children.indexOf(sender.node)
        // if (idx == 1) {
        //     let userData = Global.getInstance().userdata
        //     if (!userData.phone) {
        //         Global.getInstance().showCommonTip("Before first redeem, you must set up phone number first.", this, false, () => {
        //             showPhonePassword()
        //         });
        //         this.onTogSelect(0)
        //         return;
        //     }
        //     if (!userData.withdraw_password) {
        //         Global.getInstance().showCommonTip("Before first redeem, you must set up wallet password first.", this, false, () => {
        //             showVerification(() => {
        //                 // showSetPassword()
        //                 uiManager.instance.showDialog(UI_PATH_DIC.SetPayPassword)
        //             })
        //         });
        //         this.onTogSelect(0)
        //         return;
        //     }
        // }
        this.changeSelectTab(idx)
    }
    showThirdPay(item, payWay?: number) {
        // this.ndThirdPay.active = true;
        // this.ndThirdPay.getComponent(ThirdPay).initData(item, this)
        uiManager.instance.showDialog(UI_PATH_DIC.ThirdPay, [{ item: item, lay: this, payWay: payWay }])//未用到
    }
    onCloseBubble() {
        this.onTogSelect(1)
        this.ndRedeemBubble.active = false;

    }
    onCloseBubbleRoot() {
        this.ndRedeemBubble.active = false;
    }
    setRoomInfo() {
        this.m_money.string = utils.numFormat(Global.getInstance().balanceTotal, 2);
    }
    closeAction() {
        // Global.getInstance().updateBalanceAfterGame()
        if (!this.isCLoseClicked && Global.getInstance().is_first_charge) {
            let itemData = Global.getInstance().getRetentionData()
            if (itemData.rate > 0) {
                showPleaseRecharge(() => {
                    this.isCLoseClicked = true
                })
            } else {
                this.hide()
            }
        } else {
            this.hide();
        }

    }
    initRechargeInfoNative() {
        let configs = Global.getInstance().config.shopping;
        let count = Math.ceil(configs.length / 3);
        this.storeContent.removeAllChildren(true)
        this.storeContent.setContentSize(this.storeContent.getContentSize().width, count * 330);
        let cb = () => {
            for (let index = 0; index < configs.length; index++) {
                let node = cc.instantiate(this.m_shopItem_prefab);
                let s = node.getComponent(NewShopItem);
                let itemdata = { sku: configs[index].sku, type: 1, name: configs[index].name, price: configs[index].amount, level: index, rate: configs[index].bonus_ratio };
                s.init(itemdata, this);
                this.storeContent.addChild(node);
            }
        }
        if (this.m_shopItem_prefab == null) {
            cc.resources.load(("prefab/newShopItem"), cc.Prefab, () => {
            }, (err, prefab: any) => {
                if (err) {
                    return;
                }
                this.m_shopItem_prefab = prefab;
                cb()
            });
        } else {
            cb()
        }
    }

    getRechargeIndex(amount: number) {
        if (!amount) return 0
        let rechargeConfig = Global.getInstance().rechargeConfig;
        let options = rechargeConfig.options.recharge_options;
        for (let index = 0; index < options.length; index++) {
            if (Number(options[index]) == amount) {
                return index
            }
        }
        return 0
    }

    initRechargeInfo() {
        let rechargeConfig = Global.getInstance().rechargeConfig;
        let options = rechargeConfig.options.recharge_options;
        let count = Math.ceil(options.length / 3);
        this.storeContent.removeAllChildren(true)
        // this.storeContent.setContentSize(this.storeContent.getContentSize().width, count * 330);
        let cb = () => {
            for (let index = 0; index < options.length; index++) {
                let node = cc.instantiate(this.m_shopItem_prefab);
                let s = node.getComponent(NewShopItem);
                let config = Global.getInstance().getRate(options[index])
                let itemdata = {
                    type: 1,
                    name: options[index],
                    price: options[index],
                    level: index,
                    rate: config ? config.rate : 0,
                    configId: config ? config.id : 0
                };
                s.init(itemdata, this);
                node.name = "storeItem" + index
                this.storeContent.addChild(node);
            }
        }
        if (this.m_shopItem_prefab == null) {
            cc.resources.load(("prefab/newShopItem"), cc.Prefab, () => {
            }, (err, prefab: any) => {
                this.m_shopItem_prefab = prefab;
                cb()
            });
        } else {
            cb()
        }
    }

    getStoreIndex(amount: number, targetProductType: number) {
        if (!amount) return 0
        let products = Global.getInstance().storeConfig.product;
        for (let index = 0; index < products.length; index++) {
            if (products[index].price == amount && products[index].product_type == targetProductType) {
                return index
            }
        }
        return 0;

    }

    initStoreInfo() {
        let data = Global.getInstance().storeConfig.product;
        let domain = Global.getInstance().storeConfig.domain;
        if (!!Global.getInstance().storeConfig.show_redemption_guide) {
            this.ndRedeemBubble.active = true
            this.ndRedeemBubble.getComponentInChildren(CompBubble).showBubble()
        } else {
            this.ndRedeemBubble.active = false
        }

        this.shopContent.removeAllChildren(true)
        // this.shopContent.setContentSize(this.shopContent.getContentSize().width, count * 330);
        let cb = () => {
            for (let index = 0; index < data.length; index++) {
                let node = cc.instantiate(this.m_shopItem_prefab);
                let s = node.getComponent(NewShopItem);
                let iData = { type: 2, data: data[index], domain: domain };
                s.init(iData, this);
                node.name = "shopItem" + index
                this.shopContent.addChild(node);
            }
        }
        if (this.m_shopItem_prefab == null) {
            cc.resources.load(("prefab/newShopItem"), cc.Prefab, () => {
            }, (err, prefab: any) => {
                if (err) {
                    return;
                }
                this.m_shopItem_prefab = prefab;
                cb()
            });
        } else {
            cb()
        }
    }
    onPayEvent() {
        Global.getInstance().getShopConfig(3, "/api/global-config/recharge", () => {
            Global.getInstance().getStoreConfig(3, "/api/product/list", () => {
                if (cc.sys.os === cc.sys.OS_ANDROID && cc.sys.isNative && Global.getInstance().isReview()) {
                    this.initRechargeInfoNative()
                } else {
                    this.initRechargeInfo();
                }
                this.initStoreInfo();
            });
        });
    }
    purchaseSuc(code: number, token: string) {
        this.scheduleOnce(() => {
            if (code != 0 || !this.curProductId || !token) {
                return Global.getInstance().showSimpleTip(Global.getInstance().getLabel("tipword91"));
            }
            HttpUtils.getInstance().post(3, 3, this, "/api/pay-service/verify-pay", {
                token: Global.getInstance().token,
                purchaseToken: token,
                purchaseState: code,
                productId: this.curProductId,
                packageName: Global.getInstance().getAppBundleId()
            }, (response) => {
                if (response) {
                    Global.getInstance().showSimpleTip(Global.getInstance().getLabel("tipword90"));

                    HttpUtils.getInstance().post(3, 3, this, "/api/global-config/recharge", { token: Global.getInstance().token }, (response) => {
                        Global.getInstance().rechargeConfig = response.data;
                        this.init({ type: 0 })
                    });
                    if (Global.getInstance().is_first_charge) Global.getInstance().logNewEvent("first_buy_coins", { CUID: Global.getInstance().userdata.user_id }, 1)
                    Global.getInstance().is_first_charge = false;
                }
            });
        }, 0.3)
    }
    // purchaseFail() {
    //     Global.getInstance().showSimpleTip(Global.getInstance().getLabel("tipword91"));
    // this.ndThirdPay.active = false;
    // }
    onPasswordSuc(data: any) {
        if (data.passType == PASS_TYPE.AddAccount) { this.openAddAccount() }
    }
    onClickGameRecord() {
        // showGameRecords()
        uiManager.instance.showDialog(UI_PATH_DIC.GameRecords)//未用到
    }
    onClickFund() {
        // showFundAccounts()
        // let userData = Global.getInstance().userdata
        // if (!userData.phone) {
        //     Global.getInstance().showCommonTip("Before first redeem, you must set up phone number first.", this, false, () => {
        //         showPhonePassword()
        //     });
        //     return;
        // }
        // if (!userData.withdraw_password) {
        //     Global.getInstance().showCommonTip("Before first redeem, you must set up wallet password first.", this, false, () => {
        //         showVerification(() => {
        //             // showSetPassword()
        //             uiManager.instance.showDialog(UI_PATH_DIC.SetPayPassword)
        //         })
        //     });
        //     return;
        // }
        uiManager.instance.showDialog(UI_PATH_DIC.FundAccount)//未用到
    }
    //商场挽留,直接支付
    goPayDirect() {
        let itemData = Global.getInstance().getRetentionData()
        if (itemData.type == 1) {
            let payFunc = () => {
                if (cc.sys.os === cc.sys.OS_ANDROID && cc.sys.isNative) {
                    if (itemData.sku) {
                        console.log(itemData.sku)
                        let str = itemData.sku
                        this.curProductId = itemData.sku;
                        jsb.reflection.callStaticMethod("org/cocos2dx/javascript/AppActivity", "clickBuyProduct", "(Ljava/lang/String;)V", str);
                    } else {
                        console.log("商品sku出错")
                    }
                } else if (cc.sys.os === cc.sys.OS_IOS && cc.sys.isNative) {

                } else {
                    console.log("跳转到网页充值界面");
                    this.showThirdPay(itemData)
                }
            }
            if (Global.getInstance().isReview()) {
                payFunc()
            } else {
                this.showThirdPay(itemData)
            }
        } else {
            // showRedeemDetail(itemData)
            uiManager.instance.showDialog(UI_PATH_DIC.ShopItemDetail, [itemData])//未用到
        }
    }
    openAddAccount() {
        if (!this.curRedeemItem) return;
        HttpUtils.getInstance().post(3, 3, this, "/api/get/pay/code/list", {
            token: Global.getInstance().token,
            product_type: this.curRedeemItem.data.product_type
        }, (response) => {
            if (response.data.length == 0) return console.log(response.msg)
            let payCodeData = {
                user_account_info: [],
                all_account_code: response.data.all_account_code
            }
            if (this.curRedeemItem.data.product_type == E_FUND_TYPE.Gcash ||
                this.curRedeemItem.data.product_type == E_FUND_TYPE.Maya ||
                this.curRedeemItem.data.product_type == E_FUND_TYPE.GrabPay) {
                showChooseWallet(payCodeData, this.curRedeemItem.data.product_type)
            } else if (this.curRedeemItem.data.product_type == E_FUND_TYPE.BankCard) {
                showChooseBankCard(payCodeData)
            }
        });
    }
    checkAccount(itemData: any) {
        this.curRedeemItem = itemData;
        uiManager.instance.showDialog(UI_PATH_DIC.ShopItemDetail, [itemData])//未用到
        return;
        HttpUtils.getInstance().post(3, 3, this, "api/get/default/account", {
            token: Global.getInstance().token,
            product_type: itemData.data.product_type
        }, (response) => {
            if (response.data.account_no) {
                // showRedeemDetail(itemData)
                uiManager.instance.showDialog(UI_PATH_DIC.ShopItemDetail, [itemData])//未用到
            } else {
                let str = "You have not added %d account yet.Do you want to add now?"
                switch (itemData.data.product_type) {
                    case PRODUCT_TYPE.GCASH:
                        str = str.replace("%d", "GCash")
                        break;
                    case PRODUCT_TYPE.MAYA:
                        str = str.replace("%d", "Maya")
                        break;
                    case PRODUCT_TYPE.GRAB_PAY:
                        str = str.replace("%d", "Grab pay")
                        break;
                    case PRODUCT_TYPE.BANK_CARD:
                        str = str.replace("%d", "Bank")
                        break;
                    default:
                        break;
                }
                Global.getInstance().showCommonTip2({ word: str, confirm: "Add" }, this, false, () => {
                    // showEnterPassword(PASS_TYPE.AddAccount)
                    uiManager.instance.showDialog(UI_PATH_DIC.Password, [PASS_TYPE.AddAccount])//未用到
                })
                return;
            }
        });
    }

    onClickCustorm() {
        ZendeskManager.instance().openZenDesk_V()
    }
}
