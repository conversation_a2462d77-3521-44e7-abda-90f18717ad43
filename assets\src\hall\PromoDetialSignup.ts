import { DEEP_INDEXZ, E_CHANEL_TYPE, UI_PATH_DIC } from "../GlobalConstant";
import Global from "../GlobalScript";
import HttpUtils from "../net/HttpUtils";
import utils from "../utils/utils";
import { ALL_APP_SOURCE_CONFIG } from "../Config";
import { uiManager } from "../mgr/UIManager";
import RoundRectMask from "../RoundRectMask";
import { LOGIN_WAY } from "./Hall";
import GameControl from "../GameControl";

const {ccclass, property} = cc._decorator;

@ccclass
export default class PromoDetialSignup extends cc.Component {

    @property(cc.Node)
    signBindBonusView: cc.Node = null;

    @property(cc.Node)
    btnBack: cc.Node = null;

    @property(cc.Node)
    btnTransion: cc.Node = null;

    @property(cc.Node)
    btnDetials: cc.Node = null;

    @property(cc.Label)
    labTitle: cc.Label = null;

    @property([cc.SpriteFrame])
    statusframes: cc.SpriteFrame[] = [];
    
    openType = null;//打开页面类型

    start_period = null;//vip_cashback活动开始时间
    end_period = null;//vip_cashback活动结束时间
   
    activity_type = null;//活动类型
    config_cashback = null;

    type = null;
    onLoad () {
        this.signBindBonusView.getChildByName("needLoginBtn").active = !Global.getInstance().token;
    }

    initType(args){
        this.type = args;
    }

    start () {
        if(this.signBindBonusView.active){
            let signup = utils.getChildByPath(this.signBindBonusView, "layout"); 
            if(Global.instance.is_mini_game()){
                if(signup) signup.active = false;
                this.signBindBonusView.getChildByName("scrollview").getComponent(cc.ScrollView).content.height = 2075;
            } else if(ALL_APP_SOURCE_CONFIG.channel == E_CHANEL_TYPE.WEB){
                if(signup) signup.active = false;
                this.signBindBonusView.getChildByName("scrollview").getComponent(cc.ScrollView).content.height = 2275;
            }
        }
        this.initSignView();
        let page = this.node.getChildByName("page").getComponent(cc.PageView);
        page.node.on('page-turning', ()=>{
            let curIndex = page.getCurrentPageIndex();
            if (curIndex != 1) {
                this.node.destroy();
            }
        }, this);
        let tempnode = this.node.getChildByName("temp");
        page.insertPage(cc.instantiate(tempnode),0);
        page.scrollToPage(1,0.01);
    }


    protected onDestroy(): void {
    }

    initSignView() {
        //let rules = utils.getChildByPath(this.signBindBonusView,"rules").getComponent(RoundRectMask);
        let info = utils.getChildByPath(this.signBindBonusView,"scrollview.view.content.signbind_bonus.info").getComponent(RoundRectMask);
        let scrollview = this.signBindBonusView.getChildByName("scrollview");
        scrollview.on("scrolling", ()=>{this.title_layout_opacity(scrollview.getComponent(cc.ScrollView))}, this);
        //rules.radius = 0.1;
        //info.radius = 0.1;
        // if (!!Global.getInstance().token) {
        HttpUtils.getInstance().post(1, 3, this, "/avt/api/registration/bonus", {
            token: Global.getInstance().token,
        }, (response) => {
            if (!this.node || !this.node.isValid) {
                return;
            }
            if (response.data && response.data.list) {
                let data = response.data;
                //let layout = utils.getChildByPath(this.signBindBonusView,"info.Layout");
                //let labrules = utils.getChildByPath(this.signBindBonusView,"rules.labrules").getComponent(cc.Label);
                let labtotal = utils.getChildByPath(this.signBindBonusView,"scrollview.view.content.signbind_bonus.info.labtotal").getComponent(cc.Label);
                let spsign = utils.getChildByPath(this.signBindBonusView,"scrollview.view.content.signbind_bonus.info.spSign").getComponent(cc.Sprite);
                let spbind = utils.getChildByPath(this.signBindBonusView,"scrollview.view.content.signbind_bonus.info.spbind").getComponent(cc.Sprite);
                let labsign = utils.getChildByPath(this.signBindBonusView,"scrollview.view.content.signbind_bonus.info.labSign").getComponent(cc.Label);
                let labsignreward = utils.getChildByPath(this.signBindBonusView,"scrollview.view.content.signbind_bonus.info.labsignreward").getComponent(cc.Label);
                let labbind = utils.getChildByPath(this.signBindBonusView,"scrollview.view.content.signbind_bonus.info.labbind").getComponent(cc.Label);
                let labbindreward = utils.getChildByPath(this.signBindBonusView,"scrollview.view.content.signbind_bonus.info.labbindreward").getComponent(cc.Label);
                let ignbindlayout = utils.getChildByPath(this.signBindBonusView,"layout")
                let btnsignbind = utils.getChildByPath(this.signBindBonusView,"layout.btnsignbind")
                let labbtnsignbind = utils.getChildByPath(this.signBindBonusView,"layout.btnsignbind.Background.Label").getComponent(cc.Label);
                let spnodes = [spsign,spbind];
                let titles = [labsign,labbind];
                let rewards = [labsignreward,labbindreward];
                let signs = [labsign, labbind];
                if (data.type == 3) {
                    ignbindlayout.active = false;
                    this.signBindBonusView.getChildByName("scrollview").getComponent(cc.ScrollView).content.height = 2175;
                } else {
                    ignbindlayout.active = true;
                    this.signBindBonusView.getChildByName("scrollview").getComponent(cc.ScrollView).content.height = 2375;
                }
                if (data.list.length == 1) {
                    //layout.height = 300;
                    info.node.height = 300;
                    
                    spnodes[1].node.active = false;
                    titles[1].node.active = false;
                    rewards[1].node.active = false;
                }
                if (data.maxBonus) {
                    labtotal.string = "Up to ₱"+data.maxBonus;
                }

                for (let index = 0; index < data.list.length; index++) {
                    let element = data.list[index];
                    titles[index].string = element.title;
                    if (element.status == 1) {
                        spnodes[index].spriteFrame = this.statusframes[1];
                        rewards[index].string = "Received ₱"+element.bonus;
                        rewards[index].node.color = cc.color(255,245,0);

                        signs[index].node.color = cc.color(255,255,255);
                    } else {
                        spnodes[index].spriteFrame = this.statusframes[0];
                        rewards[index].string = "₱"+element.bonus;
                        rewards[index].node.color = cc.color(255,255,255);
                        rewards[index].node.opacity = 204;
                        signs[index].node.color = cc.color(255,255,255);
                        signs[index].node.opacity = 204;
                    }
                }
                if (data.type != 3) {
                    if (data.list[0].status == 1) {
                        labbtnsignbind.string = "Bind mobile phone";
                    } else {
                        labbtnsignbind.string = "Sign up";
                    }
                }
            }
        });
        // }
    }

    backToPromo(type?) {
        if(this.type == "banner"){
            let gamescene =  cc.director.getScene().getComponentInChildren(GameControl);
            gamescene.mTabbar.rein_home();
            this.node.destroy();
        }else{
            this.node.destroy();
            cc.director.emit("refresh_promo");
        }
    }

    cliclDetialView(event,userdata) {
        if (!Global.getInstance().token) {
            uiManager.instance.showDialog(UI_PATH_DIC.phonePasswordLogin, [{ way: LOGIN_WAY.PhoneCode, curPromoDetail:true }])//curPromoDetail:true表示当前是活动详情页面
            return;
        }
    }

    clickTransionView() {
        if (!Global.getInstance().token) {
            uiManager.instance.showDialog(UI_PATH_DIC.phonePasswordLogin, [{ way: LOGIN_WAY.PhoneCode, curPromoDetail:true }])//curPromoDetail:true表示当前是活动详情页面
            return;
        }
        uiManager.instance.showDialog(UI_PATH_DIC.Transations,["award"],null,DEEP_INDEXZ.TRANSATIONS);
    }

    clickSignUp() {
        if (ALL_APP_SOURCE_CONFIG.channel == E_CHANEL_TYPE.WEB) {//web渠道
            if (!Global.getInstance().token) {
                //未登录 跳转登录页
                uiManager.instance.showDialog(UI_PATH_DIC.phonePasswordLogin, [{ way: LOGIN_WAY.PhoneCode, curPromoDetail:true }])//curPromoDetail:true表示当前是活动详情页面
                return;
            }
        }
    }

    showLoginView() {
        uiManager.instance.showDialog(UI_PATH_DIC.phonePasswordLogin, [{ way: LOGIN_WAY.PhoneCode, curPromoDetail:true }])
        this.signBindBonusView.getChildByName("needLoginBtn").active = !Global.getInstance().token;
    }

    title_layout_opacity(node:cc.ScrollView) {
        let opacity = node.getScrollOffset().y
        if (opacity > 255 ) opacity = 255;
        let layou = utils.getChildByPath(this.signBindBonusView,"titlelayout.bg");
        layou.opacity = opacity;
        let btn_back = utils.getChildByPath(this.signBindBonusView,"titlelayout.btn_back.img");
        let btn_transiton = utils.getChildByPath(this.signBindBonusView,"titlelayout.btn_transiton.img");
        let btn_rules = utils.getChildByPath(this.signBindBonusView,"titlelayout.btn_rules.img");
        if (opacity > 0) {
            btn_back.color = cc.color(255-opacity/255*180,255-opacity/255*168,255-opacity/255*151);
            btn_transiton.color = cc.color(255-opacity/255*180,255-opacity/255*168,255-opacity/255*151);
            btn_rules.color = cc.color(255-opacity/255*180,255-opacity/255*168,255-opacity/255*151);
        } else {
            btn_back.color = cc.color(255,255,255);
            btn_transiton.color = cc.color(255,255,255);
            btn_rules.color = cc.color(255,255,255);
        }
        
    }

    initLayoutOpacity() {
        let layou = utils.getChildByPath(this.signBindBonusView,"titlelayout.bg");
        layou.opacity = 0;
        let btn_back = utils.getChildByPath(this.signBindBonusView,"titlelayout.btn_back.img");
        let btn_transiton = utils.getChildByPath(this.signBindBonusView,"titlelayout.btn_transiton.img");
        let btn_rules = utils.getChildByPath(this.signBindBonusView,"titlelayout.btn_rules.img");
        btn_back.color = cc.color(255,255,255);
        btn_transiton.color = cc.color(255,255,255);
        btn_rules.color = cc.color(255,255,255);
    }
}
