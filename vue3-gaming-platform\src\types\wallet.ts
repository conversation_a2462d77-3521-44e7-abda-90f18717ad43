// 钱包任务相关类型定义

export interface WalletTaskInfo {
  /** 任务ID */
  id: string;
  /** 钱包配置信息 */
  wallet_task_id: string;
  /** 用户ID */
  user_id: string;
  /** 钱包任务奖金 */
  bonus: string;
  /** 任务状态：1:未解锁 2:进行中 3:完成任务 4:过期 5:删除任务 */
  task_status: number;
  /** 任务发放时间 */
  created_at: string;
  /** 任务状态变更时间 */
  updated_at: string;
  /** 任务名称 */
  task_name: string;
  /** 任务规则 */
  task_rule: string;
  /** 当前注单金额 */
  bet_num: string;
  /** 任务目标投注额 */
  bet_target_value: number;
  /** 充值是否完成 */
  recharge_done: boolean;
  /** 任务目标充值额 */
  recharge_target_value: string;
  /** 任务有效时间（小时） 0:无限制 */
  expire_time: number;
  /** 游戏列表 */
  game_list: string;
  /** 游戏类别 */
  game_type: string[];
  /** 厂商列表 */
  provider_list: string[];
  /** 任务描述 */
  description?: string;
  /** 任务图标 */
  icon_url?: string;
  /** 任务优先级 */
  priority?: number;
  /** 任务标签 */
  tags?: string[];
}

// 钱包任务状态枚举
export enum WalletTaskStatus {
  LOCK = 1,        // 未解锁
  ONGOING = 2,     // 进行中
  FINISHED = 3,    // 已完成
  EXPIRED = 4,     // 已过期
  DELETE = 5       // 已删除
}

// 钱包任务状态文本映射
export const WALLET_TASK_STATUS_TEXT = {
  [WalletTaskStatus.LOCK]: '未解锁',
  [WalletTaskStatus.ONGOING]: '进行中',
  [WalletTaskStatus.FINISHED]: '已完成',
  [WalletTaskStatus.EXPIRED]: '已过期',
  [WalletTaskStatus.DELETE]: '已删除'
} as const;

// 钱包任务状态颜色映射
export const WALLET_TASK_STATUS_COLOR = {
  [WalletTaskStatus.LOCK]: '#999999',
  [WalletTaskStatus.ONGOING]: '#4CAF50',
  [WalletTaskStatus.FINISHED]: '#2196F3',
  [WalletTaskStatus.EXPIRED]: '#FF6B6B',
  [WalletTaskStatus.DELETE]: '#999999'
} as const;

// 任务进度信息
export interface TaskProgress {
  current: number;
  total: number;
  percentage: number;
  isCompleted: boolean;
  bet_progress?: {
    current: number;
    target: number;
    percentage: number;
  };
  recharge_progress?: {
    completed: boolean;
    target: number;
  };
}

// 任务倒计时信息
export interface TaskCountdown {
  hours: number;
  minutes: number;
  seconds: number;
  totalSeconds: number;
  isExpired: boolean;
  formatted?: string;
}

// 钱包任务操作类型
export enum WalletTaskAction {
  UNLOCK = 'unlock',
  COLLECT = 'collect',
  CONTINUE = 'continue',
  VIEW_RULE = 'view_rule',
  CANCEL = 'cancel',
  REFRESH = 'refresh'
}

// API响应类型
export interface WalletTaskResponse {
  code: number;
  message: string;
  data: WalletTaskInfo[];
  pagination?: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
}

export interface WalletTaskRewardResponse {
  code: number;
  message: string;
  data: {
    reward_amount: number;
    new_balance: number;
    bonus_type?: string;
    multiplier?: number;
  };
}

// 钱包任务筛选选项
export interface WalletTaskFilter {
  status?: WalletTaskStatus[];
  hasTimeLimit?: boolean;
  sortBy?: 'created_at' | 'bonus' | 'expire_time' | 'priority';
  sortOrder?: 'asc' | 'desc';
  gameTypes?: string[];
  providers?: string[];
  minReward?: number;
  maxReward?: number;
  tags?: string[];
}

// 钱包任务统计信息
export interface WalletTaskStats {
  total: number;
  locked: number;
  ongoing: number;
  finished: number;
  expired: number;
  totalRewards: number;
  completionRate: number;
  averageCompletionTime?: number;
  mostPopularGameType?: string;
  totalBetAmount?: number;
  totalRechargeAmount?: number;
}

// 游戏跳转参数
export interface GameJumpParams {
  jumpType: string;
  gameType?: string[];
  providerList?: string[];
  gameId?: string;
  roomId?: string;
  returnUrl?: string;
}

// 钱包任务事件类型
export enum WalletTaskEvent {
  TASK_UNLOCKED = 'task_unlocked',
  TASK_COMPLETED = 'task_completed',
  REWARD_COLLECTED = 'reward_collected',
  TASK_EXPIRED = 'task_expired',
  PROGRESS_UPDATED = 'progress_updated',
  TASK_CANCELLED = 'task_cancelled',
  TASK_REFRESHED = 'task_refreshed'
}

// 钱包任务配置
export interface WalletTaskConfig {
  maxConcurrentTasks: number;
  autoRefreshInterval: number;
  showGuideForNewUsers: boolean;
  enableNotifications: boolean;
  enableSoundEffects: boolean;
  enableAnimations: boolean;
  defaultSortBy: string;
  defaultSortOrder: 'asc' | 'desc';
  cacheExpiration: number;
  maxRetryAttempts: number;
}

// 本地存储键
export const WALLET_STORAGE_KEYS = {
  GUIDE_SHOWN: 'wallet_task_guide_shown',
  LAST_REFRESH: 'wallet_task_last_refresh',
  USER_PREFERENCES: 'wallet_task_preferences',
  CACHED_TASKS: 'wallet_task_cached_tasks',
  FILTER_SETTINGS: 'wallet_task_filter_settings'
} as const;

// 默认配置
export const DEFAULT_WALLET_CONFIG: WalletTaskConfig = {
  maxConcurrentTasks: 1,
  autoRefreshInterval: 30000, // 30秒
  showGuideForNewUsers: true,
  enableNotifications: true,
  enableSoundEffects: true,
  enableAnimations: true,
  defaultSortBy: 'priority',
  defaultSortOrder: 'desc',
  cacheExpiration: 300000, // 5分钟
  maxRetryAttempts: 3
};

// 任务项UI状态
export interface TaskItemUIState {
  isLoading: boolean;
  showProgress: boolean;
  showCountdown: boolean;
  isExpanded: boolean;
  animationState: 'idle' | 'collecting' | 'unlocking' | 'completing' | 'error';
  lastUpdated?: number;
}

// 钱包任务页面状态
export interface WalletTaskPageState {
  isLoading: boolean;
  showGuide: boolean;
  selectedTask: WalletTaskInfo | null;
  filter: WalletTaskFilter;
  sortBy: string;
  searchText: string;
  currentPage: number;
  pageSize: number;
  totalTasks: number;
  refreshing: boolean;
  error?: string;
}

// 错误类型
export enum WalletTaskError {
  NETWORK_ERROR = 'network_error',
  INVALID_TASK = 'invalid_task',
  TASK_EXPIRED = 'task_expired',
  INSUFFICIENT_PROGRESS = 'insufficient_progress',
  MAX_TASKS_REACHED = 'max_tasks_reached',
  PERMISSION_DENIED = 'permission_denied',
  SERVER_ERROR = 'server_error',
  VALIDATION_ERROR = 'validation_error',
  TIMEOUT_ERROR = 'timeout_error',
  UNKNOWN_ERROR = 'unknown_error'
}

// 错误信息映射
export const WALLET_TASK_ERROR_MESSAGES = {
  [WalletTaskError.NETWORK_ERROR]: '网络连接失败，请检查网络后重试',
  [WalletTaskError.INVALID_TASK]: '无效的任务信息',
  [WalletTaskError.TASK_EXPIRED]: '任务已过期',
  [WalletTaskError.INSUFFICIENT_PROGRESS]: '任务进度不足',
  [WalletTaskError.MAX_TASKS_REACHED]: '已达到最大任务数量限制',
  [WalletTaskError.PERMISSION_DENIED]: '权限不足，无法执行此操作',
  [WalletTaskError.SERVER_ERROR]: '服务器错误，请稍后重试',
  [WalletTaskError.VALIDATION_ERROR]: '数据验证失败',
  [WalletTaskError.TIMEOUT_ERROR]: '请求超时，请重试',
  [WalletTaskError.UNKNOWN_ERROR]: '未知错误，请稍后重试'
} as const;

// 任务奖励类型
export enum TaskRewardType {
  COINS = 'coins',
  BONUS = 'bonus',
  FREE_SPINS = 'free_spins',
  CASHBACK = 'cashback',
  VIP_POINTS = 'vip_points',
  ITEM = 'item',
  MULTIPLIER = 'multiplier'
}

// 任务奖励信息
export interface TaskReward {
  type: TaskRewardType;
  amount: number;
  currency?: string;
  description?: string;
  icon?: string;
  expiry_date?: string;
  conditions?: string[];
}

// 任务完成条件
export interface TaskCondition {
  type: 'bet_amount' | 'recharge_amount' | 'game_rounds' | 'win_amount' | 'login_days';
  target: number;
  current: number;
  description: string;
  is_completed: boolean;
}

// 任务模板
export interface TaskTemplate {
  id: string;
  name: string;
  description: string;
  icon: string;
  category: string;
  difficulty: 'easy' | 'medium' | 'hard';
  estimated_time: number; // 预计完成时间（分钟）
  conditions: TaskCondition[];
  rewards: TaskReward[];
  game_restrictions?: {
    allowed_types: string[];
    allowed_providers: string[];
    excluded_games: string[];
  };
  user_restrictions?: {
    min_level: number;
    max_level?: number;
    vip_only: boolean;
    new_user_only: boolean;
  };
}

// 任务历史记录
export interface TaskHistory {
  id: string;
  task_id: string;
  task_name: string;
  user_id: string;
  status: WalletTaskStatus;
  started_at: string;
  completed_at?: string;
  expired_at?: string;
  reward_amount: number;
  bet_amount: number;
  recharge_amount: number;
  completion_time?: number; // 完成耗时（秒）
}

// 任务通知设置
export interface TaskNotificationSettings {
  task_unlocked: boolean;
  task_completed: boolean;
  task_expiring: boolean; // 任务即将过期
  reward_available: boolean;
  progress_milestone: boolean; // 进度里程碑
  new_tasks_available: boolean;
  sound_enabled: boolean;
  vibration_enabled: boolean;
  email_enabled: boolean;
  push_enabled: boolean;
}

// 任务分析数据
export interface TaskAnalytics {
  task_id: string;
  total_attempts: number;
  completion_rate: number;
  average_completion_time: number;
  abandonment_rate: number;
  popular_game_types: string[];
  user_feedback_score?: number;
  difficulty_rating?: number;
}

// 任务推荐算法配置
export interface TaskRecommendationConfig {
  algorithm: 'collaborative' | 'content_based' | 'hybrid';
  max_recommendations: number;
  consider_user_history: boolean;
  consider_difficulty: boolean;
  consider_rewards: boolean;
  exclude_completed: boolean;
  weight_factors: {
    reward_amount: number;
    completion_rate: number;
    user_preference: number;
    difficulty: number;
  };
}

// 任务A/B测试配置
export interface TaskABTestConfig {
  test_id: string;
  variant: 'A' | 'B';
  test_parameters: {
    reward_multiplier?: number;
    time_limit_modifier?: number;
    difficulty_adjustment?: number;
    ui_variant?: string;
  };
  metrics_to_track: string[];
  start_date: string;
  end_date: string;
  target_sample_size: number;
}

// 任务性能指标
export interface TaskPerformanceMetrics {
  load_time: number;
  render_time: number;
  api_response_time: number;
  error_rate: number;
  user_engagement_rate: number;
  conversion_rate: number;
  retention_rate: number;
}

// 任务本地化配置
export interface TaskLocalization {
  language: string;
  currency: string;
  date_format: string;
  number_format: string;
  translations: Record<string, string>;
  cultural_adaptations?: {
    colors: Record<string, string>;
    icons: Record<string, string>;
    layouts: Record<string, any>;
  };
}
