import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { 
  HallGameIcon, 
  GameType, 
  BannerData, 
  ActivityData,
  TransactionRecord,
  GameFilter,
  GameStats,
  UserGamePreferences
} from '@/types'
import { GAME_TYPES } from '@/types'

export const useHallStore = defineStore('hall', () => {
  // 状态
  const gameList = ref<HallGameIcon[]>([])
  const currentGameType = ref<GameType>('Casino')
  const bannerData = ref<BannerData[]>([])
  const activityData = ref<ActivityData[]>([])
  const transactionRecords = ref<TransactionRecord[]>([])
  
  const isLoading = ref(false)
  const searchText = ref('')
  const gameFilter = ref<GameFilter>({
    types: [],
    providers: [],
    tags: [],
    betRange: { min: 0, max: 10000 },
    showFavorites: false,
    showRecent: false
  })
  
  const gameStats = ref<GameStats>({
    total_games: 0,
    active_games: 0,
    maintenance_games: 0,
    new_games: 0,
    hot_games: 0,
    providers_count: 0,
    categories_count: 0
  })
  
  const userPreferences = ref<UserGamePreferences>({
    favorite_types: [],
    favorite_providers: [],
    recent_games: [],
    liked_games: [],
    bet_preferences: {
      min_bet: 1,
      max_bet: 100,
      auto_play: false
    },
    display_preferences: {
      grid_size: 'medium',
      show_animations: true,
      show_sound: true
    }
  })
  
  const filterTimeType = ref(0)
  const lastRefreshTime = ref<number>(0)

  // 计算属性
  const filteredGameList = computed(() => {
    let filtered = gameList.value
    
    // 按游戏类型筛选
    if (currentGameType.value !== 'Casino') {
      if (currentGameType.value === 'History') {
        filtered = filtered.filter(game => 
          userPreferences.value.recent_games.includes(game.id.toString())
        )
      } else if (currentGameType.value === 'Like') {
        filtered = filtered.filter(game => 
          userPreferences.value.liked_games.includes(game.id.toString())
        )
      } else {
        filtered = filtered.filter(game => {
          const gameTypeMap: Record<GameType, number> = {
            'Casino': 1,
            'Slots': 2,
            'Poker': 3,
            'Bingo': 4,
            'Arcade': 5,
            'Sports': 6,
            'Like': 0,
            'History': 0,
            'Baccarat': 7,
            'Roulette': 8,
            'Blackjack': 9
          }
          return game.game_type === gameTypeMap[currentGameType.value]
        })
      }
    }
    
    // 按搜索文本筛选
    if (searchText.value) {
      filtered = filtered.filter(game => 
        game.game_name.toLowerCase().includes(searchText.value.toLowerCase()) ||
        game.provider_name?.toLowerCase().includes(searchText.value.toLowerCase())
      )
    }
    
    // 按筛选器筛选
    if (gameFilter.value.types.length > 0) {
      filtered = filtered.filter(game => 
        gameFilter.value.types.includes(currentGameType.value)
      )
    }
    
    if (gameFilter.value.providers.length > 0) {
      filtered = filtered.filter(game => 
        gameFilter.value.providers.includes(game.provider_name || '')
      )
    }
    
    if (gameFilter.value.tags.length > 0) {
      filtered = filtered.filter(game => 
        game.tags?.some(tag => gameFilter.value.tags.includes(tag))
      )
    }
    
    // 按投注范围筛选
    if (gameFilter.value.betRange.min > 0 || gameFilter.value.betRange.max < 10000) {
      filtered = filtered.filter(game => {
        const minBet = game.min_bet || 0
        const maxBet = game.max_bet || 10000
        return minBet >= gameFilter.value.betRange.min && 
               maxBet <= gameFilter.value.betRange.max
      })
    }
    
    // 收藏筛选
    if (gameFilter.value.showFavorites) {
      filtered = filtered.filter(game => 
        userPreferences.value.liked_games.includes(game.id.toString())
      )
    }
    
    // 最近游戏筛选
    if (gameFilter.value.showRecent) {
      filtered = filtered.filter(game => 
        userPreferences.value.recent_games.includes(game.id.toString())
      )
    }
    
    return filtered
  })
  
  const validActivities = computed(() => {
    const now = Date.now()
    return activityData.value.filter(activity => {
      const startTime = new Date(activity.start_at).getTime()
      const endTime = new Date(activity.end_at).getTime()
      return startTime <= now && now <= endTime && activity.status === 1
    })
  })
  
  const gamesByProvider = computed(() => {
    const providers: Record<string, HallGameIcon[]> = {}
    gameList.value.forEach(game => {
      const provider = game.provider_name || 'Unknown'
      if (!providers[provider]) {
        providers[provider] = []
      }
      providers[provider].push(game)
    })
    return providers
  })
  
  const popularGames = computed(() => {
    return gameList.value
      .filter(game => game.tags?.includes(1)) // HOT标签
      .sort((a, b) => (b.popularity || 0) - (a.popularity || 0))
      .slice(0, 10)
  })
  
  const newGames = computed(() => {
    return gameList.value
      .filter(game => game.tags?.includes(2)) // NEW标签
      .sort((a, b) => new Date(b.id).getTime() - new Date(a.id).getTime())
      .slice(0, 10)
  })

  // 方法
  function formatNumberWithCommas(num: number): string {
    return num.toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    })
  }
  
  function addToHistory(gameId: number) {
    const gameIdStr = gameId.toString()
    const history = [...userPreferences.value.recent_games]
    
    // 移除已存在的记录
    const index = history.indexOf(gameIdStr)
    if (index > -1) {
      history.splice(index, 1)
    }
    
    // 添加到开头
    history.unshift(gameIdStr)
    
    // 限制历史记录数量
    if (history.length > 20) {
      history.splice(20)
    }
    
    userPreferences.value.recent_games = history
    savePreferences()
  }
  
  function toggleLike(gameId: number) {
    const gameIdStr = gameId.toString()
    const liked = [...userPreferences.value.liked_games]
    const index = liked.indexOf(gameIdStr)
    
    if (index > -1) {
      liked.splice(index, 1)
    } else {
      liked.push(gameIdStr)
    }
    
    userPreferences.value.liked_games = liked
    savePreferences()
  }
  
  function isGameLiked(gameId: number): boolean {
    return userPreferences.value.liked_games.includes(gameId.toString())
  }
  
  function setGameType(type: GameType) {
    currentGameType.value = type
  }
  
  function setSearchText(text: string) {
    searchText.value = text
  }
  
  function setGameList(games: HallGameIcon[]) {
    gameList.value = games
    updateGameStats()
  }
  
  function addGame(game: HallGameIcon) {
    const existingIndex = gameList.value.findIndex(g => g.id === game.id)
    if (existingIndex >= 0) {
      gameList.value[existingIndex] = game
    } else {
      gameList.value.push(game)
    }
    updateGameStats()
  }
  
  function removeGame(gameId: number) {
    const index = gameList.value.findIndex(game => game.id === gameId)
    if (index >= 0) {
      gameList.value.splice(index, 1)
      updateGameStats()
    }
  }
  
  function setBannerData(banners: BannerData[]) {
    // 按排序和更新时间排序
    banners.sort((a, b) => {
      if (a.sort !== b.sort) {
        return b.sort - a.sort
      }
      return new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime()
    })
    bannerData.value = banners.filter(banner => banner.is_active)
  }
  
  function setActivityData(activities: ActivityData[]) {
    activityData.value = activities.filter(activity => activity.status === 1)
  }
  
  function setTransactionRecords(records: TransactionRecord[]) {
    transactionRecords.value = records
  }
  
  function setLoading(loading: boolean) {
    isLoading.value = loading
  }
  
  function setFilterTimeType(type: number) {
    filterTimeType.value = type
  }
  
  function updateGameFilter(filter: Partial<GameFilter>) {
    gameFilter.value = { ...gameFilter.value, ...filter }
  }
  
  function clearGameFilter() {
    gameFilter.value = {
      types: [],
      providers: [],
      tags: [],
      betRange: { min: 0, max: 10000 },
      showFavorites: false,
      showRecent: false
    }
  }
  
  function updateUserPreferences(preferences: Partial<UserGamePreferences>) {
    userPreferences.value = { ...userPreferences.value, ...preferences }
    savePreferences()
  }
  
  function savePreferences() {
    localStorage.setItem('hall_user_preferences', JSON.stringify(userPreferences.value))
  }
  
  function loadPreferences() {
    const saved = localStorage.getItem('hall_user_preferences')
    if (saved) {
      try {
        userPreferences.value = { ...userPreferences.value, ...JSON.parse(saved) }
      } catch (error) {
        console.error('Failed to load hall preferences:', error)
      }
    }
  }
  
  function updateGameStats() {
    const stats: GameStats = {
      total_games: gameList.value.length,
      active_games: gameList.value.filter(game => game.status === 1).length,
      maintenance_games: gameList.value.filter(game => game.status === 2).length,
      new_games: gameList.value.filter(game => game.tags?.includes(2)).length,
      hot_games: gameList.value.filter(game => game.tags?.includes(1)).length,
      providers_count: new Set(gameList.value.map(game => game.provider_name)).size,
      categories_count: new Set(gameList.value.map(game => game.game_type)).size
    }
    gameStats.value = stats
  }
  
  function getFilteredTransactions() {
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    
    return transactionRecords.value.filter(record => {
      const recordDate = new Date(record.created_at)
      
      switch (filterTimeType.value) {
        case 0: // 今天
          return recordDate >= today
        case 1: // 昨天
          const yesterday = new Date(today)
          yesterday.setDate(yesterday.getDate() - 1)
          return recordDate >= yesterday && recordDate < today
        case 2: // 最近3天
          const threeDaysAgo = new Date(today)
          threeDaysAgo.setDate(threeDaysAgo.getDate() - 3)
          return recordDate >= threeDaysAgo
        case 3: // 最近7天
          const sevenDaysAgo = new Date(today)
          sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)
          return recordDate >= sevenDaysAgo
        default:
          return true
      }
    })
  }
  
  function getGamesByType(type: GameType): HallGameIcon[] {
    if (type === 'Like') {
      return gameList.value.filter(game => 
        userPreferences.value.liked_games.includes(game.id.toString())
      )
    }
    
    if (type === 'History') {
      return gameList.value.filter(game => 
        userPreferences.value.recent_games.includes(game.id.toString())
      )
    }
    
    const typeMap: Record<GameType, number> = {
      'Casino': 1,
      'Slots': 2,
      'Poker': 3,
      'Bingo': 4,
      'Arcade': 5,
      'Sports': 6,
      'Like': 0,
      'History': 0,
      'Baccarat': 7,
      'Roulette': 8,
      'Blackjack': 9
    }
    
    return gameList.value.filter(game => game.game_type === typeMap[type])
  }
  
  function searchGames(query: string): HallGameIcon[] {
    if (!query.trim()) return gameList.value
    
    const lowerQuery = query.toLowerCase()
    return gameList.value.filter(game => 
      game.game_name.toLowerCase().includes(lowerQuery) ||
      game.provider_name?.toLowerCase().includes(lowerQuery) ||
      game.content?.toLowerCase().includes(lowerQuery)
    )
  }
  
  function getRecommendedGames(limit: number = 10): HallGameIcon[] {
    // 基于用户偏好推荐游戏
    const favoriteTypes = userPreferences.value.favorite_types
    const favoriteProviders = userPreferences.value.favorite_providers
    
    let recommended = gameList.value.filter(game => {
      // 排除已玩过的游戏
      if (userPreferences.value.recent_games.includes(game.id.toString())) {
        return false
      }
      
      // 优先推荐喜欢的类型和厂商
      const typeMatch = favoriteTypes.length === 0 || favoriteTypes.some(type => {
        const typeMap: Record<GameType, number> = {
          'Casino': 1, 'Slots': 2, 'Poker': 3, 'Bingo': 4,
          'Arcade': 5, 'Sports': 6, 'Like': 0, 'History': 0,
          'Baccarat': 7, 'Roulette': 8, 'Blackjack': 9
        }
        return game.game_type === typeMap[type]
      })
      
      const providerMatch = favoriteProviders.length === 0 || 
        favoriteProviders.includes(game.provider_name || '')
      
      return typeMatch || providerMatch
    })
    
    // 按热度和RTP排序
    recommended.sort((a, b) => {
      const aScore = (a.popularity || 0) + (a.rtp || 0) * 10
      const bScore = (b.popularity || 0) + (b.rtp || 0) * 10
      return bScore - aScore
    })
    
    return recommended.slice(0, limit)
  }
  
  function refreshData() {
    lastRefreshTime.value = Date.now()
  }

  return {
    // 状态
    gameList,
    currentGameType,
    bannerData,
    activityData,
    transactionRecords,
    isLoading,
    searchText,
    gameFilter,
    gameStats,
    userPreferences,
    filterTimeType,
    lastRefreshTime,
    
    // 计算属性
    filteredGameList,
    validActivities,
    gamesByProvider,
    popularGames,
    newGames,
    
    // 方法
    formatNumberWithCommas,
    addToHistory,
    toggleLike,
    isGameLiked,
    setGameType,
    setSearchText,
    setGameList,
    addGame,
    removeGame,
    setBannerData,
    setActivityData,
    setTransactionRecords,
    setLoading,
    setFilterTimeType,
    updateGameFilter,
    clearGameFilter,
    updateUserPreferences,
    savePreferences,
    loadPreferences,
    updateGameStats,
    getFilteredTransactions,
    getGamesByType,
    searchGames,
    getRecommendedGames,
    refreshData
  }
})
