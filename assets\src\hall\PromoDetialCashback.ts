import { DEEP_INDEXZ, E_CHANEL_TYPE, MAINTENANCETIPCODE, UI_PATH_DIC } from "../GlobalConstant";
import Global from "../GlobalScript";
import { showMaintenancetip } from "../Maintenancetip";
import HttpUtils from "../net/HttpUtils";
import utils from "../utils/utils";
import { ALL_APP_SOURCE_CONFIG } from "../Config";
import { uiManager } from "../mgr/UIManager";
import MoreGameManager from "../MoreGameManager";
import Big from "../libs/big.js";
import { LOGIN_WAY } from "./Hall";
import PromoDetialDetials from "./PromoDetialDetials";
import GameControl from "../GameControl";

const {ccclass, property} = cc._decorator;

enum ACTIVITY_TYPE {
    SIGNBIND = 1,
    FIRSTDEPOSIT = 2,
    CASHBACK = 3,
    VIPCASHBACK = 4,
    CASHBACK_DETIALS = 100,
    VIP_CASHBACK_DETIALS = 101,
};

@ccclass
export default class PromoDetialCashback extends cc.Component {

    @property(cc.Node)
    cashBackBonusView: cc.Node = null;

    @property(cc.Node)
    btnBack: cc.Node = null;

    @property(cc.Node)
    btnTransion: cc.Node = null;

    @property(cc.Node)
    btnDetials: cc.Node = null;

    @property(cc.Label)
    labTitle: cc.Label = null;

    @property(cc.PageView)
    cashbackPageView: cc.PageView = null;

    @property(cc.Node)
    todayCashbackList: cc.Node = null; 

    @property(cc.Node)
    yesterdayCashbackList: cc.Node = null; 

    @property(cc.Node)
    firstDepositRules: cc.Node = null;

    @property(cc.Node)
    redpoint1: cc.Node = null;

    @property(cc.PageView)
    pageView: cc.PageView = null;

    @property([cc.SpriteFrame])
    cashbackBG: cc.SpriteFrame[] = [];

    @property([cc.SpriteFrame])
    btnBG: cc.SpriteFrame[] = [];

    @property([cc.SpriteFrame])
    claimBtnBg: cc.SpriteFrame[] = [];

    @property(cc.ScrollView)
    scrollView: cc.ScrollView = null;

    yesterdatTotalBonus = 0;
    cashBackRuleClose = false;//cashback 活动是否关闭
    vipCashBackRuleClose = false;//vip cashback 活动是否关闭

    openType = null;//打开页面类型

    start_period = null;//vip_cashback活动开始时间
    end_period = null;//vip_cashback活动结束时间
    normalStatusUpdatedTime = null;
    vipStatusUpdatedTime = null;

    cashback_status = null;//普通返水活动开关状态
    vipcashback_status = null;//vip返水活动开关状态

    activity_type = null;//活动类型
    config_cashback = null;

    detailsData = null;
    timefuc = null

    type = null;
    onLoad () {
        this.cashBackBonusView.getChildByName("needLoginBtn").active = !Global.getInstance().token;
        this.scrollView.node.on("scrolling", ()=>{this.title_layout_opacity(this.scrollView)}, this);
        this.pageView.node.on('page-turning', this.turingPageNormalListener, this);
        this.pageView.enabled = false;
        // let startTouchPos
        // let scrolling = false;
        // this.pageView.node.on(cc.Node.EventType.TOUCH_START, (event: cc.Event.EventTouch) => {
        //     startTouchPos = event.getLocation();
        //     this.cashBackBonusView.getComponent(cc.ScrollView).enabled = true;
        //     this.pageView.enabled = true;
        //     scrolling = false;
        // });
        
        // this.pageView.node.on(cc.Node.EventType.TOUCH_MOVE, (event: cc.Event.EventTouch) => {
        //     const currentTouchPos = event.getLocation();
        //     const deltaX = currentTouchPos.x - startTouchPos.x;
        //     const deltaY = currentTouchPos.y - startTouchPos.y;
        //     if (scrolling) {
        //         return;
        //     }
        //     if (Math.abs(deltaX) > Math.abs(deltaY)) {
        //         this.cashBackBonusView.getComponent(cc.ScrollView).enabled = false;
        //         this.pageView.enabled = true;
        //     } else {
        //         this.cashBackBonusView.getComponent(cc.ScrollView).enabled = true;
        //         this.pageView.enabled = false;
        //     }
        //     scrolling = true;
        // });

    }

    initType(args){
        this.type = args;
    }

    start () {
        this.cashbackDetials(()=>{
            this.initCashback();
        });
        let page = this.node.getChildByName("page").getComponent(cc.PageView);
        page.node.on('page-turning', ()=>{
            let curIndex = page.getCurrentPageIndex();
            if (curIndex != 1) {
                this.node.destroy();
            }
        }, this);
        let tempnode = this.node.getChildByName("temp");
        page.insertPage(cc.instantiate(tempnode),0);
        page.scrollToPage(1,0.01);
    }

    /**普通返水活动 */
    initCashback() {
        let todayLayout = this.cashBackBonusView.getChildByName("todayLayout");
        let yesterdayLayout = this.cashBackBonusView.getChildByName("yesterdayLayout");
        if (todayLayout.active) {
            this.scrollView.content.height = 6530;
        } else {
            this.scrollView.content.height = 6530;
        }
        todayLayout.x = 0;
        yesterdayLayout.x = 6000;

        this.initCashbackToday();
        this.initCashbackYesterday();
    }

    initCashbackToday() {
        if (!!Global.getInstance().token) {
            HttpUtils.getInstance().post(1, 3, this, "/avt/api/activity/daily-rebate", {
                token: Global.getInstance().token,
            }, (response) => {
                if (!this.node || !this.node.isValid) {
                    return;
                }
                if (response.data && response.data.length > 0) {
                    let totalAwardNum = 0;
                    let totalTotal = utils.getChildByPath(this.todayCashbackList,"list.total_awrad").getComponent(cc.Label);
                    let jili_betamount = utils.getChildByPath(this.todayCashbackList, "list.jili_cashback.bet_amount").getComponent(cc.Label);
                    let jili_cashback = utils.getChildByPath(this.todayCashbackList, "list.jili_cashback.superace_cashback").getComponent(cc.Label);
                    let superace_award = utils.getChildByPath(this.todayCashbackList, "list.superace_awrad").getComponent(cc.Label);

                    //cashback rules
                    // let cashback_rules = utils.getChildByPath(this.todayCashbackList, "list.unvip_cashback_ratio");
                    // if (this.detailsData && this.detailsData.config && this.detailsData.config.length > 0) {
                    //     let config = this.detailsData.config;
                    //     let actStatus = this.detailsData.status;//status:0关 1开
                    //     for (let index = 1; index < config[0].length; index++) {
                    //         let item = config[0][index];
                    //         let rate = cashback_rules.getChildByName("level_1_"+item.game_type.toLowerCase()).getComponent(cc.Label);
                    //         if (actStatus != 1) {
                    //             rate.string = "--";
                    //         } else {
                    //             rate.string = item.rate+"%";
                    //         }
                    //     }
                    // }

                    let isVip = Number(Global.getInstance().userdata?.is_vip) || 0;
                    if (isVip) {
                        //jili cashback
                        if(jili_betamount) jili_betamount.string = "₱0";
                        if(jili_cashback) jili_cashback.string = "₱0";
                        //jili total cashback
                        if(superace_award) superace_award.string = "₱0.00";

                        for (let index = 0; index < response.data.length; index++) {
                            let element = response.data[index];
                            //let betco = utils.getChildByPath(this.todayCashbackList,"list.cashback_percent."+element.category.toLowerCase()+"_cashback").getComponent(cc.Label);
                            let betamount = utils.getChildByPath(this.todayCashbackList,"list."+element.category.toLowerCase()+"_bet_amount").getComponent(cc.Label);
                            //let nextlv = utils.getChildByPath(this.todayCashbackList,"list.next_level_bet."+element.category.toLowerCase()+"_next_level_bet").getComponent(cc.Label);
                            let cashback = utils.getChildByPath(this.todayCashbackList,"list.cashback."+element.category.toLowerCase()+"_cashback").getComponent(cc.Label);
                            if(cashback) cashback.string = "₱0";
                            //if (betco)  betco.string = "0%";
                            if (betamount)  betamount.string = "₱0";
                            //if (nextlv)  nextlv.string = "₱0";
                            totalAwardNum = 0;
                        }
                        totalTotal.string = "₱"+utils.formatNumberWithCommas(totalAwardNum);
                    } else {
                        //jili cashback
                        HttpUtils.getInstance().post(1, 3, this, "/avt/api/activity/jili_game/cashback", {
                            token: Global.getInstance().token,
                            type: 1
                        }, (response) => {
                            if(!this.node || !this.node.isValid){
                                return;
                            }
                            if(response.data){
                                let jili_bet = response.data.total_bet || "0";
                                if(jili_betamount) jili_betamount.string = "₱"+utils.formatNumberWithCommas(parseFloat(jili_bet));
                                if(jili_cashback) jili_cashback.string = "₱"+response.data.cash_back;
                                //jili total cashback
                                if(superace_award) superace_award.string = "₱"+utils.formatNumberWithCommas(parseFloat(response.data.cash_back));
                            }
                        })

                        for (let index = 0; index < response.data.length; index++) {
                            let element = response.data[index];
                            //let betco = utils.getChildByPath(this.todayCashbackList,"list.cashback_percent."+element.category.toLowerCase()+"_cashback").getComponent(cc.Label);
                            let betamount = utils.getChildByPath(this.todayCashbackList,"list."+element.category.toLowerCase()+"_bet_amount").getComponent(cc.Label);
                            //let nextlv = utils.getChildByPath(this.todayCashbackList,"list.next_level_bet."+element.category.toLowerCase()+"_next_level_bet").getComponent(cc.Label);
                            //if (betco)  betco.string = element.cashback;
                            if (betamount)  betamount.string = "₱"+element.bet_amount;
                            //if (nextlv)  nextlv.string = "₱"+element.next_level_bet;
                            let bett = new Big(parseFloat(element.cashback)).div(100).times(parseFloat(element.bet_amount)).toString();
                            totalAwardNum = new Big(totalAwardNum).add(parseFloat(utils.formatNumberDigits(parseFloat(bett)))).toString();
                            let cashback = utils.getChildByPath(this.todayCashbackList,"list.cashback."+element.category.toLowerCase()+"_cashback").getComponent(cc.Label);
                            if(cashback) cashback.string = "₱"+parseFloat(utils.formatNumberDigits(parseFloat(bett)));
                        }
                        totalTotal.string = "₱"+utils.formatNumberWithCommas(totalAwardNum);
                    }
                }
            });
        }
    }

    initCashbackYesterday() {
        if (!!Global.getInstance().token) {
            HttpUtils.getInstance().post(1, 3, this, "/avt/api/activity/daily-rebate-yesterday", {
                token: Global.getInstance().token,
            }, (response) => {
                if (!this.node || !this.node.isValid) {
                    return;
                }
                if (response.data) {
                    let gameData = response.data.data;
                    if (gameData && gameData.length > 0) {
                        let totalAwardNum = 0;
                        let yesterdayTotal = utils.getChildByPath(this.yesterdayCashbackList,"list.yesterday_total").getComponent(cc.Label);
                        let jili_betamount = utils.getChildByPath(this.yesterdayCashbackList, "list.jili_cashback.bet_amount").getComponent(cc.Label);
                        let jili_cashback = utils.getChildByPath(this.yesterdayCashbackList, "list.jili_cashback.superace_cashback").getComponent(cc.Label);
                        let superace_award = utils.getChildByPath(this.yesterdayCashbackList, "list.superace_awrad").getComponent(cc.Label);

                        //cashback rules
                        // let cashback_rules = utils.getChildByPath(this.yesterdayCashbackList, "list.unvip_cashback_ratio");
                        // if (this.detailsData && this.detailsData.config && this.detailsData.config.length > 0) {
                        //     let config = this.detailsData.config;
                        //     let actStatus = this.detailsData.status;//status:0关 1开
                        //     for (let index = 1; index < config[0].length; index++) {
                        //         let item = config[0][index];
                        //         let rate = cashback_rules.getChildByName("level_1_"+item.game_type.toLowerCase()).getComponent(cc.Label);
                        //         if (actStatus != 1) {
                        //             rate.string = "--";
                        //         } else {
                        //             rate.string = item.rate+"%";
                        //         }
                        //     }
                        // }

                        if(Number(response.data.yesterday_user_status)) {//0:普通用户 1:VIP用户(昨日身份)
                            //jili cashback
                            if(jili_betamount) jili_betamount.string = "₱0"+"(1.0%)";
                            if(jili_cashback) jili_cashback.string = "₱0";
                            //jili total cashback
                            if(superace_award) superace_award.string = "₱0.00";

                            for (let index = 0; index < gameData.length; index++) {
                                let element = gameData[index];
                                let betco = utils.getChildByPath(this.yesterdayCashbackList,"list."+element.category.toLowerCase()+"_cashback").getComponent(cc.Label);
                                let betamount = utils.getChildByPath(this.yesterdayCashbackList,"list."+element.category.toLowerCase()+"_bet_amount").getComponent(cc.Label);
                                if (betco)  betco.string = "₱0";
                                if (betamount)  betamount.string = "₱0"+"(0%)";
                                totalAwardNum = 0;
                            }
                            this.yesterdatTotalBonus = totalAwardNum;
                            yesterdayTotal.string = "₱"+utils.formatNumberWithCommas(totalAwardNum);

                            //红点和领取按钮隐藏
                            let btn_cliam_ly = utils.getChildByPath(this.cashBackBonusView,"yesterdayLayout");
                            let btn_cliam = utils.getChildByPath(btn_cliam_ly,"btn_cliam");
                            let btn_cliam_lab = utils.getChildByPath(btn_cliam,"Background.Label").getComponent(cc.Label);
                            let Background = btn_cliam.getChildByName("Background").getComponent(cc.Sprite);
                            btn_cliam_ly.active = true;
                            btn_cliam.getComponent(cc.Button).enabled = false;
                            btn_cliam.opacity = 255;
                            Background.spriteFrame = this.claimBtnBg[1];
                            btn_cliam_lab.string = "Available From ₱1"
                            this.redpoint1.active = false;
                        } else {
                            //jili cashback
                            HttpUtils.getInstance().post(1, 3, this, "/avt/api/activity/jili_game/cashback", {
                                token: Global.getInstance().token,
                                type: 2
                            }, (response) => {
                                if(!this.node || !this.node.isValid){
                                    return;
                                }
                                if(response.data){
                                    let jili_bet = response.data.total_bet || "0";
                                    if(jili_betamount) jili_betamount.string = "₱"+utils.formatNumberWithCommas(parseFloat(jili_bet))+"(1.0%)";
                                    if(jili_cashback) jili_cashback.string = "₱"+response.data.cash_back;
                                    //jili total cashback
                                    if(superace_award) superace_award.string = "₱"+utils.formatNumberWithCommas(parseFloat(response.data.cash_back));
                                }
                            })

                            for (let index = 0; index < gameData.length; index++) {
                                let element = gameData[index];
                                let betco = utils.getChildByPath(this.yesterdayCashbackList,"list."+element.category.toLowerCase()+"_cashback").getComponent(cc.Label);
                                let betamount = utils.getChildByPath(this.yesterdayCashbackList,"list."+element.category.toLowerCase()+"_bet_amount").getComponent(cc.Label);
                                let bett = new Big(parseFloat(element.cashback)).div(100).times(parseFloat(element.bet_amount)).toString();
                                if (betco)  betco.string = "₱"+utils.formatNumberWithCommas(parseFloat(bett));
                                if (betamount)  betamount.string = "₱"+element.bet_amount+"("+element.cashback+")";
                                totalAwardNum = new Big(totalAwardNum).add(parseFloat(utils.formatNumberDigits(parseFloat(bett)))).toString();
                            }
                            this.yesterdatTotalBonus = totalAwardNum;
                            yesterdayTotal.string = "₱"+utils.formatNumberWithCommas(totalAwardNum);

                            let status = response.data.status;
                            let btn_cliam_ly = utils.getChildByPath(this.cashBackBonusView,"yesterdayLayout");
                            let btn_cliam = utils.getChildByPath(btn_cliam_ly,"btn_cliam");
                            let lab_notice = this.yesterdayCashbackList.getChildByName("lan_notice").getComponent(cc.Label);
                            let btn_cliam_lab = utils.getChildByPath(btn_cliam,"Background.Label").getComponent(cc.Label);
                            let Background = btn_cliam.getChildByName("Background").getComponent(cc.Sprite);
                            btn_cliam_ly.active = true;
                            this.redpoint1.active = false;
                            btn_cliam.getComponent(cc.Button).enabled = true;
                            btn_cliam.opacity = 255;
                            Background.spriteFrame = this.claimBtnBg[0];
                            if (status === "" || this.yesterdatTotalBonus < 1) {
                                btn_cliam.getComponent(cc.Button).enabled = false;
                                Background.spriteFrame = this.claimBtnBg[1];
                                btn_cliam_lab.string = "Available From ₱1"
                                //Global.getInstance().setStoreageData("PROMO_BONUS_CAN_GET", 0);
                                Global.getInstance().unreadMarks.cashback = 0;
                            } else if (parseInt(status) == 0) {
                                btn_cliam.getComponent(cc.Button).enabled = false;
                                btn_cliam.opacity = 180;
                                btn_cliam_lab.string = "Bonus Payout"
                               // Global.getInstance().setStoreageData("PROMO_BONUS_CAN_GET", 0);
                                Global.getInstance().unreadMarks.cashback = 0;
                            } else if (parseInt(status) == 1 && this.yesterdatTotalBonus >= 1) {
                                btn_cliam.getComponent(cc.Button).enabled = true;
                                btn_cliam_lab.string = "Claim"
                                this.redpoint1.active = true;
                                //Global.getInstance().setStoreageData("PROMO_BONUS_CAN_GET", 1);
                                Global.getInstance().unreadMarks.cashback = 1;
                            } else if (parseInt(status) == 2) {
                                btn_cliam.getComponent(cc.Button).enabled = false;
                                Background.spriteFrame = this.claimBtnBg[1];
                                btn_cliam_lab.string = "Received"
                                //Global.getInstance().setStoreageData("PROMO_BONUS_CAN_GET", 0);
                                Global.getInstance().unreadMarks.cashback = 0;
                            } else if (parseInt(status) == 3) {
                                btn_cliam_ly.active = false;
                                //Global.getInstance().setStoreageData("PROMO_BONUS_CAN_GET", 0);
                                Global.getInstance().unreadMarks.cashback = 0;
                            }
                        }

                        //根据thid.cashback_status&&status_time 判定Toady/Yesterday活动是否开启
                        //let activityStatus = response.data.activity_status;
                        let statusTime = parseInt(this.normalStatusUpdatedTime);
                        let tdTimes = new Date(); // 获取当前时间
                        tdTimes.setHours(0, 0, 0, 0)

                        let list = this.todayCashbackList.getChildByName("list");
                        let td_empty = this.todayCashbackList.getChildByName("td_empty");
                        let la_notice = this.todayCashbackList.getChildByName("lab_notice");
                        let btn_bet = utils.getChildByPath(this.cashBackBonusView, "yesterdayLayout");
                        let list2 = this.yesterdayCashbackList.getChildByName("list");
                        let yd_empty = this.yesterdayCashbackList.getChildByName("yd_empty");
                        let lan_notice = this.yesterdayCashbackList.getChildByName("lan_notice");
                        if(this.cashback_status != 1 && statusTime) {
                            list.active = false;
                            td_empty.active = true;
                            this.vipCashBackRuleClose = true;
                            la_notice.active = false;
                            if (statusTime < tdTimes.getTime()/1000) {
                                list2.active = false;
                                yd_empty.active = true;
                                lan_notice.active = false;
                                btn_bet.active = false;
                            }
                        } else {
                            list.active = true;
                            list2.active = true;
                            td_empty.active = false;
                            yd_empty.active = false;
                            this.vipCashBackRuleClose = false;
                            la_notice.active = true;
                            lan_notice.active = true;
                        }
                    }
                }
            });
        }
    }

    /**cashback detials */
    cashbackDetials(cb?) {
        HttpUtils.getInstance().post(1, 3, this, "/avt/api/activity/rebate-conf", {
            token: Global.getInstance().token,
            config_type: 1,//1:普通返水 4:vip返水
        }, (response) => {
            if (!this.node || !this.node.isValid) {
                return;
            }
            if (response.data) {
                this.cashback_status = response.data.status;
                this.normalStatusUpdatedTime = response.data.updated_at;
                this.detailsData = response.data;
            }
            if(cb) cb();
        }, (response)=>{
            if(cb) cb();
        });
    }

    turingPageNormalListener(event,data) {
        let curIndex = this.pageView.getCurrentPageIndex();
        let content = this.scrollView.content;
        let todayLayout = this.cashBackBonusView.getChildByName("todayLayout");
        let yesterdayLayout = this.cashBackBonusView.getChildByName("yesterdayLayout");
        if (curIndex == 0) {
            utils.getChildByPath(content,"top.btn_today").getComponent(cc.Sprite).spriteFrame = this.btnBG[1];
            utils.getChildByPath(content,"top.btn_yesterday").getComponent(cc.Sprite).spriteFrame = this.btnBG[0];
            utils.getChildByPath(content,"top.btn_today.labtoday").color = cc.color(255,255,255);
            utils.getChildByPath(content,"top.btn_yesterday.labyestaday").color = cc.color(179,179,179);
            todayLayout.x = 0;
            yesterdayLayout.x = 6000;
            if (todayLayout.active) {
                this.scrollView.content.height = 6530;
            } else {
                this.scrollView.content.height = 6530;
            }
        } else if (curIndex == 1) {
            utils.getChildByPath(content,"top.btn_today").getComponent(cc.Sprite).spriteFrame = this.btnBG[0];
            utils.getChildByPath(content,"top.btn_yesterday").getComponent(cc.Sprite).spriteFrame = this.btnBG[1];
            utils.getChildByPath(content,"top.btn_today.labtoday").color = cc.color(179,179,179);
            utils.getChildByPath(content,"top.btn_yesterday.labyestaday").color = cc.color(255,255,255);
            todayLayout.x = 6000;
            yesterdayLayout.x = 0;
            if (yesterdayLayout.active) {
                this.scrollView.content.height = 6530;
            } else {
                this.scrollView.content.height = 6530;
            }
        }
    }

    backToPromo(type?) {
        if(this.type == "banner"){
            let gamescene =  cc.director.getScene().getComponentInChildren(GameControl);
            gamescene.mTabbar.rein_home();
            this.node.destroy();
        }else{
            this.node.destroy();
            cc.director.emit("refresh_promo");
        }
    }

    cliclDetialView(event,userdata) {
        if (!Global.getInstance().token) {
            //未登录 跳转登录页
            uiManager.instance.showDialog(UI_PATH_DIC.phonePasswordLogin, [{ way: LOGIN_WAY.PhoneCode, curPromoDetail:true }])//curPromoDetail:true表示当前是活动详情页面
            return;
        }
        uiManager.instance.loadPrefabByLoading("prefab/hall/cashback_detials",cc.Prefab,(finish, total, item) => {
        }, (err, prefab: any) => {
                if (err) {
                    return;
                }
                let pop = cc.instantiate(prefab);
                let script = pop.getComponent(PromoDetialDetials);
                script.initData(this.detailsData);
                pop.parent =Global.getInstance().popNode;
        });
    }

    clickTransionView() {
        if (!Global.getInstance().token) {
            //未登录 跳转登录页
            uiManager.instance.showDialog(UI_PATH_DIC.phonePasswordLogin, [{ way: LOGIN_WAY.PhoneCode, curPromoDetail:true }],null,DEEP_INDEXZ.TRANSATIONS)//curPromoDetail:true表示当前是活动详情页面
            return;
        }
        uiManager.instance.showDialog(UI_PATH_DIC.Transations,["award"]);
    }

    clickCashbackdays(event,userdata) {
        this.pageView.enabled = true;
        clearTimeout(this.timefuc);
        this.pageView.scrollToPage(parseInt(userdata),0.2);
        this.timefuc = setTimeout(() => {
            this.pageView.enabled = false;
        }, 300);
    }

    clickgoBet(event,userdata) {
        if (!Global.getInstance().token) {
            //未登录 跳转登录页
            uiManager.instance.showDialog(UI_PATH_DIC.phonePasswordLogin, [{ way: LOGIN_WAY.PhoneCode, curPromoDetail:true }])//curPromoDetail:true表示当前是活动详情页面
            return;
        }

        // 再跳转到游戏逻辑
        let gamescene =  cc.director.getScene().getComponentInChildren(GameControl);
        gamescene.mTabbar.clickGoBet(()=>{
            this.node.destroy();
        });
    }

    clickCliamYesterdayBonus() {
        if (!Global.getInstance().token) {
            //未登录 跳转登录页
            uiManager.instance.showDialog(UI_PATH_DIC.phonePasswordLogin, [{ way: LOGIN_WAY.PhoneCode, curPromoDetail:true }])//curPromoDetail:true表示当前是活动详情页面
            return;
        }
        if (!!Global.getInstance().token) {
            let params = {
                token:Global.getInstance().token
            };
            let btn_cliam = utils.getChildByPath(this.cashBackBonusView,"yesterdayLayout.btn_cliam").getComponent(cc.Button);
            btn_cliam.enabled = false;
            HttpUtils.getInstance().post(3, 3, this, "/avt/api/activity/daily-rebate-receive", params, (response) => {
                if (!this.node || !this.node.isValid) {
                    return;
                }
                if (response.code == 200) {
                    let totalAwardNum = utils.formatNumberWithCommas(this.yesterdatTotalBonus);
                    let string1 = "<color=#707070>You've got </c>"+"<color=#F26225>"+"<b>₱"+totalAwardNum+"</b></c>"+"<color=#707070>\n\nCashback bonus!</c>"
                    Global.getInstance().showCommonTip2({ word: string1, confirm: "Done" }, this, true, () => {},null,null,"Congratulations");
                    this.initCashbackYesterday();
                    Global.getInstance().setStoreageData("PROMO_BONUS_CAN_GET", 0);
                    Global.getInstance().unreadMarks.cashback = 0;
                    cc.director.emit("update_home_widget");
                } else {
                    btn_cliam.enabled = true;
                    if (response.msg) {
                        Global.getInstance().showSimpleTip(response.msg);
                    }
                }
            }, function (response) {
                btn_cliam.enabled = true;
                if (!!response && !!response["code"]) {
                    if (response.code == MAINTENANCETIPCODE) { //服务器维护
                        showMaintenancetip(response.msg)
                        return
                    }
                }
            });
        }
    }

    clickSignUp() {
        if (ALL_APP_SOURCE_CONFIG.channel == E_CHANEL_TYPE.WEB) {//web渠道
            if (!Global.getInstance().token) {
                //未登录 跳转登录页
                uiManager.instance.showDialog(UI_PATH_DIC.phonePasswordLogin, [{ way: LOGIN_WAY.PhoneCode, curPromoDetail:true }])//curPromoDetail:true表示当前是活动详情页面
                return;
            }
        }
    }

    showLoginView() {
        uiManager.instance.showDialog(UI_PATH_DIC.phonePasswordLogin, [{ way: LOGIN_WAY.PhoneCode, curPromoDetail:true }])
        this.cashBackBonusView.getChildByName("needLoginBtn").active = !Global.getInstance().token;
    }

    title_layout_opacity(node:cc.ScrollView) {
        let opacity = node.getScrollOffset().y
        if (opacity > 255 ) opacity = 255;
        let layou = utils.getChildByPath(this.cashBackBonusView,"titlelayout.bg");
        layou.opacity = opacity;
        let btn_back = utils.getChildByPath(this.cashBackBonusView,"titlelayout.btn_back.img");
        let btn_transiton = utils.getChildByPath(this.cashBackBonusView,"titlelayout.btn_transiton.img");
        let btn_rules = utils.getChildByPath(this.cashBackBonusView,"titlelayout.btn_rules.img");
        if (opacity > 0) {
            btn_back.color = cc.color(255-opacity/255*180,255-opacity/255*168,255-opacity/255*151);
            btn_transiton.color = cc.color(255-opacity/255*180,255-opacity/255*168,255-opacity/255*151);
            btn_rules.color = cc.color(255-opacity/255*180,255-opacity/255*168,255-opacity/255*151);
        } else {
            btn_back.color = cc.color(255,255,255);
            btn_transiton.color = cc.color(255,255,255);
            btn_rules.color = cc.color(255,255,255);
        }
        
    }

    initLayoutOpacity() {
        let layou = utils.getChildByPath(this.node,"titlelayout.bg");
        layou.opacity = 0;
        let btn_back = utils.getChildByPath(this.node,"titlelayout.btn_back.img");
        let btn_transiton = utils.getChildByPath(this.node,"titlelayout.btn_transiton.img");
        let btn_rules = utils.getChildByPath(this.node,"titlelayout.btn_rules.img");
        btn_back.color = cc.color(255,255,255);
        btn_transiton.color = cc.color(255,255,255);
        btn_rules.color = cc.color(255,255,255);
    }
}
