import UICommon from "../component/UICommon";
import GameControl from "../GameControl";
import { DEEP_INDEXZ, UI_PATH_DIC } from "../GlobalConstant";
import Global from "../GlobalScript";
import { uiManager } from "../mgr/UIManager";
import HttpUtils from "../net/HttpUtils";
import RoundRectMask from "../RoundRectMask";
import utils from "../utils/utils";

const { ccclass, property } = cc._decorator;

@ccclass
export default class LeaderBoard extends UICommon {
   
    @property(cc.Node)
    todayNode: cc.Node = null;

    @property(cc.Node)
    yestedayNode: cc.Node = null;

    @property(cc.Node)
    btnBg: cc.Node = null;

    @property(cc.Button)
    btnToday: cc.Button = null;

    @property(cc.Button)
    btnYesterday: cc.Button = null;

    @property(cc.Label)
    leftTime: cc.Label = null;

    @property(cc.ScrollView)
    todayScrollView: cc.ScrollView = null;

    @property(cc.ScrollView)
    yesterdayScrollView: cc.ScrollView = null;

    @property(cc.Node)
    model: cc.Node = null;

    @property([cc.SpriteFrame])
    rankspList: cc.SpriteFrame[] = [];

    @property(cc.Node)
    lab_today: cc.Node = null;

    isSliding = false;
    currentPage = "today";

    type = null;
    onLoad() {
        this.initWidget();
    }

    show(args) {
        this.type = args?.tpye;
        this.currentPage = args;
    }
    start() {
        if (this.currentPage == "today") {
            this.clickToday();
        } else if (this.currentPage == "yesterday") {
            this.clickYesterday();
        }
        
        let page = this.node.getChildByName("page").getComponent(cc.PageView);
        page.node.on('page-turning', ()=>{
            let curIndex = page.getCurrentPageIndex();
            if (curIndex != 1) {
                this.node.destroy();
            }
        }, self);
        let tempnode = this.node.getChildByName("temp");
        page.insertPage(cc.instantiate(tempnode),0);
        page.scrollToPage(1,0.01);
    }

    protected onDestroy(): void {
        let btn_spin = Global.getInstance().popNode.getChildByName("btn_spin");
        if (btn_spin) btn_spin.zIndex = 1;
        Global.instance.hideEnvelopeBtn(1);
        this.unscheduleAllCallbacks();
    }
  
    initWidget() {
        // this.setLeftTime();
        // this.schedule(this.setLeftTime,1,cc.macro.REPEAT_FOREVER,1);
        this.geTodaytRankData();
        this.getYesterdaytRankData();
        setTimeout(() => {
            this.todayNode.getChildByName("listview").getComponent(RoundRectMask).radius = 80;
            this.yestedayNode.getChildByName("listview").getComponent(RoundRectMask).radius = 80;
        }, 200);
    }

    geTodaytRankData() {
        HttpUtils.getInstance().post(1, 3, this, "/avt/api/rank/casino", {
            token: Global.getInstance().token,
            type:1
        }, (response) => {
            if (!this.node || !this.node.isValid) {
                return;
            }
            if (response && response.data && response.data.list) {
                let rankList = response.data.list.rank;
                let selfInfo = response.data.list.user;
                let selfBet = response.data.list.bet_amount;
                this.showselfInfo(this.todayNode,selfInfo,selfBet);
                if (rankList) {
                    this.showTop3(this.todayNode,rankList);
                    this.showScrollList(rankList,this.todayScrollView,selfInfo);
                }
            }
        },()=>{
        });
    }

    getYesterdaytRankData() {
        HttpUtils.getInstance().post(1, 3, this, "/avt/api/rank/casino", {
            token: Global.getInstance().token,
            type:2
        }, (response) => {
            if (!this.node || !this.node.isValid) {
                return;
            }
            if (response && response.data && response.data.list) {
                let rankList = response.data.list.rank;
                let selfInfo = response.data.list.user;
                let selfBet = response.data.list.bet_amount;
                this.showselfInfo(this.yestedayNode,selfInfo,selfBet);
                if (rankList) {
                    this.showTop3(this.yestedayNode,rankList);
                    this.showScrollList(rankList,this.yesterdayScrollView,selfInfo);
                }
            }
        },()=>{
        });
    }

    showselfInfo(node,selfInfo,selfBet) {
        let selfinfoNode = node.getChildByName("self_info");
        let ranksp = selfinfoNode.getChildByName("ranksp").getComponent(cc.Sprite);
        let lab_rank = selfinfoNode.getChildByName("lab_rank").getComponent(cc.Label);
        let lab_betamout = selfinfoNode.getChildByName("lab_betamout").getComponent(cc.Label);
        let lab_award = selfinfoNode.getChildByName("lab_award").getComponent(cc.Label);
        if (selfInfo) {
            lab_betamout.string = utils.formatNumberWithCommas(selfInfo.total_bet_amount,2);
            lab_award.string = utils.formatNumberWithCommas(selfInfo.award,0);
            if (parseInt(selfInfo.rank) <= 3) {
                ranksp.node.active = true;
                lab_rank.node.active = false;
                ranksp.spriteFrame = this.rankspList[parseInt(selfInfo.rank)-1];
            } else {
                ranksp.node.active = false;
                lab_rank.node.active = true;
                lab_rank.string = selfInfo.rank;
            }
        } else {
            lab_rank.string = "--";
            lab_betamout.string = utils.formatNumberWithCommas(selfBet,2);
            lab_award.string = "--";
        }
    }

    showTop3(parentNode, rankList) {
        for (let index = 0; index < 3; index++) {
            let node = utils.getChildByPath(parentNode,"top.number"+(index+1));
            let element = rankList[index];
            let lab_userid = node.getChildByName("lab_userid").getComponent(cc.Label);
            let lab_betamout = node.getChildByName("lab_betamout").getComponent(cc.Label);
            let lab_award = node.getChildByName("lab_award").getComponent(cc.Label);
            let str = element.player_id;
            if (element.player_id.length > 5) {
                str = element.player_id.substring(0, 2) + "****" + element.player_id.substring(element.player_id.length-3,element.player_id.length);
            }
            lab_userid.string = str;
            if (parseFloat(element.total_bet_amount) && parseFloat(element.total_bet_amount) > 0) {
                lab_betamout.string = utils.formatNumberWithCommas(parseFloat(element.total_bet_amount),2);
            } else {
                lab_betamout.string = "--";
            }
            lab_award.string = utils.formatNumberWithCommas(element.award,0);
        }
    }

    showScrollList(info,scrollView,selfInfo) {
        scrollView.content.removeAllChildren();
        for (let index = 0; index < info.length; index++) {
            let element = info[index];
            let node = cc.instantiate(this.model);
            let youself = node.getChildByName("youself");
            let lab_rank = node.getChildByName("lab_rank").getComponent(cc.Label);
            let ranksp = node.getChildByName("ranksp").getComponent(cc.Sprite);
            let lab_userid = node.getChildByName("lab_userid").getComponent(cc.Label);
            let lab_betamout = node.getChildByName("lab_betamout").getComponent(cc.Label);
            let lab_award = node.getChildByName("lab_award").getComponent(cc.Label);
            if (index < 3) {
                ranksp.node.active = true;
                lab_rank.node.active = false;
                ranksp.spriteFrame = this.rankspList[index];
            } else {
                ranksp.node.active = false;
                lab_rank.node.active = true;
                lab_rank.string = element.rank;
            }
            let str = element.player_id;
            if (element.player_id.length > 5) {
                str = element.player_id.substring(0, 2) + "****" + element.player_id.substring(element.player_id.length-3,element.player_id.length);
            }
            lab_userid.string = str;
            if (parseFloat(element.total_bet_amount) && parseFloat(element.total_bet_amount) > 0) {
                lab_betamout.string = utils.formatNumberWithCommas(element.total_bet_amount,2);
            } else {
                lab_betamout.string = "--";
            }
            lab_award.string = utils.formatNumberWithCommas(element.award,0);
            if (selfInfo && selfInfo.rank && selfInfo.rank == element.rank) {
                youself.active = true;
            }
            node.x = 0
            node.y = -70-142*index;
            node.parent = scrollView.content;
            scrollView.content.height = 70+142*(info.length+1);
            
        }
    }

    resize() {
        if (!this.node || !cc.isValid(this.node)) return
    }
    
    setLeftTime() {
        let now = Global.getInstance().now(); 
        let endOfDay = new Date();
        endOfDay.setHours(23, 59, 59, 999);
        let timeDiff = (endOfDay.getTime() - now)/1000;
        let hours = Math.floor(timeDiff / (60 * 60));
        let minutes = Math.floor((timeDiff % (60 * 60)) / (60));
        let seconds = Math.floor(timeDiff-hours*3600-minutes*60);
        let formattedTime = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        this.leftTime.string = formattedTime;
    }

    clickRule() {
        uiManager.instance.showDialog(UI_PATH_DIC.RankRule,null,null,DEEP_INDEXZ.ACTIVITY_CONTENT);
    }

    clickToday() {
        if (this.isSliding) {
            return;
        }
        this.isSliding = true;
        this.btnBg.active = true
        this.btnToday.target.active = false;
        this.btnYesterday.target.active = false;
        this.btnYesterday.node.getChildByName("btn_lab").active = false;
        this.todayNode.active = true;
        this.yestedayNode.active = true;
        cc.tween(this.btnBg).to(0.1,{x:-120}).call(()=>{
            this.btnBg.active = false;
            this.btnToday.target.active = true;
            this.lab_today.color = cc.color(234,137,1);
        }).start();
        cc.tween(this.todayNode).to(0.2,{x:0}).call(()=>{
            this.todayNode.active = true;
            this.isSliding = false;
        }).start();
        cc.tween(this.yestedayNode).to(0.2,{x:1080}).call(()=>{
            this.yestedayNode.active = false;
            this.isSliding = false;
        }).start();
    }

    clickYesterday() {
        if (this.isSliding) {
            return;
        }
        this.isSliding = true;
        this.btnBg.active = true
        this.btnToday.target.active = false;
        this.btnYesterday.target.active = false;
        this.btnYesterday.node.getChildByName("btn_lab").active = false;
        this.todayNode.active = true;
        this.yestedayNode.active = true;
        cc.tween(this.btnBg).to(0.1,{x:120}).call(()=>{
            this.btnBg.active = false;
            this.btnYesterday.target.active = true;
            this.btnYesterday.node.getChildByName("btn_lab").active = true;
            this.lab_today.color = cc.color(255,255,255);
        }).start();
        cc.tween(this.todayNode).to(0.2,{x:-1080}).call(()=>{
            this.todayNode.active = false;
            this.isSliding = false;
        }).start();
        cc.tween(this.yestedayNode).to(0.2,{x:0}).call(()=>{
            this.yestedayNode.active = true;
            this.isSliding = false;
        }).start();
    }

    clickClose() {
        if(this.type == "banner"){
            let gamescene =  cc.director.getScene().getComponentInChildren(GameControl);
            gamescene.mTabbar.rein_home();
            this.node.destroy();
        }else{
            this.node.destroy();
        }
    }
}

