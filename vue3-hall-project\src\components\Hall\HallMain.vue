<template>
  <div class="hall-main">
    <!-- 顶部余额区域 -->
    <div v-if="hallStore.isLoggedIn" class="top-balance">
      <div class="balance-container">
        <div class="balance-label">余额</div>
        <div class="balance-amount" :class="{ 'balance-flash': isBalanceFlashing }">
          ₱{{ hallStore.formattedBalance }}
        </div>
        <button class="refresh-btn" @click="refreshBalance">
          <i class="icon-refresh"></i>
        </button>
      </div>
    </div>

    <!-- 登录区域 -->
    <div v-else class="login-section">
      <button class="login-btn" @click="showLogin">登录</button>
      <button class="register-btn" @click="showRegister">注册</button>
    </div>

    <!-- Banner轮播 -->
    <BannerCarousel 
      :banners="hallStore.bannerData" 
      @banner-click="handleBannerClick"
    />

    <!-- 游戏分类导航 -->
    <GameTypeNav 
      :current-type="hallStore.currentGameType"
      @type-change="handleGameTypeChange"
    />

    <!-- 搜索栏 -->
    <div class="search-section">
      <div class="search-input-wrapper">
        <input
          v-model="searchText"
          type="text"
          placeholder="搜索游戏..."
          class="search-input"
          @input="handleSearch"
        />
        <i class="search-icon"></i>
      </div>
      <button class="filter-btn" @click="showFilters">
        <i class="icon-filter"></i>
      </button>
    </div>

    <!-- 游戏列表 -->
    <GameList 
      :games="hallStore.filteredGameList"
      :loading="hallStore.isLoading"
      @game-click="handleGameClick"
      @game-like="handleGameLike"
    />

    <!-- 功能按钮区域 -->
    <div class="function-buttons">
      <!-- 转盘按钮 -->
      <button 
        v-if="showSpinButton" 
        class="spin-btn"
        @click="openSpinWheel"
      >
        <i class="icon-spin"></i>
        <span v-if="spinLeftTimes > 0" class="spin-count">{{ spinLeftTimes }}</span>
      </button>

      <!-- 红包按钮 -->
      <button 
        v-if="showEnvelopeButton"
        class="envelope-btn"
        @click="openEnvelope"
      >
        <i class="icon-envelope"></i>
      </button>

      <!-- 活动按钮 -->
      <button 
        v-if="hallStore.validActivities.length > 0"
        class="activity-btn"
        @click="openActivities"
      >
        <i class="icon-activity"></i>
        <span class="activity-count">{{ hallStore.validActivities.length }}</span>
      </button>
    </div>

    <!-- 金币动画容器 -->
    <div ref="goldAnimationContainer" class="gold-animation-container"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useHallStore } from '@/stores/hallStore'
import { useRouter } from 'vue-router'
import BannerCarousel from './BannerCarousel.vue'
import GameTypeNav from './GameTypeNav.vue'
import GameList from './GameList.vue'
import type { HallGameIcon, BannerData, GameType } from '@/types/hall'

const hallStore = useHallStore()
const router = useRouter()

// 响应式数据
const searchText = ref('')
const isBalanceFlashing = ref(false)
const spinLeftTimes = ref(0)
const goldAnimationContainer = ref<HTMLElement>()

// 计算属性
const showSpinButton = computed(() => {
  // 根据转盘配置决定是否显示
  return hallStore.isLoggedIn && spinLeftTimes.value > 0
})

const showEnvelopeButton = computed(() => {
  // 根据红包配置决定是否显示
  return hallStore.isLoggedIn && checkEnvelopeCondition()
})

// 方法
function handleGameTypeChange(type: GameType) {
  hallStore.setGameType(type)
}

function handleSearch() {
  hallStore.setSearchText(searchText.value)
}

function handleGameClick(game: HallGameIcon) {
  if (!hallStore.isLoggedIn) {
    showLogin()
    return
  }
  
  if (game.status === 2) { // 维护状态
    showMaintenanceTip()
    return
  }
  
  // 添加到历史记录
  hallStore.addToHistory(game.id)
  
  // 跳转到游戏
  router.push({
    name: 'Game',
    params: { gameId: game.game_id }
  })
}

function handleGameLike(gameId: number) {
  hallStore.toggleLike(gameId)
}

function handleBannerClick(banner: BannerData) {
  if (banner.link_url) {
    if (banner.activity_type) {
      // 活动类型的banner
      router.push({
        name: 'Activity',
        params: { activityId: banner.id }
      })
    } else {
      // 外部链接
      window.open(banner.link_url, '_blank')
    }
  }
}

function refreshBalance() {
  if (isBalanceFlashing.value) return
  
  // 触发余额刷新动画
  isBalanceFlashing.value = true
  setTimeout(() => {
    isBalanceFlashing.value = false
  }, 200)
  
  // 调用API刷新余额
  // TODO: 实现余额刷新API调用
}

function showLogin() {
  router.push({ name: 'Login' })
}

function showRegister() {
  router.push({ name: 'Register' })
}

function showFilters() {
  // 显示筛选弹窗
  // TODO: 实现筛选功能
}

function openSpinWheel() {
  router.push({ name: 'SpinWheel' })
}

function openEnvelope() {
  router.push({ name: 'Envelope' })
}

function openActivities() {
  router.push({ name: 'Activities' })
}

function showMaintenanceTip() {
  // 显示维护提示
  // TODO: 实现提示功能
}

function checkEnvelopeCondition(): boolean {
  // 检查红包显示条件
  // TODO: 实现红包条件检查
  return false
}

function playGoldAnimation(data: any) {
  // 播放金币动画
  if (!goldAnimationContainer.value) return
  
  // TODO: 实现金币动画
  console.log('Playing gold animation:', data)
}

// 监听余额变化
watch(() => hallStore.userBalance, (newBalance, oldBalance) => {
  if (oldBalance && newBalance.total !== oldBalance.total) {
    isBalanceFlashing.value = true
    setTimeout(() => {
      isBalanceFlashing.value = false
    }, 200)
  }
}, { deep: true })

// 生命周期
onMounted(() => {
  // 初始化数据
  initializeHall()
})

async function initializeHall() {
  hallStore.setLoading(true)
  
  try {
    // 加载游戏列表
    await loadGameList()
    
    // 加载Banner数据
    await loadBannerData()
    
    // 如果已登录，加载用户相关数据
    if (hallStore.isLoggedIn) {
      await loadUserData()
    }
    
    // 加载活动数据
    await loadActivityData()
    
  } catch (error) {
    console.error('Failed to initialize hall:', error)
  } finally {
    hallStore.setLoading(false)
  }
}

async function loadGameList() {
  // TODO: 实现游戏列表加载
  const mockGames: HallGameIcon[] = []
  hallStore.setGameList(mockGames)
}

async function loadBannerData() {
  // TODO: 实现Banner数据加载
  const mockBanners: BannerData[] = []
  hallStore.setBannerData(mockBanners)
}

async function loadUserData() {
  // TODO: 实现用户数据加载
}

async function loadActivityData() {
  // TODO: 实现活动数据加载
}
</script>

<style scoped>
.hall-main {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.top-balance {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 20px;
  backdrop-filter: blur(10px);
}

.balance-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.balance-label {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
}

.balance-amount {
  color: #fff;
  font-size: 24px;
  font-weight: bold;
  transition: opacity 0.2s;
}

.balance-flash {
  opacity: 0.5;
}

.refresh-btn {
  background: none;
  border: none;
  color: #fff;
  font-size: 18px;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.refresh-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

.login-section {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
}

.login-btn, .register-btn {
  flex: 1;
  padding: 12px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: transform 0.2s;
}

.login-btn {
  background: #4CAF50;
  color: white;
}

.register-btn {
  background: #2196F3;
  color: white;
}

.login-btn:hover, .register-btn:hover {
  transform: translateY(-2px);
}

.search-section {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
}

.search-input-wrapper {
  flex: 1;
  position: relative;
}

.search-input {
  width: 100%;
  padding: 12px 40px 12px 16px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  background: rgba(255, 255, 255, 0.9);
}

.search-icon {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #666;
}

.filter-btn {
  padding: 12px;
  border: none;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
  cursor: pointer;
  backdrop-filter: blur(10px);
}

.function-buttons {
  position: fixed;
  right: 20px;
  bottom: 100px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.spin-btn, .envelope-btn, .activity-btn {
  width: 56px;
  height: 56px;
  border: none;
  border-radius: 50%;
  background: linear-gradient(135deg, #FF6B6B, #FF8E53);
  color: white;
  font-size: 24px;
  cursor: pointer;
  position: relative;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  transition: transform 0.2s;
}

.spin-btn:hover, .envelope-btn:hover, .activity-btn:hover {
  transform: scale(1.1);
}

.spin-count, .activity-count {
  position: absolute;
  top: -4px;
  right: -4px;
  background: #FF4444;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.gold-animation-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 9999;
}
</style>
