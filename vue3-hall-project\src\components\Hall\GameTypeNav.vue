<template>
  <div class="game-type-nav">
    <div class="nav-container">
      <div class="nav-scroll" ref="navScrollRef">
        <div class="nav-track" :style="{ transform: `translateX(${scrollOffset}px)` }">
          <button
            v-for="(type, index) in gameTypes"
            :key="type"
            class="nav-item"
            :class="{ 
              active: type === currentType,
              special: isSpecialType(type)
            }"
            @click="selectType(type)"
            :ref="el => setNavItemRef(el, index)"
          >
            <div class="nav-icon">
              <i :class="getTypeIcon(type)"></i>
            </div>
            <span class="nav-label">{{ getTypeLabel(type) }}</span>
            <div v-if="type === currentType" class="active-indicator"></div>
          </button>
        </div>
      </div>
      
      <!-- 左右滚动按钮 -->
      <button 
        v-if="canScrollLeft"
        class="scroll-btn scroll-left"
        @click="scrollLeft"
      >
        <i class="icon-chevron-left"></i>
      </button>
      <button 
        v-if="canScrollRight"
        class="scroll-btn scroll-right"
        @click="scrollRight"
      >
        <i class="icon-chevron-right"></i>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import type { GameType } from '@/types/hall'
import { GAME_TYPES, SPECIAL_TYPE } from '@/types/hall'

interface Props {
  currentType: GameType
}

interface Emits {
  (e: 'type-change', type: GameType): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const navScrollRef = ref<HTMLElement>()
const navItemRefs = ref<(HTMLElement | null)[]>([])
const scrollOffset = ref(0)
const maxScrollOffset = ref(0)

// 游戏类型配置
const gameTypes = GAME_TYPES

// 计算属性
const canScrollLeft = computed(() => scrollOffset.value < 0)
const canScrollRight = computed(() => scrollOffset.value > -maxScrollOffset.value)

// 方法
function setNavItemRef(el: HTMLElement | null, index: number) {
  navItemRefs.value[index] = el
}

function selectType(type: GameType) {
  if (type !== props.currentType) {
    emit('type-change', type)
    scrollToActiveItem()
  }
}

function isSpecialType(type: GameType): boolean {
  return Object.values(SPECIAL_TYPE).includes(type as any)
}

function getTypeIcon(type: GameType): string {
  const iconMap: Record<GameType, string> = {
    'Casino': 'icon-casino',
    'Slots': 'icon-slots',
    'Poker': 'icon-poker',
    'Bingo': 'icon-bingo',
    'Arcade': 'icon-arcade',
    'Sports': 'icon-sports',
    'Like': 'icon-heart',
    'History': 'icon-history',
    'Baccarat': 'icon-baccarat',
    'Roulette': 'icon-roulette',
    'Blackjack': 'icon-blackjack'
  }
  return iconMap[type] || 'icon-game'
}

function getTypeLabel(type: GameType): string {
  const labelMap: Record<GameType, string> = {
    'Casino': '娱乐场',
    'Slots': '老虎机',
    'Poker': '扑克',
    'Bingo': '宾果',
    'Arcade': '街机',
    'Sports': '体育',
    'Like': '收藏',
    'History': '历史',
    'Baccarat': '百家乐',
    'Roulette': '轮盘',
    'Blackjack': '21点'
  }
  return labelMap[type] || type
}

function scrollLeft() {
  const scrollAmount = 200
  scrollOffset.value = Math.min(scrollOffset.value + scrollAmount, 0)
}

function scrollRight() {
  const scrollAmount = 200
  scrollOffset.value = Math.max(scrollOffset.value - scrollAmount, -maxScrollOffset.value)
}

function scrollToActiveItem() {
  nextTick(() => {
    const activeIndex = gameTypes.findIndex(type => type === props.currentType)
    if (activeIndex === -1 || !navScrollRef.value) return
    
    const activeItem = navItemRefs.value[activeIndex]
    if (!activeItem) return
    
    const containerWidth = navScrollRef.value.clientWidth
    const itemLeft = activeItem.offsetLeft
    const itemWidth = activeItem.clientWidth
    const itemCenter = itemLeft + itemWidth / 2
    const containerCenter = containerWidth / 2
    
    // 计算需要滚动的距离，使活动项居中
    const targetOffset = containerCenter - itemCenter
    
    // 限制滚动范围
    scrollOffset.value = Math.max(
      Math.min(targetOffset, 0),
      -maxScrollOffset.value
    )
  })
}

function updateScrollLimits() {
  if (!navScrollRef.value) return
  
  const container = navScrollRef.value
  const track = container.querySelector('.nav-track') as HTMLElement
  
  if (track) {
    const containerWidth = container.clientWidth
    const trackWidth = track.scrollWidth
    maxScrollOffset.value = Math.max(0, trackWidth - containerWidth)
  }
}

function handleResize() {
  updateScrollLimits()
  scrollToActiveItem()
}

// 触摸事件处理
let touchStartX = 0
let touchStartOffset = 0

function handleTouchStart(event: TouchEvent) {
  touchStartX = event.touches[0].clientX
  touchStartOffset = scrollOffset.value
}

function handleTouchMove(event: TouchEvent) {
  event.preventDefault()
  const touchCurrentX = event.touches[0].clientX
  const diff = touchCurrentX - touchStartX
  const newOffset = touchStartOffset + diff
  
  // 限制滚动范围
  scrollOffset.value = Math.max(
    Math.min(newOffset, 0),
    -maxScrollOffset.value
  )
}

function handleTouchEnd() {
  // 可以在这里添加惯性滚动效果
}

// 监听当前类型变化
watch(() => props.currentType, () => {
  scrollToActiveItem()
})

// 生命周期
onMounted(() => {
  updateScrollLimits()
  scrollToActiveItem()
  
  window.addEventListener('resize', handleResize)
  
  // 添加触摸事件监听
  if (navScrollRef.value) {
    navScrollRef.value.addEventListener('touchstart', handleTouchStart, { passive: true })
    navScrollRef.value.addEventListener('touchmove', handleTouchMove, { passive: false })
    navScrollRef.value.addEventListener('touchend', handleTouchEnd, { passive: true })
  }
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  
  if (navScrollRef.value) {
    navScrollRef.value.removeEventListener('touchstart', handleTouchStart)
    navScrollRef.value.removeEventListener('touchmove', handleTouchMove)
    navScrollRef.value.removeEventListener('touchend', handleTouchEnd)
  }
})
</script>

<style scoped>
.game-type-nav {
  margin-bottom: 20px;
}

.nav-container {
  position: relative;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 8px;
  backdrop-filter: blur(10px);
}

.nav-scroll {
  overflow: hidden;
  position: relative;
}

.nav-track {
  display: flex;
  gap: 8px;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  padding: 4px;
}

.nav-item {
  flex: 0 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 12px 16px;
  border: none;
  border-radius: 8px;
  background: transparent;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  min-width: 80px;
}

.nav-item:hover {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
  transform: translateY(-2px);
}

.nav-item.active {
  background: rgba(255, 255, 255, 0.2);
  color: #fff;
  font-weight: bold;
}

.nav-item.special {
  background: linear-gradient(135deg, #FF6B6B, #FF8E53);
  color: white;
}

.nav-item.special:hover {
  background: linear-gradient(135deg, #FF5252, #FF7043);
}

.nav-icon {
  font-size: 20px;
  margin-bottom: 2px;
}

.nav-label {
  font-size: 12px;
  white-space: nowrap;
}

.active-indicator {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 3px;
  background: #4CAF50;
  border-radius: 2px;
}

.scroll-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.5);
  color: white;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
}

.scroll-btn:hover {
  background: rgba(0, 0, 0, 0.7);
  transform: translateY(-50%) scale(1.1);
}

.scroll-left {
  left: 8px;
}

.scroll-right {
  right: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .nav-item {
    min-width: 60px;
    padding: 8px 12px;
  }
  
  .nav-icon {
    font-size: 18px;
  }
  
  .nav-label {
    font-size: 11px;
  }
  
  .scroll-btn {
    width: 28px;
    height: 28px;
    font-size: 12px;
  }
}

/* 滚动条隐藏 */
.nav-scroll::-webkit-scrollbar {
  display: none;
}

.nav-scroll {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
</style>
