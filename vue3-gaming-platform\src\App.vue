<template>
  <div id="app" class="app-container" :class="appClasses">
    <!-- 全局加载遮罩 -->
    <GlobalLoading v-if="appStore.isLoading" />

    <!-- 网络状态提示 -->
    <NetworkStatus />

    <!-- 路由视图 -->
    <router-view v-slot="{ Component, route }">
      <transition
        :name="getTransitionName(route)"
        mode="out-in"
        appear
        @enter="onTransitionEnter"
        @leave="onTransitionLeave"
      >
        <keep-alive :include="keepAliveComponents">
          <component :is="Component" :key="route.path" />
        </keep-alive>
      </transition>
    </router-view>

    <!-- 全局提示组件 -->
    <GlobalToast />

    <!-- 全局弹窗组件 -->
    <GlobalModal />

    <!-- 底部导航栏 -->
    <TabBar v-if="showTabBar" />

    <!-- 浮动操作按钮 -->
    <FloatingActionButton v-if="showFAB" />

    <!-- 开发环境调试面板 -->
    <DebugPanel v-if="isDev && showDebugPanel" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAppStore, useUserStore } from '@/stores'
import GlobalLoading from '@/components/Common/GlobalLoading.vue'
import GlobalToast from '@/components/Common/GlobalToast.vue'
import GlobalModal from '@/components/Common/GlobalModal.vue'
import NetworkStatus from '@/components/Common/NetworkStatus.vue'
import TabBar from '@/components/Layout/TabBar.vue'
import FloatingActionButton from '@/components/Common/FloatingActionButton.vue'
import DebugPanel from '@/components/Debug/DebugPanel.vue'

const router = useRouter()
const route = useRoute()
const appStore = useAppStore()
const userStore = useUserStore()

// 响应式数据
const showDebugPanel = ref(false)
const isTransitioning = ref(false)

// 计算属性
const isDev = computed(() => import.meta.env.DEV)

const appClasses = computed(() => ({
  'app-mobile': appStore.isMobileDevice,
  'app-desktop': !appStore.isMobileDevice,
  'app-dark': appStore.currentTheme.name === 'dark',
  'app-transitioning': isTransitioning.value,
  'app-offline': !appStore.network.online
}))

const showTabBar = computed(() => {
  return route.meta?.showTabbar !== false && 
         !route.path.includes('/game/') &&
         !route.path.includes('/login') &&
         !route.path.includes('/register')
})

const showFAB = computed(() => {
  return userStore.isLoggedIn && 
         !route.path.includes('/game/') &&
         appStore.isMobileDevice
})

const keepAliveComponents = computed(() => {
  // 需要缓存的组件列表
  return ['Hall', 'Games', 'Profile', 'Transactions']
})

// 方法
function getTransitionName(route: any): string {
  // 根据路由层级和设备类型决定过渡动画
  if (route.meta?.transition) {
    return route.meta.transition
  }
  
  const depth = route.path.split('/').length
  
  if (appStore.isMobileDevice) {
    // 移动端使用滑动动画
    return depth > 2 ? 'slide-left' : 'fade'
  } else {
    // 桌面端使用淡入淡出
    return 'fade'
  }
}

function onTransitionEnter() {
  isTransitioning.value = true
}

function onTransitionLeave() {
  isTransitioning.value = false
}

function handleVisibilityChange() {
  if (document.hidden) {
    // 页面隐藏时的处理
    console.log('App hidden')
    // 可以暂停一些操作，如动画、定时器等
  } else {
    // 页面显示时的处理
    console.log('App visible')
    // 可以恢复操作，刷新数据等
    if (userStore.isLoggedIn) {
      // 刷新用户数据
      refreshUserData()
    }
  }
}

function handleOnline() {
  appStore.updateNetworkStatus(true)
  appStore.showToast('网络连接已恢复', 'success')
  
  // 网络恢复时重新加载数据
  if (userStore.isLoggedIn) {
    refreshUserData()
  }
}

function handleOffline() {
  appStore.updateNetworkStatus(false)
  appStore.showToast('网络连接已断开', 'warning', 5000)
}

function handleResize() {
  appStore.updateDeviceInfo()
}

function handleKeydown(event: KeyboardEvent) {
  // 全局快捷键处理
  if (isDev.value) {
    // 开发环境快捷键
    if (event.ctrlKey && event.shiftKey && event.key === 'D') {
      showDebugPanel.value = !showDebugPanel.value
      event.preventDefault()
    }
  }
  
  // ESC键关闭模态框
  if (event.key === 'Escape') {
    if (appStore.modals.length > 0) {
      appStore.closeModal()
      event.preventDefault()
    }
  }
}

async function refreshUserData() {
  try {
    // TODO: 刷新用户余额和其他数据
    // await userAPI.refreshBalance()
    // await userAPI.refreshProfile()
  } catch (error) {
    console.error('Failed to refresh user data:', error)
  }
}

async function initializeApp() {
  appStore.setGlobalLoading(true)
  
  try {
    // 初始化设备信息
    appStore.updateDeviceInfo()
    
    // 加载用户数据
    userStore.loadFromStorage()
    
    // 检查网络状态
    appStore.updateNetworkStatus(navigator.onLine)
    
    // 初始化主题
    const savedTheme = localStorage.getItem('app_theme')
    if (savedTheme) {
      try {
        const theme = JSON.parse(savedTheme)
        appStore.setTheme(theme)
      } catch (error) {
        console.error('Failed to load saved theme:', error)
      }
    }
    
    // 初始化语言
    const savedLanguage = localStorage.getItem('app_language')
    if (savedLanguage) {
      try {
        const language = JSON.parse(savedLanguage)
        appStore.setLanguage(language)
      } catch (error) {
        console.error('Failed to load saved language:', error)
      }
    }
    
    // 标记应用已初始化
    appStore.setInitialized(true)
    
  } catch (error) {
    console.error('Failed to initialize app:', error)
    appStore.showToast('应用初始化失败', 'error')
  } finally {
    appStore.setGlobalLoading(false)
  }
}

// 监听路由变化
watch(() => route.path, (newPath, oldPath) => {
  // 页面访问统计
  if (import.meta.env.PROD) {
    // TODO: 发送页面访问统计
    // analytics.trackPageView(newPath)
  }
  
  // 清除错误状态
  appStore.clearError?.()
})

// 监听用户登录状态变化
watch(() => userStore.isLoggedIn, (isLoggedIn) => {
  if (isLoggedIn) {
    // 用户登录后的处理
    refreshUserData()
  } else {
    // 用户登出后的处理
    // 清除敏感数据
    appStore.clearNotifications()
  }
})

// 生命周期
onMounted(() => {
  // 添加事件监听
  document.addEventListener('visibilitychange', handleVisibilityChange)
  window.addEventListener('online', handleOnline)
  window.addEventListener('offline', handleOffline)
  window.addEventListener('resize', handleResize)
  document.addEventListener('keydown', handleKeydown)
  
  // 初始化应用
  initializeApp()
  
  // 开发环境提示
  if (isDev.value) {
    console.log('🚀 Gaming Platform App Started')
    console.log('Press Ctrl+Shift+D to toggle debug panel')
  }
})

onUnmounted(() => {
  // 移除事件监听
  document.removeEventListener('visibilitychange', handleVisibilityChange)
  window.removeEventListener('online', handleOnline)
  window.removeEventListener('offline', handleOffline)
  window.removeEventListener('resize', handleResize)
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<style lang="scss">
.app-container {
  width: 100%;
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
  background: var(--color-background, #f5f5f5);
  color: var(--color-text, #333333);
  font-family: var(--font-primary, 'Inter, sans-serif');
  transition: all 0.3s ease;
}

.app-mobile {
  font-size: 14px;
  
  .app-container {
    padding-bottom: env(safe-area-inset-bottom);
  }
}

.app-desktop {
  font-size: 16px;
}

.app-dark {
  --color-background: #1a1a1a;
  --color-surface: #2d2d2d;
  --color-text: #ffffff;
}

.app-transitioning {
  pointer-events: none;
}

.app-offline {
  filter: grayscale(0.3);
}

// 过渡动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-left-enter-active,
.slide-left-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-left-enter-from {
  transform: translateX(100%);
  opacity: 0;
}

.slide-left-leave-to {
  transform: translateX(-100%);
  opacity: 0;
}

.slide-right-enter-active,
.slide-right-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-right-enter-from {
  transform: translateX(-100%);
  opacity: 0;
}

.slide-right-leave-to {
  transform: translateX(100%);
  opacity: 0;
}

.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-up-enter-from {
  transform: translateY(100%);
  opacity: 0;
}

.slide-up-leave-to {
  transform: translateY(-100%);
  opacity: 0;
}

.scale-enter-active,
.scale-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.scale-enter-from,
.scale-leave-to {
  transform: scale(0.8);
  opacity: 0;
}

// 响应式设计
@media (max-width: 768px) {
  .app-container {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .app-container {
    font-size: 13px;
  }
}

// 高对比度模式
@media (prefers-contrast: high) {
  .app-container {
    --color-primary: #0066cc;
    --color-text: #000000;
    border: 2px solid currentColor;
  }
}

// 减少动画模式
@media (prefers-reduced-motion: reduce) {
  .app-container *,
  .app-container *::before,
  .app-container *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

// 打印样式
@media print {
  .app-container {
    background: white !important;
    color: black !important;
  }
  
  .tab-bar,
  .floating-action-button,
  .debug-panel {
    display: none !important;
  }
}
</style>
