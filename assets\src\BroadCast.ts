import { ALL_APP_SOURCE_CONFIG } from "./Config";
import { DEEP_INDEXZ, E_CHANEL_TYPE, OPEN_BROADCAST_TYPE, UI_PATH_DIC } from "./GlobalConstant";
import Global from "./GlobalScript";
import RoundRectMask from "./RoundRectMask";
import UICommon from "./component/UICommon";
import { uiManager } from "./mgr/UIManager";
import HttpUtils from "./net/HttpUtils";
import utils from "./utils/utils";

const { ccclass, property } = cc._decorator;
@ccclass
export default class BroadCast extends UICommon {
    @property(cc.Node)
    uiBox:cc.Node = null;

    @property(cc.Node)
    broadcastNode:cc.Node = null;

    @property(cc.RichText)
    lab_broadcast:cc.RichText = null;

    private speed:number = 200;//每秒滚动的像素数
    private broadcastQueue = [];//消息队列
    private isPlaying:boolean = false;

    private nextBroadCast = null;//当前广播msg
    private fetchIntervalId = null;
    private readonly FETCH_INTERVAL:number = 5*60*1000;//5分钟(单位为毫秒)
    private execute_times:number = 1;
    private detailsMsgData:any[] = [];//Broadcast Message数组

    private itemMsgs:cc.Node[] = []; // 用于存储所有 itemMsg 的数组
    private tempNode:cc.Node = null;
    private broadcast_label:cc.Label = null;

    private openType = null;
    onLoad() {
        this.startFetching();
        Global.getInstance().getBroadCastsFromServer((data)=>{
            this.fetchBroadCasts();
        })
    }

    startFetching() {
        this.fetchIntervalId = setInterval(()=>{
            Global.getInstance().getBroadCastsFromServer((data)=>{
                this.fetchBroadCasts();
            })
        },this.FETCH_INTERVAL)
    }

    stopFetching() {
        if (this.fetchIntervalId) {
            clearInterval(this.fetchIntervalId);
            this.fetchIntervalId = null;
        }
    }

    start(): void {
    }

    fetchBroadCasts(){
        //假设从服务器获取数据并解析成广播消息数组
        const new_msgs = Global.instance.broadcastData;//this.getBroadCastsFromServer();
        if(new_msgs && new_msgs.length > 0){
            new_msgs.sort((a, b) => {
                if(a.list_order !== b.list_order) {
                    return b.list_order - a.list_order;
                }
                //return a.start_at - b.start_at;
            });
    
            //插入到队列尾部，但不打乱当前队列顺序
            this.broadcastQueue = this.broadcastQueue.concat(new_msgs);
    
            if (!this.isPlaying) {
                this.playNextBroadCast();
            }
        }
    }

    playNextBroadCast() {
        if (!this.broadcastQueue) {
            this.broadcastQueue = [];
        }
        if(this.broadcastQueue.length == 0) {
            this.isPlaying = false;
            return;
        }

        this.isPlaying = true;
        
        this.nextBroadCast = this.broadcastQueue.shift();// 取出队列中的第一条广播
        let richTextString = this.convertHtmlToRichText(this.nextBroadCast.content);
        this.lab_broadcast.string = richTextString;
        this.broadcastNode.x = this.broadcastNode.parent.width / 2;
        
        let textWidth = 0;
        //创建并重复使用临时的 Label 节点
        if (!this.tempNode) {
            this.tempNode = new cc.Node();
            this.broadcast_label = this.tempNode.addComponent(cc.Label);
            //设置Label的属性和RichText保持一致
            this.broadcast_label.font = this.lab_broadcast.font;
            this.broadcast_label.fontSize = this.lab_broadcast.fontSize;
            this.broadcast_label.lineHeight = this.lab_broadcast.lineHeight;
            this.broadcast_label.overflow = cc.Label.Overflow.NONE;//确保宽度是自动计算的
        }
        //获取去除标签后的纯文本内容
        let plainText = this.lab_broadcast.string.replace(/<[^>]+>/g, '');
    
        //将纯文本赋值给broadcast_label
        this.broadcast_label.string = plainText;
        //强制更新渲染数据以确保宽度正确
        this.broadcast_label._forceUpdateRenderData(true);
        //计算宽度
        textWidth = this.broadcast_label.node.width;

        const dur = (this.node.width + textWidth) / this.speed;
        this.broadcastNode.opacity = 255;
        const new_msgs = Global.instance.broadcastData;//this.getBroadCastsFromServer();
        //播放广播,如果有队列，就往左滚动完接新的一条
        if(this.execute_times < new_msgs.length){
            cc.tween(this.broadcastNode)
                .to(dur, { x: -(this.node.width + textWidth) })
                .call(() => {
                    this.execute_times += 1;
                    //检查interval字段来决定是否需要插入队列并等待
                    //if (this.nextBroadCast.interval && this.nextBroadCast.interval > 0) {
                        //设置定时器，在interval间隔时间后将该广播重新插入队列尾部

                        //---取消interval间隔 改为无限循环播放---
                        let temp = this.nextBroadCast;
                        setTimeout(() => {
                            if (this.broadcastQueue) {
                                this.broadcastQueue.push(temp);
                            }
                            if(!this.isPlaying){
                                this.playNextBroadCast(); // 在回调中启动下一个 tween
                            }
                        }, 0.1);
                    //}
                    if(!this.isPlaying){
                        this.playNextBroadCast(); // 在回调中启动下一个 tween
                    }

                    //继续播放下一条
                    this.isPlaying = false;
                    let canAddHistory = true;
                    for (let index = 0; index < 5; index++) {
                        let element = this.detailsMsgData[index];
                        if (element && element.id && this.nextBroadCast.id == element.id) {
                            canAddHistory = false;
                            break;
                        }
                    }
                    if (canAddHistory) {
                        this.detailsMsgData.reverse();//反转
                        this.detailsMsgData.push(this.nextBroadCast);
                        this.detailsMsgData.reverse();//反转
                        this.saveBroadCastData(this.nextBroadCast);
                    }
                    
                    this.playNextBroadCast();
                })
                .start();         
        } else {//如果没有下一条，做一个渐隐的动画，表明本条结束
            cc.tween(this.broadcastNode)
                .to(dur, { x: -(this.node.width / 2 + textWidth) })
                .to(1.5, {opacity:0})//scaleY: -1
                .call(()=>{
                    this.isPlaying = false;//重置状态
                    this.tempNode = null;
                    this.execute_times = 1;
                    //if (this.nextBroadCast.interval && this.nextBroadCast.interval > 0) {
                        //设置定时器，在interval间隔时间后将该广播重新插入队列尾部

                        //---取消interval间隔 改为无限循环播放---
                        let temp = this.nextBroadCast;
                        setTimeout(() => {
                            this.broadcastQueue.push(temp);
                            if(!this.isPlaying){
                                this.playNextBroadCast(); // 在回调中启动下一个 tween
                            }
                        }, 0.1);
                    //} 

                    let canAddHistory = true;
                    for (let index = 0; index < 5; index++) {
                        let element = this.detailsMsgData[index];
                        if (element && element.id && this.nextBroadCast.id == element.id) {
                            canAddHistory = false;
                            break;
                        }
                    }
                    if (canAddHistory) {
                        this.detailsMsgData.reverse();//反转
                        this.detailsMsgData.push(this.nextBroadCast);
                        this.detailsMsgData.reverse();//反转
                        this.saveBroadCastData(this.nextBroadCast);
                    }
                    this.playNextBroadCast();
                })
                .start();   
        }
    }

    saveBroadCastData(data){
        let tempList = Global.getInstance().getStoreageData("BroadCast_history_list","");
        if(tempList != ""){
            let broad_cast = JSON.parse(tempList);
            let canSave = true;
            for(let k = 0;k < broad_cast.length; k++){
                const temp = broad_cast[k];
                if(temp && temp.id && temp.id == data.id){
                    canSave = false;
                    break;
                }
            }
            
            if(canSave){
                broad_cast.reverse();//反转
                broad_cast.push(data);
                broad_cast.reverse();//反转
                // 保持列表最多只有5条数据
                if (broad_cast.length > 5) {
                    broad_cast = broad_cast.slice(0, 5);
                }
                Global.getInstance().setStoreageData("BroadCast_history_list",JSON.stringify(broad_cast));
            }
        }else{
            let broad_cast = [];
            broad_cast.push(data);
            Global.getInstance().setStoreageData("BroadCast_history_list",JSON.stringify(broad_cast));
        }
    }

    clickBroadCast() {
        this.showDetailsView();
    }

    showDetailsView() {
        let tempList = Global.getInstance().getStoreageData("BroadCast_history_list","");
        if(tempList != ""){
            let broad_cast = JSON.parse(tempList);
            //console.log("---broad_cast---",broad_cast);
            uiManager.instance.showDialog(UI_PATH_DIC.BroadCastDetail, [broad_cast],null,DEEP_INDEXZ.BroadCastDetail)
        }else{
            uiManager.instance.showDialog(UI_PATH_DIC.BroadCastDetail, [],null,DEEP_INDEXZ.BroadCastDetail);
        }
    }

    closeDetailsView() {
        this.itemMsgs.forEach(item => {
            item.destroy(); // 销毁每个 itemMsg
        });
        this.itemMsgs = []; // 清空数组
    }
    /**转换为 Cocos Creator 支持的 <color> 标签 */
    convertHtmlToRichText(htmlString: string): string {
        // 使用正则表达式匹配带有color样式的span标签
        const colorRegex = /<span style="color:\s*rgb\((\d+),\s*(\d+),\s*(\d+)\);?">/gi;
        
        // 将匹配到的rgb颜色值转换为16进制颜色值
        htmlString = htmlString.replace(colorRegex, (match, r, g, b) => {
            const hexColor = `#${parseInt(r).toString(16).padStart(2, '0')}${parseInt(g).toString(16).padStart(2, '0')}${parseInt(b).toString(16).padStart(2, '0')}`;
            return `<color=${hexColor}>`;
        });
    
        // 替换掉span结束标签为<color>结束标签
        htmlString = htmlString.replace(/<\/span>/gi, '</color>');
        
        return htmlString;
    }
    

    onDestroy(): void {
        this.stopFetching();//停止拉取数据
        if (this.tempNode) {//在类销毁时，清理临时节点
            this.tempNode.destroy();
            this.tempNode = null;
            this.broadcast_label = null;
        }
    }
}