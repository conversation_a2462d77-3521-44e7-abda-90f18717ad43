import { DEEP_INDEXZ, E_CHANEL_TYPE, MAINTENANC<PERSON>IPCODE, OPEN_BROADCAST_TYPE, UI_PATH_DIC } from "../GlobalConstant";
import Global from "../GlobalScript";
import { showMaintenancetip } from "../Maintenancetip";
import HttpUtils from "../net/HttpUtils";
import utils from "../utils/utils";
import { ALL_APP_SOURCE_CONFIG } from "../Config";
import { uiManager } from "../mgr/UIManager";
import MoreGameManager from "../MoreGameManager";
import RoundRectMask from "../RoundRectMask";
import Big from "../libs/big.js";
import { LOGIN_WAY } from "./Hall";
import GameControl from "../GameControl";

const {ccclass, property} = cc._decorator;

enum ACTIVITY_TYPE {
    SIGNBIND = 1,
    FIRSTDEPOSIT = 2,
    CASHBACK = 3,
    VIPCASHBACK = 4,
    CASHBACK_DETIALS = 100,
    VIP_CASHBACK_DETIALS = 101,
};

@ccclass
export default class Promo extends cc.Component {

    @property(cc.Node)
    signBindBonusView: cc.Node = null;

    @property(cc.Node)
    firstChargeBonusView: cc.Node = null;

    @property(cc.Node)
    cashBackBonusView: cc.Node = null;

    @property(cc.Node)
    cashBackDetialsView: cc.Node = null;

    @property(cc.Node)
    btnBack: cc.Node = null;

    @property(cc.Node)
    btnTransion: cc.Node = null;

    @property(cc.Node)
    btnDetials: cc.Node = null;

    @property(cc.Label)
    labTitle: cc.Label = null;

    @property(cc.PageView)
    cashbackPageView: cc.PageView = null;

    @property([cc.SpriteFrame])
    statusframes: cc.SpriteFrame[] = [];

    @property(cc.Node)
    todayCashbackList: cc.Node = null; 

    @property(cc.Node)
    yesterdayCashbackList: cc.Node = null; 

    @property(cc.Node)
    cashbackRules: cc.Node = null;

    @property(cc.Node)
    vipCashBackRules: cc.Node = null;

    @property(cc.Node)
    firstDepositRules: cc.Node = null;

    @property(cc.Node)
    redpoint1: cc.Node = null;

    @property(cc.PageView)
    pageView: cc.PageView = null;

    @property([cc.SpriteFrame])
    cashbackBG: cc.SpriteFrame[] = [];

    @property([cc.SpriteFrame])
    btnBG: cc.SpriteFrame[] = [];

    yesterdatTotalBonus = 0;
    cashBackRuleClose = false;//cashback 活动是否关闭
    vipCashBackRuleClose = false;//vip cashback 活动是否关闭

    openType = null;//打开页面类型

    start_period = null;//vip_cashback活动开始时间
    end_period = null;//vip_cashback活动结束时间
    normalStatusUpdatedTime = null;
    vipStatusUpdatedTime = null;

    cashback_status = null;//普通返水活动开关状态
    vipcashback_status = null;//vip返水活动开关状态

    activity_type = null;//活动类型
    config_cashback = null;
    onLoad () {
        this.signBindBonusView.getChildByName("needLoginBtn").active = !Global.getInstance().token;
        this.firstChargeBonusView.getChildByName("needLoginBtn").active = !Global.getInstance().token;
        this.cashBackBonusView.getChildByName("needLoginBtn").active = !Global.getInstance().token;
    }

    start () {
        if(this.signBindBonusView.active){
            let signup = utils.getChildByPath(this.signBindBonusView, "layout"); 
            if(ALL_APP_SOURCE_CONFIG.channel == E_CHANEL_TYPE.MAYA){
                if(signup) signup.active = false;
                this.signBindBonusView.getChildByName("scrollview").getComponent(cc.ScrollView).content.height = 2469;
            } else if(ALL_APP_SOURCE_CONFIG.channel == E_CHANEL_TYPE.WEB){
                if(signup) signup.active = true;
                this.signBindBonusView.getChildByName("scrollview").getComponent(cc.ScrollView).content.height = 2669;
            }
        }
        
    }


    protected onDestroy(): void {
    }

    initSignView() {
        //let rules = utils.getChildByPath(this.signBindBonusView,"rules").getComponent(RoundRectMask);
        let info = utils.getChildByPath(this.signBindBonusView,"scrollview.view.content.signbind_bonus.info").getComponent(RoundRectMask);
        let scrollview = this.signBindBonusView.getChildByName("scrollview");
        scrollview.on("scrolling", ()=>{this.title_layout_opacity(scrollview.getComponent(cc.ScrollView))}, this);
        //rules.radius = 0.1;
        //info.radius = 0.1;
        // if (!!Global.getInstance().token) {
        HttpUtils.getInstance().post(1, 3, this, "/avt/api/registration/bonus", {
            token: Global.getInstance().token,
        }, (response) => {
            if (!this.node || !this.node.isValid) {
                return;
            }
            if (response.data && response.data.list) {
                let data = response.data;
                //let layout = utils.getChildByPath(this.signBindBonusView,"info.Layout");
                //let labrules = utils.getChildByPath(this.signBindBonusView,"rules.labrules").getComponent(cc.Label);
                let labtotal = utils.getChildByPath(this.signBindBonusView,"scrollview.view.content.signbind_bonus.info.labtotal").getComponent(cc.Label);
                let spsign = utils.getChildByPath(this.signBindBonusView,"scrollview.view.content.signbind_bonus.info.spSign").getComponent(cc.Sprite);
                let spbind = utils.getChildByPath(this.signBindBonusView,"scrollview.view.content.signbind_bonus.info.spbind").getComponent(cc.Sprite);
                let labsign = utils.getChildByPath(this.signBindBonusView,"scrollview.view.content.signbind_bonus.info.labSign").getComponent(cc.Label);
                let labsignreward = utils.getChildByPath(this.signBindBonusView,"scrollview.view.content.signbind_bonus.info.labsignreward").getComponent(cc.Label);
                let labbind = utils.getChildByPath(this.signBindBonusView,"scrollview.view.content.signbind_bonus.info.labbind").getComponent(cc.Label);
                let labbindreward = utils.getChildByPath(this.signBindBonusView,"scrollview.view.content.signbind_bonus.info.labbindreward").getComponent(cc.Label);
                let ignbindlayout = utils.getChildByPath(this.signBindBonusView,"layout")
                let btnsignbind = utils.getChildByPath(this.signBindBonusView,"layout.btnsignbind")
                let labbtnsignbind = utils.getChildByPath(this.signBindBonusView,"layout.btnsignbind.Background.Label").getComponent(cc.Label);
                let spnodes = [spsign,spbind];
                let titles = [labsign,labbind];
                let rewards = [labsignreward,labbindreward];
                let signs = [labsign, labbind];
                if (data.type == 3) {
                    ignbindlayout.active = false;
                    this.signBindBonusView.getChildByName("scrollview").getComponent(cc.ScrollView).content.height = 2469;
                } else {
                    ignbindlayout.active = true;
                    this.signBindBonusView.getChildByName("scrollview").getComponent(cc.ScrollView).content.height = 2669;
                }
                if (data.list.length == 1) {
                    //layout.height = 300;
                    info.node.height = 300;
                    
                    spnodes[1].node.active = false;
                    titles[1].node.active = false;
                    rewards[1].node.active = false;
                }
                if (data.maxBonus) {
                    labtotal.string = "Up to ₱"+data.maxBonus;
                }

                for (let index = 0; index < data.list.length; index++) {
                    let element = data.list[index];
                    titles[index].string = element.title;
                    if (element.status == 1) {
                        spnodes[index].spriteFrame = this.statusframes[1];
                        rewards[index].string = "Received ₱"+element.bonus;
                        rewards[index].node.color = cc.color(255,245,0);

                        signs[index].node.color = cc.color(255,255,255);
                    } else {
                        spnodes[index].spriteFrame = this.statusframes[0];
                        rewards[index].string = "₱"+element.bonus;
                        rewards[index].node.color = cc.color(255,255,255,204);

                        signs[index].node.color = cc.color(255,255,255,204);
                    }
                }
                if (data.type != 3) {
                    if (data.list[0].status == 1) {
                        labbtnsignbind.string = "Bind mobile phone";
                    } else {
                        labbtnsignbind.string = "Sign up";
                    }
                }
                
                //labrules.string = labrules.string.replace("xxx", data.minBonus);
                //labrules.string = labrules.string.replace("yyy", data.maxBonus);
                //labrules.node.active = true;
            }
        });
        // }
    }

    initFistDeposit() {
        if (!!Global.getInstance().token) {
            //PERIOD COVERED
            // let lab_period = this.firstDepositRules.getChildByName("lab_period").getComponent(cc.Label);
            // lab_period.string = `${this.start_period} - ${this.end_period}`;
            //PRIZES
            let scrollview = this.firstChargeBonusView.getChildByName("scrollview");
            scrollview.on("scrolling", ()=>{this.title_layout_opacity(scrollview.getComponent(cc.ScrollView))}, this);
            HttpUtils.getInstance().post(1, 3, this, "/common/api/global-config/first/recharge/rule", {
                token: Global.getInstance().token,
            }, (response) => {
                if (!this.node || !this.node.isValid) {
                    return;
                }
                if (response.data && response.data.recharge && response.data.recharge.length > 0) {
                    let rechargeList = response.data.recharge;
                    rechargeList.sort((a,b)=>{return a.id - b.id});
                    for (let index = 0; index < rechargeList.length; index++) {
                        let element = rechargeList[index];
                        let labbuy = this.firstDepositRules.getChildByName("lab_buy_"+(index+1)).getComponent(cc.Label);
                        let labbonus = this.firstDepositRules.getChildByName("lab_bonus_"+(index+1)).getComponent(cc.Label);
                        if (labbuy) {
                            if (element && element.amount) {
                                labbuy.string = "₱"+utils.formatNumberWithCommas(element.amount,0);
                            } else {
                                labbuy.string = "";
                            }
                        }
                        if (labbonus) {
                            if (element && element.award) {
                                labbonus.string = "₱"+utils.formatNumberWithCommas(element.award,0);
                            } else {
                                labbonus.string = "";
                            }
                        }
                    }
                }
            });
        }
    }

    /**普通返水活动 */
    initCashback() {
        let content = this.cashBackBonusView.getComponent(cc.ScrollView).content;
        utils.getChildByPath(this.cashBackBonusView,"todayLayout.btn_bet.Background").getComponent(cc.Sprite).spriteFrame = this.cashbackBG[2];
        utils.getChildByPath(this.cashBackBonusView,"yesterdayLayout.btn_cliam.Background").getComponent(cc.Sprite).spriteFrame = this.cashbackBG[2];
        utils.getChildByPath(content,"layout.bg1").getComponent(cc.Sprite).spriteFrame = this.cashbackBG[0];
        utils.getChildByPath(content,"layout.bg2").getComponent(cc.Sprite).spriteFrame = this.cashbackBG[4];
        this.pageView.node.on('page-turning', this.turingPageNormalListener, this);
        utils.getChildByPath(content,"top.btn_today").getComponent(cc.Sprite).spriteFrame = this.btnBG[1];
        utils.getChildByPath(content,"top.btn_yesterday").getComponent(cc.Sprite).spriteFrame = this.btnBG[0];
        utils.getChildByPath(content,"top.btn_today.labtoday").color = cc.color(255,255,255);
        utils.getChildByPath(content,"top.btn_yesterday.labyestaday").color = cc.color(128,147,203);
        let todayLayout = this.cashBackBonusView.getChildByName("todayLayout");
        let yesterdayLayout = this.cashBackBonusView.getChildByName("yesterdayLayout");
        if (todayLayout.active) {
            this.cashBackBonusView.getComponent(cc.ScrollView).content.height = 2783;
        } else {
            this.cashBackBonusView.getComponent(cc.ScrollView).content.height = 2583;
        }
        todayLayout.x = 0;
        yesterdayLayout.x = 6000;
        this.cashBackBonusView.on("scrolling", ()=>{this.title_layout_opacity(this.cashBackBonusView.getComponent(cc.ScrollView))}, this);

        this.initCashbackToday();
        this.initCashbackYesterday();
    }

    /**VIP专属返水活动 */
    initVipCashback() {
        let content = this.cashBackBonusView.getComponent(cc.ScrollView).content;
        utils.getChildByPath(this.cashBackBonusView,"todayLayout.btn_bet.Background").getComponent(cc.Sprite).spriteFrame = this.cashbackBG[3];
        utils.getChildByPath(this.cashBackBonusView,"yesterdayLayout.btn_cliam.Background").getComponent(cc.Sprite).spriteFrame = this.cashbackBG[3];
        utils.getChildByPath(content,"layout.bg1").getComponent(cc.Sprite).spriteFrame = this.cashbackBG[1];
        utils.getChildByPath(content,"layout.bg2").getComponent(cc.Sprite).spriteFrame = this.cashbackBG[5];
        this.pageView.node.on('page-turning', this.turingPageVipListener, this);
        utils.getChildByPath(content,"top.btn_today").getComponent(cc.Sprite).spriteFrame = this.btnBG[2];
        utils.getChildByPath(content,"top.btn_yesterday").getComponent(cc.Sprite).spriteFrame = this.btnBG[3];
        utils.getChildByPath(content,"top.btn_today.labtoday").color = cc.color(255,255,255);
        utils.getChildByPath(content,"top.btn_yesterday.labyestaday").color = cc.color(193,146,106);
        utils.getChildByPath(this.todayCashbackList,"list.title1").color = cc.color(176,133,98);
        utils.getChildByPath(this.todayCashbackList,"list.title2").color = cc.color(176,133,98);
        utils.getChildByPath(this.todayCashbackList,"list.cashback_percent").color = cc.color(176,133,98);
        utils.getChildByPath(this.todayCashbackList,"list.vip_cashback").color = cc.color(176,133,98);
        utils.getChildByPath(this.yesterdayCashbackList,"list.title1").color = cc.color(176,133,98);
        utils.getChildByPath(this.yesterdayCashbackList,"list.title2").color = cc.color(176,133,98);
        utils.getChildByPath(this.yesterdayCashbackList,"list.title3").color = cc.color(176,133,98);

        let startTouchPos
        this.pageView.node.on(cc.Node.EventType.TOUCH_START, (event: cc.Event.EventTouch) => {
            startTouchPos = event.getLocation();
            this.cashBackBonusView.getComponent(cc.ScrollView).enabled = false;
            this.pageView.enabled = false;
        });
        
        this.pageView.node.on(cc.Node.EventType.TOUCH_MOVE, (event: cc.Event.EventTouch) => {
            const currentTouchPos = event.getLocation();
            const deltaX = currentTouchPos.x - startTouchPos.x;
            const deltaY = currentTouchPos.y - startTouchPos.y;
            if (Math.abs(deltaX) > Math.abs(deltaY)) {
                this.cashBackBonusView.getComponent(cc.ScrollView).enabled = false;
                this.pageView.enabled = true;
            } else {
                this.cashBackBonusView.getComponent(cc.ScrollView).enabled = true;
                this.pageView.enabled = false;
            }
        });

        let todayLayout = this.cashBackBonusView.getChildByName("todayLayout");
        let yesterdayLayout = this.cashBackBonusView.getChildByName("yesterdayLayout");
        if (todayLayout.active) {
            this.cashBackBonusView.getComponent(cc.ScrollView).content.height = 2783;
        } else {
            this.cashBackBonusView.getComponent(cc.ScrollView).content.height = 2583;
        }
        todayLayout.x = 0;
        yesterdayLayout.x = 6000;
        this.cashBackBonusView.on("scrolling", ()=>{this.title_layout_opacity(this.cashBackBonusView.getComponent(cc.ScrollView))}, this);
        
        this.initVipCashbackToday();
        this.initVipCashbackYesterday();
    }

    initCashbackToday() {
        if (!!Global.getInstance().token) {
            HttpUtils.getInstance().post(1, 3, this, "/avt/api/activity/daily-rebate", {
                token: Global.getInstance().token,
            }, (response) => {
                if (!this.node || !this.node.isValid) {
                    return;
                }
                if (response.data && response.data.length > 0) {
                    let totalAwardNum = 0;
                    let totalTotal = utils.getChildByPath(this.todayCashbackList,"list.total_awrad").getComponent(cc.Label);
                    let isVip = Number(Global.getInstance().userdata?.is_vip) || 0;
                    if (isVip) {
                        for (let index = 0; index < response.data.length; index++) {
                            let element = response.data[index];
                            //let betco = utils.getChildByPath(this.todayCashbackList,"list.cashback_percent."+element.category.toLowerCase()+"_cashback").getComponent(cc.Label);
                            let betamount = utils.getChildByPath(this.todayCashbackList,"list."+element.category.toLowerCase()+"_bet_amount").getComponent(cc.Label);
                            //let nextlv = utils.getChildByPath(this.todayCashbackList,"list.next_level_bet."+element.category.toLowerCase()+"_next_level_bet").getComponent(cc.Label);
                            let cashback = utils.getChildByPath(this.todayCashbackList,"list.cashback."+element.category.toLowerCase()+"_cashback").getComponent(cc.Label);
                            if(cashback) cashback.string = "₱0";
                            //if (betco)  betco.string = "0%";
                            if (betamount)  betamount.string = "₱0";
                            //if (nextlv)  nextlv.string = "₱0";
                            totalAwardNum = 0;
                        }
                        totalTotal.string = "₱"+utils.formatNumberWithCommas(totalAwardNum);
                    } else {
                        for (let index = 0; index < response.data.length; index++) {
                            let element = response.data[index];
                            //let betco = utils.getChildByPath(this.todayCashbackList,"list.cashback_percent."+element.category.toLowerCase()+"_cashback").getComponent(cc.Label);
                            let betamount = utils.getChildByPath(this.todayCashbackList,"list."+element.category.toLowerCase()+"_bet_amount").getComponent(cc.Label);
                            //let nextlv = utils.getChildByPath(this.todayCashbackList,"list.next_level_bet."+element.category.toLowerCase()+"_next_level_bet").getComponent(cc.Label);
                            //if (betco)  betco.string = element.cashback;
                            if (betamount)  betamount.string = "₱"+element.bet_amount;
                            //if (nextlv)  nextlv.string = "₱"+element.next_level_bet;
                            let bett = new Big(parseFloat(element.cashback)).div(100).times(parseFloat(element.bet_amount)).toString();
                            totalAwardNum = totalAwardNum +  parseFloat(utils.formatNumberDigits(parseFloat(bett)));
                            let cashback = utils.getChildByPath(this.todayCashbackList,"list.cashback."+element.category.toLowerCase()+"_cashback").getComponent(cc.Label);
                            if(cashback) cashback.string = "₱"+parseFloat(utils.formatNumberDigits(parseFloat(bett)));
                        }
                        totalTotal.string = "₱"+utils.formatNumberWithCommas(totalAwardNum);
                    }
                }
            });
        }
    }

    /**vip cashback Betted Today*/
    initVipCashbackToday() {
        //cashback
        let cashback = utils.getChildByPath(this.todayCashbackList,"list.cashback");
        if (cashback) cashback.active = false;
        //cashback_percent
        let cashback_percent = utils.getChildByPath(this.todayCashbackList, "list.cashback_percent");
        if (cashback_percent) cashback_percent.active = false;
        //next_level_bet
        let next_levelbet = utils.getChildByPath(this.todayCashbackList, "list.next_level_bet");
        if (next_levelbet) next_levelbet.active = false;
        //vip_cashback
        let vip_cashback = utils.getChildByPath(this.todayCashbackList, "list.vip_cashback");
        if (vip_cashback) vip_cashback.active = true;

        if (!!Global.getInstance().token) {
            HttpUtils.getInstance().post(1, 3, this, "/avt/api/activity/daily-rebate", {
                token: Global.getInstance().token,
            }, (response) => {
                if (!this.node || !this.node.isValid) {
                    return;
                }
                if (response.data && response.data.length > 0) {
                    let totalAwardNum = 0;
                    let totalTotal = utils.getChildByPath(this.todayCashbackList,"list.total_awrad").getComponent(cc.Label);
                    let isVip = Number(Global.getInstance().userdata?.is_vip) || 0;
                    if (isVip) {
                        for (let index = 0; index < response.data.length; index++) {
                            let element = response.data[index];
                            //Betted Today(Valid Bets)
                            let betamount = utils.getChildByPath(this.todayCashbackList,"list."+element.category.toLowerCase()+"_bet_amount").getComponent(cc.Label);
                            if (betamount)  betamount.string = "₱"+element.bet_amount;
    
                            let cashback_v = utils.getChildByPath(vip_cashback, element.category.toLowerCase()+"_vip_cashback").getComponent(cc.Label);
                            let vipcashback_num = new Big(parseFloat(element.cashback)).div(100).times(parseFloat(element.bet_amount)).toString(); 
                            if(cashback_v) cashback_v.string = "₱"+parseFloat(utils.formatNumberDigits(parseFloat(vipcashback_num)));
    
                            //Total Cashback
                            totalAwardNum = totalAwardNum +  parseFloat(utils.formatNumberDigits(parseFloat(vipcashback_num)));
                        }
                        totalTotal.string = "₱"+utils.formatNumberWithCommas(totalAwardNum);
                    } else {
                        for (let index = 0; index < response.data.length; index++) {
                            let element = response.data[index];
                            //Betted Today(Valid Bets)
                            let betamount = utils.getChildByPath(this.todayCashbackList,"list."+element.category.toLowerCase()+"_bet_amount").getComponent(cc.Label);
                            if (betamount)  betamount.string = "₱0";
                            let cashback_v = utils.getChildByPath(vip_cashback, element.category.toLowerCase()+"_vip_cashback").getComponent(cc.Label);
                            if(cashback_v) cashback_v.string = "₱0";
    
                            //Total Cashback
                            totalAwardNum = 0;
                        }
                        totalTotal.string = "₱"+utils.formatNumberWithCommas(totalAwardNum);
                    }
                }
            });
        }
    }

    /**vip cashback Yesterday */
    initVipCashbackYesterday() {
        if (!!Global.getInstance().token) {
            HttpUtils.getInstance().post(1, 3, this, "/avt/api/activity/daily-rebate-yesterday", {
                token: Global.getInstance().token,
            }, (response) => {
                if (!this.node || !this.node.isValid) {
                    return;
                }
                if (response.data) {
                    let gameData = response.data.data;
                    if (gameData && gameData.length > 0) {
                        let totalAwardNum = 0;
                        let yesterdayTotal = utils.getChildByPath(this.yesterdayCashbackList,"list.yesterday_total").getComponent(cc.Label);
                        if(Number(response.data.yesterday_user_status)) {//0:普通用户 1:VIP用户(昨日身份)
                            for (let index = 0; index < gameData.length; index++) {
                                let element = gameData[index];
                                let betco = utils.getChildByPath(this.yesterdayCashbackList,"list."+element.category.toLowerCase()+"_cashback").getComponent(cc.Label);
                                let betamount = utils.getChildByPath(this.yesterdayCashbackList,"list."+element.category.toLowerCase()+"_bet_amount").getComponent(cc.Label);
                                let bett = new Big(parseFloat(element.cashback)).div(100).times(parseFloat(element.bet_amount)).toString();
                                if (betco)  betco.string = "₱"+utils.formatNumberWithCommas(parseFloat(bett));
                                if (betamount)  betamount.string = "₱"+element.bet_amount+"("+element.cashback+")";
                                totalAwardNum = totalAwardNum + parseFloat(utils.formatNumberDigits(parseFloat(bett)));
                            }
                            this.yesterdatTotalBonus = totalAwardNum;
                            yesterdayTotal.string = "₱"+utils.formatNumberWithCommas(totalAwardNum);

                            let status = response.data.status;
                            let btn_cliam_ly = utils.getChildByPath(this.cashBackBonusView,"yesterdayLayout");
                            let btn_cliam = utils.getChildByPath(this.cashBackBonusView,"yesterdayLayout.btn_cliam");
                            let lab_notice = this.yesterdayCashbackList.getChildByName("lan_notice").getComponent(cc.Label);
                            let btn_cliam_lab = utils.getChildByPath(btn_cliam,"Background.Label").getComponent(cc.Label);
                            btn_cliam_ly.active = true;
                            this.redpoint1.active = false;
                            btn_cliam.getComponent(cc.Button).enabled = true;
                            btn_cliam.opacity = 255;
                            if (status === "") {
                                btn_cliam_ly.active = false;
                                lab_notice.string = "Sorry, there are currently no rewards available to claim";
                                Global.getInstance().setStoreageData("PROMO_VIPBONUS_CAN_GET", 0);
                            } else if (parseInt(status) == 0) {
                                btn_cliam.getComponent(cc.Button).enabled = false;
                                btn_cliam.opacity = 180;
                                btn_cliam_lab.string = "Pending......"
                                Global.getInstance().setStoreageData("PROMO_VIPBONUS_CAN_GET", 0);
                            } else if (parseInt(status) == 1) {
                                btn_cliam.getComponent(cc.Button).enabled = true;
                                btn_cliam.opacity = 255;
                                btn_cliam_lab.string = "Claim"
                                this.redpoint1.active = true;
                                Global.getInstance().setStoreageData("PROMO_VIPBONUS_CAN_GET", 1);
                            } else if (parseInt(status) == 2) {
                                btn_cliam.getComponent(cc.Button).enabled = false;
                                btn_cliam.opacity = 180;
                                btn_cliam_lab.string = "Received"
                                Global.getInstance().setStoreageData("PROMO_VIPBONUS_CAN_GET", 0);
                            } else if (parseInt(status) == 3) {
                                btn_cliam_ly.active = false;
                                Global.getInstance().setStoreageData("PROMO_VIPBONUS_CAN_GET", 0);
                            }
                        } else {
                            for (let index = 0; index < gameData.length; index++) {
                                let element = gameData[index];
                                let betco = utils.getChildByPath(this.yesterdayCashbackList,"list."+element.category.toLowerCase()+"_cashback").getComponent(cc.Label);
                                let betamount = utils.getChildByPath(this.yesterdayCashbackList,"list."+element.category.toLowerCase()+"_bet_amount").getComponent(cc.Label);
                                if (betco)  betco.string = "₱0";
                                if (betamount)  betamount.string = "₱0"+"(0%)";
                                totalAwardNum = 0;
                            }
                            this.yesterdatTotalBonus = totalAwardNum;
                            yesterdayTotal.string = "₱"+utils.formatNumberWithCommas(totalAwardNum);

                            //红点和领取按钮隐藏
                            let btn_cliam = utils.getChildByPath(this.cashBackBonusView,"yesterdayLayout");
                            btn_cliam.active = false;
                            this.redpoint1.active = false;
                        }

                        //根据vipcashback_status&&status_time 判定Toady/Yesterday活动是否开启
                        //let activityStatus = response.data.activity_status;
                        let statusTime = parseInt(this.vipStatusUpdatedTime);
                        let tdTimes = new Date(); // 获取当前时间
                        tdTimes.setHours(0, 0, 0, 0)
                        let list = this.todayCashbackList.getChildByName("list");
                        let td_empty = this.todayCashbackList.getChildByName("td_empty");
                        let la_notice = this.todayCashbackList.getChildByName("lab_notice");
                        let btn_bet = utils.getChildByPath(this.cashBackBonusView, "yesterdayLayout");
                        let list2 = this.yesterdayCashbackList.getChildByName("list");
                        let yd_empty = this.yesterdayCashbackList.getChildByName("yd_empty");
                        let lan_notice = this.yesterdayCashbackList.getChildByName("lan_notice");
                        if(this.vipcashback_status != 1 && statusTime) {
                            list.active = false;
                            td_empty.active = true;
                            this.vipCashBackRuleClose = true;
                            la_notice.active = false;
                            btn_bet.active = false;
                            if (statusTime < tdTimes.getTime()/1000) {
                                list2.active = false;
                                yd_empty.active = true;
                                lan_notice.active = false;
                            }
                        } else {
                            list.active = true;
                            list2.active = true;
                            td_empty.active = false;
                            yd_empty.active = false;
                            this.vipCashBackRuleClose = false;
                            la_notice.active = true;
                            lan_notice.active = true;
                            //btn_bet.active = true;
                        }
                    }
                }
            });
        }
    }

    initCashbackYesterday() {
        if (!!Global.getInstance().token) {
            HttpUtils.getInstance().post(1, 3, this, "/avt/api/activity/daily-rebate-yesterday", {
                token: Global.getInstance().token,
            }, (response) => {
                if (!this.node || !this.node.isValid) {
                    return;
                }
                if (response.data) {
                    let gameData = response.data.data;
                    if (gameData && gameData.length > 0) {
                        let totalAwardNum = 0;
                        let yesterdayTotal = utils.getChildByPath(this.yesterdayCashbackList,"list.yesterday_total").getComponent(cc.Label);
                        if(Number(response.data.yesterday_user_status)) {//0:普通用户 1:VIP用户(昨日身份)
                            for (let index = 0; index < gameData.length; index++) {
                                let element = gameData[index];
                                let betco = utils.getChildByPath(this.yesterdayCashbackList,"list."+element.category.toLowerCase()+"_cashback").getComponent(cc.Label);
                                let betamount = utils.getChildByPath(this.yesterdayCashbackList,"list."+element.category.toLowerCase()+"_bet_amount").getComponent(cc.Label);
                                if (betco)  betco.string = "₱0";
                                if (betamount)  betamount.string = "₱0"+"(0%)";
                                totalAwardNum = 0;
                            }
                            this.yesterdatTotalBonus = totalAwardNum;
                            yesterdayTotal.string = "₱"+utils.formatNumberWithCommas(totalAwardNum);

                            //红点和领取按钮隐藏
                            let btn_cliam = utils.getChildByPath(this.cashBackBonusView,"yesterdayLayout");
                            btn_cliam.active = false;
                            this.redpoint1.active = false;
                        } else {
                            for (let index = 0; index < gameData.length; index++) {
                                let element = gameData[index];
                                let betco = utils.getChildByPath(this.yesterdayCashbackList,"list."+element.category.toLowerCase()+"_cashback").getComponent(cc.Label);
                                let betamount = utils.getChildByPath(this.yesterdayCashbackList,"list."+element.category.toLowerCase()+"_bet_amount").getComponent(cc.Label);
                                let bett = new Big(parseFloat(element.cashback)).div(100).times(parseFloat(element.bet_amount)).toString();
                                if (betco)  betco.string = "₱"+utils.formatNumberWithCommas(parseFloat(bett));
                                if (betamount)  betamount.string = "₱"+element.bet_amount+"("+element.cashback+")";
                                totalAwardNum = totalAwardNum + parseFloat(utils.formatNumberDigits(parseFloat(bett)));
                            }
                            this.yesterdatTotalBonus = totalAwardNum;
                            yesterdayTotal.string = "₱"+utils.formatNumberWithCommas(totalAwardNum);

                            let status = response.data.status;
                            let btn_cliam_ly = utils.getChildByPath(this.cashBackBonusView,"yesterdayLayout");
                            let btn_cliam = utils.getChildByPath(btn_cliam_ly,"btn_cliam");
                            let lab_notice = this.yesterdayCashbackList.getChildByName("lan_notice").getComponent(cc.Label);
                            let btn_cliam_lab = utils.getChildByPath(btn_cliam,"Background.Label").getComponent(cc.Label);
                            btn_cliam_ly.active = true;
                            this.redpoint1.active = false;
                            btn_cliam.getComponent(cc.Button).enabled = true;
                            btn_cliam.opacity = 255;
                            if (status === "") {
                                btn_cliam_ly.active = false;
                                lab_notice.string = "Sorry, there are currently no rewards available to claim";
                                Global.getInstance().setStoreageData("PROMO_BONUS_CAN_GET", 0);
                            } else if (parseInt(status) == 0) {
                                btn_cliam.getComponent(cc.Button).enabled = false;
                                btn_cliam.opacity = 180;
                                btn_cliam_lab.string = "Pending......"
                                Global.getInstance().setStoreageData("PROMO_BONUS_CAN_GET", 0);
                            } else if (parseInt(status) == 1) {
                                btn_cliam.getComponent(cc.Button).enabled = true;
                                btn_cliam.opacity = 255;
                                btn_cliam_lab.string = "Claim"
                                this.redpoint1.active = true;
                                Global.getInstance().setStoreageData("PROMO_BONUS_CAN_GET", 1);
                            } else if (parseInt(status) == 2) {
                                btn_cliam.getComponent(cc.Button).enabled = false;
                                btn_cliam.opacity = 180;
                                btn_cliam_lab.string = "Received"
                                Global.getInstance().setStoreageData("PROMO_BONUS_CAN_GET", 0);
                            } else if (parseInt(status) == 3) {
                                btn_cliam_ly.active = false;
                                Global.getInstance().setStoreageData("PROMO_BONUS_CAN_GET", 0);
                            }
                        }

                        //根据thid.cashback_status&&status_time 判定Toady/Yesterday活动是否开启
                        //let activityStatus = response.data.activity_status;
                        let statusTime = parseInt(this.normalStatusUpdatedTime);
                        let tdTimes = new Date(); // 获取当前时间
                        tdTimes.setHours(0, 0, 0, 0)

                        let list = this.todayCashbackList.getChildByName("list");
                        let td_empty = this.todayCashbackList.getChildByName("td_empty");
                        let la_notice = this.todayCashbackList.getChildByName("lab_notice");
                        let btn_bet = utils.getChildByPath(this.cashBackBonusView, "yesterdayLayout");
                        let list2 = this.yesterdayCashbackList.getChildByName("list");
                        let yd_empty = this.yesterdayCashbackList.getChildByName("yd_empty");
                        let lan_notice = this.yesterdayCashbackList.getChildByName("lan_notice");
                        if(this.cashback_status != 1 && statusTime) {
                            list.active = false;
                            td_empty.active = true;
                            this.vipCashBackRuleClose = true;
                            la_notice.active = false;
                            btn_bet.active = false;
                            if (statusTime < tdTimes.getTime()/1000) {
                                list2.active = false;
                                yd_empty.active = true;
                                lan_notice.active = false;
                            }
                        } else {
                            list.active = true;
                            list2.active = true;
                            td_empty.active = false;
                            yd_empty.active = false;
                            this.vipCashBackRuleClose = false;
                            la_notice.active = true;
                            lan_notice.active = true;
                        }
                    }
                }
            });
        }
    }

    /**cashback detials */
    cashbackDetials(cb?) {
        let content = utils.getChildByPath(this.cashBackDetialsView, "scrollview.view.content");
        let unvip_cashback = content.getChildByName("unvip_cashback");
        let vip_cashback = content.getChildByName("vip_cashback");
        vip_cashback.active = false;
        unvip_cashback.active = true;
        content.height = unvip_cashback.height;
        //period covered
        // let lab_period = unvip_cashback.getChildByName("period_covered").getChildByName("lab_period").getComponent(cc.Label);
        // if (lab_period.node) lab_period.string = `${this.start_period} - ${this.end_period}`;

        //Cashback Details
        HttpUtils.getInstance().post(1, 3, this, "/avt/api/activity/rebate-conf", {
            token: Global.getInstance().token,
            config_type: 1,//1:普通返水 4:vip返水
        }, (response) => {
            if (!this.node || !this.node.isValid) {
                return;
            }
            if (response.data) {
                this.cashback_status = response.data.status;
                this.normalStatusUpdatedTime = response.data.updated_at;
            }
            if (response.data && response.data.config && response.data.config.length > 0) {
                let config = response.data.config;
                let actStatus = response.data.status;//status:0关 1开
                for (let index = 1; index < config[0].length; index++) {
                    let item = config[0][index];
                    let rate = this.cashbackRules.getChildByName("level_1_"+item.game_type.toLowerCase()).getComponent(cc.Label);
                    if (actStatus != 1) {
                        rate.string = "--";
                    } else {
                        rate.string = item.rate+"%";
                    }
                }
                if(cb) cb();
            }
        }, (response)=>{
            if(cb) cb();
        });
    }

    /**vip cashback detials */
    initVipCashbackDetials(cb?) {
        let content = utils.getChildByPath(this.cashBackDetialsView, "scrollview.view.content");
        let unvip_cashback = content.getChildByName("unvip_cashback");
        let vip_cashback = content.getChildByName("vip_cashback");
        unvip_cashback.active = false;
        vip_cashback.active = true;
        content.height = vip_cashback.height;
        //period covered
        // let lab_period = vip_cashback.getChildByName("period_covered").getChildByName("lab_period").getComponent(cc.Label);
        // if (lab_period.node) lab_period.string = `${this.start_period} - ${this.end_period}`;

        //Vip Cashback Details
        HttpUtils.getInstance().post(1, 3, this, "/avt/api/activity/rebate-conf", {
            token: Global.getInstance().token,
            config_type: 4,//1:普通返水 4:vip返水
        }, (response) => {
            if (!this.node || !this.node.isValid) {
                return;
            }
            if (response.data) {
                this.vipcashback_status = response.data.status;
                this.vipStatusUpdatedTime = response.data.updated_at;
            }
            if (response.data && response.data.config && response.data.config.length > 0) {
                let config = response.data.config[0];
                this.config_cashback = response.data.config[0][1];
                let actStatus = response.data.status;//status 0关 1开
                for(let k = 1; k < config.length; k++) {
                    let rebate = utils.getChildByPath(this.vipCashBackRules, config[k].game_type.toLowerCase()+"_cashback").getComponent(cc.Label);
                    if (actStatus != 1) {
                        if(rebate) rebate.string = "";
                    } else {
                        if(rebate) rebate.string = config[k].rate+"%";
                    }
                }
                if(cb){cb()}
            }
        }, (response)=>{
            if(cb){cb()}
        });
    }

    turingPageNormalListener(event,data) {
        let curIndex = this.pageView.getCurrentPageIndex();
        let content = this.cashBackBonusView.getComponent(cc.ScrollView).content;
        let todayLayout = this.cashBackBonusView.getChildByName("todayLayout");
        let yesterdayLayout = this.cashBackBonusView.getChildByName("yesterdayLayout");
        if (curIndex == 0) {
            utils.getChildByPath(content,"top.btn_today").getComponent(cc.Sprite).spriteFrame = this.btnBG[1];
            utils.getChildByPath(content,"top.btn_yesterday").getComponent(cc.Sprite).spriteFrame = this.btnBG[0];
            utils.getChildByPath(content,"top.btn_today.labtoday").color = cc.color(255,255,255);
            utils.getChildByPath(content,"top.btn_yesterday.labyestaday").color = cc.color(128,147,203);
            todayLayout.x = 0;
            yesterdayLayout.x = 6000;
            if (todayLayout.active) {
                this.cashBackBonusView.getComponent(cc.ScrollView).content.height = 2783;
            } else {
                this.cashBackBonusView.getComponent(cc.ScrollView).content.height = 2583;
            }
        } else {
            utils.getChildByPath(content,"top.btn_today").getComponent(cc.Sprite).spriteFrame = this.btnBG[0];
            utils.getChildByPath(content,"top.btn_yesterday").getComponent(cc.Sprite).spriteFrame = this.btnBG[1];
            utils.getChildByPath(content,"top.btn_today.labtoday").color = cc.color(126,144,255);
            utils.getChildByPath(content,"top.btn_yesterday.labyestaday").color = cc.color(255,255,255);
            todayLayout.x = 6000;
            yesterdayLayout.x = 0;
            if (yesterdayLayout.active) {
                this.cashBackBonusView.getComponent(cc.ScrollView).content.height = 2783;
            } else {
                this.cashBackBonusView.getComponent(cc.ScrollView).content.height = 2583;
            }
        }
    }

    turingPageVipListener(event,data) {
        let curIndex = this.pageView.getCurrentPageIndex();
        let content = this.cashBackBonusView.getComponent(cc.ScrollView).content;
        let todayLayout = this.cashBackBonusView.getChildByName("todayLayout");
        let yesterdayLayout = this.cashBackBonusView.getChildByName("yesterdayLayout");
        if (curIndex == 0) {
            let content = this.cashBackBonusView.getComponent(cc.ScrollView).content;
            utils.getChildByPath(content,"top.btn_today").getComponent(cc.Sprite).spriteFrame = this.btnBG[2];
            utils.getChildByPath(content,"top.btn_yesterday").getComponent(cc.Sprite).spriteFrame = this.btnBG[3];
            utils.getChildByPath(content,"top.btn_today.labtoday").color = cc.color(255,255,255);
            utils.getChildByPath(content,"top.btn_yesterday.labyestaday").color = cc.color(193,146,106);
            utils.getChildByPath(this.todayCashbackList,"list.title1").color = cc.color(176,133,98);
            utils.getChildByPath(this.todayCashbackList,"list.title2").color = cc.color(176,133,98);
            utils.getChildByPath(this.todayCashbackList,"list.cashback_percent").color = cc.color(176,133,98);
            utils.getChildByPath(this.todayCashbackList,"list.vip_cashback").color = cc.color(176,133,98);
            todayLayout.x = 0;
            yesterdayLayout.x = 6000;
            if (todayLayout.active) {
                this.cashBackBonusView.getComponent(cc.ScrollView).content.height = 2783;
            } else {
                this.cashBackBonusView.getComponent(cc.ScrollView).content.height = 2583;
            }
        } else {
            utils.getChildByPath(content,"top.btn_today").getComponent(cc.Sprite).spriteFrame = this.btnBG[3];
            utils.getChildByPath(content,"top.btn_yesterday").getComponent(cc.Sprite).spriteFrame = this.btnBG[2];
            utils.getChildByPath(content,"top.btn_today.labtoday").color = cc.color(192,146,106);
            utils.getChildByPath(content,"top.btn_yesterday.labyestaday").color = cc.color(255,255,255);
            utils.getChildByPath(this.yesterdayCashbackList,"list.title1").color = cc.color(176,133,98);
            utils.getChildByPath(this.yesterdayCashbackList,"list.title2").color = cc.color(176,133,98);
            utils.getChildByPath(this.yesterdayCashbackList,"list.title3").color = cc.color(176,133,98);
            todayLayout.x = 6000;
            yesterdayLayout.x = 0;
            if (yesterdayLayout.active) {
                this.cashBackBonusView.getComponent(cc.ScrollView).content.height = 2783;
            } else {
                this.cashBackBonusView.getComponent(cc.ScrollView).content.height = 2583;
            }
        }
    }

    showActivityView(activeType, type?, start_time?, end_time?, detial_mark?) {
        this.activity_type = activeType;
        if (type) this.openType = type;
        if (start_time) this.start_period = utils.timestampToTime4(start_time);
        if (end_time) this.end_period = utils.timestampToTime4(end_time);

        let detialsBack = false;
        if (this.cashBackDetialsView.active) {
            detialsBack = true
        }
        this.signBindBonusView.active = false;
        this.firstChargeBonusView.active = false;
        this.cashBackBonusView.active = false;
        this.cashBackDetialsView.active = false;
        this.btnTransion.active = false;
        this.btnDetials.active = false;
        this.labTitle.string = "Promo";
        if (activeType == ACTIVITY_TYPE.SIGNBIND) {//signup
            this.signBindBonusView.active = true;
            this.btnTransion.active = true;
            this.labTitle.string = "REGISTER TO GET FREE ₱30";
            this.labTitle.node.x = 0;
            this.initSignView();
        } else if (activeType == ACTIVITY_TYPE.FIRSTDEPOSIT) {//firstrecharge
            this.firstChargeBonusView.active = true;
            this.btnTransion.active = true;
            this.labTitle.string = "Deposit Now, Enjoy ₱200 Bonus!";
            this.labTitle.node.x = 0;
            this.initFistDeposit();
        } else if (activeType == ACTIVITY_TYPE.CASHBACK) {//cashback
            this.cashBackBonusView.active = true;
            this.btnTransion.active = true;
            this.btnDetials.active = true;
            this.labTitle.string = "GET UNLIMITED CASHBACK";
            this.labTitle.node.x = -35;
            this.cashbackDetials(()=>{
                this.initCashback();
            });
        } else if (activeType == ACTIVITY_TYPE.CASHBACK_DETIALS) {//cashback detials(普通返水详情)
            this.cashBackDetialsView.active = true;
            this.labTitle.string = "GET UNLIMITED CASHBACK";
            this.labTitle.node.x = -35;
            this.initLayoutOpacity();
            let scrollview = this.cashBackDetialsView.getChildByName("scrollview");
            scrollview.on("scrolling", ()=>{this.title_layout_opacity(scrollview.getComponent(cc.ScrollView))}, this);
        } else if (activeType == ACTIVITY_TYPE.VIP_CASHBACK_DETIALS) {//vip cashback detials(vip专属返水详情)
            this.cashBackDetialsView.active = true;
            this.labTitle.string = "VIP BENEFITS 0.8% CASHBACK";
            this.labTitle.node.x = -35;
            this.initLayoutOpacity();
            let scrollview = this.cashBackDetialsView.getChildByName("scrollview");
            scrollview.on("scrolling", ()=>{this.title_layout_opacity(scrollview.getComponent(cc.ScrollView))}, this);
        } else if (activeType == ACTIVITY_TYPE.VIPCASHBACK) {//vip cashback
            this.cashBackBonusView.active = true;
            this.btnTransion.active = true;
            this.btnDetials.active = true;
            this.labTitle.string = "VIP BENEFITS 0.8% CASHBACK";
            this.labTitle.node.x = -35;
            this.initVipCashbackDetials(()=>{
                this.initVipCashback();
                //是否是vip用户
                let isVip;
                if (Global.getInstance().userdata) {
                    isVip = parseInt(Global.getInstance().userdata.is_vip);
                } else {
                    isVip = 0;
                }
                if(!isVip){//非VIP用户进入到VIP返水活动
                    if(detial_mark && detial_mark == "vip_cashback_detials"){
                    }else{
                        let cashback_rate = this.config_cashback?.rate || "--";
                        let string1 = `Qualified VIP members will enjoy ${cashback_rate}% cashback`;
                        Global.getInstance().showCommonTip2({ word: string1, confirm: "Done" }, this, true, () => {
                            //点击Done跳转至VIP的活动详情页面
                            uiManager.instance.showDialog(UI_PATH_DIC.VipDetail,null,null,DEEP_INDEXZ.ACTIVITY_CONTENT);
                        },null,null,"Tips");
                    }
                }
            });
        }
        if (detialsBack) {
            this.cashBackBonusView.active = true;
            this.btnTransion.active = true;
            this.btnDetials.active = true;
            
            if (activeType == ACTIVITY_TYPE.CASHBACK) {
                this.labTitle.string = "GET UNLIMITED CASHBACK";
            } else if (activeType == ACTIVITY_TYPE.VIPCASHBACK) {
                this.labTitle.string = "VIP BENEFITS 0.8% CASHBACK";
            }
        }
    }

    backToPromo(type?) {
        //1.从vip页面Detail按钮点击进入 2.vip用户进入普通返水活动 点击Click To Go进入
        if (this.openType && this.openType == "vip_cashback") {
            if (this.cashBackDetialsView.active) {
                this.showActivityView(4);
                return;
            }
            this.node.destroy();
            if(type && type == "vip"){
                uiManager.instance.hideDialog(UI_PATH_DIC.Vip);
                cc.director.emit("ReturnToHome","","home");
            } 
        } else {
            if (this.cashBackDetialsView.active) {
                if (this.activity_type == ACTIVITY_TYPE.VIP_CASHBACK_DETIALS) {
                    this.showActivityView(4, null, null, null, "vip_cashback_detials");
                } else if (this.activity_type == ACTIVITY_TYPE.CASHBACK_DETIALS) {
                    this.showActivityView(3);
                }
                return;
            }
            this.node.destroy();
            cc.director.emit("refresh_promo");
        }
    }

    cliclDetialView(event,userdata) {
        if (!Global.getInstance().token) {
            //未登录 跳转登录页
            uiManager.instance.showDialog(UI_PATH_DIC.phonePasswordLogin, [{ way: LOGIN_WAY.PhoneCode, curPromoDetail:true }])//curPromoDetail:true表示当前是活动详情页面
            return;
        }
        if (userdata == "rules") {
            if (this.activity_type == ACTIVITY_TYPE.CASHBACK) {//普通返水活动
                this.showActivityView(ACTIVITY_TYPE.CASHBACK_DETIALS);
            } else if (this.activity_type == ACTIVITY_TYPE.VIPCASHBACK) {//VIP专属返水活动
                this.showActivityView(ACTIVITY_TYPE.VIP_CASHBACK_DETIALS);
            }
        }
    }

    clickTransionView() {
        if (!Global.getInstance().token) {
            //未登录 跳转登录页
            uiManager.instance.showDialog(UI_PATH_DIC.phonePasswordLogin, [{ way: LOGIN_WAY.PhoneCode, curPromoDetail:true }],null,DEEP_INDEXZ.TRANSATIONS)//curPromoDetail:true表示当前是活动详情页面
            return;
        }
        uiManager.instance.showDialog(UI_PATH_DIC.Transations,["award"]);
    }

    clickCashbackdays(event,userdata) {
        this.pageView.enabled = true;
        this.pageView.scrollToPage(parseInt(userdata),0.2);
    }

    clickDeposit(event,userdata) {
       Global.getInstance().loadWithdrawConfig(()=>{
            if(ALL_APP_SOURCE_CONFIG.channel == E_CHANEL_TYPE.G_CASH){
                //gcash 直接弹窗提示 是否跳转充值
                Global.getInstance().back_mini_buy_tips();
                return;
            }
            uiManager.instance.showDialog(UI_PATH_DIC.Deposit,null,null,DEEP_INDEXZ.DEPOSIT);                           
        },true);
    }

    clickgoBet(event,userdata) {
        if (!Global.getInstance().token) {
            //未登录 跳转登录页
            uiManager.instance.showDialog(UI_PATH_DIC.phonePasswordLogin, [{ way: LOGIN_WAY.PhoneCode, curPromoDetail:true }])//curPromoDetail:true表示当前是活动详情页面
            return;
        }
        // 再跳转到游戏逻辑
        let gamescene =  cc.director.getScene().getComponentInChildren(GameControl);
        gamescene.mTabbar.clickGoBet(()=>{
            this.backToPromo("vip");
        });
    }

    clickCliamYesterdayBonus() {
        if (!Global.getInstance().token) {
            //未登录 跳转登录页
            uiManager.instance.showDialog(UI_PATH_DIC.phonePasswordLogin, [{ way: LOGIN_WAY.PhoneCode, curPromoDetail:true }])//curPromoDetail:true表示当前是活动详情页面
            return;
        }
        if (!!Global.getInstance().token) {
            let params = {
                token:Global.getInstance().token
            };
            let btn_cliam = utils.getChildByPath(this.cashBackBonusView,"yesterdayLayout.btn_cliam").getComponent(cc.Button);
            btn_cliam.enabled = false;
            HttpUtils.getInstance().post(3, 3, this, "/avt/api/activity/daily-rebate-receive", params, (response) => {
                if (!this.node || !this.node.isValid) {
                    return;
                }
                if (response.code == 200) {
                    btn_cliam.enabled = true;
                    let string1;
                    let totalAwardNum = utils.formatNumberWithCommas(this.yesterdatTotalBonus);
                    let isVip = parseInt(Global.getInstance().userdata?.is_vip) || 0;
                    if (!isVip && this.activity_type == ACTIVITY_TYPE.CASHBACK) {
                        string1 = "<color=#707070>You've got </c>"+"<color=#F26225>"+"<b>₱"+totalAwardNum+"</b></c>"+"<color=#707070>\n\nCashback bonus!</c>"
                        Global.getInstance().showCommonTip2({ word: string1, confirm: "Done" }, this, true, () => {},null,null,"Congratulations");
                        this.initCashbackYesterday();
                        Global.getInstance().setStoreageData("PROMO_BONUS_CAN_GET", 0);
                    } else if (isVip && this.activity_type == ACTIVITY_TYPE.VIPCASHBACK) {
                        string1 = "<color=#707070>You've got </c>"+"<color=#F26225>"+"<b>₱"+totalAwardNum+"</b></c>"+"<color=#707070>\n\nVIP Cashback bonus!</c>"
                        Global.getInstance().showCommonTip2({ word: string1, confirm: "Done" }, this, true, () => {},null,null,"Congratulations");
                        this.initVipCashbackYesterday();
                        Global.getInstance().setStoreageData("PROMO_VIPBONUS_CAN_GET", 0);
                    }
                    cc.director.emit("update_home_widget");
                } else {
                    btn_cliam.enabled = true;
                    if (response.msg) {
                        Global.getInstance().showSimpleTip(response.msg);
                    }
                }
            }, function (response) {
                btn_cliam.enabled = true;
                if (!!response && !!response["code"]) {
                    if (response.code == MAINTENANCETIPCODE) { //服务器维护
                        showMaintenancetip(response.msg)
                        return
                    }
                }
            });
        }
    }

    clickSignUp() {
        if (ALL_APP_SOURCE_CONFIG.channel == E_CHANEL_TYPE.WEB) {//web渠道
            if (!Global.getInstance().token) {
                //未登录 跳转登录页
                uiManager.instance.showDialog(UI_PATH_DIC.phonePasswordLogin, [{ way: LOGIN_WAY.PhoneCode, curPromoDetail:true }])//curPromoDetail:true表示当前是活动详情页面
                return;
            }
        }
    }

    showLoginView() {
        uiManager.instance.showDialog(UI_PATH_DIC.phonePasswordLogin, [{ way: LOGIN_WAY.PhoneCode, curPromoDetail:true }])
        this.signBindBonusView.getChildByName("needLoginBtn").active = !Global.getInstance().token;
        this.firstChargeBonusView.getChildByName("needLoginBtn").active = !Global.getInstance().token;
        this.cashBackBonusView.getChildByName("needLoginBtn").active = !Global.getInstance().token;
    }

    title_layout_opacity(node:cc.ScrollView) {
        let opacity = node.getScrollOffset().y
        if (opacity > 255 ) opacity = 255;
        let layou = utils.getChildByPath(this.node,"titlelayout.bg");
        layou.opacity = opacity;
        let btn_back = utils.getChildByPath(this.node,"titlelayout.btn_back.img");
        let btn_transiton = utils.getChildByPath(this.node,"titlelayout.btn_transiton.img");
        let btn_rules = utils.getChildByPath(this.node,"titlelayout.btn_rules.img");
        if (opacity > 0) {
            btn_back.color = cc.color(255-opacity/255*180,255-opacity/255*168,255-opacity/255*151);
            btn_transiton.color = cc.color(255-opacity/255*180,255-opacity/255*168,255-opacity/255*151);
            btn_rules.color = cc.color(255-opacity/255*180,255-opacity/255*168,255-opacity/255*151);
        } else {
            btn_back.color = cc.color(255,255,255);
            btn_transiton.color = cc.color(255,255,255);
            btn_rules.color = cc.color(255,255,255);
        }
        
    }

    initLayoutOpacity() {
        let layou = utils.getChildByPath(this.node,"titlelayout.bg");
        layou.opacity = 0;
        let btn_back = utils.getChildByPath(this.node,"titlelayout.btn_back.img");
        let btn_transiton = utils.getChildByPath(this.node,"titlelayout.btn_transiton.img");
        let btn_rules = utils.getChildByPath(this.node,"titlelayout.btn_rules.img");
        btn_back.color = cc.color(255,255,255);
        btn_transiton.color = cc.color(255,255,255);
        btn_rules.color = cc.color(255,255,255);
    }
}
