import { UI_PATH_DIC } from "../../GlobalConstant";
import UICommon from "../../component/UICommon";
import { IActivityJump } from "../../hall/ActivityMgr";
import { uiManager } from "../../mgr/UIManager";

const { ccclass, property } = cc._decorator;

export function showRedeemPromiseUI(actData?) {
    uiManager.instance.showDialog(UI_PATH_DIC.RedeemPromise, [{ actData: actData }])//未用到
}

@ccclass
export default class RedeemPromise extends UICommon implements IActivityJump {
    activityId: number;
    init(data?) {
        this.activityId = data.actData.related_activity_id
    }
    onClickDetail() {
        this.jumpToActivityDetail()
    }
    onClickClose() {
        this.hide()
    }
    jumpToActivityDetail() {
        this.hide()
        cc.director.emit("activityJump", { actId: this.activityId });
    }
}