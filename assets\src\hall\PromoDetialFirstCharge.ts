import { DEEP_INDEXZ, E_CHANEL_TYPE, UI_PATH_DIC } from "../GlobalConstant";
import Global from "../GlobalScript";
import HttpUtils from "../net/HttpUtils";
import utils from "../utils/utils";
import { ALL_APP_SOURCE_CONFIG } from "../Config";
import { uiManager } from "../mgr/UIManager";
import { LOGIN_WAY } from "./Hall";
import GameControl from "../GameControl";

const {ccclass, property} = cc._decorator;

@ccclass
export default class PromoDetialFirstCharge extends cc.Component {

    @property(cc.Node)
    firstChargeBonusView: cc.Node = null;

    @property(cc.Node)
    btnBack: cc.Node = null;

    @property(cc.Node)
    firstDepositRules: cc.Node = null;

    @property(cc.Node)
    btnTransion: cc.Node = null;

    @property(cc.Node)
    btnDetials: cc.Node = null;

    @property(cc.Label)
    labTitle: cc.Label = null;

    type = null;
    onLoad () {
        this.firstChargeBonusView.getChildByName("needLoginBtn").active = !Global.getInstance().token;
        this.initFistDeposit();
    }

    initType(args){
        this.type = args;
    }

    start () {
        let page = this.node.getChildByName("page").getComponent(cc.PageView);
        page.node.on('page-turning', ()=>{
            let curIndex = page.getCurrentPageIndex();
            if (curIndex != 1) {
                this.node.destroy();
            }
        }, this);
        let tempnode = this.node.getChildByName("temp");
        page.insertPage(cc.instantiate(tempnode),0);
        page.scrollToPage(1,0.01);
    }

    initFistDeposit() {
        let scrollview = this.firstChargeBonusView.getChildByName("scrollview");
            scrollview.on("scrolling", ()=>{this.title_layout_opacity(scrollview.getComponent(cc.ScrollView))}, this);
        // if (!!Global.getInstance().token) {
        //     HttpUtils.getInstance().post(1, 3, this, "/common/api/global-config/first/recharge/rule", {
        //         token: Global.getInstance().token,
        //     }, (response) => {
        //         if (!this.node || !this.node.isValid) {
        //             return;
        //         }
        //         if (response.data && response.data.recharge && response.data.recharge.length > 0) {
        //             let rechargeList = response.data.recharge;
        //             rechargeList.sort((a,b)=>{return a.id - b.id});
        //             for (let index = 0; index < rechargeList.length; index++) {
        //                 let element = rechargeList[index];
        //                 let labbuy = this.firstDepositRules.getChildByName("lab_buy_"+(index+1)).getComponent(cc.Label);
        //                 let labbonus = this.firstDepositRules.getChildByName("lab_bonus_"+(index+1)).getComponent(cc.Label);
        //                 if (labbuy) {
        //                     if (element && element.amount) {
        //                         labbuy.string = "₱"+utils.formatNumberWithCommas(element.amount,0);
        //                     } else {
        //                         labbuy.string = "";
        //                     }
        //                 }
        //                 if (labbonus) {
        //                     if (element && element.award) {
        //                         labbonus.string = "₱"+utils.formatNumberWithCommas(element.award,0);
        //                     } else {
        //                         labbonus.string = "";
        //                     }
        //                 }
        //             }
        //         }
        //     });
        // }
    }

    backToPromo(type?) {
        if(this.type == "banner"){
            let gamescene =  cc.director.getScene().getComponentInChildren(GameControl);
            gamescene.mTabbar.rein_home();
            this.node.destroy();
        }else{
            this.node.destroy();
            cc.director.emit("refresh_promo");
        }
    }

    clickTransionView() {
        if (!Global.getInstance().token) {
            //未登录 跳转登录页
            uiManager.instance.showDialog(UI_PATH_DIC.phonePasswordLogin, [{ way: LOGIN_WAY.PhoneCode, curPromoDetail:true }])//curPromoDetail:true表示当前是活动详情页面
            return;
        }
        uiManager.instance.showDialog(UI_PATH_DIC.Transations,["award"],null,DEEP_INDEXZ.TRANSATIONS);
    }

    clickDeposit(event,userdata) {
        Global.getInstance().loadWithdrawConfig(()=>{
            if(ALL_APP_SOURCE_CONFIG.channel == E_CHANEL_TYPE.G_CASH){
                //gcash 直接弹窗提示 是否跳转充值
                Global.getInstance().back_mini_buy_tips();
                return;
            }
            uiManager.instance.showDialog(UI_PATH_DIC.Deposit,null,null,DEEP_INDEXZ.DEPOSIT);                           
        },true);
    }

    clickSignUp() {
        if (ALL_APP_SOURCE_CONFIG.channel == E_CHANEL_TYPE.WEB) {//web渠道
            if (!Global.getInstance().token) {
                //未登录 跳转登录页
                uiManager.instance.showDialog(UI_PATH_DIC.phonePasswordLogin, [{ way: LOGIN_WAY.PhoneCode, curPromoDetail:true }])//curPromoDetail:true表示当前是活动详情页面
                return;
            }
        }
    }

    showLoginView() {
        uiManager.instance.showDialog(UI_PATH_DIC.phonePasswordLogin, [{ way: LOGIN_WAY.PhoneCode, curPromoDetail:true }])
        this.firstChargeBonusView.getChildByName("needLoginBtn").active = !Global.getInstance().token;
    }

    title_layout_opacity(node:cc.ScrollView) {
        let opacity = node.getScrollOffset().y
        if (opacity > 255 ) opacity = 255;
        let layou = utils.getChildByPath(this.firstChargeBonusView,"titlelayout.bg");
        layou.opacity = opacity;
        let btn_back = utils.getChildByPath(this.firstChargeBonusView,"titlelayout.btn_back.img");
        let btn_transiton = utils.getChildByPath(this.firstChargeBonusView,"titlelayout.btn_transiton.img");
        let btn_rules = utils.getChildByPath(this.firstChargeBonusView,"titlelayout.btn_rules.img");
        if (opacity > 0) {
            btn_back.color = cc.color(255-opacity/255*180,255-opacity/255*168,255-opacity/255*151);
            btn_transiton.color = cc.color(255-opacity/255*180,255-opacity/255*168,255-opacity/255*151);
            btn_rules.color = cc.color(255-opacity/255*180,255-opacity/255*168,255-opacity/255*151);
        } else {
            btn_back.color = cc.color(255,255,255);
            btn_transiton.color = cc.color(255,255,255);
            btn_rules.color = cc.color(255,255,255);
        }
        
    }

    initLayoutOpacity() {
        let layou = utils.getChildByPath(this.firstChargeBonusView,"titlelayout.bg");
        layou.opacity = 0;
        let btn_back = utils.getChildByPath(this.firstChargeBonusView,"titlelayout.btn_back.img");
        let btn_transiton = utils.getChildByPath(this.firstChargeBonusView,"titlelayout.btn_transiton.img");
        let btn_rules = utils.getChildByPath(this.firstChargeBonusView,"titlelayout.btn_rules.img");
        btn_back.color = cc.color(255,255,255);
        btn_transiton.color = cc.color(255,255,255);
        btn_rules.color = cc.color(255,255,255);
    }
}
