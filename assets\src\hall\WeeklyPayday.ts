import { DEEP_INDEXZ, E_CHANEL_TYPE, UI_PATH_DIC } from "../GlobalConstant";
import Global from "../GlobalScript";
import HttpUtils from "../net/HttpUtils";
import utils from "../utils/utils";
import { uiManager } from "../mgr/UIManager";
import MoreGameManager from "../MoreGameManager";
import { LOGIN_WAY } from "./Hall";
import UICommon from "../component/UICommon";
import { AWARD_UPDATE_TYPE } from "./Transations";
import { ALL_APP_SOURCE_CONFIG } from "../Config";
import Promo from "./Promo";
import GameControl from "../GameControl";

const {ccclass, property} = cc._decorator;

@ccclass
export default class WeeklyPayday extends UICommon {
    @property(cc.Node)
    weeklyPaydayView: cc.Node = null;
    @property(cc.Node)
    btnBack: cc.Node = null;
    @property(cc.Node)
    btnRule: cc.Node = null;
    @property(cc.Label)
    labTitle: cc.Label = null;
    @property(cc.Node)
    lastWeekList: cc.Node = null; 
    @property(cc.Node)
    curWeekList: cc.Node = null;
    @property(cc.Node)
    performanceNode: cc.Node = null;
    @property([cc.SpriteFrame])
    weeklyBtnBg: cc.SpriteFrame[] = [];
    @property([cc.SpriteFrame])
    claimBtnBg: cc.SpriteFrame[] = [];
    @property([cc.SpriteFrame])
    levelBtnBg: cc.SpriteFrame[] = [];
    @property([cc.SpriteFrame])
    weeklyItemBg: cc.SpriteFrame[] = [];
    @property([cc.SpriteFrame])
    depositBg: cc.SpriteFrame[] = [];
    @property([cc.SpriteFrame])
    betBg: cc.SpriteFrame[] = [];
    @property(cc.ScrollView)
    scrollView: cc.ScrollView = null;

    @property(cc.Node)
    btnLastWeek: cc.Node = null;

    @property(cc.Node)
    btnCurWeek: cc.Node = null;

    openType = null;//打开页面类型
    activity_type = null;//活动类型
    timefuc = null;
    lastReqTime = 0;//间隔请求

    weekly_data = null;
    weekly_status = null;//活动开关状态 1开启 2关闭
    update_time = null;//活动更新时间

    isLastWeekClose = 0;//上周 是否关闭 标记
    isCurWeekClose = 0;//本周 是否关闭 标记

    lastweek_receive_data = null;//个人上周领奖数据
    curweek_receive_data = null;//个人本周领奖数据

    sp_lastweek = null;
    sp_curweek = null;
    lab_lastweek = null;
    lab_ended = null;
    lab_curweek = null;
    lab_ongoing = null;

    type = null;
    onLoad () {
        this.weeklyPaydayView.getChildByName("needLoginBtn").active = !Global.getInstance().token;
        this.scrollView.node.on("scrolling", ()=>{this.title_layout_opacity(this.scrollView)}, this);
    }

    init(args){
        this.type = args?.tpye;
    }

    start () {
        this.initWeekly();
        let page = this.node.getChildByName("page").getComponent(cc.PageView);
        page.node.on('page-turning', ()=>{
            let curIndex = page.getCurrentPageIndex();
            if (curIndex != 1) {
                this.node.destroy();
            }
        }, this);
        let tempnode = this.node.getChildByName("temp");
        page.insertPage(cc.instantiate(tempnode),0);
        page.scrollToPage(1,0.01);
    }

    /**WeeklyPayday活动 */
    initWeekly() {
        let self = this;
        setTimeout(() => {
            self.get_all_activity_bonus();
            self.initLastAndCurWeek();
            self.initWeeklyBtnTime();
        }, 100);
    }

    initLastAndCurWeek() {
        this.initLastWeekReceiveInfo(()=>{
            this.initCurWeekReceiveInfo(()=>{
                this.initLastAndCurWeekConfig();
                //上周有可领取奖励的默认展示上周页面，否则显示本周页面
                if(this.lastweek_receive_data.status == 2){
                    this.defaultShowLastWeek();
                }else{
                    this.defaultShowCurWeek();
                }
            })
        })  
    }

    initLastWeekReceiveInfo(cb?){
        if (!!Global.getInstance().token) {
            HttpUtils.getInstance().get(1, 3, this, "/open/api/activity/weekly/receive/info", {
                token: Global.getInstance().token,
                turn: 2//1:本周 2上周
            }, (response) => {
                if (response.data) {
                    this.lastweek_receive_data = response.data;
                    //上周个人周领奖信息
                    this.initLastWeekInfo();
                    if(cb) cb();
                }
            });
        }
    }

    initCurWeekReceiveInfo(cb?){
        HttpUtils.getInstance().get(1, 3, this, "/open/api/activity/weekly/receive/info", {
            token: Global.getInstance().token,
            turn: 1//1:本周 2上周
        }, (response) => {
            if (response.data) {
                this.curweek_receive_data = response.data;
                if(cb) cb();
            }
        });
    }

    initLastAndCurWeekConfig(){
        HttpUtils.getInstance().get(1, 3, this, "/open/api/activity/weekly/config", {
            token: Global.getInstance().token,
        }, (response) => {
            if (!this.node || !this.node.isValid) {
                return;
            }
            if (response.data && response.data.weekly_config.length > 0) {//list配置信息
                this.weekly_data = response.data.weekly_config;
                this.weekly_status = response.data.is_start;//1:开启 2关闭
                this.update_time = response.data.activity_updated_at;
                this.initLastWeekConfig(this.weekly_data, this.weekly_status, this.update_time);
                this.initCurWeekConfig(this.weekly_data, this.weekly_status);
            }
        });
    }

    initLastWeekConfig(data, status, updatetime){
        let deposit_level_index = [];
        let bet_level_index = [];
        let idx;
        for(let i = 0; i < data.length; i++){
            let level = utils.getChildByPath(this.lastWeekList, "list.levelnode.level_"+i);
            let lab_deposit = utils.getChildByPath(level, "deposit.lab_deposit").getComponent(cc.Label);
            let lab_bet = utils.getChildByPath(level, "bet.lab_bet").getComponent(cc.Label);
            let lab_bonus = utils.getChildByPath(level, "lab_bonus").getComponent(cc.Label);
            lab_deposit.string = "₱"+utils.formatNumberWithCommas(parseInt(data[i].deposit),0);
            lab_bet.string = "₱"+utils.formatNumberWithCommas(parseInt(data[i].bet),0);
            lab_bonus.string = "₱"+utils.formatNumberWithCommas(parseInt(data[i].bonus),0);

            if(this.lastweek_receive_data){
                //上周deposit bet条件满足 高亮显示
                if(parseInt(this.lastweek_receive_data.deposit) >= parseInt(data[i].deposit)){
                    let level_deposit = utils.getChildByPath(level, "deposit").getComponent(cc.Sprite);
                    level_deposit.spriteFrame = this.levelBtnBg[1];
                    lab_deposit.node.color = cc.color(0,0,0);
                    deposit_level_index.push(i);
                }
                if(parseInt(this.lastweek_receive_data.bet) >= parseInt(data[i].bet)){
                    let level_bet = utils.getChildByPath(level, "bet").getComponent(cc.Sprite);
                    level_bet.spriteFrame = this.levelBtnBg[1];
                    lab_bet.node.color = cc.color(0,0,0);
                    bet_level_index.push(i);
                }

                //上周deposit_progress进度
                if(deposit_level_index.length > 0){
                    let deposit_progress = utils.getChildByPath(this.lastWeekList, "list.levelnode.deposit_progress.bar").getComponent(cc.Sprite);
                    deposit_progress.fillRange = 1/7*deposit_level_index[deposit_level_index.length - 1];
                }
                if(bet_level_index.length > 0){
                    //上周bet_progress进度
                    let bet_progress = utils.getChildByPath(this.lastWeekList, "list.levelnode.bet_progress.bar").getComponent(cc.Sprite);
                    bet_progress.fillRange = 1/7*bet_level_index[bet_level_index.length - 1];
                }
            }
        }

        for(let i = 0; i < data.length; i++){
            let level = utils.getChildByPath(this.lastWeekList, "list.levelnode.level_"+i);
            let item_bg = utils.getChildByPath(this.lastWeekList, "list.levelnode.bg_"+i).getComponent(cc.Sprite);
            let lab_bonus = utils.getChildByPath(level, "lab_bonus").getComponent(cc.Label);
            //bonus满足条件 最大的level bonus高亮显示
            if(deposit_level_index.length < bet_level_index.length){
                idx = deposit_level_index[deposit_level_index.length-1];
                if(i == idx && parseInt(this.lastweek_receive_data.bonus) == parseInt(data[idx].bonus)){
                    lab_bonus.node.color = cc.color(255,198,102);
                    item_bg.spriteFrame = this.weeklyItemBg[1];
                }else{
                    lab_bonus.node.color = cc.color(168,146,128);
                }
            }else{
                let idx = bet_level_index[bet_level_index.length-1];
                if(i == idx && parseInt(this.lastweek_receive_data.bonus) == parseInt(data[idx].bonus)){
                    lab_bonus.node.color = cc.color(255,198,102);
                    item_bg.spriteFrame = this.weeklyItemBg[1];
                }else{
                    lab_bonus.node.color = cc.color(168,146,128);
                }
            }
        }
        
        //weekly payday活动状态 1:开启 2关闭
        let lw_empty = utils.getChildByPath(this.lastWeekList, "lw_empty");
        let lastweek_list = utils.getChildByPath(this.lastWeekList, "list");
        const today = new Date();
        //获取当前日期的起始日期
        const startOfWeek = utils.timeToTimestamp(this.formatDate(this.getStartOfWeek(new Date(today))));
        const updateTime = utils.timeToTimestamp(updatetime);
        if(updateTime < startOfWeek && status != 1){
            lw_empty.active = true;
            lastweek_list.active = false;
            this.isLastWeekClose = 1;
            this.initLastWeekInfo(true);
        }else{
            lw_empty.active = false;
            lastweek_list.active = true;
            this.isLastWeekClose = 0;
        }
    }

    initCurWeekConfig(data, status){
        let deposit_level_index2 = [];
        let bet_level_index2 = [];
        let idx;
        for(let k = 0; k < data.length; k++){
            let level2 = utils.getChildByPath(this.curWeekList, "list.levelnode.level_"+k);
            let lab_deposit2 = utils.getChildByPath(level2, "deposit.lab_deposit").getComponent(cc.Label);
            let lab_bet2 = utils.getChildByPath(level2, "bet.lab_bet").getComponent(cc.Label);
            let lab_bonus2 = utils.getChildByPath(level2, "lab_bonus").getComponent(cc.Label);
            lab_deposit2.string = "₱"+utils.formatNumberWithCommas(parseInt(data[k].deposit),0);
            lab_bet2.string = "₱"+utils.formatNumberWithCommas(parseInt(data[k].bet),0);
            lab_bonus2.string = "₱"+utils.formatNumberWithCommas(parseInt(data[k].bonus),0);

            if(this.curweek_receive_data){
                //本周deposit bet条件满足 高亮显示
                if(parseInt(this.curweek_receive_data.deposit) >= parseInt(data[k].deposit)){
                    let level_deposit2 = utils.getChildByPath(level2, "deposit").getComponent(cc.Sprite);
                    level_deposit2.spriteFrame = this.levelBtnBg[1];
                    lab_deposit2.node.color = cc.color(0,0,0);
                    deposit_level_index2.push(k);
                }
                if(parseInt(this.curweek_receive_data.bet) >= parseInt(data[k].bet)){
                    let level_bet2 = utils.getChildByPath(level2, "bet").getComponent(cc.Sprite);
                    level_bet2.spriteFrame = this.levelBtnBg[1];
                    lab_bet2.node.color = cc.color(0,0,0);
                    bet_level_index2.push(k);
                }
                

                //本周deposit_progress进度
                if(deposit_level_index2.length > 0){
                    let deposit_progress2 = utils.getChildByPath(this.curWeekList, "list.levelnode.deposit_progress.bar").getComponent(cc.Sprite);
                    deposit_progress2.fillRange = 1/7*deposit_level_index2[deposit_level_index2.length - 1];
                }
                if(bet_level_index2.length > 0){
                    //bet_progress进度
                    let bet_progress2 = utils.getChildByPath(this.curWeekList, "list.levelnode.bet_progress.bar").getComponent(cc.Sprite);
                    bet_progress2.fillRange = 1/7*bet_level_index2[bet_level_index2.length - 1];
                }
            }
        }

        for(let i = 0; i < data.length; i++){
            let level = utils.getChildByPath(this.curWeekList, "list.levelnode.level_"+i);
            let item_bg = utils.getChildByPath(this.curWeekList, "list.levelnode.bg_"+i).getComponent(cc.Sprite);
            let lab_bonus = utils.getChildByPath(level, "lab_bonus").getComponent(cc.Label);
            //bonus满足条件 最大的level bonus高亮显示
            if(deposit_level_index2.length < bet_level_index2.length){
                idx = deposit_level_index2[deposit_level_index2.length-1];
                if(i == idx && parseInt(this.curweek_receive_data.bonus) == parseInt(data[idx].bonus)){
                    lab_bonus.node.color = cc.color(255,198,102);
                    item_bg.spriteFrame = this.weeklyItemBg[1];
                }else{
                    lab_bonus.node.color = cc.color(168,146,128);
                }
            }else{
                let idx = bet_level_index2[bet_level_index2.length-1];
                if(i == idx && parseInt(this.curweek_receive_data.bonus) == parseInt(data[idx].bonus)){
                    lab_bonus.node.color = cc.color(255,198,102);
                    item_bg.spriteFrame = this.weeklyItemBg[1];
                }else{
                    lab_bonus.node.color = cc.color(168,146,128);
                }
            }
        }

        //weekly payday活动状态 1:开启 2关闭
        let cw_empty = utils.getChildByPath(this.curWeekList, "cw_empty");
        let curweek_list = utils.getChildByPath(this.curWeekList, "list");
        if(status == 1){
            cw_empty.active = false;
            curweek_list.active = true;
            this.isCurWeekClose = 0;
        }else if(status == 2){
            cw_empty.active = true;
            curweek_list.active = false;
            this.isCurWeekClose = 1;
        }
    }

    initWeeklyBtnTime() {
        let content = this.scrollView.content;
        let lab_lastWeek = utils.getChildByPath(content,"top.btn_lastweek.lab_lastweek").getComponent(cc.Label);
        lab_lastWeek.string = this.getWeekDateRange(-1);// 获取上周的日期范围（自然周）
        let lab_curWeek = utils.getChildByPath(content,"top.btn_curweek.lab_curweek").getComponent(cc.Label);
        lab_curWeek.string = this.getWeekDateRange(0);// 获取当前周的日期范围（自然周）
    }

    formatDate(date: Date) {
        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString();//月份从0开始 所以要加1
        const day = date.getDate().toString();
        return `${year}/${month}/${day}`;
    }

    getStartOfWeek(date: Date) {
        const dayOfWeek = date.getDay() || 7;
        const diff = date.getDate() - dayOfWeek + 1;
        return new Date(date.setDate(diff));
    }

    getWeekDateRange(weekOffest: number = 0) {
        const today = new Date();
        //获取当前日期的起始日期
        const startOfWeek = this.getStartOfWeek(new Date(today));
        startOfWeek.setDate(startOfWeek.getDate() + (weekOffest * 7));//根据周偏移量调整
        const endOfWeek = new Date(startOfWeek);
        endOfWeek.setDate(startOfWeek.getDate() + 6);//当前周的最后一天
        return `${this.formatDate(startOfWeek)} - ${this.formatDate(endOfWeek)}`;
    }

    //获取所有 返奖 活动奖励 弹窗
    get_all_activity_bonus(){
        if(Global.getInstance().popActivityBonus.length == 0){
            let token_ = Global.getInstance().token;
            if(!token_)return;
            let gap = (Global.getInstance().now() - this.lastReqTime)/1000;
            if (gap < 30) {
                return;
            }
            this.lastReqTime = Global.getInstance().now()
            HttpUtils.getInstance().post(1, 3, this, "/avt/api/activity/bonus_list", {token:token_}, (response) => {
                let ac_data = response?.data?.list
                if (ac_data && ac_data.length > 0) {
                    Global.getInstance().popActivityBonus = ac_data;
                }
            }, function (response) {
            });
        }
    }

    initLastWeekInfo(is_close?){
        //my performance
        let per_deposit = utils.getChildByPath(this.performanceNode, "bg.lab_deposit").getComponent(cc.Label);
        let per_bet = utils.getChildByPath(this.performanceNode, "bg.lab_bet").getComponent(cc.Label);
        let per_bonus = utils.getChildByPath(this.performanceNode, "bg.lab_bonus").getComponent(cc.Label);
        let btn_deposit = utils.getChildByPath(this.performanceNode, "btn_deposit");
        let btn_bet = utils.getChildByPath(this.performanceNode, "btn_bet");
        if(this.lastweek_receive_data){
            per_deposit.string = "₱"+utils.formatNumberWithCommas(parseFloat(this.lastweek_receive_data.deposit));
            per_bet.string = "₱"+utils.formatNumberWithCommas(parseFloat(this.lastweek_receive_data.bet));
            per_bonus.string = "₱"+utils.formatNumberWithCommas(parseFloat(this.lastweek_receive_data.bonus),0);
            if(is_close){
                per_deposit.string = "--";
                per_bet.string = "--";
                per_bonus.string = "--";
            }
            //bonus领取按钮状态
            let btn_claim = utils.getChildByPath(this.performanceNode, "btn_claim").getComponent(cc.Sprite);
            let status = this.lastweek_receive_data.status;
            let userId = Global.getInstance().userdata.user_id;
            let content = this.scrollView.content;
            let redpoint = utils.getChildByPath(content, "top.redpoint");
            if(status == 0){//未生成数据
                btn_claim.node.active = false;
                Global.getInstance().setStoreageData("PROMO_WEEKLY_CAN_GET"+userId, 0);
            }else if(status == 1){//有奖金，未审核
                btn_claim.node.active = true;
                btn_claim.spriteFrame = this.claimBtnBg[2];
                Global.getInstance().setStoreageData("PROMO_WEEKLY_CAN_GET"+userId, 0);
            }else if(status == 2){//已审核，未领取
                btn_claim.node.active = true;
                btn_claim.spriteFrame = this.claimBtnBg[0];
                redpoint.active = true;
                Global.getInstance().setStoreageData("PROMO_WEEKLY_CAN_GET"+userId, 1);
            }else if(status == 4){//已领取
                btn_claim.node.active = true;
                btn_claim.spriteFrame = this.claimBtnBg[1];
                Global.getInstance().setStoreageData("PROMO_WEEKLY_CAN_GET"+userId, 0);
            }

            if(btn_claim.node.active){
                btn_deposit.active = true;
                btn_bet.active = true;
                btn_deposit.getComponent(cc.Sprite).spriteFrame = this.depositBg[0];
                btn_bet.getComponent(cc.Sprite).spriteFrame = this.betBg[0];
                btn_deposit.x = -365;
                btn_bet.x = -45;
            }else{
                btn_deposit.active = true;
                btn_bet.active = true;
                btn_deposit.getComponent(cc.Sprite).spriteFrame = this.depositBg[1];
                btn_bet.getComponent(cc.Sprite).spriteFrame = this.betBg[1];
                btn_deposit.x = -220;
                btn_bet.x = 220;
            }
            


            btn_claim.node.on(cc.Node.EventType.TOUCH_END, ()=>{
                let gap = (Global.getInstance().now() - this.lastReqTime)/1000;
                if (gap < 3) {
                    return;
                }
                this.lastReqTime = Global.getInstance().now();
                if(!!Global.getInstance().token){
                    HttpUtils.getInstance().get(1, 3, this, "/open/api/activity/weekly/receive/info", {
                        token: Global.getInstance().token,
                        turn: 2//1:本周 2上周
                    }, (response) => {
                        if (response.data) {
                           let status = response.data.status;
                           let btn_claim = utils.getChildByPath(this.performanceNode, "btn_claim").getComponent(cc.Sprite);
                           if(status == 1){
                                let str = "Bonus distribution in progress.";
                                Global.getInstance().showCommonTip2({ word: str, confirm: "Done" }, this, true, () => {},null,null,"Tips");
                                btn_claim.spriteFrame = this.claimBtnBg[2];
                            }else if(status == 2){
                                uiManager.instance.showDialog(UI_PATH_DIC.ActivityBonusTip, [AWARD_UPDATE_TYPE.WEEKLY_PAYDAY], ()=>{
                                    btn_claim.spriteFrame = this.claimBtnBg[1];
                                });//通用领奖弹窗
                                redpoint.active = false;
                            }else if(status == 4){
                                let str = "You have claimed the current reward.";
                                Global.getInstance().showCommonTip2({ word: str, confirm: "Done" }, this, true, () => {},null,null,"Tips");
                                btn_claim.spriteFrame = this.claimBtnBg[1];
                            }
                        }
                    });
                }
            })
        }
    }

    initCurWeekInfo(is_close?){
        //my performance
        let per_deposit = utils.getChildByPath(this.performanceNode, "bg.lab_deposit").getComponent(cc.Label);
        let per_bet = utils.getChildByPath(this.performanceNode, "bg.lab_bet").getComponent(cc.Label);
        let per_bonus = utils.getChildByPath(this.performanceNode, "bg.lab_bonus").getComponent(cc.Label);
        
        if(this.curweek_receive_data){
            per_deposit.string = "₱"+utils.formatNumberWithCommas(parseFloat(this.curweek_receive_data.deposit));
            per_bet.string = "₱"+utils.formatNumberWithCommas(parseFloat(this.curweek_receive_data.bet));
            per_bonus.string = "₱"+utils.formatNumberWithCommas(parseFloat(this.curweek_receive_data.bonus),0);
            if(is_close){
                per_deposit.string = "--";
                per_bet.string = "--";
                per_bonus.string = "--";
            }
        }
        //bonus 领取按钮隐藏
        let btn_claim = utils.getChildByPath(this.performanceNode, "btn_claim");
        btn_claim.active = false;
        //deposit和bet按钮居中显示
        let btn_deposit = utils.getChildByPath(this.performanceNode, "btn_deposit");
        let btn_bet = utils.getChildByPath(this.performanceNode, "btn_bet");
        btn_deposit.active = true;
        btn_bet.active = true;
        btn_deposit.getComponent(cc.Sprite).spriteFrame = this.depositBg[1];
        btn_bet.getComponent(cc.Sprite).spriteFrame = this.betBg[1];
        btn_deposit.x = -220;
        btn_bet.x = 220;
   }

    backToPromo(type?) {
        if(this.type == "banner"){
            let gamescene =  cc.director.getScene().getComponentInChildren(GameControl);
            gamescene.mTabbar.rein_home();
            this.node.destroy();
        }else{
            this.node.destroy();
            cc.director.emit("refresh_promo");
        }
    }

    clickWeeklyRule() {
        if (!Global.getInstance().token) {
            //未登录 跳转登录页
            uiManager.instance.showDialog(UI_PATH_DIC.phonePasswordLogin, [{ way: LOGIN_WAY.PhoneCode, curPromoDetail:true }])//curPromoDetail:true表示当前是活动详情页面
            return;
        }
        uiManager.instance.showDialog(UI_PATH_DIC.WeeklyRule,null,null,DEEP_INDEXZ.ACTIVITYS);
    }

    clickWeekBtns(event,userdata) {
        let content = this.scrollView.content;
        let sp_lastweek = utils.getChildByPath(content,"top.btn_lastweek.sp_lastweek").getComponent(cc.Sprite);
        let sp_curweek = utils.getChildByPath(content,"top.btn_curweek.sp_curweek").getComponent(cc.Sprite);
        let lab_lastweek = utils.getChildByPath(this.btnLastWeek, "lab_lastweek");
        let lab_ended = utils.getChildByPath(this.btnLastWeek, "lab_ended");
        let lab_curweek = utils.getChildByPath(this.btnCurWeek, "lab_curweek");
        let lab_ongoing = utils.getChildByPath(this.btnCurWeek, "lab_ongoing");
        if (parseInt(userdata) == 0) {
            this.btnLastWeek.zIndex = 1;
            this.btnCurWeek.zIndex = 0;
            cc.tween(this.lastWeekList).to(0.2, { x: 0 }).start();
            cc.tween(this.curWeekList).to(0.2, { x: 1080 }).start();
            sp_lastweek.spriteFrame = this.weeklyBtnBg[0];
            sp_curweek.spriteFrame = this.weeklyBtnBg[3];
            lab_lastweek.opacity = 255;
            lab_ended.opacity = 255;
            lab_curweek.opacity = 128;
            lab_ongoing.opacity = 128;
            this.initLastWeekInfo(this.isLastWeekClose);
        } else {
            this.btnLastWeek.zIndex = 0;
            this.btnCurWeek.zIndex = 1;
            cc.tween(this.lastWeekList).to(0.2, { x: -1080 }).start();
            cc.tween(this.curWeekList).to(0.2, { x: 0 }).start();
            sp_lastweek.spriteFrame = this.weeklyBtnBg[1];
            sp_curweek.spriteFrame = this.weeklyBtnBg[2];
            lab_lastweek.opacity = 128;
            lab_ended.opacity = 128;
            lab_curweek.opacity = 255;
            lab_ongoing.opacity = 255;
            this.initCurWeekInfo(this.isCurWeekClose);
        }
    }

    initUINode(){
        let content = this.scrollView.content;
        this.sp_lastweek = utils.getChildByPath(content,"top.btn_lastweek.sp_lastweek").getComponent(cc.Sprite);
        this.sp_curweek = utils.getChildByPath(content,"top.btn_curweek.sp_curweek").getComponent(cc.Sprite);
        this.lab_lastweek = utils.getChildByPath(this.btnLastWeek, "lab_lastweek");
        this.lab_ended = utils.getChildByPath(this.btnLastWeek, "lab_ended");
        this.lab_curweek = utils.getChildByPath(this.btnCurWeek, "lab_curweek");
        this.lab_ongoing = utils.getChildByPath(this.btnCurWeek, "lab_ongoing");
    }

    defaultShowLastWeek(){
        this.initUINode();
        this.btnLastWeek.zIndex = 1;
        this.btnCurWeek.zIndex = 0;
        this.lastWeekList.x = 0;
        this.curWeekList.x = 1080;
        this.sp_lastweek.spriteFrame = this.weeklyBtnBg[0];
        this.sp_curweek.spriteFrame = this.weeklyBtnBg[3];
        this.lab_lastweek.opacity = 255;
        this.lab_ended.opacity = 255;
        this.lab_curweek.opacity = 128;
        this.lab_ongoing.opacity = 128;
        this.initLastWeekInfo(this.isLastWeekClose);
    }

    defaultShowCurWeek(){
        this.initUINode();
        this.btnLastWeek.zIndex = 0;
        this.btnCurWeek.zIndex = 1;
        this.lastWeekList.x = -1080;
        this.curWeekList.x = 0;
        this.sp_lastweek.spriteFrame = this.weeklyBtnBg[1];
        this.sp_curweek.spriteFrame = this.weeklyBtnBg[2];
        this.lab_lastweek.opacity = 128;
        this.lab_ended.opacity = 128;
        this.lab_curweek.opacity = 255;
        this.lab_ongoing.opacity = 255;
        this.initCurWeekInfo(this.isCurWeekClose);
    }

    clickDepsoit(event,userdata) {
        Global.getInstance().loadWithdrawConfig(()=>{
            if(ALL_APP_SOURCE_CONFIG.channel == E_CHANEL_TYPE.G_CASH){
                //gcash 直接弹窗提示 是否跳转充值
                Global.getInstance().back_mini_buy_tips();
                return;
            }
            uiManager.instance.showDialog(UI_PATH_DIC.Deposit,null,null,DEEP_INDEXZ.DEPOSIT);                           
        },true);
    }

    clickgoBet(event,userdata) {
        if (!Global.getInstance().token) {
            //未登录 跳转登录页
            uiManager.instance.showDialog(UI_PATH_DIC.phonePasswordLogin, [{ way: LOGIN_WAY.PhoneCode }])
            return;
        }
        // 再跳转到游戏逻辑
        let gamescene =  cc.director.getScene().getComponentInChildren(GameControl);
        gamescene.mTabbar.clickGoBet(()=>{
            this.node.destroy();
        },'weekly');
    }

    showLoginView() {
        uiManager.instance.showDialog(UI_PATH_DIC.phonePasswordLogin, [{ way: LOGIN_WAY.PhoneCode, curPromoDetail:true }])
        this.weeklyPaydayView.getChildByName("needLoginBtn").active = !Global.getInstance().token;
    }

    title_layout_opacity(node:cc.ScrollView) {
        let opacity = node.getScrollOffset().y
        if (opacity > 255 ) opacity = 255;
        let layou = utils.getChildByPath(this.weeklyPaydayView,"titlelayout.bg");
        layou.opacity = opacity;
        // let btn_back = utils.getChildByPath(this.weeklyPaydayView,"titlelayout.btn_back.img");
        // let btn_rule = utils.getChildByPath(this.weeklyPaydayView,"titlelayout.btn_rule.img");
        // if (opacity > 0) {
        //     btn_back.color = cc.color(255-opacity/255*180,255-opacity/255*168,255-opacity/255*151);
        //     btn_rule.color = cc.color(255-opacity/255*180,255-opacity/255*168,255-opacity/255*151);
        // } else {
        //     btn_back.color = cc.color(255,255,255);
        //     btn_rule.color = cc.color(255,255,255);
        // }
    }

    initLayoutOpacity() {
        let layou = utils.getChildByPath(this.node,"titlelayout.bg");
        layou.opacity = 0;
        // let btn_back = utils.getChildByPath(this.node,"titlelayout.btn_back.img");
        // let btn_rule = utils.getChildByPath(this.node,"titlelayout.btn_rule.img");
        // btn_back.color = cc.color(255,255,255);
        // btn_rule.color = cc.color(255,255,255);
    }
}
