// Hall模块类型定义
export interface HallGameIcon {
  company_id: number;
  content: string;
  game_id: string | number;
  game_name: string;
  game_type: number; // 0代表没有房间的 1代表有房间列表的 2代表分类
  group: number;
  icon_url: string; // 图片的远程地址
  id: number;
  is_animation: boolean; // true是动画 false是静态图
  order_num: number;
  rule: string;
  status: number;
  tags?: number[]; // 游戏标签
  provider_name?: string; // 厂商名称
  min_bet?: number; // 最小投注
  max_bet?: number; // 最大投注
  rtp?: number; // 返还率
  popularity?: number; // 热度
}

export interface ConfigType {
  pos: { x: number; y: number };
  k: number;
  index: number;
}

export interface ConfigK {
  node: any;
  iindex: number; // 滑动到的index
  type: string; // 标识poker这种
}

export enum LiveTag {
  LIVE1,
  LIVE2,
  LIVE3
}

export enum LoginWay {
  PhoneCode,
  Password
}

// 游戏分类
export const GAME_TYPES = [
  'Casino',
  'Slots', 
  'Poker',
  'Bingo',
  'Arcade',
  'Sports',
  'Like',
  'History',
  'Baccarat',
  'Roulette',
  'Blackjack'
] as const;

export type GameType = typeof GAME_TYPES[number];

// 游戏标签
export const GAME_TAGS = {
  REGULAR: 0,
  HOT: 1,
  NEW: 2
} as const;

// 特殊游戏类型
export const SPECIAL_TYPE = {
  BACCARAT: 'baccarat',
  ROULETTE: 'roulette',
  BLACKJACK: 'blackjack'
} as const;

// 图标状态
export const ICON_STATUS = {
  NORMAL: 1,
  MAINTENANCE: 2
} as const;

// 交易记录类型
export const RECORD_TYPE = {
  ADJUSTMENT: 'Adjustment',
  MAYAPAY: 'mayapay',
  MAYA_WEB: 'mayawebpay',
  GCASH_WEB: 'gcashwebpay',
  DEPOSIT: 'Deposit',
  WITHDRAWAL: 'Withdrawal',
  BATCH_WITHDRAWAL: 'Batch Withdrawal',
  REWARD: 'Reward',
  TRANSFER: 'Transfer'
} as const;

// 筛选数据类型
export enum FilterData {
  TODAY,
  YESTERDAY,
  LAST_THREEDAYS,
  LAST_SEVENTDAYS
}

// 充值状态
export enum RechargeStatus {
  SUCCESS = 1, // 成功
  PENDING = 2, // 等待
  FAILURE = 3, // 失败
  OPERATIONAL = 4 // 人工审核
}

// 充值网页状态
export enum RechargeWebStatus {
  SUCCESS = 1, // 成功
  PENDING = 2, // 等待
  FAILED = 3, // 失败
  CANCEL = 4, // 取消
  WAITING = 5, // 等待处理
  OPERATION = 6, // 人工处理
  WAITING_PAYMENT = 7, // 等待支付
  WAITING_CHANGE_BALANCE = 8 // 等待余额变更
}

// 提现状态
export enum WithdrawStatus {
  PENDING = 1, // 待到账
  SUCCESS = 2, // 成功
  FAILURE = 3, // 失败
  OPERATIONAL = 4, // 人工审核
  PENDING_APPROVAL = 5 // 待审核
}

// 记录标签
export enum RecordTag {
  DEPOSIT = 1,
  WITHDRAW = 2,
  AWARD = 3
}

// 奖励更新类型
export enum AwardUpdateType {
  FIRST_RECHARGE = 12,
  FREE_REGISTRATION = 65,
  CASHBACK = 111,
  VIP_CASHBACK_119 = 119,
  BING_PHONE = 294,
  SIGN_UP_BONUS = 265,
  WEEKLY_SIGNIN = 310,
  DAILY_BETTING = 311
}

// 交易记录接口
export interface TransactionRecord {
  id: number;
  type: string;
  amount: number;
  status: number;
  created_at: string;
  updated_at: string;
  pay_channel?: string;
  quantity?: number;
  update_type?: number;
  description?: string;
  reference_id?: string;
  fee?: number;
  exchange_rate?: number;
  original_amount?: number;
  original_currency?: string;
}

// 游戏房间信息
export interface GameRoomInfo {
  id: string;
  name: string;
  min_bet: number;
  max_bet: number;
  players_count: number;
  max_players: number;
  is_vip: boolean;
  status: 'active' | 'maintenance' | 'full';
}

// 游戏规则信息
export interface GameRuleInfo {
  game_id: string;
  game_name: string;
  rules: string;
  how_to_play: string;
  payout_table?: Array<{
    combination: string;
    payout: number;
  }>;
  rooms: GameRoomInfo[];
}

// 游戏搜索参数
export interface GameSearchParams {
  keyword?: string;
  type?: GameType;
  provider?: string;
  tags?: number[];
  min_bet?: number;
  max_bet?: number;
  sort_by?: 'popularity' | 'name' | 'rtp' | 'newest';
  sort_order?: 'asc' | 'desc';
}

// 游戏筛选器
export interface GameFilter {
  types: GameType[];
  providers: string[];
  tags: number[];
  betRange: {
    min: number;
    max: number;
  };
  showFavorites: boolean;
  showRecent: boolean;
}

// 游戏统计信息
export interface GameStats {
  total_games: number;
  active_games: number;
  maintenance_games: number;
  new_games: number;
  hot_games: number;
  providers_count: number;
  categories_count: number;
}

// 用户游戏偏好
export interface UserGamePreferences {
  favorite_types: GameType[];
  favorite_providers: string[];
  recent_games: string[];
  liked_games: string[];
  bet_preferences: {
    min_bet: number;
    max_bet: number;
    auto_play: boolean;
  };
  display_preferences: {
    grid_size: 'small' | 'medium' | 'large';
    show_animations: boolean;
    show_sound: boolean;
  };
}

// 游戏启动参数
export interface GameLaunchParams {
  game_id: string;
  room_id?: string;
  demo_mode?: boolean;
  return_url?: string;
  language?: string;
  currency?: string;
}

// 游戏会话信息
export interface GameSession {
  session_id: string;
  game_id: string;
  user_id: string;
  start_time: string;
  end_time?: string;
  total_bet: number;
  total_win: number;
  profit: number;
  rounds_played: number;
  status: 'active' | 'completed' | 'disconnected';
}

// 快速开始配置
export interface QuickStartConfig {
  enabled: boolean;
  default_bet: number;
  auto_select_room: boolean;
  preferred_game_types: GameType[];
  skip_tutorial: boolean;
}

// 游戏推荐配置
export interface GameRecommendation {
  algorithm: 'popularity' | 'collaborative' | 'content_based' | 'hybrid';
  max_recommendations: number;
  include_new_games: boolean;
  include_similar_games: boolean;
  exclude_played_games: boolean;
  weight_factors: {
    popularity: number;
    rtp: number;
    user_preference: number;
    recent_activity: number;
  };
}

// 游戏加载状态
export interface GameLoadingState {
  game_id: string;
  status: 'loading' | 'ready' | 'error';
  progress: number;
  error_message?: string;
  estimated_time?: number;
}

// 游戏错误信息
export interface GameError {
  code: string;
  message: string;
  game_id?: string;
  timestamp: string;
  details?: any;
}

// 游戏事件
export interface GameEvent {
  type: 'start' | 'end' | 'bet' | 'win' | 'bonus' | 'error';
  game_id: string;
  session_id: string;
  timestamp: string;
  data: any;
}

// 游戏分析数据
export interface GameAnalytics {
  game_id: string;
  total_sessions: number;
  total_players: number;
  average_session_duration: number;
  total_bets: number;
  total_wins: number;
  house_edge: number;
  popularity_score: number;
  retention_rate: number;
  conversion_rate: number;
}

// 游戏配置
export interface GameConfig {
  auto_refresh_interval: number;
  max_concurrent_games: number;
  enable_sound: boolean;
  enable_animations: boolean;
  default_language: string;
  default_currency: string;
  cache_duration: number;
  preload_games: boolean;
  lazy_load_images: boolean;
}

// 游戏缓存策略
export interface GameCacheStrategy {
  cache_games: boolean;
  cache_duration: number;
  max_cache_size: number;
  preload_popular_games: boolean;
  cache_user_favorites: boolean;
  clear_cache_on_logout: boolean;
}

// 游戏性能指标
export interface GamePerformanceMetrics {
  load_time: number;
  render_time: number;
  memory_usage: number;
  fps: number;
  network_latency: number;
  error_rate: number;
}

// 游戏A/B测试配置
export interface GameABTestConfig {
  test_id: string;
  variant: 'A' | 'B';
  feature_flags: Record<string, boolean>;
  metrics_to_track: string[];
  start_date: string;
  end_date: string;
}

// 游戏本地化配置
export interface GameLocalization {
  language: string;
  currency: string;
  date_format: string;
  number_format: string;
  rtl_support: boolean;
  translations: Record<string, string>;
}

// 游戏无障碍配置
export interface GameAccessibility {
  high_contrast: boolean;
  large_text: boolean;
  screen_reader_support: boolean;
  keyboard_navigation: boolean;
  voice_commands: boolean;
  color_blind_support: boolean;
}
