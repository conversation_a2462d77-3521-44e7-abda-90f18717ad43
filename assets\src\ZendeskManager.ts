import { DEEP_INDEXZ, UI_PATH_DIC } from "./GlobalConstant";
import MoreGameManager from "./MoreGameManager";
import { uiManager } from "./mgr/UIManager";

export default class ZendeskManager {
    private static instanceObj: ZendeskManager = null;

    private constructor() {
        cc.director.on('ZendeskCloseWebWidget', this.ZendeskCloseWebWidget, this);
        cc.director.on('ZendeskOpenWebWidget', this.ZendeskOpenWebWidget, this);
    }

    private _isShowZendesk = false

    public set isShowZendesk(v: boolean) {
        this._isShowZendesk = v;
    }

    public get isShowZendesk(): boolean {
        return this._isShowZendesk
    }

    private _isNeedShowZendesk = false

    public set isNeedShowZendesk(v: boolean) {
        this._isNeedShowZendesk = v;
    }

    public get isNeedShowZendesk(): boolean {
        return this._isNeedShowZendesk
    }


    public static instance(): ZendeskManager {
        if (!ZendeskManager.instanceObj) {
            ZendeskManager.instanceObj = new ZendeskManager();
        }
        return ZendeskManager.instanceObj;
    }

    ZendeskCloseWebWidget() {
        this.isShowZendesk = false
    }

    ZendeskOpenWebWidget() {
        this.isShowZendesk = true
    }


    openZenDesk_Ori() {
        if (cc.sys.OS_ANDROID === cc.sys.os && cc.sys.isNative) {
            jsb.reflection.callStaticMethod("org/cocos2dx/javascript/AppActivity", "showMessaging", "()V");
        } else if (cc.sys.OS_IOS === cc.sys.os && cc.sys.isNative) {
        } else if (MoreGameManager.instance().isBrowserDevices()) {
            // 手机H5
            cc.director.emit("openZenDesk")
        } else {
            //PC网页
            cc.director.emit("openZenDesk")
        }
    }

    openZenDesk_V() {
        if (cc.sys.OS_ANDROID === cc.sys.os && cc.sys.isNative) {
            jsb.reflection.callStaticMethod("org/cocos2dx/javascript/AppActivity", "showMessaging", "()V");
        } else if (cc.sys.OS_IOS === cc.sys.os && cc.sys.isNative) {
        } else if (MoreGameManager.instance().isBrowserDevices()) {
            // 手机H5
            if (ZendeskManager.instance().isShowZendesk) return
            if (MoreGameManager.instance().isVertical()) {
                //竖屏
                cc.director.emit("ZendeskCloseWebWidget")
                cc.director.emit("openZenDesk")
            } else if (MoreGameManager.instance().isHorizontal()) {
                //横屏
                ZendeskManager.instance().isNeedShowZendesk = true
                uiManager.instance.showDialog(UI_PATH_DIC.ChangeOrientationTip,null,null,DEEP_INDEXZ.MAX)
            }
        } else {
            //PC网页
            cc.director.emit("openZenDesk")
        }
    }

}