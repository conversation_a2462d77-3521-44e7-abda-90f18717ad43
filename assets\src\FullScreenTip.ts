
import { DEEP_INDEXZ, UI_PATH_DIC } from "./GlobalConstant";
import Global from "./GlobalScript";
import MoreGameManager from "./MoreGameManager";
import UICommon from "./component/UICommon";
import { uiManager } from "./mgr/UIManager";

const { ccclass, property } = cc._decorator;

export function showFullScreenTip() {
    uiManager.instance.showDialog(UI_PATH_DIC.FullScreenTip,null,null,DEEP_INDEXZ.MAX)
}

@ccclass
export default class FullScreenTip extends UICommon {

    @property(cc.Node)
    ndTouch: cc.Node = null
    startPos = cc.v2(0, 0);

    swipeThreshold = 50
    onLoad() {
        // this.ndTouch.on(cc.Node.EventType.TOUCH_START, this.onTouchStart, this);
        // this.ndTouch.on(cc.Node.EventType.TOUCH_MOVE, this.onTouchMove, this);
        // this.ndTouch.on(cc.Node.EventType.TOUCH_END, this.onTouchEnd, this);
        // this.ndTouch.on(cc.Node.EventType.TOUCH_CANCEL, this.onTouchCancel, this);
        cc.game.on("screenResize", () => {
            this.resize()
        })
    }

    resize() {
        if (document.fullscreenElement) {
            this.hide()
        }
    }

    onDestroy() {
        cc.director.emit("DestroyQueuePopup")
    }

    onClickToFullScreen() {
        if (!document.fullscreenElement) {
            cc.view.enableAutoFullScreen(false)
            this.hide();
        }
    }

    // onTouchStart(event) {
    //     // 触摸开始时的处理
    //     // 可以记录起始点的位置等信息
    //     this.startPos = event.getLocation();
    //     if (!document.fullscreenElement) {
    //         cc.view.enableAutoFullScreen(true)
    //     }
    // }

    // onTouchMove(event) {
    //     // 触摸移动时的处理
    //     // 可以根据移动的距离和方向判断手势
    // }

    // onTouchEnd(event) {
    //     // 触摸结束时的处理
    //     // 可以判断手势的最终结果
    //     const endPos = event.getLocation();
    //     const deltaY = endPos.y - this.startPos.y;
    //     if (deltaY > this.swipeThreshold) {
    //         // 上划手势被触发
    //         // console.log("Swipe Up Detected!");
    //         // Global.getInstance().showSimpleTip("onTouchEnd ==> " + cc.view.isAutoFullScreenEnabled())
    //         cc.view.enableAutoFullScreen(true)
    //         this.scheduleOnce(() => {
    //             if (document.fullscreenElement) {
    //                 this.closeAction()
    //             }
    //         }, 0.1)
    //     }
    // }

    // onTouchCancel(event) {
    //     // 触摸取消时的处理
    //     // 可以在需要时进行一些清理工作
    // }


    closeAction() {
        cc.view.enableAutoFullScreen(false)
        this.hide();
    }

}
