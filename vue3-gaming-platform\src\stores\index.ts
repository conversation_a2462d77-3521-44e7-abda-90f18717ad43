import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { 
  UserInfo, 
  UserBalance, 
  AppConfig, 
  LoadingState, 
  NetworkState,
  DeviceInfo,
  Theme,
  Language,
  Notification,
  Toast,
  Modal
} from '@/types'

// 全局应用状态管理
export const useAppStore = defineStore('app', () => {
  // 状态
  const isInitialized = ref(false)
  const config = ref<AppConfig>({
    apiBaseUrl: import.meta.env.VITE_API_BASE_URL || '/api',
    wsUrl: import.meta.env.VITE_WS_URL || 'ws://localhost:8080/ws',
    version: '1.0.0',
    environment: (import.meta.env.MODE as any) || 'development',
    features: {
      wallet: true,
      activities: true,
      leaderboard: true,
      notifications: true
    }
  })
  
  const loading = ref<LoadingState>({
    global: false
  })
  
  const network = ref<NetworkState>({
    online: navigator.onLine
  })
  
  const device = ref<DeviceInfo>({
    isMobile: false,
    isTablet: false,
    isDesktop: true,
    platform: navigator.platform,
    userAgent: navigator.userAgent,
    screenWidth: window.innerWidth,
    screenHeight: window.innerHeight
  })
  
  const currentTheme = ref<Theme>({
    name: 'default',
    colors: {
      primary: '#667eea',
      secondary: '#764ba2',
      success: '#4CAF50',
      warning: '#FF9800',
      error: '#F44336',
      info: '#2196F3',
      background: '#f5f5f5',
      surface: '#ffffff',
      text: '#333333'
    },
    fonts: {
      primary: 'Inter, sans-serif',
      secondary: 'Roboto, sans-serif'
    }
  })
  
  const currentLanguage = ref<Language>({
    code: 'zh-CN',
    name: '简体中文',
    flag: '🇨🇳'
  })
  
  const notifications = ref<Notification[]>([])
  const toasts = ref<Toast[]>([])
  const modals = ref<Modal[]>([])

  // 计算属性
  const isLoading = computed(() => {
    return Object.values(loading.value).some(Boolean)
  })
  
  const unreadNotifications = computed(() => {
    return notifications.value.length
  })
  
  const isMobileDevice = computed(() => {
    return device.value.isMobile || device.value.screenWidth < 768
  })

  // 方法
  function setInitialized(initialized: boolean) {
    isInitialized.value = initialized
  }
  
  function setLoading(key: string, value: boolean) {
    loading.value[key] = value
  }
  
  function setGlobalLoading(value: boolean) {
    loading.value.global = value
  }
  
  function updateNetworkStatus(online: boolean) {
    network.value.online = online
  }
  
  function updateDeviceInfo() {
    device.value = {
      ...device.value,
      screenWidth: window.innerWidth,
      screenHeight: window.innerHeight,
      isMobile: window.innerWidth < 768,
      isTablet: window.innerWidth >= 768 && window.innerWidth < 1024,
      isDesktop: window.innerWidth >= 1024
    }
  }
  
  function setTheme(theme: Partial<Theme>) {
    currentTheme.value = { ...currentTheme.value, ...theme }
    // 应用主题到CSS变量
    applyThemeToCSS(currentTheme.value)
  }
  
  function setLanguage(language: Language) {
    currentLanguage.value = language
    // 这里可以添加语言切换逻辑
  }
  
  function addNotification(notification: Omit<Notification, 'id'>) {
    const id = Date.now().toString()
    notifications.value.push({ ...notification, id })
    
    // 自动移除通知
    if (notification.duration && notification.duration > 0) {
      setTimeout(() => {
        removeNotification(id)
      }, notification.duration)
    }
  }
  
  function removeNotification(id: string) {
    const index = notifications.value.findIndex(n => n.id === id)
    if (index > -1) {
      notifications.value.splice(index, 1)
    }
  }
  
  function clearNotifications() {
    notifications.value = []
  }
  
  function showToast(message: string, type: Toast['type'] = 'info', duration = 3000) {
    const id = Date.now().toString()
    const toast: Toast = { id, message, type, duration }
    
    toasts.value.push(toast)
    
    setTimeout(() => {
      removeToast(id)
    }, duration)
  }
  
  function removeToast(id: string) {
    const index = toasts.value.findIndex(t => t.id === id)
    if (index > -1) {
      toasts.value.splice(index, 1)
    }
  }
  
  function showModal(component: any, props?: Record<string, any>, options?: Modal['options']) {
    const id = Date.now().toString()
    const modal: Modal = { id, component, props, options }
    
    modals.value.push(modal)
    return id
  }
  
  function closeModal(id?: string) {
    if (id) {
      const index = modals.value.findIndex(m => m.id === id)
      if (index > -1) {
        modals.value.splice(index, 1)
      }
    } else {
      // 关闭最后一个模态框
      modals.value.pop()
    }
  }
  
  function closeAllModals() {
    modals.value = []
  }
  
  function applyThemeToCSS(theme: Theme) {
    const root = document.documentElement
    Object.entries(theme.colors).forEach(([key, value]) => {
      root.style.setProperty(`--color-${key}`, value)
    })
    Object.entries(theme.fonts).forEach(([key, value]) => {
      root.style.setProperty(`--font-${key}`, value)
    })
  }
  
  function updateConfig(newConfig: Partial<AppConfig>) {
    config.value = { ...config.value, ...newConfig }
  }

  return {
    // 状态
    isInitialized,
    config,
    loading,
    network,
    device,
    currentTheme,
    currentLanguage,
    notifications,
    toasts,
    modals,
    
    // 计算属性
    isLoading,
    unreadNotifications,
    isMobileDevice,
    
    // 方法
    setInitialized,
    setLoading,
    setGlobalLoading,
    updateNetworkStatus,
    updateDeviceInfo,
    setTheme,
    setLanguage,
    addNotification,
    removeNotification,
    clearNotifications,
    showToast,
    removeToast,
    showModal,
    closeModal,
    closeAllModals,
    updateConfig
  }
})

// 用户状态管理
export const useUserStore = defineStore('user', () => {
  // 状态
  const isLoggedIn = ref(false)
  const userInfo = ref<UserInfo | null>(null)
  const balance = ref<UserBalance>({
    total: 0,
    available: 0,
    frozen: 0,
    currency: 'PHP',
    last_updated: new Date().toISOString()
  })
  const token = ref<string>('')
  const permissions = ref<string[]>([])
  const preferences = ref<Record<string, any>>({})

  // 计算属性
  const formattedBalance = computed(() => {
    if (balance.value.total >= 9999999999) {
      return '9,999,999,999.00'
    }
    return balance.value.total.toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    })
  })
  
  const userLevel = computed(() => {
    return userInfo.value?.vip_level || 0
  })
  
  const hasPermission = computed(() => {
    return (permission: string) => permissions.value.includes(permission)
  })

  // 方法
  function setLoginStatus(status: boolean) {
    isLoggedIn.value = status
    if (!status) {
      // 登出时清除用户数据
      userInfo.value = null
      token.value = ''
      permissions.value = []
      resetBalance()
    }
  }
  
  function setUserInfo(info: UserInfo) {
    userInfo.value = info
    isLoggedIn.value = true
  }
  
  function updateBalance(newBalance: Partial<UserBalance>) {
    balance.value = { 
      ...balance.value, 
      ...newBalance,
      last_updated: new Date().toISOString()
    }
  }
  
  function resetBalance() {
    balance.value = {
      total: 0,
      available: 0,
      frozen: 0,
      currency: 'PHP',
      last_updated: new Date().toISOString()
    }
  }
  
  function setToken(newToken: string) {
    token.value = newToken
    if (newToken) {
      localStorage.setItem('auth_token', newToken)
    } else {
      localStorage.removeItem('auth_token')
    }
  }
  
  function setPermissions(newPermissions: string[]) {
    permissions.value = newPermissions
  }
  
  function updatePreferences(newPreferences: Record<string, any>) {
    preferences.value = { ...preferences.value, ...newPreferences }
    localStorage.setItem('user_preferences', JSON.stringify(preferences.value))
  }
  
  function loadFromStorage() {
    const savedToken = localStorage.getItem('auth_token')
    const savedUserInfo = localStorage.getItem('user_info')
    const savedPreferences = localStorage.getItem('user_preferences')
    
    if (savedToken) {
      token.value = savedToken
    }
    
    if (savedUserInfo) {
      try {
        const info = JSON.parse(savedUserInfo)
        setUserInfo(info)
      } catch (error) {
        console.error('Failed to parse saved user info:', error)
      }
    }
    
    if (savedPreferences) {
      try {
        preferences.value = JSON.parse(savedPreferences)
      } catch (error) {
        console.error('Failed to parse saved preferences:', error)
      }
    }
  }
  
  function clearStorage() {
    localStorage.removeItem('auth_token')
    localStorage.removeItem('user_info')
    localStorage.removeItem('user_preferences')
  }
  
  function logout() {
    setLoginStatus(false)
    clearStorage()
  }

  return {
    // 状态
    isLoggedIn,
    userInfo,
    balance,
    token,
    permissions,
    preferences,
    
    // 计算属性
    formattedBalance,
    userLevel,
    hasPermission,
    
    // 方法
    setLoginStatus,
    setUserInfo,
    updateBalance,
    resetBalance,
    setToken,
    setPermissions,
    updatePreferences,
    loadFromStorage,
    clearStorage,
    logout
  }
})

// 导出所有store
export * from './hallStore'
export * from './walletStore'
