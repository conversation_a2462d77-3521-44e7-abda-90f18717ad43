import UICommon from "./component/UICommon";
import { DEEP_INDEXZ, E_TRANSACTION_TYPE, MAINTENANCETIPCODE, PASS_TYPE, PRODUCT_TYPE, UI_PATH_DIC } from "./GlobalConstant";
import Global from "./GlobalScript";
import { showPhonePassword } from "./hall/PhonePassword";
import { showMaintenancetip } from "./Maintenancetip";
import { uiManager } from "./mgr/UIManager";
import HttpUtils from "./net/HttpUtils";
import { ShowSecurityRequirement } from "./SecurityRequirements";
import utils from "./utils/utils";
import { showWalletLimitPop } from "./WalletLimitPop";

const { ccclass, property } = cc._decorator;


@ccclass
export default class ShopItemDetail extends UICommon {

    @property(cc.Label)
    lbName: cc.Label = null;

    // @property(cc.Label)
    // lbDesc: cc.Label = null;

    @property(cc.Label)
    lbPrice: cc.Label = null;
    @property(cc.Label)
    lbTotalRedeem: cc.Label = null;

    @property(cc.Label)
    lbCount: cc.Label = null;

    @property(cc.Label)
    lbAccount: cc.Label = null;

    @property(cc.Label)
    lbNoAccount: cc.Label = null;

    @property(cc.Node)
    btnSelect: cc.Node = null;

    @property(cc.Button)
    inputBtn: cc.Button = null;

    @property(cc.Node)
    ndHelp: cc.Node = null;

    @property(cc.Sprite)
    moneySprite: cc.Sprite = null;

    @property(cc.Sprite)
    iconSprite: cc.Sprite = null;

    // @property(cc.Prefab)
    // explainViewPrefab: cc.Prefab = null;
    // @property(cc.Prefab)
    // chooseWalletPfb: cc.Prefab = null;
    // @property(cc.Prefab)
    // chooseBankCardPfb: cc.Prefab = null;

    // @property(cc.Label)
    // lbDeliverBank: cc.Label = null;

    count = 1;
    itemDetail: any = null;

    GcashCount: string = null;

    @property(cc.SpriteFrame)
    typeIcons: cc.SpriteFrame[] = []
    idAccount: any;
    bankName: string = "";

    onLoad() {
        this.preLoadPrefab()
    }
    preLoadPrefab() {
        let prefabs = [
            // UI_PATH_DIC.AccountList,
            // UI_PATH_DIC.SecurityRequirement1,
            // UI_PATH_DIC.SecurityRequirement2,
            // UI_PATH_DIC.SecurityRequirement3,
        ]
        cc.resources.preload(prefabs)
    }
    start() {
        cc.director.on('select_account', this.selectAccount, this);
        cc.director.on('phoneBindSuc', this.onPhoneBindSuc, this);
        cc.director.on('password_suc', this.onPasswordVerified, this);
        cc.director.on('modifyWithdrawNoSuc', this.showCurrentBankCard, this);

        this.show();
    }
    onDestroy() {
        cc.director.off('select_account', this.selectAccount, this);
        cc.director.off('phoneBindSuc', this.onPhoneBindSuc, this);
        cc.director.off('password_suc', this.onPasswordVerified, this);
        cc.director.off('modifyWithdrawNoSuc', this.showCurrentBankCard, this);

    }
    closeAction() {
        this.hide();
    }

    init(data) {
        cc.log("NewShopDetail ====>", data)
        // this.lbName.string = data.data.name;
        this.lbName.string = Global.getInstance().getCountryCurrency() + " " + data.data.price
        // this.lbDesc.string = data.data.description;
        this.lbCount.string = this.count.toString();
        // this.ndHelp.active = !!data.data.desc_images
        let url = data.domain + data.data.images;


        this.itemDetail = data;
        Global.getInstance().loadImgFromUrl(url, this.moneySprite);
        this.updateTotalLabel();
        this.iconSprite.spriteFrame = this.typeIcons[data.data.product_type]
        if (data.data.product_type == PRODUCT_TYPE.ITEM) {//实物商品隐藏输入账号
            this.inputBtn.node.active = false;
            // this.tipsNode.active = true;
        } else {
            this.inputBtn.node.active = true;
            // this.tipsNode.active = false;
        }
        this.showAccount()
    }
    // onClickExplain() {
    //     let t = cc.instantiate(this.explainViewPrefab);
    //     t.parent = this.node;
    //     let s = t.getComponent(ExplanationTip);
    //     let url = this.itemDetail.domain + this.itemDetail.data.desc_images;
    //     s.init({ description: this.itemDetail.data.description, imgUrl: url })
    // }
    addCount() {
        this.count++;
        this.lbCount.string = this.count.toString();
        this.updateTotalLabel();
    }
    showAccount() {
        if (this.itemDetail.data.product_type == PRODUCT_TYPE.MAYA_MINI) {
            this.inputBtn.interactable = false;
            this.btnSelect.active = false;
        } else {
            this.inputBtn.interactable = true;
            this.btnSelect.active = true;
        }
        HttpUtils.getInstance().post(3, 3, this, "api/get/default/account", {
            token: Global.getInstance().token,
            product_type: this.itemDetail.data.product_type
        }, (response) => {
            if (response.data.account_no) {
                this.lbAccount.string = response.data.account_no;
                this.lbNoAccount.node.active = !this.lbAccount.string
                this.idAccount = response.data.account_id
                this.GcashCount = response.data.account_no;
                this.bankName = response.data.account_name || '';
                // this.lbDeliverBank.string = "Deliver to " + this.bankName
            } else {
                this.lbNoAccount.node.active = true
            }
        });
    }
    reduceCount() {
        if (this.count == 1) {
            return;
        }
        this.count--;
        this.lbCount.string = this.count.toString();
        this.updateTotalLabel();
    }

    updateTotalLabel() {
        this.lbPrice.string = utils.numFormat(this.count * this.itemDetail.data.price, 0)
        this.lbTotalRedeem.string = Global.getInstance().getCountryCurrency() + " " + utils.numFormat(this.count * this.itemDetail.data.exchange, 0)
    }

    onConfirmBtnClick() {
        let userData = Global.getInstance().userdata;
        if (!userData.phone || !userData.withdraw_password || !this.GcashCount) {
            let data = {
                type: (Global.getInstance().mayaMode || this.itemDetail.data.product_type == PRODUCT_TYPE.CASH) ? 2 : 3,
                redeemType: this.itemDetail.data.product_type,
                redeemAccount: this.GcashCount,
                title: ''
            }
            ShowSecurityRequirement(data)
            return;
        }

        HttpUtils.getInstance().get(3, 3, this, "/common/api/check/balance", {
            token: Global.getInstance().token,
            price: this.itemDetail.data.price,
            quantity: this.count
        },(response) => {
            // showEnterPassword(PASS_TYPE.RedeemSuc)
            uiManager.instance.showDialog(UI_PATH_DIC.Password, [PASS_TYPE.RedeemSuc],null,DEEP_INDEXZ.PASSWORD)
        }, (err) => {
            if (err.code == MAINTENANCETIPCODE) { //服务器维护
                showMaintenancetip(err.msg)
            }
            else if (err.code == "1") {
                Global.getInstance().showSimpleTip(err.msg)
            } else if (err.code == "102034") {
                let str = ''
                let conf = "OK"
                if (Global.getInstance().gcashMode) {
                    str = "Your GCash wallet has reached limit. Please contact GCash customer service to increase limit."
                } else if (Global.getInstance().mayaMode) {
                    str = Global.getInstance().getLabel("php_code_" + err.code) //"Your Maya wallet has reached limit. Please contact Maya customer service to increase limit."
                } else {
                    str = Global.getInstance().getLabel("php_code_" + err.code)
                    conf = "Buy Coins"
                }

                Global.getInstance().showCommonTip2({ word: str, confirm: conf }, this, false, () => {
                    this.hide()
                    Global.getInstance().showShop(0, true)
                });
            } else {
                Global.getInstance().showSimpleTip(Global.getInstance().getLabel("php_code_" + err.code));
            }
        });
    }
    onPasswordVerified(data) {
        if (data.passType == PASS_TYPE.RedeemSuc) {
            this.reqExchange();
        }
    }
    onPhoneBindSuc(data) {
        this.showAccount()
    }
    onClickWallet() {
        uiManager.instance.showDialog(UI_PATH_DIC.AccountList, [{
            account: this.GcashCount,
            fundType: this.itemDetail.data.product_type
        }]);//未用到
    }
    showAccountList() {
        if (this.itemDetail.data.product_type == PRODUCT_TYPE.ITEM || this.itemDetail.data.product_type == PRODUCT_TYPE.CASH) {
            if (!this.GcashCount) {
                showPhonePassword()
            }
        } else if (this.itemDetail.data.product_type == PRODUCT_TYPE.MAYA
            || this.itemDetail.data.product_type == PRODUCT_TYPE.BANK_CARD
            || this.itemDetail.data.product_type == PRODUCT_TYPE.GCASH
            || this.itemDetail.data.product_type == PRODUCT_TYPE.GRAB_PAY
            || this.itemDetail.data.product_type == PRODUCT_TYPE.MAYA_MINI) {
            HttpUtils.getInstance().post(3, 3, this, "/api/get/pay/code/list", {
                token: Global.getInstance().token,
                product_type: this.itemDetail.data.product_type
            }, (response) => {
                if (response.data.length == 0) return console.log(response.msg)
                uiManager.instance.showDialog(UI_PATH_DIC.AccountList, [{
                    account: this.GcashCount,
                    fundType: this.itemDetail.data.product_type
                }])//未用到
            });
        } else {
            console.log("error in pay methods")
        }
    }
    showCurrentBankCard(data) {
        if (!data.account_id || !data.account_no) return console.error("account info error");
        this.idAccount = data.account_id;
        this.lbAccount.string = data.account_no;
        this.lbNoAccount.node.active = !this.lbAccount.string
        this.GcashCount = data.account_no
        // this.lbDeliverBank.string = "Deliver to " + this.bankName
    }

    selectAccount(data) {
        this.GcashCount = data.account_no;
        this.idAccount = data.account_id
        this.lbAccount.string = data.account_no;
        this.lbNoAccount.node.active = !this.lbAccount.string
        this.bankName = data.withdraw_channel_name || ""
        // this.lbDeliverBank.string = "Deliver to " + this.bankName
    }

    reqExchange() {
        // Global.getInstance().showLoading("req_exchange");
        HttpUtils.getInstance().post(3, 3, this, "/api/exchange", {
            id: this.itemDetail.data.id,
            token: Global.getInstance().token,
            product_type: this.itemDetail.data.product_type,
            g_cash_no: this.GcashCount || "",
            quantity: this.count,
            account_id: this.idAccount || "",
        }, (response) => {
            if (Global.getInstance().mayaMode) {
                uiManager.instance.showDialog(UI_PATH_DIC.TipsRedeemSuc)//未用到
            } else {
                Global.getInstance().showCommonTip("Your redemption request has been submitted successfully. You will receive in your account within 10 minutes.", this, true)
            }
            this.hide()
            Global.getInstance().updateBalanceAfterGame()
            // showExchangeSucceed(itemdata)
        }, (err) => {
            if (err.code == MAINTENANCETIPCODE) { //服务器维护
                showMaintenancetip(err.msg)
            }
            else if (err.code == "1") {
                Global.getInstance().showCommonTip(err.msg, this, true)
            } else if (err.code == "105104") {
                if (Global.getInstance().mayaMode) {
                    showWalletLimitPop(PRODUCT_TYPE.MAYA, E_TRANSACTION_TYPE.redeem, this.itemDetail.data.price)
                }
            } else {
                Global.getInstance().showCommonTip(Global.getInstance().getLabel("php_code_" + err.code), this, true);
            }
            this.hide();
        });
    }
}
