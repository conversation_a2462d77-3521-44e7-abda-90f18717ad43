import MoreGameManager from "../MoreGameManager";
import UICommon from "../component/UICommon";
import Global from "../GlobalScript";
import List from "../listView/List";
import {GlobalEnum} from "../room/GlobalEnum";
import { uiManager } from "../mgr/UIManager";
import { DEEP_INDEXZ, OPEN_BROADCAST_TYPE, UI_PATH_DIC } from "../GlobalConstant";
import utils from "../utils/utils";
import RoundRectMask from "../RoundRectMask";
import GameControl from "../GameControl";

const {ccclass, property} = cc._decorator;

@ccclass
export default class AllGameView extends UICommon {

    @property(cc.PageView)
    pageView: cc.PageView = null;

    @property(cc.ScrollView)
    tagScroll: cc.ScrollView = null;

    @property(cc.ScrollView)
    specialTagScroll: cc.ScrollView = null;

    @property(cc.Node)
    gameScrollModel: cc.Node = null;

    @property(cc.Node)
    gameScrollModel2: cc.Node = null;
    
    @property(cc.Node)
    btnFilters: cc.Node = null;

    @property([cc.SpriteFrame])
    btnFiltersSpFrames: cc.SpriteFrame[] = [];

    @property(cc.Node)
    itemRender: cc.Node = null

    @property([cc.Button])
    specialTagBtns: cc.Button[] = [];
    
    @property(cc.PageView)
    specialPageView: cc.PageView = null;

    @property(cc.Node)
    specialNode: cc.Node = null;

    @property(cc.ScrollView)
    gameTypeScroll: cc.ScrollView = null;

    @property(cc.Node)
    searchBgNode: cc.Node = null;

    @property([cc.SpriteFrame])
    btnSearchSpFrames: cc.SpriteFrame[] = [];

    @property(cc.EditBox)
    editBoxSearch: cc.EditBox = null;

    @property(cc.Node)
    typeAnimBg: cc.Node = null;

    @property(cc.Node)
    specTypeAnimBg: cc.Node = null;

    lastReshLocation = 0;
    currentGameType = null;
    specialGameType = null;
    gameListData = [];
    tagBtns = [];
    gameScroll = [];
    allPages: cc.Node[] = [];
    currentType = null;
    filterResultList = [];
    itemPrefab: cc.Prefab = null;
    bigItemPrefab: cc.Prefab = null;
    filterPrefab: cc.Prefab = null;
    filterText: string = "";
    historyList = null;
    homeCb = null;
    currentGameListPage = 0;
    pageSizeDefine = 200;

    maint_hide_list = '';//隐藏和维护的列表string  用来实时更新

    GAME_TAGS = {
        "REGULAR": 0,
        "HOT": 1,
        "NEW": 2
    }

    SPECIAL_TYPE = {
        BACCARAT: "baccarat",
        ROULETTE: "roulette",
        BLACKJACK: "blackjack"
    }

    typeList = ["Casino","Slots","Poker","Bingo","Arcade","Sports","Like","History","Baccarat","Roulette","Blackjack"]
    typeIdList = [
              GlobalEnum.GAME_TYPE.CASINO,
              GlobalEnum.GAME_TYPE.SLOTS,
              GlobalEnum.GAME_TYPE.POKER,
              GlobalEnum.GAME_TYPE.BINGO,
              GlobalEnum.GAME_TYPE.FISHING,
              GlobalEnum.GAME_TYPE.SPORTS,
              "Like",
              "History",
              GlobalEnum.GAME_TYPE.CASINO,
              GlobalEnum.GAME_TYPE.CASINO,
              GlobalEnum.GAME_TYPE.CASINO
            ]

    provider = [];//游戏供应商

    openmark = null;//打开入口标记

    onLoad () {
        this.pageView.node.on('page-turning', this.turingPageListener, this);
        this.specialPageView.node.on('page-turning', this.turingSpecialPageListener, this);
        cc.director.on("Multi_Toggle_Provider_Result",this.filterGameList,this);
        cc.director.on('updateLikeState', this.updateLikeList, this);
        cc.director.on('ShowMoreGameEvent', this.ShowMoreGameEvent, this);
        cc.director.on('CloseMoreGameEvent', this.CloseMoreGameEvent, this);
        cc.director.on('refresh_gamelist', this.refresh_gamelist, this);
        this.maint_hide_list = JSON.stringify(Global.getInstance().all_hide_game_list);
        this.initData();
    }
    protected onEnable(): void {
        let btn_spin = Global.getInstance().popNode.getChildByName("btn_spin");
        if (btn_spin) btn_spin.zIndex = 1;
        cc.director.emit('check_show_spin')
    }
    init(args) {
        if(args){
            this.openmark = args;
        }
    }
    start () {
        let allgamepageview = this.node.getChildByName("allgamepageview");
        let tview = allgamepageview.getChildByName("view");
        let tconent = allgamepageview.getComponent(cc.PageView);
        allgamepageview.height = allgamepageview.height + cc.winSize.height - cc.view.getDesignResolutionSize().height;
        tview.height = tview.height + cc.winSize.height - cc.view.getDesignResolutionSize().height;
        this.specialNode.height = this.specialNode.height +  cc.winSize.height - cc.view.getDesignResolutionSize().height;
        let spgamepageview = this.specialNode.getChildByName("spgamepageview");
        let sptview = spgamepageview.getChildByName("view");
        spgamepageview.height = spgamepageview.height +  cc.winSize.height - cc.view.getDesignResolutionSize().height;
        sptview.height = sptview.height +  cc.winSize.height - cc.view.getDesignResolutionSize().height;
        for (let k = 0; k < this.typeList.length; k++) {
            let lview = this.gameScroll[k].node.getChildByName("view");
            this.gameScroll[k].node.height = this.gameScroll[k].node.height + cc.winSize.height - cc.view.getDesignResolutionSize().height;
            this.allPages[k].height = this.allPages[k].height + cc.winSize.height - cc.view.getDesignResolutionSize().height;
            lview.height = lview.height + cc.winSize.height - cc.view.getDesignResolutionSize().height;
        }
        this.initTagBtns(0);
        // this.initGameListDataByFilter();
        // this.refreshHistoryListList("Like");
        // this.refreshHistoryListList("History");

        this.updateLikeList();


        // this.currentGameListPage = 0;
        // let gameList = MoreGameManager.instance().getAllThirdGameData();
        // if ((gameList.length>0 && gameList.length%this.pageSizeDefine==0) || gameList.length <= 0) {
        //     Global.getInstance().showLoading("GAME_LIST");
        //     this.reqNextPage();
        // } else {
        //     this.initGameListDataByFilter();
        //     this.refreshHistoryList();
        //     this.prepareShowListView();  
        // }
        cc.director.emit('hideHallLive');
        //隐藏首页
        let gamescene =  cc.director.getScene().getComponentInChildren(GameControl);
        gamescene.mTabbar.hideHallview();
        //隐藏首存红包入口按钮
        Global.getInstance().hideEnvelopeBtn();
    }
    ShowMoreGameEvent() {
        this.node.active = false
    }
    CloseMoreGameEvent() {
        this.node.active = true
        // this.showTargetListView(1)
    }
    onDestroy(): void {
        cc.director.off("Multi_Toggle_Provider_Result",this.filterGameList,this);
        cc.director.off('updateLikeState', this.updateLikeList, this);
        cc.director.off('ShowMoreGameEvent', this.ShowMoreGameEvent, this);
        cc.director.off('CloseMoreGameEvent', this.CloseMoreGameEvent, this);
        cc.director.off('refresh_gamelist', this.refresh_gamelist, this);
        this.filterText = "";
        if (this.homeCb) {
            this.homeCb();
        }
    }

    initData() {
        let gameTypes = Global.getInstance().getGameType();
        for (let index = 0; index < gameTypes.length; index++) {
            let element = gameTypes[index];
            let str = element.game_type;
            if (str && str.length > 0) {
                let type = str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
                this.typeList[index] = type;
                this.typeIdList[index] = element.id;
            }
        }

        let content = this.gameTypeScroll.content;
        for (let index = 0; index <  this.typeList.length; index++) {
            let element =  this.typeList[index];
            if(index == 0){
                this.currentGameType = element.toLowerCase()
            }
            let node = content.getChildByName(element.toLocaleLowerCase());
            if (node) {
                node.position = new cc.Vec3(150+index*250,0);
                let btn = node.getChildByName("btn_"+element.toLocaleLowerCase());
                if (btn) {
                    this.tagBtns[index] = btn.getComponent(cc.Button);
                }
            }
            
            let model = cc.instantiate(this.gameScrollModel);
            model.position = new cc.Vec3(0,0);
            let norecord = model.getChildByName("norecord");
            let noresult = model.getChildByName("noresult");
            let btn_baccarat = model.getChildByName("btn_baccarat");
            let btn_roulette = model.getChildByName("btn_roulette");
            let btn_blackjack = model.getChildByName("btn_blackjack");
            let game_list = model.getChildByName("game_list");
            if (element == "Casino") {
                model = cc.instantiate(this.gameScrollModel2);
                model.position = new cc.Vec3(0,0);
                // this.currentGameType = 'casino'
                let game_list2 = model.getChildByName("game_list2");
                norecord = utils.getChildByPath(game_list2,"view.content.norecord");
                noresult = utils.getChildByPath(game_list2,"view.content.noresult");
                btn_baccarat = utils.getChildByPath(game_list2,"view.content.btn_baccarat");
                btn_roulette = utils.getChildByPath(game_list2,"view.content.btn_roulette");
                btn_blackjack = utils.getChildByPath(game_list2,"view.content.btn_blackjack");
                game_list = utils.getChildByPath(game_list2,"view.content.game_list");

                btn_baccarat.active = true;
                btn_roulette.active = true;
                btn_blackjack.active = true;
                // game_list.height = 800;
                // view.height = 800;
                game_list.position = new cc.Vec3(0,-355);

                let parentScrollView = game_list2.getComponent(cc.ScrollView);
                let lview = parentScrollView.node.getChildByName("view");
                parentScrollView.node.height = parentScrollView.node.height + cc.winSize.height - cc.view.getDesignResolutionSize().height;
                parentScrollView.content.height = parentScrollView.content.height + cc.winSize.height - cc.view.getDesignResolutionSize().height;
                lview.height = lview.height + cc.winSize.height - cc.view.getDesignResolutionSize().height;

                let gameScrollView = game_list.getComponent(cc.ScrollView);
                let onInnerScroll = (event) => {
                    if (parentScrollView.getScrollOffset().y < parentScrollView.getMaxScrollOffset().y-5) {
                        let endOffset = parentScrollView.getScrollOffset();
                        let newOffset = new cc.Vec2(endOffset.x,endOffset.y+event.getDelta().y);
                        parentScrollView.scrollToOffset(newOffset);
                        gameScrollView.enabled = false;
                        if (gameScrollView.getScrollOffset().y >= 5) {
                            gameScrollView.enabled = true;
                        } 
                    } else {
                        gameScrollView.enabled = true;
                        if (gameScrollView.getScrollOffset().y <= 5) {
                            let endOffset = parentScrollView.getScrollOffset();
                            let newOffset = new cc.Vec2(endOffset.x,endOffset.y+event.getDelta().y);
                            parentScrollView.scrollToOffset(newOffset);
                        }
                    } 
                }
                game_list.on(cc.Node.EventType.TOUCH_MOVE, onInnerScroll, this);
                game_list2.on(cc.Node.EventType.TOUCH_MOVE, onInnerScroll, this);
                this.pageView.addPage(model);
                this.gameScroll[index] = game_list.getComponent(List);
                this.allPages[index] = model;
            } else if (element == "Baccarat") {
                let page = this.specialPageView.content.getChildByName("page_9");
                this.gameScroll[index] = page.getChildByName("game_list").getComponent(List);
                this.allPages[index] = page;
            } else if (element == "Roulette") {
                let page = this.specialPageView.content.getChildByName("page_10");
                this.gameScroll[index] = page.getChildByName("game_list").getComponent(List);
                this.allPages[index] = page;
            } else if (element == "Blackjack") {
                let page = this.specialPageView.content.getChildByName("page_11");
                this.gameScroll[index] = page.getChildByName("game_list").getComponent(List);
                this.allPages[index] = page;
            } else {
                this.pageView.addPage(model);
                this.gameScroll[index] = game_list.getComponent(List);
                this.allPages[index] = model;
            }
            
        }
    }
    reqNextPage() {
        let gameList = MoreGameManager.instance().getAllThirdGameData();
        if ((gameList.length==this.currentGameListPage*this.pageSizeDefine && gameList.length%this.pageSizeDefine==0) || gameList.length <= 0) {
            this.currentGameListPage = this.currentGameListPage+1;
            let params = {
                token: Global.getInstance().token,
                page: this.currentGameListPage,
                page_size:this.pageSizeDefine
            };
            MoreGameManager.instance().doQueryMoreGameList(()=>{
                if(!this.node || !this.node.isValid) {
                    return;
                }
                this.initGameListDataByFilter();
                let gameList = MoreGameManager.instance().getAllThirdGameData();
                if (gameList.length==this.currentGameListPage*this.pageSizeDefine && gameList.length%this.pageSizeDefine==0) {
                    this.reqNextPage();
                    this.prepareShowListView();  
                } else {
                    this.refreshHistoryListList("History");
                    this.refreshHistoryListList("Like");
                    this.prepareShowListView();
                    Global.getInstance().hideShowLoading("GAME_LIST");
                }
            },params,true)
        }
    }

    gameDataList(currentType,cb?) {
        this.currentType = currentType;
        if (this.currentType == "Hot") {
            this.currentType = this.typeList[0];
        }
        this.homeCb = cb;
        for (let index = 0; index < this.typeList.length; index++) {
            const element = this.typeList[index];
            if (element == this.currentType) {
                this.pageView.scrollToPage(index,0.2);
                this.initGameListDataByFilter();
                this.refreshHistoryListList("History");
                this.refreshHistoryListList("Like");
                this.prepareShowListView();
                let lab_type = this.typeAnimBg.getChildByName("lab_type");
                if(lab_type) lab_type.getComponent(cc.Label).string = this.currentType;
            }
            
        }
    }

    prepareShowListView() {
        this.showListView();
    }

    updateLikeList() {
        MoreGameManager.instance().queryLikeGameList(()=>{
            if(!this.node || !this.node.isValid) {
                return;
            }
            this.initGameListDataByFilter();
            this.refreshHistoryListList("Like");
            this.refreshHistoryListList("History");
            // this.prepareShowListView();
        });
    }

    refreshHistoryListList(type) {
        let list;
        let copylikeOrHistoryList; 
        if (type == "History") {
            list = MoreGameManager.instance().getHistoruGameData();
        } else {
            list = MoreGameManager.instance().getLikeGameData();
        }
        copylikeOrHistoryList = utils.DeepClone(list); 
        
        if (list == null || copylikeOrHistoryList == null) {
            return;
        }
        this.gameListData[type] = [];
        let temp = [];
        temp[type] = [];

        if(list && list.length > 0) {
            for (let index = 0; index < list.length; index++) {
                let element = list[index];
                let newem = this.filterCompany(element) && this.filterGameName(element);
                if (newem) {
                    temp[type].push(element);
                }
            }
        }
        //历史 根据后端传的列表显示 
        if (type == "History") {
            temp[type] = temp[type].slice(0, 50);
        } else {
            temp[type].sort(this.sortFunc);
            temp[type] = temp[type].slice(0, 1000);
        }

        let array = temp[type];
        if (array.length != 0)  {
            let lastItem = "";
            let rowItem = [];
            let rowNum = 0;
            for (let index = 0; index < array.length; index++) {
                if (parseInt(array[index].big_images_set) == 1) {
                    array[index].big_images_set = 0;//Like/History 大icon改为小icon
                    if (lastItem == "big" || rowNum == 3) {
                        this.gameListData[type].push(rowItem);
                        rowItem = [];
                        rowNum = 0;
                    }
                    lastItem = "small";
                    rowNum = rowNum + 1;
                    //     if (lastItem == "small" || rowNum == 2) {
                    //         this.gameListData[type].push(rowItem);
                    //         rowItem = [];
                    //         rowNum = 0;
                    //     }
                    //     lastItem = "big";
                    //     rowNum = rowNum + 1;
                  
                } else {
                    if (lastItem == "big" || rowNum == 3) {
                        this.gameListData[type].push(rowItem);
                        rowItem = [];
                        rowNum = 0;
                    }
                    lastItem = "small";
                    rowNum = rowNum + 1;
                }
                rowItem.push(array[index]);
                if (index == array.length-1) {
                    this.gameListData[type].push(rowItem);
                }
            }
        }
    }

    filterGameList(result) {
        this.filterResultList = result;
        let sp = this.btnFilters.getChildByName("Background").getComponent(cc.Sprite);
        if (this.filterResultList.length > 0) {
            sp.spriteFrame = this.btnFiltersSpFrames[1];
        } else {
            sp.spriteFrame = this.btnFiltersSpFrames[0];
        }

        this.initGameListDataByFilter();
        this.refreshHistoryListList("Like");
        this.refreshHistoryListList("History");
        this.prepareShowListView();
    }

    loadGameItemPrefab(cb) {
        if (!this.itemPrefab) {
            uiManager.instance.loadPrefabByLoading("prefab/HallGameItem",cc.Prefab,(finish, total, item) => {
            }, (err, prefab: any) => {
                if (err) {
                    return;
                }
                this.itemPrefab = prefab;
                cb();
            });
        } else {
            cb();
        }
    }

    loadGameBigItemPrefab(cb) {
        if (!this.bigItemPrefab) {
            uiManager.instance.loadPrefabByLoading("prefab/HallGameBigItem",cc.Prefab,(finish, total, item) => {
            }, (err, prefab: any) => {
                if (err) {
                    return;
                }
                this.bigItemPrefab = prefab;
                cb();
            });
        } else {
            cb();
        }
    }

    initTagBtns(currentIIndex) {
        for (let index = 0; index < this.tagBtns.length; index++) {
            const element = this.tagBtns[index];
            element.node.opacity = 0;
        }
        this.tagBtns[currentIIndex].node.opacity = 255;
    }

    turingPageListener(pageView1) {
        this.turingPage(this.pageView.getCurrentPageIndex());
    }
    refresh_gamelist(){
        this.showTargetListView(this.pageView.getCurrentPageIndex());
    }
    turingPage(idnex) {
        this.currentType = this.typeList[idnex];
        this.initTagBtns(idnex); 
        this.showTargetListView(idnex);
        this.gameTypeButtonChanged(this.currentType.toLocaleLowerCase());
        if (idnex >= 4) {
            this.tagScroll.scrollToRight(0.2);
        } else if (idnex <= 3) {
            this.tagScroll.scrollToLeft(0.2);
        }
    }

    clickTagBtn(event, userdata) {
        for (let index = 0; index <  this.typeList.length; index++) {
            let element =  this.typeList[index].toLocaleLowerCase();
            if (element == userdata) {
                this.currentType = this.typeList[index];
                //重复调用 注释
                // this.initTagBtns(index); 
                // this.gameTypeButtonChanged(userdata);
                if (index >= 4) {
                    this.tagScroll.scrollToRight(0.2);
                } else if (index <= 3) {
                    this.tagScroll.scrollToLeft(0.2);
                }
                this.pageView.scrollToPage(index,0.2);
            }
        }
    }
    //SpecialBtns标签页滑块动画显示
    gameSpecTypeBntsChanged(typename){
        if(this.specialGameType == typename){
            return
        }
        this.specialGameType = typename;
        let parent = this.specialTagScroll.content;

        let btn_baccarat = utils.getChildByPath(parent, "baccarat.btn_baccarat");
        let btn_roulette = utils.getChildByPath(parent, "roulette.btn_roulette");
        let btn_blackjack = utils.getChildByPath(parent, "blackjack.btn_blackjack");
        btn_baccarat.opacity = 0;
        btn_roulette.opacity = 0;
        btn_blackjack.opacity = 0;

        let target = null;
        let targetParent = null;
        if(typename == "0"){
            target = btn_baccarat;
            targetParent = utils.getChildByPath(parent, "baccarat");
        }else if(typename == "1"){
            target = btn_roulette;
            targetParent = utils.getChildByPath(parent, "roulette");
        }else if(typename == "2"){
            target = btn_blackjack;
            targetParent = utils.getChildByPath(parent, "blackjack");
        }

        if(target){
            cc.Tween.stopAllByTarget(target);
            cc.Tween.stopAllByTarget(this.specTypeAnimBg);
            this.specTypeAnimBg.opacity = 255;
            cc.tween(this.specTypeAnimBg)
              .to(0.2,{x:targetParent.x},{easing:"smooth"})
              .call(()=>{this.specTypeAnimBg.x = targetParent.x})
              .start();
            cc.tween(target)
              .delay(0.2)
              .to(0.05,{opacity:255})
              .call(()=>{
                    this.specTypeAnimBg.opacity = 0
                    btn_baccarat.opacity = 0;
                    btn_roulette.opacity = 0;
                    btn_blackjack.opacity = 0;
                    target.opacity = 255;
                })
              .start();
        }

    }


    //AllGame标签页滑块动画显示
    gameTypeButtonChanged(typename) {
        if (this.currentGameType == typename) {
            return
        }
        this.lastReshLocation = Global.getInstance().now();
        this.currentGameType = typename;
        let parent = this.tagScroll.content;
        this.typeList.forEach(element => {
            const lower_ele = element.toLocaleLowerCase();
            const btn_ele = utils.getChildByPath(parent,lower_ele+".btn_"+lower_ele);
            if(btn_ele){
                btn_ele.opacity = 0;
            }
        });
        let targetParent = utils.getChildByPath(parent,typename);
        let target = utils.getChildByPath(targetParent,'btn_'+typename);
        if(target){
            cc.Tween.stopAllByTarget(target);
            cc.Tween.stopAllByTarget(this.typeAnimBg);
            this.typeAnimBg.opacity = 255;
            let type = typename.charAt(0).toUpperCase() + typename.slice(1).toLowerCase();
            let lab_type = this.typeAnimBg.getChildByName("lab_type").getComponent(cc.Label);
            if(lab_type.node) lab_type.string = type;
            cc.tween(this.typeAnimBg)
              .to(0.2,{x:targetParent.x},{easing:"smooth"})
              .call(()=>{this.typeAnimBg.x = targetParent.x})
              .start();
            cc.tween(target)
              .delay(0.2)
              .to(0.05,{opacity:255})
              .call(()=>{
                    target.opacity = 255;
                })
              .start();
        }
    }

    clickFilterBtn(event, userdata) {
        //根据 typestr 获取 typeid
        // this.currentGameType
        let gameTypes = Global.getInstance().getGameType();
        let nowid = '';
        for (let index = 0; index < gameTypes.length; index++) {
            const element = gameTypes[index];
            if(this.currentGameType == element.game_type.toLowerCase()){
                nowid = element.id + '';//转成string使用
            }
        }
        Global.getInstance().getGameProvider((provider:any)=>{
            let providerItemsList = provider;
            let toggleItemsList = [];
            for (let index = 0; index < providerItemsList.length; index++) {
                let element = providerItemsList[index];
                // {"id": 3,"provider": "Evo","short_name": "","icon_home": "images\/daef73b18c6e52061a2c614ee0a3f9fb.png",
                //     "icon_other": "images\/63d11ff4929c045a20228906748c0caa.png",
                //     "game_type": [ "10001","10004","10006"],"status": 1,"content": ""},
                //1 正常  2维护  3隐藏
                if(element.status != 3){
                    //再次判断 game_type是否包含
                    if(nowid == ''){
                        toggleItemsList.push(element);
                    }else{
                        //解决 未配置 的报错
                        if(element.game_type && element.game_type.length > 0 &&element.game_type.indexOf(nowid) >= 0){
                            toggleItemsList.push(element);
                        }
                    }
                }
            }
            uiManager.instance.showDialog(UI_PATH_DIC.MultiTogProviders, [{list:toggleItemsList, result:this.filterResultList, title:"Select a Game Provider"}], null, DEEP_INDEXZ.ALL_GAME_MUTIPROVIDER);
        }); 
    }

    clickResultClearBtn() {
        this.filterResultList = [];
        this.filterText = "";
        let sp = this.btnFilters.getChildByName("Background").getComponent(cc.Sprite);
        if (this.filterResultList.length > 0) {
            sp.spriteFrame = this.btnFiltersSpFrames[1];
        } else {
            sp.spriteFrame = this.btnFiltersSpFrames[0];
        }

        this.initGameListDataByFilter();
        this.refreshHistoryListList("History");
        this.refreshHistoryListList("Like");
        this.prepareShowListView();
    }
    
    clickBackBtn() {
        if (this.specialNode.active) {
            this.specialNode.active = false;
            this.currentType = "Casino";
            this.prepareShowListView();
            //返回到allgame 显示系统广播
            return;
        }
        // if (this.specialNode.x < 100) {
        //     this.specialNode.position = new cc.Vec3(3000,3000);
        //     return;
        // }
        cc.director.emit("RefreshGameListThird");
        let gamescene =  cc.director.getScene().getComponentInChildren(GameControl);
        gamescene.mTabbar.tap_bar(null,'home')
        this.hide(true);
        if(this.openmark && (this.openmark == "weekly" || this.openmark == "before_withdrawal")){
            this.hide_spin_btn();
            //隐藏video
            cc.director.emit("Hide_LiveVideo");
        }
    }

    // 隐藏转盘按钮
    hide_spin_btn(){
        let btn_spin = Global.getInstance().popNode.getChildByName("btn_spin");
        if (btn_spin) btn_spin.zIndex = -1;
    }

    // update (dt) {}

    initGameListDataByFilter() {
        let gameList = MoreGameManager.instance().getAllThirdGameData();
        this.gameListData["Casino"] = [];
        this.gameListData["Slots"] = [];
        this.gameListData["Poker"] = [];
        this.gameListData["Bingo"] = [];
        this.gameListData["Arcade"] = [];
        this.gameListData["Sports"] = [];
        this.gameListData["Like"] = [];
        this.gameListData["History"] = [];
        this.gameListData["Baccarat"] = [];
        this.gameListData["Roulette"] = [];
        this.gameListData["Blackjack"] = [];
        let temp = [];
        temp["Casino"] = [];
        temp["Slots"] = [];
        temp["Poker"] = [];
        temp["Bingo"] = [];
        temp["Arcade"] = [];
        temp["Sports"] = [];
        temp["Like"] = [];
        temp["History"] = [];
        temp["Baccarat"] = [];
        temp["Roulette"] = [];
        temp["Blackjack"] = [];

        for (let index = 0; index < gameList.length; index++) {
            let element = gameList[index];
            let newem = this.filterCompany(element) && this.filterGameName(element);
            if (parseInt(element.game_id) == GlobalEnum.GAME_TYPE.CASINO) {
                if (newem) {
                    if (element.table_group == 0) {
                        temp["Casino"].push(element);
                    } else if (element.table_group == 1) {
                        temp["Baccarat"].push(element);
                    } else if (element.table_group == 2) {
                        temp["Roulette"].push(element);
                    } else if (element.table_group == 3) {
                        temp["Blackjack"].push(element);
                    }
                }
            } else if (parseInt(element.game_id) == GlobalEnum.GAME_TYPE.SLOTS) {
                if (newem) {
                    temp["Slots"].push(element);
                }
            } else if (parseInt(element.game_id) == GlobalEnum.GAME_TYPE.POKER) {
                if (newem) {
                    temp["Poker"].push(element);
                }
            } else if (parseInt(element.game_id) == GlobalEnum.GAME_TYPE.BINGO) {
                if (newem) {
                    temp["Bingo"].push(element);
                }
            } else if (parseInt(element.game_id) == GlobalEnum.GAME_TYPE.FISHING) {
                if (newem) {
                    temp["Arcade"].push(element);
                }
            } else if (parseInt(element.game_id) == GlobalEnum.GAME_TYPE.SPORTS) {
                if (newem) {
                    temp["Sports"].push(element);
                }
            }
        }

        temp["Casino"].sort(this.sortFunc);
        temp["Slots"].sort(this.sortFunc);
        temp["Poker"].sort(this.sortFunc);
        temp["Bingo"].sort(this.sortFunc);
        temp["Arcade"].sort(this.sortFunc);
        temp["Sports"].sort(this.sortFunc);
        temp["Like"].sort(this.sortFunc);
        temp["History"].sort(this.sortFunc);
        temp["Baccarat"].sort(this.sortFunc);
        temp["Roulette"].sort(this.sortFunc);
        temp["Blackjack"].sort(this.sortFunc);

        for (let k = 0; k < this.typeList.length; k++) {
            let element = this.typeList[k];
            let array = temp[element];
            if (array.length != 0)  {
                let lastItem = "";
                let rowItem = [];
                let rowNum = 0;
                for (let index = 0; index < array.length; index++) {
                    if (parseInt(array[index].big_images_set) == 1) {
                        if (lastItem == "small" || rowNum == 2) {
                            this.gameListData[element].push(rowItem);
                            rowItem = [];
                            rowNum = 0;
                        }
                        lastItem = "big";
                        rowNum = rowNum + 1;
                    } else {
                        if (lastItem == "big" || rowNum == 3) {
                            this.gameListData[element].push(rowItem);
                            rowItem = [];
                            rowNum = 0;
                        }
                        lastItem = "small";
                        rowNum = rowNum + 1;
                    }
                    rowItem.push(array[index]);
                    if (index == array.length-1) {
                        this.gameListData[element].push(rowItem);
                    }
                }
            }
        }
    }

    filterTextBegin(editbox: cc.EditBox) {
         Global.getInstance().editParentMove(editbox, this.node,0);
    }

    filterTextChanged(text) {
        Global.getInstance().editBoxzIndex();
        this.filterText = text;
        if (this.filterText.length > 0) {
            this.searchBgNode.getComponent(cc.Sprite).spriteFrame = this.btnSearchSpFrames[1];
        } else {
            this.searchBgNode.getComponent(cc.Sprite).spriteFrame = this.btnSearchSpFrames[0];
        }
        this.initGameListDataByFilter();
        this.refreshHistoryListList("History");
        this.refreshHistoryListList("Like");
        this.prepareShowListView();
    }

    onClickBtnSearch() {
        if (this.editBoxSearch.string.length > 0) {
            this.editBoxSearch.string = "";
            this.searchBgNode.getComponent(cc.Sprite).spriteFrame = this.btnSearchSpFrames[0];

            this.filterTextChanged("");
        } else {
            this.editBoxSearch.focus();
        }
    }

    filterCompany(element) {
        if (this.filterResultList.length > 0) {
            for (let index = 0; index < this.filterResultList.length; index++) {
                const compId = this.filterResultList[index];
                let emna = Global.getInstance().getGameProviderNameById(element.company_id);
                if (emna == compId) {
                    return true;
                }
                
            }
        } else {
            return true;
        }
        return false;
    }

    filterGameName(element) {
        if (this.filterText.length > 0) {
            if (element.name.toLowerCase().replace(/\s+/g, '').includes(this.filterText.toLowerCase().replace(/\s+/g, ''))) {
                return true;
            }
            // const regex = new RegExp(this.filterText.toLowerCase().split('').map(char => `${char}`).join('.*'), 'i');
            // return regex.test(element.name.toLowerCase());
        } else {
            return true;
        }
        return false;
    }

    onClickSpecialBnts(event, data) {
        this.specialNode.active = true;
        // this.specialNode.position = new cc.Vec3(0,-100);
        
        if (data == this.SPECIAL_TYPE.BACCARAT) {
            this.currentType = "Baccarat";
            this.specialPageView.scrollToPage(0,0.1);
            this.initSpecialTagBtns(0);
        } else if (data == this.SPECIAL_TYPE.ROULETTE) {
            this.currentType = "Roulette";
            this.specialPageView.scrollToPage(1,0.1);
            this.initSpecialTagBtns(1);
        } else if (data == this.SPECIAL_TYPE.BLACKJACK) {
            this.currentType = "Blackjack";
            this.specialPageView.scrollToPage(2,0.1);
            this.initSpecialTagBtns(2);
        }
        this.prepareShowListView();
    }

    showListView() {
        for (let index = 0; index < this.typeList.length; index++) {
            const element = this.typeList[index];
            if (element == this.currentType) {
                if (index <= 7) {
                    this.pageView.scrollToPage(index,0.2);
                }
                this.showTargetListView(index);
            } else if (this.currentType == "Hot") {
                this.pageView.scrollToPage(0,0.2);
                this.showTargetListView(0);
            }
        }
    }

    showTargetListView(index) {
        for (let k = 0; k < 11; k++) {
            this.gameScroll[k].node.active = false;
        }
        const element = this.typeList[index];
        let array = this.gameListData[element];
        let qureyarr = MoreGameManager.instance().getThirdGameDataByType(this.typeIdList[index]+"");
        if (!qureyarr) {
            let cb = ()=>{
                if(!this.node || !this.node.isValid) {
                    return;
                }
                this.initGameListDataByFilter();
                this.refreshHistoryListList("History");
                this.refreshHistoryListList("Like");
                this.prepareShowListView();
            }
            if (element == "History") {
                MoreGameManager.instance().queryHistoryGameList(cb);
            } else if (element == "Like") {
                MoreGameManager.instance().queryLikeGameList(cb);
            } else {
                MoreGameManager.instance().doQueryGameListByTag(cb,this.typeIdList[index]);
            }
            return;
        }else{
            let nowstr = JSON.stringify(Global.getInstance().all_hide_game_list);
            if(this.maint_hide_list != nowstr){
                this.initGameListDataByFilter();
                array = this.gameListData[element];
                this.maint_hide_list = nowstr;
            }
        }
        MoreGameManager.instance().setGameDataByType("Like",null);
        MoreGameManager.instance().setGameDataByType("History",null);
        let norecord = this.allPages[index].getChildByName("norecord");
        let noresult = this.allPages[index].getChildByName("noresult");
        if (!norecord || !noresult) {
            norecord = utils.getChildByPath(this.allPages[index],"game_list2.view.content.norecord");
            noresult = utils.getChildByPath(this.allPages[index],"game_list2.view.content.noresult");
        }
        if (array.length == 0) {
            if (this.filterResultList.length > 0) {
                norecord.active = false;
                noresult.active = true;
            } else {
                norecord.active = true;
                noresult.active = false;
            }
            this.gameScroll[index].node.active = false;
        } else {
            norecord.active = false;
            noresult.active = false;
            // if (this.gameScroll[index-1] && this.gameScroll[index-1].isValid) {
            //     this.gameScroll[index-1].node.active = true;
            // }
            // if (this.gameScroll[index+1] && this.gameScroll[index+1].isValid) {
            //     this.gameScroll[index+1].node.active = true;
            // }
            this.gameScroll[index].node.active = true;
            this.gameScroll[index].setData(array);
        }
        //设置圆角
        let btn_baccarat = utils.getChildByPath(this.allPages[index],"game_list2.view.content.btn_baccarat");
        let btn_roulette = utils.getChildByPath(this.allPages[index],"game_list2.view.content.btn_roulette");
        let btn_blackjack = utils.getChildByPath(this.allPages[index],"game_list2.view.content.btn_blackjack");
        if(btn_baccarat){
            btn_baccarat.active = true;
            btn_baccarat.getComponent(RoundRectMask).radius = 0.1
        };
        if(btn_roulette){
            btn_roulette.active = true;
            btn_roulette.getComponent(RoundRectMask).radius = 0.1
        };
        if(btn_blackjack){
            btn_blackjack.active = true;
            btn_blackjack.getComponent(RoundRectMask).radius = 0.1
        };
    }

    sortFunc(a, b) {
        if (parseInt(a.big_images_set) !== parseInt(b.big_images_set)) return parseInt(b.big_images_set) - parseInt(a.big_images_set);
        if (parseInt(a.tags) === 0 && parseInt(b.tags) !== 0) return 1;
        if (parseInt(a.tags) !== 0 && parseInt(b.tags) === 0) return -1;
        //排序tags 1>3>2
        const aint = parseInt(a.tags)
        const bint = parseInt(b.tags)
        if (aint !== bint){
            if(aint == 1)return -1;
            if(bint == 1)return 1;
            if(aint == 3)return -1;
            if(bint == 3)return 1;
        }
        if (parseInt(a.balance_record) !== parseInt(b.balance_record)) return parseInt(b.balance_record) - parseInt(a.balance_record);
        if (parseInt(a.sort) !== parseInt(b.sort)) return parseInt(b.sort) - parseInt(a.sort);
        if (a.name && b.name) {
            return a.name.localeCompare(b.name);
        }
    }


    initSpecialTagBtns(currentIIndex) {
        for (let index = 0; index < this.specialTagBtns.length; index++) {
            const element = this.specialTagBtns[index];
            element.node.opacity = 0;
        }
        this.specialTagBtns[currentIIndex].node.opacity = 255;
    }

    turingSpecialPageListener(pageView1) {
        this.initSpecialTagBtns(this.specialPageView.getCurrentPageIndex());
        this.showTargetListView(this.specialPageView.getCurrentPageIndex()+8);
        this.gameSpecTypeBntsChanged(this.specialPageView.getCurrentPageIndex().toString());
        this.currentType = this.typeList[this.specialPageView.getCurrentPageIndex()+8];
    }

    turingSpecialPage(idnex) {
        for (let index = 0; index < this.specialTagBtns.length; index++) {
            const element = this.specialTagBtns[index];
            element.node.opacity = 0;
        }
        this.specialTagBtns[idnex].node.opacity = 255;
    }

    clickSpecialTagBtn(event, userdata) {
        this.initSpecialTagBtns(parseInt(userdata))
        this.gameSpecTypeBntsChanged(userdata);
        this.specialPageView.scrollToPage(parseInt(userdata),0.2);
    }
}