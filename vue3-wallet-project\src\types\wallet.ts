// 钱包任务相关类型定义

export interface WalletTaskInfo {
  /** 任务ID */
  id: string;
  /** 钱包配置信息 */
  wallet_task_id: string;
  /** 用户ID */
  user_id: string;
  /** 钱包任务奖金 */
  bonus: string;
  /** 任务状态：1:未解锁 2:进行中 3:完成任务 4:过期 5:删除任务 */
  task_status: number;
  /** 任务发放时间 */
  created_at: string;
  /** 任务状态变更时间 */
  updated_at: string;
  /** 任务名称 */
  task_name: string;
  /** 任务规则 */
  task_rule: string;
  /** 当前注单金额 */
  bet_num: string;
  /** 任务目标投注额 */
  bet_target_value: number;
  /** 充值是否完成 */
  recharge_done: boolean;
  /** 任务目标充值额 */
  recharge_target_value: string;
  /** 任务有效时间（小时） 0:无限制 */
  expire_time: number;
  /** 游戏列表 */
  game_list: string;
  /** 游戏类别 */
  game_type: string[];
  /** 厂商列表 */
  provider_list: string[];
}

// 钱包任务状态枚举
export enum WalletTaskStatus {
  LOCK = 1,        // 未解锁
  ONGOING = 2,     // 进行中
  FINISHED = 3,    // 已完成
  EXPIRED = 4,     // 已过期
  DELETE = 5       // 已删除
}

// 钱包任务状态文本映射
export const WALLET_TASK_STATUS_TEXT = {
  [WalletTaskStatus.LOCK]: '未解锁',
  [WalletTaskStatus.ONGOING]: '进行中',
  [WalletTaskStatus.FINISHED]: '已完成',
  [WalletTaskStatus.EXPIRED]: '已过期',
  [WalletTaskStatus.DELETE]: '已删除'
} as const;

// 钱包任务状态颜色映射
export const WALLET_TASK_STATUS_COLOR = {
  [WalletTaskStatus.LOCK]: '#999999',
  [WalletTaskStatus.ONGOING]: '#4CAF50',
  [WalletTaskStatus.FINISHED]: '#2196F3',
  [WalletTaskStatus.EXPIRED]: '#FF6B6B',
  [WalletTaskStatus.DELETE]: '#999999'
} as const;

// 任务进度信息
export interface TaskProgress {
  current: number;
  total: number;
  percentage: number;
  isCompleted: boolean;
}

// 任务倒计时信息
export interface TaskCountdown {
  hours: number;
  minutes: number;
  seconds: number;
  totalSeconds: number;
  isExpired: boolean;
}

// 钱包任务操作类型
export enum WalletTaskAction {
  UNLOCK = 'unlock',
  COLLECT = 'collect',
  CONTINUE = 'continue',
  VIEW_RULE = 'view_rule'
}

// API响应类型
export interface WalletTaskResponse {
  code: number;
  message: string;
  data: WalletTaskInfo[];
}

export interface WalletTaskRewardResponse {
  code: number;
  message: string;
  data: {
    reward_amount: number;
    new_balance: number;
  };
}

// 钱包任务筛选选项
export interface WalletTaskFilter {
  status?: WalletTaskStatus[];
  hasTimeLimit?: boolean;
  sortBy?: 'created_at' | 'bonus' | 'expire_time';
  sortOrder?: 'asc' | 'desc';
}

// 钱包任务统计信息
export interface WalletTaskStats {
  total: number;
  locked: number;
  ongoing: number;
  finished: number;
  expired: number;
  totalRewards: number;
  completionRate: number;
}

// 游戏跳转参数
export interface GameJumpParams {
  jumpType: string;
  gameType?: string[];
  providerList?: string[];
}

// 钱包任务事件类型
export enum WalletTaskEvent {
  TASK_UNLOCKED = 'task_unlocked',
  TASK_COMPLETED = 'task_completed',
  REWARD_COLLECTED = 'reward_collected',
  TASK_EXPIRED = 'task_expired',
  PROGRESS_UPDATED = 'progress_updated'
}

// 钱包任务配置
export interface WalletTaskConfig {
  maxConcurrentTasks: number;
  autoRefreshInterval: number;
  showGuideForNewUsers: boolean;
  enableNotifications: boolean;
}

// 本地存储键
export const WALLET_STORAGE_KEYS = {
  GUIDE_SHOWN: 'wallet_task_guide_shown',
  LAST_REFRESH: 'wallet_task_last_refresh',
  USER_PREFERENCES: 'wallet_task_preferences'
} as const;

// 默认配置
export const DEFAULT_WALLET_CONFIG: WalletTaskConfig = {
  maxConcurrentTasks: 1,
  autoRefreshInterval: 30000, // 30秒
  showGuideForNewUsers: true,
  enableNotifications: true
};

// 任务项UI状态
export interface TaskItemUIState {
  isLoading: boolean;
  showProgress: boolean;
  showCountdown: boolean;
  isExpanded: boolean;
  animationState: 'idle' | 'collecting' | 'unlocking' | 'completing';
}

// 钱包任务页面状态
export interface WalletTaskPageState {
  isLoading: boolean;
  showGuide: boolean;
  selectedTask: WalletTaskInfo | null;
  filter: WalletTaskFilter;
  sortBy: string;
  searchText: string;
}

// 错误类型
export enum WalletTaskError {
  NETWORK_ERROR = 'network_error',
  INVALID_TASK = 'invalid_task',
  TASK_EXPIRED = 'task_expired',
  INSUFFICIENT_PROGRESS = 'insufficient_progress',
  MAX_TASKS_REACHED = 'max_tasks_reached',
  UNKNOWN_ERROR = 'unknown_error'
}

// 错误信息映射
export const WALLET_TASK_ERROR_MESSAGES = {
  [WalletTaskError.NETWORK_ERROR]: '网络连接失败，请检查网络后重试',
  [WalletTaskError.INVALID_TASK]: '无效的任务信息',
  [WalletTaskError.TASK_EXPIRED]: '任务已过期',
  [WalletTaskError.INSUFFICIENT_PROGRESS]: '任务进度不足',
  [WalletTaskError.MAX_TASKS_REACHED]: '已达到最大任务数量限制',
  [WalletTaskError.UNKNOWN_ERROR]: '未知错误，请稍后重试'
} as const;
