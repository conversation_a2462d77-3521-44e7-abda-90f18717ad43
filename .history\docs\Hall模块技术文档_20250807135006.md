# Hall模块技术文档

## 📖 概述

Hall模块是游戏平台的核心大厅系统，负责整个应用的主界面展示、用户交互、游戏导航等核心功能。该模块采用 Cocos Creator 框架开发，使用 TypeScript 语言实现，是整个应用的中枢控制中心。

## 🏗️ 模块架构

### 核心模块分类

```
assets/src/hall/
├── 🎮 游戏相关模块
├── 🎯 活动系统模块  
├── 💰 交易系统模块
├── 👤 用户管理模块
├── 🤝 MGM邀请系统模块
└── 🔧 其他功能模块
```

### 依赖关系图

```mermaid
graph TD
    A[Hall.ts 主控制器] --> B[游戏模块]
    A --> C[活动系统]
    A --> D[交易系统]
    A --> E[用户管理]
    A --> F[MGM邀请]
    A --> G[其他功能]
    
    B --> B1[AllGameView]
    B --> B2[GameRule]
    
    C --> C1[ActivityMgr]
    C --> C2[Promo]
    C --> C3[Task]
    
    D --> D1[Transations]
    D --> D2[BeforeWithdrawal]
    
    E --> E1[Mail]
    E --> E2[Setting]
    E --> E3[Notice]
    
    F --> F1[InviteMain]
    F --> F2[InviteHomeUI]
    
    G --> G1[Banners]
    G --> G2[LeaderBoard]
    G --> G3[SpinWheel]
```

## 🎮 游戏相关模块

### AllGameView - 游戏展示视图

**核心功能：**
- 游戏列表展示和分类
- 游戏搜索和筛选
- 特殊游戏类型处理（百家乐、轮盘、21点）
- 游戏历史记录

**关键特性：**
```typescript
// 游戏分类
typeList = ["Casino","Slots","Poker","Bingo","Arcade","Sports","Like","History","Baccarat","Roulette","Blackjack"]

// 游戏标签
GAME_TAGS = {
    "REGULAR": 0,
    "HOT": 1,
    "NEW": 2
}

// 特殊游戏类型
SPECIAL_TYPE = {
    BACCARAT: "baccarat",
    ROULETTE: "roulette", 
    BLACKJACK: "blackjack"
}
```

### GameRule - 游戏规则管理

**核心功能：**
- 房间列表管理
- 游戏规则展示
- 快速开始功能
- 最佳匹配房间推荐

**业务逻辑：**
- 根据用户余额推荐合适房间
- 支持多种游戏类型的规则配置
- 动态加载游戏预制体

## 🎯 活动系统模块

### ActivityMgr - 活动管理器

**活动类型枚举：**
```typescript
export enum ACTIVITY_TYPE {
    URL = 1,        // 链接跳转
    DETAIL = 2,     // 活动详情
    POP_WINDOW = 3  // 弹窗展示
}

export enum ALERT_TYPE {
    CHARGE = 1,      // 充值活动
    SIGN_IN = 2,     // 签到活动
    FIRST_CHARGE = 3, // 首充活动
    INVITE = 4       // 邀请活动
}
```

### Promo - 促销活动

**支持的活动类型：**
- 签到绑定 (SIGNBIND)
- 首次充值 (FIRSTDEPOSIT)
- 返水活动 (CASHBACK)
- VIP返水 (VIPCASHBACK)
- 排行榜 (RANK)
- 转盘活动 (SPIN)
- 情人节活动 (VALENTINE)
- 周薪活动 (WEEKLY)

### Task - 任务系统

**任务类型：**
```typescript
const TASK_TYPE = {
    UPGRADE_VIP: "upgrade_vip",
    DAILY_CASHBACK: "daily_cashback", 
    VIP_CASHBACK: "vip_cashback",
    FIRST_DEPOSIT: "first_deposit",
    BIND_PHONE: "bind_phone_number",
    REGISTER: "register"
}
```

## 💰 交易系统模块

### Transations - 交易记录

**记录类型：**
```typescript
const RECORD_TYPE = {
    ADJUSTMENT: "Adjustment",
    MAYAPAY: "mayapay",
    MAYA_WEB: "mayawebpay", 
    GCASH_WEB: "gcashwebpay",
    DEPOSIT: "Deposit",
    WITHDRAWAL: "Withdrawal",
    BATCH_WITHDRAWAL: 'Batch Withdrawal',
    REWARD: "Reward",
    TRANSFER: "Transfer"
}
```

**状态管理：**
- 充值状态：成功、等待、失败、人工审核
- 提现状态：待到账、成功、失败、待审核
- 奖励类型：多达20+种不同奖励来源

### BeforeWithdrawal - 提现前验证

**功能特性：**
- 投注额度验证
- 进度条显示
- 跳转游戏引导

## 👤 用户管理模块

### Mail - 邮件系统

**核心功能：**
- 邮件列表展示
- 邮件详情查看
- 附件奖励领取
- 已读状态管理

### Setting - 用户设置

**设置项目：**
- 音效开关
- 音乐开关
- 震动开关
- 语言设置
- 版本信息
- 账号绑定状态

### Notice - 通知公告

**功能特性：**
- 公告列表展示
- 公告详情查看
- 已读状态跟踪
- 分页加载

## 🤝 MGM邀请系统模块

### InviteMain - 邀请主控制器

**邀请页面枚举：**
```typescript
export enum invitePage {
    page_none = 0,
    page_home = 1,     // 主页
    page_help = 2,     // 帮助
    page_detail = 3,   // 详情
    page_member = 4,   // 成员
    page_rules = 5,    // 规则
    page_count = 6     // 统计
}
```

**核心功能：**
- 邀请码生成和绑定
- 邀请奖励计算
- 邀请历史记录
- 等级晋升系统

### InviteHomeUI - 邀请主界面

**界面元素：**
- 邀请码显示
- 奖励进度条
- 晋升状态
- 奖励金额展示

## 🔧 其他功能模块

### Banners - 横幅广告

**功能特性：**
- 多图轮播
- 自动切换
- 点击跳转
- 分页指示器
- 预加载优化

### LeaderBoard - 排行榜

**排行榜类型：**
- 今日排行
- 昨日排行
- 实时更新
- 奖励展示

### SpinWheel - 转盘活动

**核心功能：**
- 转盘动画
- 奖励计算
- 历史记录
- 进度跟踪

## 🎨 UI架构设计

### 组件继承关系

```typescript
// 基础UI组件
UIComponent
├── UICommon
├── UIPopIn
└── UISlideComponent

// Hall主控制器
Hall extends EventComponent implements MessageImpl
```

### 事件系统

**全局事件：**
- `update_gold` - 金币更新
- `refresh_promo` - 活动刷新
- `showGoldAni` - 金币动画
- `update_home_widget` - 首页组件更新

## 🔄 数据流管理

### 数据获取流程

```mermaid
sequenceDiagram
    participant UI as 用户界面
    participant Hall as Hall控制器
    participant HTTP as HTTP请求
    participant Global as 全局数据
    
    UI->>Hall: 用户操作
    Hall->>HTTP: 发起请求
    HTTP->>Global: 更新数据
    Global->>Hall: 数据变更通知
    Hall->>UI: 界面更新
```

### 状态管理

**登录状态：**
- 已登录：显示用户信息和余额
- 未登录：显示登录界面

**页面状态：**
- 大厅页面：允许弹窗显示
- 其他页面：限制某些功能

## 🚀 性能优化

### 资源管理

1. **预制体动态加载**
   ```typescript
   uiManager.instance.loadPrefabByLoading(UI_PATH_DIC.XXX, cc.Prefab, callback)
   ```

2. **图片预加载**
   - Banner图片分批加载
   - 首屏优先加载策略

3. **对象池管理**
   - 列表项复用
   - 节点缓存机制

### 内存优化

- 及时销毁不用的节点
- 事件监听器自动清理
- 定时器统一管理

## 📱 适配策略

### 屏幕适配

```typescript
cc.game.on("screenResize", () => {
    this.resize()
})
```

### 平台兼容

- iOS/Android 特殊处理
- 浏览器环境适配
- Maya/GCash 模式支持

## 🔐 安全机制

### 数据验证

- Token有效性检查
- 用户权限验证
- 请求频率限制

### 防作弊

- 客户端状态校验
- 服务器数据同步
- 异常行为监控

## 📊 监控与统计

### 用户行为追踪

- 页面访问统计
- 功能使用频率
- 错误日志收集

### 性能监控

- 加载时间统计
- 内存使用监控
- 网络请求分析

## 🔮 扩展建议

1. **模块化重构**
   - 按功能拆分独立模块
   - 统一接口规范
   - 依赖注入机制

2. **状态管理优化**
   - 引入状态管理库
   - 数据流标准化
   - 响应式更新

3. **性能提升**
   - 虚拟列表实现
   - 图片懒加载
   - 代码分割

4. **用户体验**
   - 骨架屏加载
   - 离线缓存
   - 手势操作优化

---

*文档版本：v1.0*  
*最后更新：2025-08-07*  
*维护者：开发团队*
