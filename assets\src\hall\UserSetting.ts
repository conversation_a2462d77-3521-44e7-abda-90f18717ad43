

import UICommon from "../component/UICommon";
import { ALL_APP_SOURCE_CONFIG } from "../Config";
import { GameData } from "../data/GameData";
import GameControl from "../GameControl";
import { ACCOUNT_TYPE, DEEP_INDEXZ, E_CHANEL_TYPE, UI_PATH_DIC } from "../GlobalConstant";
import Global from "../GlobalScript";
import { showKYCVerification } from "../KYC/KYCVerification";
import { SERVICE_TYPE, serviceMgr } from "../mgr/serviceMgr";
import { uiManager } from "../mgr/UIManager";
import HttpProxy from "../net/HttpProxy";
import HttpUtils from "../net/HttpUtils";
import RoundRectMask from "../RoundRectMask";
import utils from "../utils/utils";
import { showBetLevelInfo } from "./BetLevelInfo";
import BetLevelManager from "./BetLevelManager";
import Hall from "./Hall";
const { ccclass, property } = cc._decorator;

@ccclass
export default class UserSetting extends cc.Component {
    @property(cc.Node)
    mask: cc.Node = null;

    @property(cc.Node)
    lay: cc.Node = null;

    //bgOrder Node
    @property(cc.Node)
    bgOrder: cc.Node = null;

    //new ndaccounts lemoon
    @property(cc.Node)
    ndAccounts: cc.Node = null;

    @property(cc.Sprite)
    avatarSprite: cc.Sprite = null;

    @property(cc.Label)
    lbName: cc.Label = null;

    @property(cc.Label)
    idLabel: cc.Label = null;

    @property(cc.Label)
    vipLabel: cc.Label = null;

    @property(cc.Node)
    ndPhoneGetFree: cc.Node = null;
    @property(cc.Label)
    giftCount: cc.Label = null

    @property(cc.ToggleContainer)
    Checkboxs: cc.ToggleContainer[] = [];

    @property(cc.ToggleContainer)
    FCheckboxs: cc.ToggleContainer[] = [];

    @property(cc.Node)
    profileNode: cc.Node = null

    @property(cc.Node)
    ndMask2: cc.Node = null

    @property(cc.Node)
    ndPhonePW: cc.Node = null

    @property(cc.Sprite)
    spLeve: cc.Sprite = null

    @property(cc.ProgressBar)
    pbLevel: cc.ProgressBar = null

    @property(cc.Label)
    lbRate: cc.Label = null

    @property(cc.Node)
    btnRefreshNode: cc.Node = null;

    //客服btn
    @property(cc.Node)
    coustom_btn: cc.Node = null;
    //通知btn
    @property(cc.Node)
    ser_btn: cc.Node = null;

    //更改昵称 按钮
    @property(cc.Sprite)
    sp_c_name: cc.Sprite = null
    //复制 按钮
    @property(cc.Sprite)
    sp_copy: cc.Sprite = null
    //id 按钮
    @property(cc.Sprite)
    sp_id: cc.Sprite = null


    //6张
    @property([cc.SpriteFrame])
    v6SpriteFrames: cc.SpriteFrame[] = [];
    // @property(cc.Prefab)
    // accountListPrefab: cc.Prefab = null;

    addView: any = null;

    //测试客服 node 只有在测试和pre模式才生效
    @property(cc.Node)
    ser_test_node: cc.Node = null;
    @property(cc.Node)
    ser_test_node_001: cc.Node = null;
    @property(cc.Node)
    ser_test_node_002: cc.Node = null;

    @property(cc.Label)
    lbLevel: cc.Label = null

    @property(cc.Node)
    ndCustomer: cc.Node = null;

    @property(cc.Node)
    ndGcashAccount: cc.Node = null;

    @property(cc.Node)
    ndTransaction: cc.Node = null;

    @property(cc.Node)
    ndBetLevel: cc.Node = null

    @property(cc.Label)
    labBalance: cc.Label = null;

    @property(cc.Node)
    bgNode: cc.Node = null;

    @property(cc.Node)
    unVipWalletNode: cc.Node = null;

    @property(cc.Node)
    vipWalletNode: cc.Node = null;

    @property(cc.Node)
    vipBgNode: cc.Node = null;

    @property(cc.Label)
    vipValidTime: cc.Label = null;

    @property(cc.Node)
    mainNode: cc.Node = null;

    @property(cc.Node)
    itemMaskNode: cc.Node = null;

    @property(cc.Node)
    itemScrollNode: cc.Node = null;

    m_orderList_prefab: any;
    m_gameRecord_prefab: any;

    mNoticePrefab: cc.Prefab = null;
    mDepositPrefab: cc.Prefab = null;
    mWithdrawPrefab: cc.Prefab = null;

    private avatarIndex = null;
    private avatar: string = null;
    private card: string = null;
    private avatarFrame: string = null;
    accountListView: cc.Node = null;
    lastUpdateBanlanceTime = 0;
    hasUpdataBalance = true;

    private currentBalance: any = null;

    lastReqBanlanceTime = 0;//刷新金币的时间戳
    private isVip: any = 0;//标记当前用户是否是vip

    async onLoad() {
        let gamescene = cc.director.getScene().getComponentInChildren(GameControl);
        gamescene.mAccount = this;
        let userData = Global.getInstance().userdata;
        let show_nick = utils.get_maxstr_with3dot(userData.nickname, 200)
        this.lbName.string = show_nick;
        let user_id = userData.user_id || (userData.user_id + "");
        if (typeof user_id == "number") user_id = user_id.toString();
        user_id = utils.formatDesensitization(user_id, 3, 3)
        this.idLabel.string = user_id;
        cc.director.on("update_home_widget", this.updateWidget, this);
        cc.director.on("updateTotalBalance", this.onClickRefreshBtn, this);
        cc.director.on("update_gold", this.updateShowBalance, this);
        cc.director.on("updateAvatar", this.updateAvatar, this);

        // this.ndPhonePW.active = !Global.getInstance().gcashMode

        let debug_maya = utils.getBrowserValue("ser_test");
        if (debug_maya) {
            this.ser_test_node.active = true
            this.ser_test_node_001.active = true
            this.ser_test_node_002.active = true
        }
        let isVip = parseInt(Global.getInstance().userdata?.is_vip) || 0;
        if (gamescene.mTabbar.re_page == 'vip') {
            if (!isVip) {
                this.onClickHowToVip();
            } else {
                this.onClickToVip();
            }
        }

        const avatar = Global.getInstance().getAvatar();
        this.updateAvatar(avatar);

        await HttpProxy.instance.getRechargeWithdrawConfig();
    }
    preLoadPrefab() {
        let prefabs = [
            // UI_PATH_DIC.SecurityCenter,
            // UI_PATH_DIC.KYCVerification,
        ]
        // for (let index = 0; index < prefabs.length; index++) {
        //     const prefabPath = prefabs[index];
        cc.resources.preload(prefabs)
        // }
    }
    start() {
        cc.director.on("updateUserName", this.updateName, this);
        cc.director.on("closeProfileView", this.closeView, this);
        this.showView()
        //Total balance
        this.currentBalance = Global.getInstance().balanceTotal;
        if (this.currentBalance >= 0) this.labBalance.string = utils.formatNumberWithCommas(this.currentBalance);
        else this.labBalance.string = "--";

        //this.initLayOut();
        this.isVip = parseInt(Global.getInstance().userdata?.is_vip) || 0;
        if (this.isVip) {
            this.vipWalletNode.active = true;
            this.unVipWalletNode.active = false;
            //vip有效期
            let vip_starttime = Global.getInstance().userdata.vip_start;
            let vip_endtime = Global.getInstance().userdata.vip_end;
            let startTime = utils.timestampToTime3(vip_starttime);
            let endTime = utils.timestampToTime3(vip_endtime);
            this.vipValidTime.string = `${startTime}-${endTime}`;
        } else {
            this.unVipWalletNode.active = true;
            this.vipWalletNode.active = false;
        }

        let page = this.node.getChildByName("page").getComponent(cc.PageView);
        page.node.on('page-turning', () => {
            let curIndex = page.getCurrentPageIndex();
            if (curIndex != 1) {
                //cc.director.emit("ReturnToHome",[null, "Home"])
                cc.director.emit("ReturnToHome", null, "home")
            }
        }, this);
        let tempnode = this.node.getChildByName("temp");
        page.insertPage(cc.instantiate(tempnode), 0);
        page.scrollToPage(1, 0.01);

        this.scheduleOnce(() => {
            Global.getInstance().updateBalanceAfterGame();
        }, 0.2)

        this.node.active = false;
        cc.director.emit('change_tabbar_state');//加载出来之后再刷新tabbar状态

        let gamescene = cc.director.getScene().getComponentInChildren(GameControl);
        const sec_pr = gamescene.mTabbar.re_page_params
        const sec_pr3 = gamescene.mTabbar.re_page_params3
        if (sec_pr == 'service') {
            //客服调用
            setTimeout(() => {
                serviceMgr.instance.show_achat(SERVICE_TYPE.coustom)
            }, 200);
        }
        if(sec_pr == 'pending'){
            let self = this;
            //打开transaction页面
            setTimeout(() => {
                self.onClickTrasactions();
            }, 200);
        }
        if(sec_pr3){
            let self = this;
            //打开充值页面
            setTimeout(() => {
                self.onClickDepositBtn(null,sec_pr3);
            }, 200);
        }
        //适配item选项的滑动区域
        let content = utils.getChildByPath(this.itemScrollNode, "view.content");
        content.height = this.bgOrder.height + 130;
    }

    /**点击How To Become VIP*/
    onClickHowToVip() {
        //检查是否是第一次点击
        let isFirstClick = Global.getInstance().getStoreageData("isFirstClick" + Global.getInstance().userdata["user_id"], true);
        if (isFirstClick) {
            //第一次点击 进VIP详情页
            uiManager.instance.showDialog(UI_PATH_DIC.VipDetail, null, () => {
                uiManager.instance.showDialog(UI_PATH_DIC.Vip, [{ type: "unvip_user" }]);//VIP页
            }, DEEP_INDEXZ.ACTIVITY_CONTENT);
            Global.getInstance().setStoreageData("isFirstClick" + Global.getInstance().userdata["user_id"], false);
        } else {
            //非第一次点击进普通用户VIP页
            uiManager.instance.showDialog(UI_PATH_DIC.Vip, [{ type: "unvip_user" }],null,DEEP_INDEXZ.ACTIVITYS);
        }
    }

    /**点击进入VIP页 */
    onClickToVip() {
        uiManager.instance.showDialog(UI_PATH_DIC.Vip, [{ type: "vip_user" }],null,DEEP_INDEXZ.ACTIVITYS);
    }

    protected onEnable(): void {
        this.updateWidget()
        let page = this.node.getChildByName("page").getComponent(cc.PageView);
        page.scrollToPage(1, 0.01);
    }

    initLayOut() {
        this.scheduleOnce(() => {
            let roundret = this.bgNode.getComponent(RoundRectMask);
            roundret.radius = 0.06;
        }, 0.01)
    }

    showView() {
        this.node.active = true
        this.node.scale = 1;
        // this.mask.opacity = 180;
        this.lay.setScale(1);
        //设置 order 项目
        let channel = ALL_APP_SOURCE_CONFIG.channel;
        if (channel == E_CHANEL_TYPE.WEB) {
            this.ndAccounts.active = true;
            this.itemMaskNode.getComponent(RoundRectMask).radius = 30;
        } else {
            this.ndAccounts.active = false;
            this.itemMaskNode.getComponent(RoundRectMask).radius = 30;
        }
    }

    onDestroy() {
        cc.director.off("updateUserName", this.updateName, this);
        cc.director.off('updateBetLevel', this.refreshLevelUI, this)
        cc.director.off("update_home_widget", this.updateWidget, this);
        cc.director.off("updateTotalBalance", this.onClickRefreshBtn, this);
        cc.director.off("update_gold", this.updateShowBalance, this);
    }

    updateName() {
        let userData = Global.getInstance().userdata;
        let show_nick = utils.get_maxstr_with3dot(userData.nickname, 200)
        this.lbName.string = show_nick;
    }

    updateWidget() {
        let btnNotice = utils.getChildByPath(this.mainNode, "userInfo.btnNotice.reddot");
        let totalInbox = Global.getInstance().unreadMarks?.inbox;//Global.getInstance().getStoreageData("INBOX_UNREAD_TOTAL_NUMNER", 0);
        if (totalInbox > 0) {
            btnNotice.active = true;
        } else {
            btnNotice.active = false;
        }


        let redpointcustom = utils.getChildByPath(this.mainNode, "userInfo.btnCustomer.reddot");
        if (Global.getInstance().custom_msg > 0) {
            redpointcustom.active = true
        } else {
            redpointcustom.active = false
        }
    }

    updateAvatar(avatar: string) {
        if (avatar) utils.loadAvatar(this.avatarSprite, avatar);
    }

    copy() {
        Global.getInstance().setPasteboard(Global.getInstance().userdata.user_id.toString());
        Global.getInstance().showSimpleTip("Copied!");
    }

    closeView() {
        // if (this.mask && this.lay) {
        //     this.mask.opacity = 0;
        //     this.lay.setScale(0);
        //     this.node.destroy();
        // } 
        this.node.destroy();
    }

    onClickChangeName() {
        // showChangeName()
        uiManager.instance.showDialog(UI_PATH_DIC.ChangeName,null,null,DEEP_INDEXZ.CHANGE_NAME)
    }

    onClickPassword() {
        // showPhonePassword()
        // showSecurity();
        uiManager.instance.showDialog(UI_PATH_DIC.SecurityCenter)//未用到
    }
    //点击 设置账户 第二项目 再web端显示
    onClickAccount() {
        uiManager.instance.showDialog(UI_PATH_DIC.WithdrawAccount,null,null,DEEP_INDEXZ.ADD_WITHDRAW);
    }
    onClickFundAccounts() {
        // let userData = Global.getInstance().userdata
        // if (!userData.phone) {
        //     Global.getInstance().showCommonTip("Before first redeem, you must set up phone number first.", this, false, () => {
        //         showPhonePassword()
        //     });
        //     return;
        // }
        // if (!userData.withdraw_password) {
        //     Global.getInstance().showCommonTip("Before first redeem, you must set up wallet password first.", this, false, () => {
        //         showVerification(() => {
        //             // showSetPassword()
        //             uiManager.instance.showDialog(UI_PATH_DIC.SetPayPassword)
        //         })
        //     });
        //     return;
        // }
        // showFundAccounts();
        console.log("onClickFundAccounts!!!");
        uiManager.instance.showDialog(UI_PATH_DIC.BetOrder,null,null,DEEP_INDEXZ.BETORDER)
    }

    onClickTrasactions() {
        // Global.getInstance().showShop(2)
        // let cb = () => {
        //     let node = cc.instantiate(this.m_orderList_prefab);
        //     node.parent = cc.director.getScene();
        // }
        // if (this.m_orderList_prefab == null) {
        //     cc.resources.load(("prefab/shopOrderList"), cc.Prefab, () => {
        //     }, (err, prefab: any) => {
        //         if (err) {
        //             return;
        //         }
        //         this.m_orderList_prefab = prefab;
        //         cb()
        //     });
        // } else {
        //     cb()
        // }
        // showGameRecords()
        uiManager.instance.showDialog(UI_PATH_DIC.Transations, ["deposit"],null,DEEP_INDEXZ.TRANSATIONS)
    }
    onClickGameRecords() {
        let cb = () => {
            let node = cc.instantiate(this.m_gameRecord_prefab);
            node.parent = cc.director.getScene();
        }
        if (this.m_gameRecord_prefab == null) {
            uiManager.instance.loadPrefabByLoading(UI_PATH_DIC.GameRecords, cc.Prefab, () => {
            }, (err, prefab: any) => {
                if (err) {
                    return;
                }
                this.m_gameRecord_prefab = prefab;
                cb()
            });
        } else {
            cb()
        }
    }
    onClickCustomer() {
        serviceMgr.instance.show_achat(SERVICE_TYPE.coustom);
    }

    onClickChangeHead() {
        uiManager.instance.showDialog(UI_PATH_DIC.ProfileNode,null,null,DEEP_INDEXZ.HEAD_IMG)
    }

    onClickCloseChangeHead() {
    }

    onclickLevelHelp() {
        showBetLevelInfo()
    }

    onClickTerm() {
        uiManager.instance.showDialog(UI_PATH_DIC.PrivacyTip, [null, "term"],null,DEEP_INDEXZ.PRIVACY_TIPS);
    }

    refreshLevelUI() {
        let userLevel = BetLevelManager.instance().getUserLevel()
        let levelNum = userLevel.level
        cc.resources.load("Levels/ironlevel" + (levelNum + 1), cc.SpriteFrame, (err, spriteFrame: cc.SpriteFrame) => {
            if (err || !spriteFrame) return
            this.spLeve.spriteFrame = spriteFrame
        })
        this.lbLevel.string = "LV" + userLevel.level || "0"
        if (userLevel.is_down) {
            this.lbRate.string = userLevel.points + ""
            this.pbLevel.progress = 1
        } else {
            let rate = userLevel.points / userLevel.max_points
            this.pbLevel.progress = rate
            this.lbRate.string = utils.numFormat2(userLevel.points) + "/" + utils.numFormat2(userLevel.max_points)
        }
    }
    onClickRefreshBtn() {
        this.btnRefreshNode.angle = 0;
        this.btnRefreshNode.stopAllActions();
        let action = cc.rotateBy(0.2, -72);
        this.btnRefreshNode.runAction(action);
        cc.Tween.stopAllByTarget(this.labBalance.node);
        cc.tween(this.labBalance.node).to(0.05, { opacity: 0 }).to(0.1, { opacity: 255 }).start();
        //防止 连续 点击
        let nowTime = Global.getInstance().now()
        let timegap = (nowTime - this.lastReqBanlanceTime)/1000;
        if (timegap < 2) {
            cc.warn("updateBalanceAfterGame too frequently")
            return;
        }
        this.lastReqBanlanceTime = nowTime;
        Global.getInstance().updateBalanceAfterGame();
    }

    updateShowBalance() {
        let balance = Global.getInstance().balanceTotal;
        if (balance >= 0) {
            this.labBalance.string = utils.formatNumberWithCommas(balance);
        } else {
            this.labBalance.string = "--";
        }
        cc.Tween.stopAllByTarget(this.labBalance.node);
        cc.tween(this.labBalance.node).to(0.05, { opacity: 0 }).to(0.1, { opacity: 255 }).start();
    }

    // animateBalanceUpdate() {
    //     //获取旧余额
    //     let oldBalance = 0//this.currentBalance;
    //     //更新后新余额
    //     Global.getInstance().updateBalanceAfterGame(()=>{

    //     });
    //     let newBalance = Global.getInstance().balanceTotal;

    //     //余额变化的动画时长
    //     let duration = 2;
    //     //创建Tween动画
    //     cc.tween({value: oldBalance})
    //       .to(duration, {value: newBalance}, {
    //          progress: (start, end, current, ratio) => {
    //             //计算当前的余额值，并更新到labBalance
    //             let currentBalance = start + (end - start) * ratio;
    //             this.labBalance.string = utils.formatNumberWithCommas(currentBalance);
    //             return current;
    //          }
    //       })
    //       .call(()=>{
    //          //动画完成后，确保显示最终的余额
    //          this.labBalance.string = utils.formatNumberWithCommas(newBalance);
    //          // 启用按钮
    //          this.btnRefreshNode.getComponent(cc.Button).interactable = true;
    //       })
    //       .start()
    // }

    onClickNoticeBtn() {
        this.showNoticeView();
    }

    showNoticeView() {
        uiManager.instance.showDialog(UI_PATH_DIC.Notice,null,null,DEEP_INDEXZ.NOTICE);
    }
    onClickCustomerBtn_test(event, datastr: string) {
        if (datastr == '1') {
            serviceMgr.instance.show_achat(SERVICE_TYPE.test);
        } else if (datastr == '2') {
            serviceMgr.instance.show_achat(SERVICE_TYPE.test001);
        } else {
            serviceMgr.instance.show_achat(SERVICE_TYPE.test003);
        }
    }

    onClickCustomerBtn() {
        this.onClickCustomer()
    }

    onClickWithdrawBtn() {
        Global.getInstance().clickToWithdrawal();
    }
    onClickDepositBtn(eveent,amount) {
        Global.getInstance().loadWithdrawConfig(()=>{
            // if (ALL_APP_SOURCE_CONFIG.channel == E_CHANEL_TYPE.G_CASH && !GameData.instance.existDepositMethod()) {
            if (ALL_APP_SOURCE_CONFIG.channel == E_CHANEL_TYPE.G_CASH) {
                //gcash 直接弹窗提示 是否跳转充值
                Global.getInstance().back_mini_buy_tips();
                return;
            }
            if(amount != 'no'){
                uiManager.instance.showDialog(UI_PATH_DIC.Deposit, [{type:"settoDeposit",num:amount}],null,DEEP_INDEXZ.DEPOSIT);
            }else{
                uiManager.instance.showDialog(UI_PATH_DIC.Deposit,null,null,DEEP_INDEXZ.DEPOSIT);
            }
        },true);
    }

    showDepositView() {
        let cb = () => {
            let node = cc.instantiate(this.mDepositPrefab);
            node.parent = Global.getInstance().prefabNode;
        }
        if (this.mDepositPrefab == null) {
            uiManager.instance.loadPrefabByLoading(UI_PATH_DIC.Deposit, cc.Prefab, () => {
            }, (err, prefab: any) => {
                if (err) {
                    return;
                }
                this.mDepositPrefab = prefab;
                cb();
            });
        } else {
            cb();
        }
    }

    showWithDrawView() {
        let cb = () => {
            let node = cc.instantiate(this.mWithdrawPrefab);
            node.parent = Global.getInstance().prefabNode;
        }
        if (this.mWithdrawPrefab == null) {
            uiManager.instance.loadPrefabByLoading(UI_PATH_DIC.WithDraw, cc.Prefab, () => {
            }, (err, prefab: any) => {
                if (err) {
                    return;
                }
                this.mWithdrawPrefab = prefab;
                cb();
            });
        } else {
            cb();
        }
    }
}
