import { ALL_APP_SOURCE_CONFIG } from "../Config";
import { E_CHANEL_TYPE, E_FUND_TYPE, GOLD_RATIO, OPEN_BROADCAST_TYPE, UI_PATH_DIC } from "../GlobalConstant";
import Global from "../GlobalScript";
import RoundRectMask from "../RoundRectMask";
import UICommon from "../component/UICommon";
import List from "../listView/List";
import { uiManager } from "../mgr/UIManager";
import HttpUtils from "../net/HttpUtils";
import utils from "../utils/utils";
import MultiToggles from "./MultiToggles";
import SingleToggle from "./SingleToggle";
import TransationsBatch from "./TransationsBatch";
import TrasationsDetials from "./TrasationsDetials";

const {ccclass, property} = cc._decorator;
const TAG_TYPE = {
    DEPOSIT:"deposit",
    WITHDRAW:"withdraw",
    AWARD:"award"
}

const RECORD_TYPE = {
    ADJUSTMENT: "Adjustment",
    MAYAPAY: "mayapay",
    MAYA_WEB: "mayawebpay",
    GCASH_WEB: "gcashwebpay",
    DEPOSIT: "Deposit",
    WITHDRAWAL: "Withdrawal",
    BATCH_WITHDRAWAL:'Batch Withdrawal',
    REWARD: "Reward",
    TRANSFER: "Transfer"
}

enum FILTER_DATA {
    TODAY,
    YESTERDAY,
    LAST_THREEDAYS,
    LAST_SEVENTDAYS
}

enum RECHARGE_STATUS {
    SUCCESS = 1,//成功-1
    PENDING = 2,//等待-2
    FAILURE = 3,//失败-3
    OPERATIONAL = 4//充值金额不对但是成功回调(人工审核)
}

enum RECHARGE_WEB_STATUS {
    SUCCESS = 1,//成功
    PENDING = 2,//等待
    FAILED = 3,//失败
    CANCEL = 4,//取消
    WAITING = 5,//充值金额不对但是成功回调
    OPERATION = 6,
    WAITING_PAYMENT = 7,
    WAITING_CHANGE_BALANCE = 8,
}

enum WITHDRAW_STATUS {
    PENDING = 1,//待到账
    SUCCESS = 2,//成功
    FAILURE = 3,//失败
    OPERATIONAL = 4,//人工审核(待返回)
    PENDING_APPROVAL = 5,//待审核
}

const RECHAGE_STATUS_DESC = {
    SUCCESS: "Successful",
    PENDING: "Pending",
    FAILED: "Unsuccessful",
    OPERATION: "Operational Handling",
    CANCEL: "Cancel",
    WAITING: "Waiting",
    WAITING_PAYMENT: "Waiting for Payment"
}



const WITHDRAW_STATUS_DESC = {
    PENDING: "Pending",
    SUCCESS: "Successful",
    FAILED: "Unsuccessful",
    OPERATION: "Operational Handling"
}

enum RECORD_TAG {
    DEPOSIT = 1,
    WITHDRAW = 2,
    AWARD = 3
}

export enum AWARD_UPDATE_TYPE {
    FIRST_RECHARGE = 12,
    FREE_REGISTRATION = 65,
    CASHBACK = 111,
    VIP_CASHBACK_119 = 119, // VIP Cashback //原来是119
    BING_PHONE = 294, // Bind mobile phone
    SIGN_UP_BONUS = 265, // Sign up bonus
    WEEKLY_SIGNIN = 310, // Weekly sign-in
    DAILY_BETTING = 311, // Daily betting rebate
    PAYDAY_BONUS = 312, // Payday bonus
    SPORT_FIRST_TIME = 313, // Sport first time rebate
    INVITE_FRIENDS = 236, // Invite friends
    INVITE_5GIFT = 237, // Invite 5 peoples gift
    INVITEE_GIFT = 238, // Invitee gift
    FIRST_DESPOSIT = 239, // First deposit bonus
    VIP_CASHBACK = 241, // VIP Cashback //原来是119
    SUPERACE_CASH = 242, // 
    JILI_GAMES_CASHBACK = 243,//Jili Games cashback
    CASINO_LEADERBOARD = 244, //排行榜奖励
    GET_EASTER_BONUS = 245,//Get Easter Bonus
    YB_SLOT_CASHBACK = 246,//YB Slot Cashback
    SPORTS_LOSS_CASHBACK = 247,//Sports Loss Cashback
    SPORTS_DAILY_BONUS = 248,//Sports Daily Bonus
    NBA_CHAMPION_PREDICTION = 249,//NBA Champion Prediction
    SPIN_ACTIVITY = 400,
    JILI_LEADERBOARD = 401, //jili排行榜奖励
    Valentine = 402, //情人节活动
    WEEKLY_PAYDAY = 403,//WeeklyPayday活动
    LATE_NIGHT_CASHBACK = 404,//Late Night Cashback

    REGISTER_USER = 10000,//新用户注册 奖励30
    BING_IPHONE_USER = 10001,//新用户绑定 原来这三个活动是系统弹窗改变过来
}

export const AWARD_NAME = {
    FREE_REGISTRATION: "Free Registration Bonus",
    CASHBACK: "Daily Cashback",
    BING_PHONE : 'Bind mobile phone',
    SIGN_UP_BONUS : 'Sign up bonus',
    WEEKLY_SIGNIN : 'Weekly sign-in',
    DAILY_BETTING : 'Daily Cashback',
    PAYDAY_BONUS : 'Payday bonus',
    SPORT_FIRST_TIME : 'Sport first time rebate',
    INVITE_FRIENDS :'Invite friends',
    INVITE_5GIFT:'Invite 5 peoples gift',
    INVITEE_GIFT:'Invitee gift',
    FIRST_DESPOSIT:'First deposit bonus',
    VIP_CASHBACK:'VIP Cashback', //原来是119
    SUPERACE_CASH:'SuperAce 1.0% Cashback',
    JILI_GAMES_CASH:'Jili Games 1.0% Cashback',
    CASINO_LEADERBOARD:"Casino Elite Wealth Leaderboard",
    GET_EASTER_BONUS:"Get Easter Bonus",
    YB_SLOT_CASHBACK:"YB Extra 0.35% Cashback",
    SPORTS_LOSS_CASHBACK:"Sports Loss Cashback",
    SPORTS_DAILY_BONUS:"Sports Daily Bonus",
    NBA_CHAMPION_PREDICTION:"NBA Champion Prediction",
    SPIN_ACTIVITY: "Spin Activity",
    JILI_LEADERBOARD: "JILI Slot Rank",
    Valentine: "Valentine",
    WEEKLY_PAYDAY: "Weekly payday",
    LATE_NIGHT_CASHBACK: "Late Night Cashback",

    REGISTER_USER: "Welcome to NUSTAR Online!",
    BING_IPHONE_USER: "Congratulations on successfully\nbinding your mobile phone number",
}

const DATE_TYPE = {
    TODAY:"Today",
    YESTERDAY:"Yesterday",
    LAST_THREEDAYS:"Last 3 days",
    LAST_SEVENTDAYS:"Last 7 days"
}

@ccclass
export default class Transations extends UICommon {

    @property(cc.PageView)
    pageView: cc.PageView = null;

    @property(cc.ScrollView)
    depositList: cc.ScrollView = null;

    @property(cc.ScrollView)
    withdrawList: cc.ScrollView = null;

    @property(cc.ScrollView)
    awardList: cc.ScrollView = null;

    @property(cc.Button)
    btnDeposit: cc.Button = null;

    @property(cc.Button)
    btnWithdraw: cc.Button = null;

    @property(cc.Button)
    btnAward: cc.Button = null;

    @property(cc.Button)
    btnFilterTime: cc.Button = null;

    @property(cc.Button)
    btnFilterStatus: cc.Button = null;

    @property(cc.Button)
    btnClearFilter: cc.Button = null;

    @property(cc.Node)
    itemLayoutModel: cc.Node = null;

    @property(cc.Node)
    itemModel: cc.Node = null;

    @property(cc.Label)
    labTimeFilter: cc.Label = null

    @property(cc.Label)
    labStatusFilter: cc.Label = null

    @property(cc.Node)
    statusBg: cc.Node = null

    @property(cc.Node)
    tagBgNode: cc.Node = null;

    @property(cc.Node)
    dnoRecord: cc.Node = null;

    @property(cc.Node)
    wnoRecord: cc.Node = null;

    @property(cc.Node)
    anoRecord: cc.Node = null;

    @property([cc.SpriteFrame])
    spriteFreams: cc.SpriteFrame[] = [];

    filterStatusPrefab: cc.Prefab = null;
    detialsPrefab: cc.Prefab = null;
    batchPrefab: cc.Prefab = null;//子订单 的prafab
    filterTimePrefab: cc.Prefab = null;
    filterStatusResult = [];
    filterTimeResult = [];
    filterTimeData = FILTER_DATA.LAST_SEVENTDAYS;
    filterStatusData = [];
    tagType = null;

    deposit = [];
    withdraw = [];
    award = [];
    current_deposit_page:number = 1;
    current_withdraw_page:number = 1;
    current_award_page:number = 1;
    total_deposit_page:number = 1;
    total_withdraw_page:number = 1;
    total_award_page:number = 1;

    recordtype = TAG_TYPE.DEPOSIT;
    depositStatus = "";
    withdrawStatus = "";

    _isQuerying = false

    openType = null;

    enum_data = null;

    //三种状态颜色
    font_color = [
        cc.color(17,190,107),//绿色
        cc.color(255,72,72),//红色
        cc.color(255,183,1),//黄色
        cc.color(0,0,0),//黑色
        cc.color(153,153,153)//灰色 奖励使用
    ]

    onLoad () {
        this.filterTimeResult = ["Last 7 days"];
        this.filterStatusResult = ["All"];
        this.pageView.node.on('page-turning', this.turingPageListener, this);
        // cc.director.on("Multi_Toggle_Filter_Result",this.filterStatus,this);
        // cc.director.on("Single_Toggle_Filter_Result",this.filterTime,this);
        cc.director.on("Single_Toggle_Filter_Result",this.filterStatus,this);
        this.depositList.node.on(cc.Node.EventType.TOUCH_MOVE, this.onDepositScroll, this);
        this.withdrawList.node.on(cc.Node.EventType.TOUCH_MOVE, this.onWithdrawScroll, this);
        this.awardList.node.on(cc.Node.EventType.TOUCH_MOVE, this.onAwardScroll, this);

        this.filterTimeData = FILTER_DATA.LAST_SEVENTDAYS;
        this.dnoRecord.active = false;
        this.wnoRecord.active = false;
        this.anoRecord.active = false;
        Global.getInstance().actAdjustmentData(()=>{
            this.enum_data = Global.getInstance().adjustment_data;
            this.loadDepositData(1);
            this.loadWithdrawData(1);
            this.loadAwardData(1);
        })
    }

    start () {
        this.scheduleOnce(()=>{
            let roundret = this.tagBgNode.getComponent(RoundRectMask);
            roundret.radius = 0.48;
        },0.1)
        this.initLayOut();
        let tempnode = this.node.getChildByName("temp");
        this.pageView.insertPage(cc.instantiate(tempnode),0);
        // this.pageView.scrollToPage(1,0.01);
        this.clickTagBnts(null,this.tagType);
    }

    initLayOut() {
        let offset = cc.winSize.height - cc.view.getDesignResolutionSize().height;
        this.pageView.node.height += offset;
        this.depositList.node.height += offset;
        this.withdrawList.node.height += offset;
        this.awardList.node.height += offset;
        this.pageView.node.getChildByName("view").height += offset;
        this.depositList.node.getChildByName("view").height += offset;
        this.withdrawList.node.getChildByName("view").height += offset;
        this.awardList.node.getChildByName("view").height += offset;
    }

    onDepositScroll() {
        if (this._isQuerying) {
            return;
        }
        let offset = this.depositList.getScrollOffset().y;
        let maxoffset = this.depositList.getMaxScrollOffset().y;
        if ((maxoffset - offset) < 20) {
            if (this.current_deposit_page <= 0) {
                this.current_deposit_page = 1;
            }
            if (this.current_deposit_page < this.total_deposit_page) {
                let page = this.current_deposit_page + 1; 
                this.loadDepositData(page);
            }
        }
    }

    onWithdrawScroll() {
        if (this._isQuerying) {
            return;
        }
        let offset = this.withdrawList.getScrollOffset().y;
        let maxoffset = this.withdrawList.getMaxScrollOffset().y;
        if ((maxoffset - offset) < 20) {
            if (this.current_withdraw_page <= 0) {
                this.current_withdraw_page = 1;
            }
            if (this.current_withdraw_page < this.total_withdraw_page) {
                let page = this.current_withdraw_page + 1; 
                this.loadWithdrawData(page);
            }
        }
    }

    onAwardScroll() {
        if (this._isQuerying) {
            return;
        }
        let offset = this.awardList.getScrollOffset().y;
        let maxoffset = this.awardList.getMaxScrollOffset().y;
        if ((maxoffset - offset) < 20) {
            if (this.current_award_page <= 0) {
                this.current_award_page = 1;
            }
            if (this.current_award_page < this.total_award_page) {
                let page = this.current_award_page + 1;
                this.loadAwardData(page);
            }
        }
    }

    /**加载deposit列表数据 */
    loadDepositData(page) {
        this._isQuerying = true
        HttpUtils.getInstance().get(3, 3, this, "/common/api/get/recharge/record", {
            token: Global.getInstance().token,
            date_type: this.filterTimeData,
            status: this.depositStatus || "",
            page: page,
            page_number: 15
        }, (response) => {
            if (!this.node || !this.node.isValid) {
                return;
            }
            
            let curPage = response.data.current_page;
            let totalPage = response.data.total_page;
            if (typeof curPage == "number") this.current_deposit_page = curPage;
            else if (typeof curPage == "string") this.current_deposit_page = parseInt(curPage);
            if (typeof totalPage == "number") this.total_deposit_page = totalPage;
            else if (typeof totalPage == "string") this.total_deposit_page = parseInt(totalPage);

            if (response.data.length == 0) {
                this.dnoRecord.active = true;
            }

            if (response.data.list && response.data.list.length > 0) {
                this.dnoRecord.active = false;
                if (this.current_deposit_page == 1) {
                    this.initItemsList(response.data.list,this.depositList, TAG_TYPE.DEPOSIT, this.total_award_page==1);
                } else {
                    this.addItemsList(response.data.list,this.depositList, TAG_TYPE.DEPOSIT);
                }
                
            }
            this._isQuerying = false;
        },(response)=>{
            this._isQuerying = false;
        });
    }

    /**加载withdraw列表数据 */
    loadWithdrawData(page) {
        this._isQuerying = true
        HttpUtils.getInstance().get(3, 3, this, "/common/api/exchange/list", {
            token: Global.getInstance().token,
            date_type: this.filterTimeData,
            status: this.withdrawStatus || "",
            page: page,
            page_number: 15
        }, (response) => {
            if (!this.node || !this.node.isValid) {
                return;
            }

            let curPage = response.data.current_page;
            let totalPage = response.data.total_page;
            if (typeof curPage == "number") this.current_withdraw_page = curPage;
            else if (typeof curPage == "string") this.current_withdraw_page = parseInt(curPage);
            if (typeof totalPage == "number") this.total_withdraw_page = totalPage;
            else if (typeof totalPage == "string") this.total_withdraw_page = parseInt(totalPage);

            if (response.data.count == 0) {
                this.wnoRecord.active = true;
            }

            if (response.data.list && response.data.list.length > 0) {
                this.wnoRecord.active = false;
                if (this.current_withdraw_page == 1) {
                    this.initItemsList(response.data.list,this.withdrawList, TAG_TYPE.WITHDRAW, this.total_award_page==1);
                } else {
                    this.addItemsList(response.data.list,this.withdrawList, TAG_TYPE.WITHDRAW);
                }
            }
            this._isQuerying = false;
        },(response)=>{
            this._isQuerying = false;
        });
    }

    /**加载award列表数据 */
    loadAwardData(page) {
        this._isQuerying = true
        HttpUtils.getInstance().post(3, 3, this, "/common/api/get/award", {
            token: Global.getInstance().token,
            date_type: this.filterTimeData,
            page: page,
            page_number: 15
        }, (response) => {
            if (!this.node || !this.node.isValid) {
                return;
            }

            let curPage = response.data.current_page;
            let totalPage = response.data.total_page;
            if (typeof curPage == "number") this.current_award_page = curPage;
            else if (typeof curPage == "string") this.current_award_page = parseInt(curPage);
            if (typeof totalPage == "number") this.total_award_page = totalPage;
            else if (typeof totalPage == "string") this.total_award_page = parseInt(totalPage);

            if (response.data.list.length == 0) {
                this.anoRecord.active = true;
            }

            if (response.data.list && response.data.list.length > 0) {
                this.anoRecord.active = false;
                if (this.current_award_page == 1) {
                    this.initItemsList(response.data.list,this.awardList, TAG_TYPE.AWARD, this.total_award_page==1);
                } else {
                    this.addItemsList(response.data.list,this.awardList, TAG_TYPE.AWARD);
                }
            }
            this._isQuerying = false;
        },(response)=>{
            this._isQuerying = false;
        });
    }

    /**初始化listItem */
    initItemsList(msg, listNode, tagRecord, onlyone) {
        let deposit = [];
        let lastItemTime = "";
        let tempinx = 0;
        for (let index = 0; index < msg.length; index++) {
            let element = msg[index];
            if (tagRecord != TAG_TYPE.AWARD) {
                if (this.formatTime(element) == lastItemTime) {
                    deposit[tempinx].push(element);
                } else {
                    if (index != 0) {
                        tempinx = tempinx + 1;
                    }
                    deposit[tempinx] = [];
                    deposit[tempinx].push(element);
                }
            } else {
                if (this.formatAwardTime(element) == lastItemTime) {
                    deposit[tempinx].push(element);
                } else {
                    if (index != 0) {
                        tempinx = tempinx + 1;
                    }
                    deposit[tempinx] = [];
                    deposit[tempinx].push(element);
                }
            }
            if (tagRecord != TAG_TYPE.AWARD) lastItemTime = this.formatTime(element);
            else lastItemTime = this.formatAwardTime(element);
        }
        let y = 0;
        let height = 0;
        let totalHeight = 0;
        listNode.content.removeAllChildren(true);
        listNode.scrollToTop();
        for (let index = 0; index < deposit.length; index++) {
            let itmeeml = deposit[index];
            if (!itmeeml || itmeeml.length == 0) {
                continue;
            }
            let element = cc.instantiate(this.itemLayoutModel);
            if (tagRecord != TAG_TYPE.AWARD) element.getChildByName("lab_recordtime").getComponent(cc.Label).string = this.formatTime(itmeeml[0]);
            else element.getChildByName("lab_recordtime").getComponent(cc.Label).string = this.formatAwardTime(itmeeml[0]);
            if (tagRecord == TAG_TYPE.DEPOSIT) {
                element.name = RECORD_TAG.DEPOSIT + this.formatTime(itmeeml[0]);
            } else if (tagRecord == TAG_TYPE.WITHDRAW) {
                element.name = RECORD_TAG.WITHDRAW + this.formatTime(itmeeml[0]);
            } else if (tagRecord == TAG_TYPE.AWARD) {
                element.name = RECORD_TAG.AWARD + this.formatAwardTime(itmeeml[0]);
            }

            let masknode = element.getChildByName("masknode");
            let itemBg = masknode.getChildByName("item_bg");
            itemBg.height = 220 + (deposit[index].length-1)*180;
            element.height = 370 + (deposit[index].length-1)*180;
            masknode.height = itemBg.height;
            height = height + element.height + 30;
            totalHeight = totalHeight + height;
            element.position = new cc.Vec3(0,y-25);
            y = y - element.height - 30;
            element.parent = listNode.content;
            for (let k = 0; k < deposit[index].length; k++) {
                let item = cc.instantiate(this.itemModel);
                if (tagRecord == TAG_TYPE.DEPOSIT) {
                    this.initDepositItem(item,deposit,index,k,tagRecord);
                } else if (tagRecord == TAG_TYPE.WITHDRAW) {
                    this.initWithdrawItem(item,deposit,index,k,tagRecord);
                } else if (tagRecord == TAG_TYPE.AWARD) {
                    this.initAwardItem(item,deposit,index,k,tagRecord);
                }
                    
                item.position = new cc.Vec3(0,-260-180*k);
                item.parent = element;
            }
        }
        listNode.content.height = height ;
        if (onlyone && listNode.content.height < listNode.node.height) {
            listNode.content.height = listNode.node.height;
        }
    }

    formatTime(e) {
        let tstamp = utils.timeToTimestamp(e.created_at); 
        let eTime = utils.dateToMDY(new Date(tstamp));
        return eTime;
    }

    formatAwardTime(e){
        let tstamp = utils.timeToTimestamp(e.updated_at); 
        let eTime = utils.dateToMDY(new Date(tstamp));
        return eTime;
    }

    /**新增listItem */
    addItemsList(msg, listNode, tagRecord) {
        let deposit = [];
        let lastItemTime = "";
        let tempinx = 0;
        for (let index = 0; index < msg.length; index++) {
            let element = msg[index];

            if (tagRecord != TAG_TYPE.AWARD) {
                if (this.formatTime(element) == lastItemTime) {
                    deposit[tempinx].push(element);
                } else {
                    if (index != 0) {
                        tempinx = tempinx + 1;
                    }
                    deposit[tempinx] = [];
                    deposit[tempinx].push(element);
                }
            } else {
                if (this.formatAwardTime(element) == lastItemTime) {
                    deposit[tempinx].push(element);
                } else {
                    if (index != 0) {
                        tempinx = tempinx + 1;
                    }
                    deposit[tempinx] = [];
                    deposit[tempinx].push(element);
                }
            }
            if (tagRecord != TAG_TYPE.AWARD) lastItemTime = this.formatTime(element);
            else lastItemTime = this.formatAwardTime(element);
        }
        let y = -listNode.content.height;
        let height = listNode.content.height;
        for (let index = 0; index < deposit.length; index++) {
            let itmeeml = deposit[index];
            if (!itmeeml || itmeeml.length == 0) {
                continue;
            }
            let element;
            if (tagRecord == TAG_TYPE.DEPOSIT) {
                element = listNode.content.getChildByName(RECORD_TAG.DEPOSIT + this.formatTime(itmeeml[0]));
            } else if (tagRecord == TAG_TYPE.WITHDRAW) {
                element = listNode.content.getChildByName(RECORD_TAG.WITHDRAW + this.formatTime(itmeeml[0]));
            } else if (tagRecord == TAG_TYPE.AWARD) {
                element = listNode.content.getChildByName(RECORD_TAG.AWARD + this.formatAwardTime(itmeeml[0]));
            }
             
            let oldHeiht = 0;
            let xNum = 0;
            if (!element) {
                element = cc.instantiate(this.itemLayoutModel);
                if (tagRecord == TAG_TYPE.DEPOSIT) {
                    element.name = RECORD_TAG.DEPOSIT + this.formatTime(itmeeml[0]);
                } else if (tagRecord == TAG_TYPE.WITHDRAW) {
                    element.name = RECORD_TAG.WITHDRAW + this.formatTime(itmeeml[0]);
                } else if (tagRecord == TAG_TYPE.AWARD) {
                    element.name = RECORD_TAG.AWARD + this.formatAwardTime(itmeeml[0]);
                }
                if (tagRecord != TAG_TYPE.AWARD) element.getChildByName("lab_recordtime").getComponent(cc.Label).string = this.formatTime(itmeeml[0]);
                else element.getChildByName("lab_recordtime").getComponent(cc.Label).string = this.formatAwardTime(itmeeml[0]);
                oldHeiht = 0;
                xNum = 0;
                element.position = new cc.Vec3(0,y);
                element.parent = listNode.content;
                element.height = element.height + (deposit[index].length-1+xNum)*180;
                height = height + element.height + 30;
            } else {
                oldHeiht = element.height-370;
                xNum = 1;
                y = y + element.height;
                element.height = element.height + (deposit[index].length-1+xNum)*180;
                height = height + (deposit[index].length-1+xNum)*180 + 30;
            }
            //适配一下白色item底
            let masknode = element.getChildByName("masknode");
            let itemBg = masknode.getChildByName("item_bg");
            itemBg.height = itemBg.height + (deposit[index].length-1+xNum)*180;
            masknode.height = itemBg.height;
            masknode.getComponent(RoundRectMask).radius = 40;

            y = y - element.height + 10;
            for (let k = 0; k < deposit[index].length; k++) {
                let item = cc.instantiate(this.itemModel);
                if (tagRecord == TAG_TYPE.DEPOSIT) {
                    this.initDepositItem(item,deposit,index,k,tagRecord);
                } else if (tagRecord == TAG_TYPE.WITHDRAW) {
                    this.initWithdrawItem(item,deposit,index,k,tagRecord);
                } else if (tagRecord == TAG_TYPE.AWARD) {
                    this.initAwardItem(item,deposit,index,k,tagRecord);
                }
                item.position = new cc.Vec3(0,-260-180*(k+xNum)-oldHeiht);
                item.parent = element;
            }
        }
        listNode.content.height = height;
        if (listNode.content.height < listNode.node.height) {
            listNode.content.height = listNode.node.height;
        }
    }

    /**初始化deposit itemModel */
    initDepositItem(item,deposit,index,k,tagRecord) {
        let numlab = item.getChildByName("lab_num").getComponent(cc.Label);
        let sp =  item.getChildByName("sp_icon").getComponent(cc.Sprite);
        sp.spriteFrame = this.spriteFreams[0];
        if (deposit[index][k].pay_channel == RECORD_TYPE.ADJUSTMENT) {
            item.getChildByName("lab_recordtype").getComponent(cc.Label).string = RECORD_TYPE.TRANSFER;
            sp.spriteFrame = this.spriteFreams[1];
        } else if (deposit[index][k].pay_channel == RECORD_TYPE.MAYAPAY ||
                   deposit[index][k].pay_channel == RECORD_TYPE.MAYA_WEB ||
                   deposit[index][k].pay_channel == RECORD_TYPE.GCASH_WEB) {
            item.getChildByName("lab_recordtype").getComponent(cc.Label).string = RECORD_TYPE.DEPOSIT;
            //根据支付渠道 改变icon
            let channel = deposit[index][k].pay_channel; 
            if (channel == RECORD_TYPE.MAYAPAY || channel == RECORD_TYPE.MAYA_WEB) {
                sp.spriteFrame = this.spriteFreams[0];
            } else if (channel == RECORD_TYPE.GCASH_WEB) {
                sp.spriteFrame = this.spriteFreams[2];
            }
        }else{
            item.getChildByName("lab_recordtype").getComponent(cc.Label).string = RECORD_TYPE.DEPOSIT;
        }
        let method = deposit[index][k]?.payment_method
        if(method && method.length > 0){
            //如果存在 则直接显示支付方式
            if(method.indexOf('Gcash') != -1){
                sp.spriteFrame = this.spriteFreams[2];
            }else if(method.indexOf('Maya') != -1){
                sp.spriteFrame = this.spriteFreams[0];
            }
        }

        let rechargeStatus = item.getChildByName("lab_status"); 
        if (deposit[index][k].recharge_status == RECHARGE_WEB_STATUS.SUCCESS) {
            rechargeStatus.getComponent(cc.Label).string = RECHAGE_STATUS_DESC.SUCCESS;
            rechargeStatus.color = this.font_color[0];
        } else if (deposit[index][k].recharge_status == RECHARGE_WEB_STATUS.PENDING ||
                   deposit[index][k].recharge_status == RECHARGE_WEB_STATUS.WAITING ||
                   deposit[index][k].recharge_status == RECHARGE_WEB_STATUS.OPERATION ||
                   deposit[index][k].recharge_status == RECHARGE_WEB_STATUS.WAITING_CHANGE_BALANCE ||
                   deposit[index][k].recharge_status == RECHARGE_WEB_STATUS.WAITING_PAYMENT
        ) {
            rechargeStatus.getComponent(cc.Label).string = RECHAGE_STATUS_DESC.PENDING;
            rechargeStatus.color = this.font_color[2]
        } else if (deposit[index][k].recharge_status == RECHARGE_WEB_STATUS.FAILED) {
            //如果有 需要显示的错误标识 直接显示错误信息
            if(deposit[index][k].err_msg && deposit[index][k].err_msg.length > 0){
                rechargeStatus.getComponent(cc.Label).string = deposit[index][k].err_msg;
            }else{
                rechargeStatus.getComponent(cc.Label).string = WITHDRAW_STATUS_DESC.FAILED;
            }
            rechargeStatus.color = this.font_color[1];
        }

        numlab.string = "+"+utils.formatNumberWithCommas(deposit[index][k].amount / GOLD_RATIO); 
        if (deposit[index][k].amount > 0) {
            if (deposit[index][k].recharge_status == RECHARGE_STATUS.SUCCESS) {
                numlab.node.color = this.font_color[3];
                numlab.string = "+"+utils.formatNumberWithCommas(deposit[index][k].amount / GOLD_RATIO); 
            } else if (deposit[index][k].recharge_status == RECHARGE_WEB_STATUS.PENDING) {
                numlab.node.color = this.font_color[3];
                numlab.string = "+"+utils.formatNumberWithCommas(deposit[index][k].amount / GOLD_RATIO);
            } else if (deposit[index][k].recharge_status == RECHARGE_WEB_STATUS.FAILED) {
                numlab.node.color = this.font_color[3];
                numlab.string = "+"+utils.formatNumberWithCommas(deposit[index][k].amount / GOLD_RATIO);
            } else {
                numlab.node.color = this.font_color[3];
                numlab.string = "+"+utils.formatNumberWithCommas(deposit[index][k].amount / GOLD_RATIO);
            }
        } 
        numlab.node.color = this.font_color[3];
        item.getChildByName("btn_detial").on(cc.Node.EventType.TOUCH_END,(event)=>{
            this.showDetials(deposit[index][k], tagRecord);
        },this);
    }

    /**初始化withdraw itemModel */
    initWithdrawItem(item,withdraw,index,k,tagRecord) {
    //         account_no: "**********"audit_status: 1created_at: "2025-06-25 16:13:46"id: "104347419213496327"images: nullname: "Adjustment"
    // order_no: "GC101055832081416198685bafba66cd5"paid_at: "2025-06-25 16:13:46"payment_method: "Gcash"quantity: 1remark: "订单创建成功"ship_fail_code
    // : 0ship_status: 1status_desc: "Pending"sys_remark: "cumulative withdrawal amount on the day"total_amount: 10000type: 3
        let numlab = item.getChildByName("lab_num").getComponent(cc.Label);
        let sp =  item.getChildByName("sp_icon").getComponent(cc.Sprite);
        sp.spriteFrame = this.spriteFreams[0];
        if (withdraw[index][k].sys_remark == RECORD_TYPE.ADJUSTMENT) {
            item.getChildByName("lab_recordtype").getComponent(cc.Label).string = RECORD_TYPE.TRANSFER;
            sp.spriteFrame = this.spriteFreams[1];
        } else {
            item.getChildByName("lab_recordtype").getComponent(cc.Label).string = RECORD_TYPE.WITHDRAWAL;
            //提现方式为MAYA_WEB
            if (withdraw[index][k].type == E_FUND_TYPE.MAYA_WEB) {
                sp.spriteFrame = this.spriteFreams[0];
            } else if (withdraw[index][k].type == E_FUND_TYPE.GCASH_WEB) {
                //提现方式为GCASH_WEB
                sp.spriteFrame = this.spriteFreams[2];
            } else if (withdraw[index][k].type == E_FUND_TYPE.MAYA_MINI) {
                //提现方式为MAYA_MINI
                sp.spriteFrame = this.spriteFreams[0];
            }
        }
        let method = withdraw[index][k]?.payment_method
        let frame_with = null;
        if(method && method.length > 0){
            //如果存在 则直接显示支付方式
            if(method.indexOf('Gcash') != -1){
                sp.spriteFrame = this.spriteFreams[2];
                frame_with = this.spriteFreams[2];
            }else if(method.indexOf('Maya') != -1){
                sp.spriteFrame = this.spriteFreams[0];
                frame_with = this.spriteFreams[0];
            }
        }
        let withdrawStatus = item.getChildByName("lab_status"); 
        /**
            `ship_status` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '打款状态: 1:待到账,2:成功,3:失败\n',
            `audit_status` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '审核状态：1:风控待审核，2：审核通过；3：审核不通过'',4:待返回，5待审核',
         */
        if (withdraw[index][k].ship_status == WITHDRAW_STATUS.FAILURE) {
            //如果有 需要显示的错误标识 直接显示错误信息
            if(withdraw[index][k].err_msg && withdraw[index][k].err_msg.length > 0){
                withdrawStatus.getComponent(cc.Label).string = withdraw[index][k].err_msg;
            }else{
                withdrawStatus.getComponent(cc.Label).string = WITHDRAW_STATUS_DESC.FAILED;
            }
            withdrawStatus.color = this.font_color[1];
            numlab.node.color = this.font_color[3];
        }
        if (withdraw[index][k].ship_status == WITHDRAW_STATUS.SUCCESS) {
            withdrawStatus.getComponent(cc.Label).string = WITHDRAW_STATUS_DESC.SUCCESS;
            withdrawStatus.color = this.font_color[0];
            numlab.node.color = this.font_color[3];
        }
        if (withdraw[index][k].ship_status == WITHDRAW_STATUS.PENDING) {
            if (withdraw[index][k].audit_status == WITHDRAW_STATUS.PENDING ||
                withdraw[index][k].audit_status == WITHDRAW_STATUS.OPERATIONAL ||
                withdraw[index][k].audit_status == WITHDRAW_STATUS.PENDING_APPROVAL || 
                withdraw[index][k].audit_status == WITHDRAW_STATUS.SUCCESS) {
                    withdrawStatus.getComponent(cc.Label).string = WITHDRAW_STATUS_DESC.PENDING;
                withdrawStatus.color = this.font_color[2];
                numlab.node.color = this.font_color[3];
            }
            if (withdraw[index][k].audit_status == WITHDRAW_STATUS.FAILURE) {
                // withdrawStatus.getComponent(cc.Label).string = WITHDRAW_STATUS_DESC.FAILED;
                withdrawStatus.color = this.font_color[1];
                numlab.node.color = this.font_color[3];
                //如果有 需要显示的错误标识 直接显示错误信息
                if(withdraw[index][k].err_msg && withdraw[index][k].err_msg.length > 0){
                    withdrawStatus.getComponent(cc.Label).string = withdraw[index][k].err_msg;
                }else{
                    withdrawStatus.getComponent(cc.Label).string = WITHDRAW_STATUS_DESC.FAILED;
                }
            }
        }
        if(withdraw[index][k].quantity > 1){
            item.getChildByName("order_num_bg").active = true;
            item.getChildByName("order_num_bg").getChildByName('label_num').getComponent(cc.Label).string = withdraw[index][k].quantity + ''
            item.getChildByName("lab_recordtype").getComponent(cc.Label).string = RECORD_TYPE.BATCH_WITHDRAWAL;//组合订单
        }else{
            item.getChildByName("order_num_bg").active = false;
        }
        numlab.string = "+"+utils.formatNumberWithCommas(withdraw[index][k].total_amount / GOLD_RATIO); 
        if (withdraw[index][k].total_amount > 0) {
            numlab.string = "-"+utils.formatNumberWithCommas(withdraw[index][k].total_amount / GOLD_RATIO);
        } 

        item.getChildByName("btn_detial").on(cc.Node.EventType.TOUCH_END,(event)=>{
            if(withdraw[index][k].quantity > 1){
                this.showBatch(withdraw[index][k],frame_with)
            }else{
                this.showDetials(withdraw[index][k], tagRecord);
            }
        },this);
    }

    /**初始化award itemModel */
    initAwardItem(item, award, index, k, tagRecord) {
        item.getChildByName("lab_recordtype").getComponent(cc.Label).string = RECORD_TYPE.REWARD;
        let sp =  item.getChildByName("sp_icon").getComponent(cc.Sprite);
        sp.spriteFrame = this.spriteFreams[1];
        let update_type = award[index][k].update_type
        let labStatus = item.getChildByName("lab_status").getComponent(cc.Label);
        labStatus.node.color = this.font_color[4];
        item.getChildByName("sprite_status").active = false;
        //筛选出满足条件的adjustment_type
        if(this.enum_data && this.enum_data.length > 0){
            let target_type = this.enum_data.filter(ele => ele.change_type == update_type);
            if(target_type.length > 0){
                let elem = target_type[0];
                labStatus.string = elem.title;
                if(!elem.title || update_type == AWARD_UPDATE_TYPE.FREE_REGISTRATION){
                    let register_str = `Free ₱${award[index][k].amount / GOLD_RATIO} Registration Bonus`
                    Global.instance.init_enum_typestr(update_type, '', register_str, labStatus);
                }
            }
        }else{
            //防止接口出问题以前的枚举值不显示
            let register_str = `Free ₱${award[index][k].amount / GOLD_RATIO} Registration Bonus`
            Global.instance.init_enum_typestr(update_type, '', register_str, labStatus);
        }
        
        let numlab = item.getChildByName("lab_num").getComponent(cc.Label);
        let award_num = award[index][k].amount
        if(award_num >= 0){
            numlab.string = "+"+utils.formatNumberWithCommas(award_num / GOLD_RATIO);
            numlab.node.color = this.font_color[3];
        }else{
            numlab.string = ""+utils.formatNumberWithCommas(award_num / GOLD_RATIO);
            numlab.node.color = this.font_color[3];
        }
        
        item.getChildByName("btn_detial").on(cc.Node.EventType.TOUCH_END,(event)=>{
            this.showDetials(award[index][k], tagRecord);
        },this);
    }

    show(arg) {
        this.tagType = arg;
        if(arg.type) this.tagType = arg.type;
        if(arg.openMark) {//为了标记是从哪个入口打开的Transation
            this.openType = arg.openMark;
        }
    }

    onDestroy(): void {
        // cc.director.off("Multi_Toggle_Filter_Result",this.filterStatus,this);
        cc.director.off("Single_Toggle_Filter_Result",this.filterStatus,this);
    }
    showBatch(info,frame_with) {
        let cb = ()=>{
            let node = cc.instantiate(this.batchPrefab);
            node.position = new cc.Vec3(0,0);
            let script = node.getComponent(TransationsBatch);
            script.initData(info,this.filterTimeData,frame_with);
            node.parent = this.node;
        }
        if (!this.batchPrefab) {
            uiManager.instance.loadPrefabByLoading(UI_PATH_DIC.TransationsBatch,cc.Prefab,(finish, total, item) => {
            }, (err, prefab: any) => {
                    if (err) {
                        return;
                    }
                    this.batchPrefab = prefab;
                    cb();
            });
        } else {
            cb();
        }
    }

    showDetials(info, tagRecord) {
        let cb = ()=>{
            let node = cc.instantiate(this.detialsPrefab);
            node.position = new cc.Vec3(0,0);
            let script = node.getComponent(TrasationsDetials);
            script.initData(info, this.filterTimeData, tagRecord, Global.getInstance().adjustment_data);
            node.parent = this.node;
        }
        if (!this.detialsPrefab) {
            uiManager.instance.loadPrefabByLoading(UI_PATH_DIC.TransationsDetail,cc.Prefab,(finish, total, item) => {
            }, (err, prefab: any) => {
                    if (err) {
                        return;
                    }
                    this.detialsPrefab = prefab;
                    cb();
            });
        } else {
            cb();
        }
    }

    turingPageListener() {
        let curIndex = this.pageView.getCurrentPageIndex();
        this.btnDeposit.node.opacity = 0;
        this.btnWithdraw.node.opacity = 0;
        this.btnAward.node.opacity = 0;
        if (curIndex == 0) {
            this.node.destroy();
        } else if (curIndex == 1) {
            this.btnDeposit.node.opacity = 255; 
            this.recordtype = TAG_TYPE.DEPOSIT; 
            this.statusBg.active = true;
        } else if (curIndex == 2) {
            this.btnWithdraw.node.opacity = 255;
            this.recordtype = TAG_TYPE.WITHDRAW; 
            this.statusBg.active = true;
        } else if (curIndex == 3) {
            this.btnAward.node.opacity = 255;
            this.recordtype = TAG_TYPE.AWARD;
            this.statusBg.active = false;
        }
    }

    filterStatus(result) {
        this.depositStatus = "";
        this.withdrawStatus = "";
        this.filterStatusResult = result;

        let dpsStatus = "";
        let withStatus = ""
        for (let index = 0; index < result.length; index++) {
            let element = result[index];
            switch (element) {
                case RECHAGE_STATUS_DESC.SUCCESS:
                    dpsStatus = RECHARGE_WEB_STATUS.SUCCESS.toString();
                    withStatus = WITHDRAW_STATUS.SUCCESS.toString();
                    break;
                case RECHAGE_STATUS_DESC.PENDING:
                    dpsStatus = RECHARGE_WEB_STATUS.PENDING.toString();
                    withStatus = WITHDRAW_STATUS.PENDING.toString();
                    break;
                case RECHAGE_STATUS_DESC.FAILED:
                    dpsStatus = RECHARGE_WEB_STATUS.FAILED.toString();
                    withStatus = WITHDRAW_STATUS.FAILURE.toString();
                    break;
                case RECHAGE_STATUS_DESC.OPERATION:
                    dpsStatus = RECHARGE_WEB_STATUS.OPERATION.toString();
                    withStatus = WITHDRAW_STATUS.OPERATIONAL.toString();
                    break;
                case RECHAGE_STATUS_DESC.CANCEL:
                    dpsStatus = RECHARGE_WEB_STATUS.CANCEL.toString();
                    break;
                case RECHAGE_STATUS_DESC.WAITING:
                    dpsStatus = RECHARGE_WEB_STATUS.WAITING.toString();
                    break;   
                case RECHAGE_STATUS_DESC.WAITING_PAYMENT:
                    dpsStatus = RECHARGE_WEB_STATUS.WAITING_PAYMENT.toString();
                    break; 
                default:
                    break;
            }
        }
        this.depositStatus = dpsStatus;
        this.withdrawStatus = withStatus;

        // const depositMap = {
        //     [RECHAGE_STATUS_DESC.SUCCESS]: [RECHARGE_STATUS.SUCCESS.toString()],
        //     [RECHAGE_STATUS_DESC.PENDING]: [RECHARGE_STATUS.PENDING.toString(), RECHARGE_STATUS.OPERATIONAL.toString()],
        //     [RECHAGE_STATUS_DESC.FAILED]: [RECHARGE_STATUS.FAILURE.toString()],
        //     [RECHAGE_STATUS_DESC.WAITING_PAYMENT]: [RECHARGE_WEB_STATUS.WAITING_PAYMENT.toString()],
        // };

        // const withdrawMap = {
        //     [WITHDRAW_STATUS_DESC.PENDING]: [WITHDRAW_STATUS.PENDING.toString(), WITHDRAW_STATUS.OPERATIONAL.toString()],
        //     [WITHDRAW_STATUS_DESC.SUCCESS]: [WITHDRAW_STATUS.SUCCESS.toString()],
        //     [WITHDRAW_STATUS_DESC.FAILED]: [WITHDRAW_STATUS.FAILURE.toString()],
        //     [WITHDRAW_STATUS_DESC.OPERATION]: [WITHDRAW_STATUS.OPERATIONAL.toString()]
        // }

        // let depositSta = [];
        // let withdrawSta = [];
        // for (let idx = 0; idx < this.filterStatusResult.length; idx++) {
        //     let status = this.filterStatusResult[idx];
        //     if (depositMap[status]) {
        //         depositSta = depositSta.concat(depositMap[status]);
        //     }
        //     if (withdrawMap[status]) {
        //         withdrawSta = withdrawSta.concat(withdrawMap[status]);
        //     }
        // }

        // this.depositStatus = Array.from(new Set(depositSta)).join(",");
        // this.withdrawStatus = Array.from(new Set(withdrawSta)).join(",");


        if (result.length > 0 && result[0] != "All") {
            this.statusBg.width = 430;
            this.btnClearFilter.node.active = true;
            this.statusBg.getChildByName("arrow").active = false;
            // this.labStatusFilter.string = "Status  +"+result.length;
            this.labStatusFilter.string = result[0]
            this.labStatusFilter.node.color = cc.color(172, 17, 64);
            // let wlenghth = utils.get_str_length_true(result[0])
            this.btnFilterStatus.node.width = 230;
            // this.statusBg.width = wlenghth + 100;
            let self = this;
            setTimeout(() => {
                self.statusBg.width = self.labStatusFilter.node.width + 160;
                self.btnClearFilter.node.x = self.labStatusFilter.node.width + 100
                this.statusBg.getChildByName("bg").width = self.statusBg.width;
                this.statusBg.getComponent(RoundRectMask).radius = 50;
            }, 100);
            if (this.recordtype == TAG_TYPE.DEPOSIT && result.length == 3) this.depositStatus = "";
            else if (this.recordtype == TAG_TYPE.WITHDRAW && result.length == 4) this.withdrawStatus = "";
        } else {
            this.statusBg.width = 297;
            this.statusBg.getChildByName("bg").width = 297;
            this.statusBg.getChildByName("arrow").active = true;
            this.statusBg.getComponent(RoundRectMask).radius = 50;
            this.btnFilterStatus.node.width = 297;
            this.btnClearFilter.node.active = false;
            this.labStatusFilter.string = "All";
            this.labStatusFilter.node.color = cc.color(34, 34, 34);
            this.depositStatus = "";
            this.withdrawStatus = "";
        }
        this.refreshTransListView();
    }

    refreshTransListView() {
        this.depositList.getComponent(cc.ScrollView).content.removeAllChildren(true);
        this.withdrawList.getComponent(cc.ScrollView).content.removeAllChildren(true);
        this.awardList.getComponent(cc.ScrollView).content.removeAllChildren(true);
        this.loadDepositData(1);
        this.loadWithdrawData(1);
        this.loadAwardData(1);
    }

    initRechargeListByFilter(msg) {
        let deposit = [];
        let lastItemTime = 0;
        let tempinx = 0;
        if (this.filterStatusData.length == 0) return msg;
        for (let index = 0; index < msg.length; index++) {
            let element = msg[index];
            let newStatus = this.filterRecordStatus(element);
            if (newStatus) {
                if (element.created_at.substring(0, 10) == lastItemTime) {
                    deposit[tempinx].push(element);
                } else {
                    if (index != 0) {
                        tempinx = tempinx + 1;
                    }
                    deposit[tempinx] = [];
                    deposit[tempinx].push(element);
                }
                lastItemTime = element.created_at.substring(0, 10);
            }
       }
       return deposit;
    }

    filterRecordStatus(s) {
        if (this.filterStatusData.length > 0) {
            for (let index = 0; index < this.filterStatusData.length; index++) {
                let statusId = this.filterStatusData[index];
                if (s.recharge_status == statusId) {
                    return true;
                }
            }
        }
        return false;
    }

    filterTime(result) {
        this.filterTimeData = FILTER_DATA.LAST_SEVENTDAYS;
        this.filterTimeResult = result;
        this.labTimeFilter.string = this.filterTimeResult[0];
        let self = this;
        setTimeout(() => {
            self.btnFilterTime.node.width = self.labTimeFilter.node.width + 76;
            self.btnFilterTime.node.getChildByName('Background').width = self.btnFilterTime.node.width
            self.btnFilterTime.node.parent.getComponent(cc.Layout).updateLayout();
        }, 200);
        if (this.filterTimeResult[0] == DATE_TYPE.TODAY) this.filterTimeData = FILTER_DATA.TODAY;
        else if (this.filterTimeResult[0] == DATE_TYPE.YESTERDAY) this.filterTimeData = FILTER_DATA.YESTERDAY;
        else if (this.filterTimeResult[0] == DATE_TYPE.LAST_THREEDAYS) this.filterTimeData = FILTER_DATA.LAST_THREEDAYS;
        else if (this.filterTimeResult[0] == DATE_TYPE.LAST_SEVENTDAYS) this.filterTimeData = FILTER_DATA.LAST_SEVENTDAYS;
        this.refreshTransListView();
    }

    clickTagBnts(event,tagType) {
        if (tagType == null) {
            tagType = TAG_TYPE.DEPOSIT;
        }
        this.btnDeposit.node.opacity = 0;
        this.btnWithdraw.node.opacity = 0;
        this.btnAward.node.opacity = 0;
        if (tagType == TAG_TYPE.DEPOSIT) {
            this.btnDeposit.node.opacity = 255;
            this.pageView.scrollToPage(1,0.2);
            this.pageView.getComponent(cc.Widget).top = 420;
        } else if (tagType == TAG_TYPE.WITHDRAW) {
            this.btnWithdraw.node.opacity = 255;
            this.pageView.getComponent(cc.Widget).top = 420;
            this.pageView.scrollToPage(2,0.2);
        } else if (tagType == TAG_TYPE.AWARD) {
            this.btnAward.node.opacity = 255;
            this.pageView.getComponent(cc.Widget).top = 300;
            this.pageView.scrollToPage(3,0.2);
        }
        this.pageView.getComponent(cc.Widget).updateAlignment();
        cc.director.on("Multi_Toggle_Filter_Result",this.filterStatus,this);
    }
    /**点击订单状态筛选 */
    clickStatusFilterBtn_new(event, userdata) {
        let cb = ()=>{
            let toggleItemsList;
            // if (this.recordtype == TAG_TYPE.DEPOSIT) toggleItemsList = ["All","Pending", "Successful", "Unsuccessful", "Waiting for Payment"];
            if (this.recordtype == TAG_TYPE.DEPOSIT) toggleItemsList = ["All","Pending", "Successful", "Unsuccessful"];
            else if (this.recordtype == TAG_TYPE.WITHDRAW) toggleItemsList = ["All","Pending", "Successful", "Unsuccessful"];
            let node = cc.instantiate(this.filterStatusPrefab);
            node.getComponent(SingleToggle).setToggleItems(toggleItemsList,this.filterStatusResult,"Select Status");
            node.position = new cc.Vec3(0,-960);
            node.zIndex = 2;
            node.parent = Global.getInstance().popNode;
        }
        if (!this.filterStatusPrefab) {
            uiManager.instance.loadPrefabByLoading(UI_PATH_DIC.SingleToggle,cc.Prefab,(finish, total, item) => {
            }, (err, prefab: any) => {
                    if (err) {
                        return;
                    }
                    this.filterStatusPrefab = prefab;
                    cb();
            });
        } else {
            cb();
        }
    }
    /**点击订单状态筛选 */
    clickStatusFilterBtn(event, userdata) {
        let cb = ()=>{
            let toggleItemsList;
            if (this.recordtype == TAG_TYPE.DEPOSIT) toggleItemsList = ["Pending", "Successful", "Unsuccessful", "Waiting for Payment"];
            else if (this.recordtype == TAG_TYPE.WITHDRAW) toggleItemsList = ["Pending", "Successful", "Unsuccessful"]; 
            let node = cc.instantiate(this.filterStatusPrefab);
            node.getComponent(MultiToggles).setToggleItems(toggleItemsList,this.filterStatusResult,"Select Status");
            node.position = new cc.Vec3(0,-960);
            node.zIndex = 2;
            node.parent = Global.getInstance().popNode;
        }
        if (!this.filterStatusPrefab) {
            uiManager.instance.loadPrefabByLoading(UI_PATH_DIC.MultiToggles,cc.Prefab,(finish, total, item) => {
            }, (err, prefab: any) => {
                    if (err) {
                        return;
                    }
                    this.filterStatusPrefab = prefab;
                    cb();
            });
        } else {
            cb();
        }
    }

    /**点击时间筛选 */
    clickTimeFilterBtn(event, userdata) {
        let cb = ()=>{
            let toggleItemsList = ["Today","Yesterday","Last 3 days","Last 7 days"];
            let node = cc.instantiate(this.filterTimePrefab);
            node.getComponent(SingleToggle).setToggleItems(toggleItemsList,this.filterTimeResult,"Select a Date");
            node.position = new cc.Vec3(0,-960);
            node.zIndex = 2;
            node.parent = Global.getInstance().popNode;
        }
        if (!this.filterTimePrefab) {
            uiManager.instance.loadPrefabByLoading(UI_PATH_DIC.SingleToggle,cc.Prefab,(finish, total, item) => {
            }, (err, prefab: any) => {
                    if (err) {
                        return;
                    }
                    this.filterTimePrefab = prefab;
                    cb();
            });
        } else {
            cb();
        }
    }

    clickClearFilter() {
        this.filterStatusResult = ['All'];
        this.depositStatus = "";
        this.withdrawStatus = "";
        this.statusBg.width = 297;
        this.statusBg.getChildByName("arrow").active = true;
        this.statusBg.getComponent(RoundRectMask).radius = 50;
        this.btnFilterStatus.node.width = 297;
        this.btnClearFilter.node.active = false;
        this.labStatusFilter.string = "All";
        this.labStatusFilter.node.color = cc.color(34, 34, 34);

        this.refreshTransListView();
    }

    closeTransation() {
        this.hide();
    }
    // update (dt) {}
}
