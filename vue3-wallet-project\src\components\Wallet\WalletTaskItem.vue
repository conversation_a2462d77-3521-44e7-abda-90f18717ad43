<template>
  <div class="wallet-task-item" :class="getTaskStatusClass()" @click="handleItemClick">
    <div class="task-header">
      <div class="task-title-section">
        <h3 class="task-title">{{ walletStore.formatTaskTitle(task.task_name) }}</h3>
        <div class="task-status-badge" :style="{ backgroundColor: getStatusColor() }">
          {{ getStatusText() }}
        </div>
      </div>
      <div class="task-reward">
        <span class="reward-amount">₱{{ walletStore.formatNumber(task.bonus) }}</span>
        <span class="reward-label">奖励</span>
      </div>
    </div>

    <!-- 进度条区域 -->
    <div v-if="showProgress" class="progress-section">
      <!-- 投注进度 -->
      <div v-if="betProgress.total > 0" class="progress-item">
        <div class="progress-header">
          <span class="progress-label">投注进度</span>
          <span class="progress-text">
            {{ walletStore.formatNumber(betProgress.current) }} / {{ walletStore.formatNumber(betProgress.total) }}
          </span>
        </div>
        <div class="progress-bar">
          <div 
            class="progress-fill" 
            :style="{ width: `${Math.min(betProgress.percentage, 100)}%` }"
          ></div>
        </div>
      </div>

      <!-- 充值进度 -->
      <div v-if="rechargeTarget > 0" class="progress-item">
        <div class="progress-header">
          <span class="progress-label">充值进度</span>
          <span class="progress-text">
            {{ task.recharge_done ? walletStore.formatNumber(rechargeTarget) : '0' }} / {{ walletStore.formatNumber(rechargeTarget) }}
          </span>
        </div>
        <div class="progress-bar">
          <div 
            class="progress-fill" 
            :style="{ width: task.recharge_done ? '100%' : '0%' }"
          ></div>
        </div>
      </div>
    </div>

    <!-- 倒计时区域 -->
    <div v-if="showCountdown" class="countdown-section">
      <div class="countdown-icon">
        <i class="icon-clock"></i>
      </div>
      <div class="countdown-time">
        {{ formatCountdown(countdown) }}
      </div>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-section">
      <!-- 解锁按钮 -->
      <button 
        v-if="task.task_status === WalletTaskStatus.LOCK"
        class="action-btn unlock-btn"
        :class="{ disabled: hasOngoingTask }"
        :disabled="hasOngoingTask"
        @click.stop="handleUnlock"
      >
        <i class="icon-unlock"></i>
        <span>{{ hasOngoingTask ? '有任务进行中' : '解锁任务' }}</span>
      </button>

      <!-- 进行中按钮 -->
      <button 
        v-else-if="task.task_status === WalletTaskStatus.ONGOING"
        class="action-btn continue-btn"
        @click.stop="handleContinue"
      >
        <i class="icon-play"></i>
        <span>继续任务</span>
      </button>

      <!-- 领取按钮 -->
      <button 
        v-else-if="task.task_status === WalletTaskStatus.FINISHED"
        class="action-btn collect-btn"
        :class="{ loading: isCollecting }"
        :disabled="isCollecting"
        @click.stop="handleCollect"
      >
        <div v-if="isCollecting" class="btn-spinner"></div>
        <i v-else class="icon-gift"></i>
        <span>{{ isCollecting ? '领取中...' : '领取奖励' }}</span>
      </button>

      <!-- 已过期状态 -->
      <div 
        v-else-if="task.task_status === WalletTaskStatus.EXPIRED"
        class="expired-notice"
      >
        <i class="icon-clock-expired"></i>
        <span>任务已过期</span>
      </div>
    </div>

    <!-- 任务遮罩 -->
    <div v-if="task.task_status === WalletTaskStatus.LOCK" class="task-mask">
      <div class="lock-icon">
        <i class="icon-lock"></i>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useWalletStore } from '@/stores/walletStore'
import type { WalletTaskInfo, TaskProgress, TaskCountdown } from '@/types/wallet'
import { 
  WalletTaskStatus, 
  WALLET_TASK_STATUS_TEXT, 
  WALLET_TASK_STATUS_COLOR 
} from '@/types/wallet'

interface Props {
  task: WalletTaskInfo
}

interface Emits {
  (e: 'unlock', task: WalletTaskInfo): void
  (e: 'collect', task: WalletTaskInfo): void
  (e: 'continue', task: WalletTaskInfo): void
  (e: 'view-rule', task: WalletTaskInfo): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const walletStore = useWalletStore()

// 响应式数据
const isCollecting = ref(false)
const countdown = ref<TaskCountdown>({
  hours: 0,
  minutes: 0,
  seconds: 0,
  totalSeconds: 0,
  isExpired: false
})
const countdownTimer = ref<NodeJS.Timeout>()

// 计算属性
const betProgress = computed((): TaskProgress => {
  return walletStore.getTaskProgress(props.task)
})

const rechargeTarget = computed(() => {
  return Number(props.task.recharge_target_value)
})

const showProgress = computed(() => {
  return props.task.task_status !== WalletTaskStatus.FINISHED &&
         (betProgress.value.total > 0 || rechargeTarget.value > 0)
})

const showCountdown = computed(() => {
  return props.task.expire_time > 0 && 
         props.task.task_status !== WalletTaskStatus.FINISHED &&
         !countdown.value.isExpired
})

const hasOngoingTask = computed(() => {
  return walletStore.hasOngoingTask && props.task.task_status === WalletTaskStatus.LOCK
})

// 方法
function getTaskStatusClass(): string {
  const statusClasses = {
    [WalletTaskStatus.LOCK]: 'status-lock',
    [WalletTaskStatus.ONGOING]: 'status-ongoing',
    [WalletTaskStatus.FINISHED]: 'status-finished',
    [WalletTaskStatus.EXPIRED]: 'status-expired'
  }
  return statusClasses[props.task.task_status] || ''
}

function getStatusText(): string {
  return WALLET_TASK_STATUS_TEXT[props.task.task_status] || '未知状态'
}

function getStatusColor(): string {
  return WALLET_TASK_STATUS_COLOR[props.task.task_status] || '#999999'
}

function handleItemClick() {
  emit('view-rule', props.task)
}

function handleUnlock() {
  if (hasOngoingTask.value) return
  emit('unlock', props.task)
}

async function handleCollect() {
  if (isCollecting.value) return
  
  isCollecting.value = true
  try {
    emit('collect', props.task)
  } finally {
    // 延迟重置状态，给动画时间
    setTimeout(() => {
      isCollecting.value = false
    }, 1000)
  }
}

function handleContinue() {
  emit('continue', props.task)
}

function formatCountdown(countdown: TaskCountdown): string {
  const { hours, minutes, seconds } = countdown
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
}

function updateCountdown() {
  countdown.value = walletStore.getTaskCountdown(props.task)
  
  if (countdown.value.isExpired) {
    stopCountdown()
    // 通知任务过期
    walletStore.updateTaskStatus(props.task.id, WalletTaskStatus.EXPIRED)
  }
}

function startCountdown() {
  if (props.task.expire_time === 0) return
  
  updateCountdown()
  countdownTimer.value = setInterval(updateCountdown, 1000)
}

function stopCountdown() {
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value)
    countdownTimer.value = undefined
  }
}

// 生命周期
onMounted(() => {
  if (showCountdown.value) {
    startCountdown()
  }
})

onUnmounted(() => {
  stopCountdown()
})
</script>

<style scoped>
.wallet-task-item {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  border: 2px solid transparent;
}

.wallet-task-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.wallet-task-item.status-lock {
  opacity: 0.7;
}

.wallet-task-item.status-ongoing {
  border-color: #4CAF50;
  box-shadow: 0 0 20px rgba(76, 175, 80, 0.3);
}

.wallet-task-item.status-finished {
  border-color: #2196F3;
  box-shadow: 0 0 20px rgba(33, 150, 243, 0.3);
}

.wallet-task-item.status-expired {
  opacity: 0.5;
  cursor: not-allowed;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.task-title-section {
  flex: 1;
  min-width: 0;
}

.task-title {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: bold;
  color: white;
  line-height: 1.3;
}

.task-status-badge {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  color: white;
  font-weight: 500;
}

.task-reward {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  flex-shrink: 0;
  margin-left: 12px;
}

.reward-amount {
  font-size: 18px;
  font-weight: bold;
  color: #FFD700;
  margin-bottom: 2px;
}

.reward-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
}

.progress-section {
  margin-bottom: 12px;
}

.progress-item {
  margin-bottom: 8px;
}

.progress-item:last-child {
  margin-bottom: 0;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.progress-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
}

.progress-text {
  font-size: 12px;
  color: white;
  font-weight: 500;
}

.progress-bar {
  height: 6px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4CAF50, #45A049);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.countdown-section {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  padding: 8px 12px;
  background: rgba(255, 193, 7, 0.2);
  border-radius: 8px;
}

.countdown-icon {
  color: #FFC107;
  font-size: 14px;
}

.countdown-time {
  font-size: 14px;
  font-weight: bold;
  color: #FFC107;
  font-family: 'Courier New', monospace;
}

.action-section {
  display: flex;
  justify-content: center;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px;
  justify-content: center;
}

.unlock-btn {
  background: linear-gradient(135deg, #4CAF50, #45A049);
  color: white;
}

.unlock-btn:hover:not(.disabled) {
  background: linear-gradient(135deg, #45A049, #388E3C);
  transform: translateY(-1px);
}

.unlock-btn.disabled {
  background: #999999;
  cursor: not-allowed;
  opacity: 0.6;
}

.continue-btn {
  background: linear-gradient(135deg, #2196F3, #1976D2);
  color: white;
}

.continue-btn:hover {
  background: linear-gradient(135deg, #1976D2, #1565C0);
  transform: translateY(-1px);
}

.collect-btn {
  background: linear-gradient(135deg, #FF6B6B, #FF5252);
  color: white;
  position: relative;
}

.collect-btn:hover:not(.loading) {
  background: linear-gradient(135deg, #FF5252, #F44336);
  transform: translateY(-1px);
}

.collect-btn.loading {
  cursor: not-allowed;
}

.btn-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.expired-notice {
  display: flex;
  align-items: center;
  gap: 8px;
  color: rgba(255, 255, 255, 0.6);
  font-size: 14px;
}

.task-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
}

.lock-icon {
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .wallet-task-item {
    padding: 12px;
  }
  
  .task-title {
    font-size: 14px;
  }
  
  .reward-amount {
    font-size: 16px;
  }
  
  .action-btn {
    padding: 8px 16px;
    font-size: 13px;
    min-width: 100px;
  }
}

@media (max-width: 480px) {
  .task-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .task-reward {
    align-items: flex-start;
    margin-left: 0;
  }
  
  .progress-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 2px;
  }
}
</style>
