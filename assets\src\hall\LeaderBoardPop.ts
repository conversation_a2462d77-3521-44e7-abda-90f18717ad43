import UICommon from "../component/UICommon";
import { DEEP_INDEXZ, E_PAGE_TYPE, UI_PATH_DIC } from "../GlobalConstant";
import Global from "../GlobalScript";
import { AutoPopMgr } from "../mgr/AutoPopMgr";
import { uiManager } from "../mgr/UIManager";
import HttpUtils from "../net/HttpUtils";
import RoundRectMask from "../RoundRectMask";
import utils from "../utils/utils";
import { LOGIN_WAY } from "./Hall";

const { ccclass, property } = cc._decorator;

export function showRankPop() {
    let parent = Global.getInstance().popNode
    if (!parent) return
    let gameNd = parent.getChildByName("RankPop")
    if (gameNd) {
        return
    }
    uiManager.instance.loadPrefabByLoading(UI_PATH_DIC.RankPop,cc.Prefab, () => {
    }, (err, prefab: cc.Prefab) => {
        if (!prefab) return
        let gameNd = parent.getChildByName("RankPop");
        if (gameNd) {
            return
        }
        if (err) return
        if(Global.getInstance().getPageId() != E_PAGE_TYPE.HALL){
            AutoPopMgr.set_isshowing();//没弹出来后卡住问题
            return
        }//控制必须在hall 页面弹出
        let ndPop = cc.instantiate(prefab)
        ndPop.name = "RankPop"
        ndPop.zIndex = 999
        ndPop.parent = parent
    })
}

@ccclass
export default class LeaderBoardPop extends UICommon {
   
    @property(cc.Label)
    title: cc.Label = null;

    onLoad() {
        
    }

    start() {
        this.initWidget();
        this.formatTitleForm();
        this.getYesterdaytRankData();
    }

    protected onDestroy(): void {
    }
  
    initWidget() {
        let currentDate = new Date();
        let currentDateStr = `${currentDate.getFullYear()}-${currentDate.getMonth() + 1}-${currentDate.getDate()}`;
        let lastEnterDateStr = Global.getInstance().getStoreageData("RANK_LAST_ENTER_TIME","");
        if (lastEnterDateStr == "") {
            Global.getInstance().setStoreageData("RANK_LAST_ENTER_TIME",currentDateStr+"_1");
        } else {
            let split = lastEnterDateStr.split("_");
            if (split && split[0] && split[1]) {
                if(currentDateStr == split[0]){
                    let number = parseInt(split[1]) + 1;
                    Global.getInstance().setStoreageData("RANK_LAST_ENTER_TIME",currentDateStr+"_"+number);
                }else{
                    Global.getInstance().setStoreageData("RANK_LAST_ENTER_TIME",currentDateStr+"_1");
                }
            }
        }
    }

    getYesterdaytRankData() {
        HttpUtils.getInstance().post(1, 3, this, "/avt/api/rank/casino", {
            token: Global.getInstance().token,
            type:2
        }, (response) => {
            if (!this.node || !this.node.isValid) {
                return;
            }
            if (response && response.data && response.data.list) {
                let rankList = response.data.list.rank;
                let time = response.data.list.time;
                if (rankList) {
                    this.showTop3(rankList);
                }
                if (time) {
                    this.formatTitleForm(time);
                }
            }
        },()=>{
        });
    }

    showTop3(rankList) {
        for (let index = 0; index < 3; index++) {
            let node = utils.getChildByPath(this.node,"bg.number"+(index+1));
            let element = rankList[index];
            let lab_userid = node.getChildByName("lab_userid").getComponent(cc.Label);
            let lab_betamout = node.getChildByName("lab_betamout").getComponent(cc.Label);
            let lab_award = node.getChildByName("lab_award").getComponent(cc.Label);
            lab_userid.string = element.player_id;
            if (parseFloat(element.total_bet_amount) && parseFloat(element.total_bet_amount) > 0) {
                lab_betamout.string = utils.formatNumberWithCommas(element.total_bet_amount,2);
            } else {
                lab_betamout.string = "--";
            }
            lab_award.string = utils.formatNumberWithCommas(element.award,0);
        }
    }

    formatTitleForm(dateStr?){
        let dateString = dateStr;
        if (!dateStr) {
            dateString = this.getCurrentDateFormatted();
        }
        let date = new Date(dateString); 
        let monthAbbreviations = [
            "JAN", "FEB", "MAR", "APR", "MAY", "JUN", 
            "JUL", "AUG", "SEP", "OCT", "NOV", "DEC"
        ];
    
        let month = monthAbbreviations[date.getMonth()]; // 获取对应的月份缩写
        let day = date.getDate(); // 获取日期

        this.title.string = `${month}.${day}` + " TOP 3 WINNERS";
    }

    clickClose() {
        cc.director.emit("DestroyQueuePopup");
        this.node.destroy();
    }

    getCurrentDateFormatted() {
        let now = new Date(); 
        now.setDate(now.getDate() - 1); 
        let year = now.getFullYear(); 
        let month = (now.getMonth() + 1).toString().padStart(2, '0'); 
        let day = now.getDate().toString().padStart(2, '0'); 
    
        return `${year}-${month}-${day}`; 
    }

    showRankView() {
        let btn_spin = Global.getInstance().popNode.getChildByName("btn_spin");
        if (btn_spin) btn_spin.zIndex = -1;
        Global.instance.hideEnvelopeBtn();
        uiManager.instance.showDialog(UI_PATH_DIC.Rank,["yesterday"],null,DEEP_INDEXZ.ACTIVITY_CONTENT);
        // this.node.destroy();
        this.clickClose();
    }
}

