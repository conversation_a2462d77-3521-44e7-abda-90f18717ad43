<template>
  <div id="app" class="app-container">
    <!-- 全局加载遮罩 -->
    <div v-if="globalLoading" class="global-loading">
      <div class="loading-spinner"></div>
      <p class="loading-text">{{ loadingText }}</p>
    </div>

    <!-- 路由视图 -->
    <router-view v-slot="{ Component, route }">
      <transition
        :name="getTransitionName(route)"
        mode="out-in"
        appear
      >
        <component :is="Component" :key="route.path" />
      </transition>
    </router-view>

    <!-- 全局提示 -->
    <GlobalToast />

    <!-- 全局弹窗 -->
    <GlobalModal />

    <!-- 网络状态提示 -->
    <NetworkStatus />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useHallStore } from '@/stores/hallStore'
import GlobalToast from '@/components/Common/GlobalToast.vue'
import GlobalModal from '@/components/Common/GlobalModal.vue'
import NetworkStatus from '@/components/Common/NetworkStatus.vue'

const router = useRouter()
const route = useRoute()
const hallStore = useHallStore()

// 响应式数据
const globalLoading = ref(false)
const loadingText = ref('加载中...')

// 方法
function getTransitionName(route: any): string {
  // 根据路由层级决定过渡动画
  const depth = route.path.split('/').length
  
  if (route.meta?.transition) {
    return route.meta.transition
  }
  
  // 默认过渡动画
  return depth > 2 ? 'slide-left' : 'fade'
}

function handleVisibilityChange() {
  if (document.hidden) {
    // 页面隐藏时的处理
    console.log('Page hidden')
  } else {
    // 页面显示时的处理
    console.log('Page visible')
    // 可以在这里刷新数据
  }
}

function handleOnline() {
  console.log('Network online')
  // 网络恢复时的处理
}

function handleOffline() {
  console.log('Network offline')
  // 网络断开时的处理
}

// 生命周期
onMounted(() => {
  // 添加事件监听
  document.addEventListener('visibilitychange', handleVisibilityChange)
  window.addEventListener('online', handleOnline)
  window.addEventListener('offline', handleOffline)
  
  // 初始化应用
  initializeApp()
})

onUnmounted(() => {
  // 移除事件监听
  document.removeEventListener('visibilitychange', handleVisibilityChange)
  window.removeEventListener('online', handleOnline)
  window.removeEventListener('offline', handleOffline)
})

async function initializeApp() {
  globalLoading.value = true
  loadingText.value = '初始化应用...'
  
  try {
    // 检查登录状态
    loadingText.value = '检查登录状态...'
    await checkLoginStatus()
    
    // 加载基础配置
    loadingText.value = '加载配置...'
    await loadAppConfig()
    
    // 预加载资源
    loadingText.value = '预加载资源...'
    await preloadResources()
    
  } catch (error) {
    console.error('Failed to initialize app:', error)
  } finally {
    globalLoading.value = false
  }
}

async function checkLoginStatus() {
  // TODO: 实现登录状态检查
  const token = localStorage.getItem('token')
  if (token) {
    hallStore.setLoginStatus(true)
    // 验证token有效性
    // await validateToken(token)
  }
}

async function loadAppConfig() {
  // TODO: 加载应用配置
  // const config = await api.getAppConfig()
  // 设置全局配置
}

async function preloadResources() {
  // TODO: 预加载关键资源
  // 预加载图片、字体等
}
</script>

<style lang="scss">
.app-container {
  width: 100%;
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
}

.global-loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  color: white;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 16px;
  margin: 0;
  opacity: 0.8;
}

// 过渡动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-left-enter-active,
.slide-left-leave-active {
  transition: all 0.3s ease;
}

.slide-left-enter-from {
  transform: translateX(100%);
  opacity: 0;
}

.slide-left-leave-to {
  transform: translateX(-100%);
  opacity: 0;
}

.slide-right-enter-active,
.slide-right-leave-active {
  transition: all 0.3s ease;
}

.slide-right-enter-from {
  transform: translateX(-100%);
  opacity: 0;
}

.slide-right-leave-to {
  transform: translateX(100%);
  opacity: 0;
}

.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 0.3s ease;
}

.slide-up-enter-from {
  transform: translateY(100%);
  opacity: 0;
}

.slide-up-leave-to {
  transform: translateY(-100%);
  opacity: 0;
}

.scale-enter-active,
.scale-leave-active {
  transition: all 0.3s ease;
}

.scale-enter-from,
.scale-leave-to {
  transform: scale(0.8);
  opacity: 0;
}
</style>
