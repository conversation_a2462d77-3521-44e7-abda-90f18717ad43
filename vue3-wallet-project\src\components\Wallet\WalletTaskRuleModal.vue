<template>
  <div class="wallet-task-rule-modal" @click="handleBackdropClick">
    <div class="modal-container" @click.stop>
      <!-- 头部 -->
      <header class="modal-header">
        <h2 class="modal-title">任务规则</h2>
        <button class="close-btn" @click="closeModal">
          <i class="icon-close"></i>
        </button>
      </header>

      <!-- 内容区域 -->
      <div class="modal-content">
        <div class="rule-content" v-html="formattedRuleContent"></div>
      </div>

      <!-- 底部 -->
      <footer class="modal-footer">
        <button class="confirm-btn" @click="closeModal">
          我知道了
        </button>
      </footer>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  ruleContent: string
}

interface Emits {
  (e: 'close'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 计算属性
const formattedRuleContent = computed(() => {
  if (!props.ruleContent) return ''
  
  // 处理换行符
  let formatted = props.ruleContent.replace(/\n/g, '<br>')
  
  // 处理特殊格式
  formatted = formatted
    // 处理标题（以数字开头的行）
    .replace(/^(\d+\.\s*[^<\n]+)/gm, '<h3>$1</h3>')
    // 处理粗体文本
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    // 处理斜体文本
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    // 处理列表项
    .replace(/^[-•]\s*(.+)/gm, '<li>$1</li>')
    // 包装连续的列表项
    .replace(/(<li>.*<\/li>)/gs, '<ul>$1</ul>')
    // 处理重要提示（以"注意"、"提示"等开头）
    .replace(/(注意|提示|重要|警告)[:：]\s*([^<\n]+)/g, '<div class="notice">$1: $2</div>')
  
  return formatted
})

// 方法
function handleBackdropClick() {
  closeModal()
}

function closeModal() {
  emit('close')
}
</script>

<style scoped>
.wallet-task-rule-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1100;
  padding: 20px;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.modal-container {
  background: white;
  border-radius: 16px;
  width: 100%;
  max-width: 500px;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
  animation: slideUp 0.3s ease;
}

@keyframes slideUp {
  from {
    transform: translateY(50px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.modal-title {
  margin: 0;
  font-size: 18px;
  font-weight: bold;
}

.close-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.modal-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.rule-content {
  line-height: 1.6;
  color: #333;
  font-size: 14px;
}

/* 规则内容样式 */
.rule-content :deep(h3) {
  margin: 16px 0 8px 0;
  font-size: 16px;
  font-weight: bold;
  color: #2c3e50;
  border-left: 4px solid #667eea;
  padding-left: 12px;
}

.rule-content :deep(h3:first-child) {
  margin-top: 0;
}

.rule-content :deep(p) {
  margin: 8px 0;
}

.rule-content :deep(strong) {
  font-weight: bold;
  color: #2c3e50;
}

.rule-content :deep(em) {
  font-style: italic;
  color: #7f8c8d;
}

.rule-content :deep(ul) {
  margin: 12px 0;
  padding-left: 0;
  list-style: none;
}

.rule-content :deep(li) {
  margin: 6px 0;
  padding-left: 20px;
  position: relative;
}

.rule-content :deep(li::before) {
  content: '•';
  color: #667eea;
  font-weight: bold;
  position: absolute;
  left: 0;
}

.rule-content :deep(.notice) {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 6px;
  padding: 12px;
  margin: 12px 0;
  color: #856404;
  font-weight: 500;
}

.rule-content :deep(.notice::before) {
  content: '⚠️';
  margin-right: 8px;
}

.modal-footer {
  padding: 20px;
  background: #f8f9fa;
  display: flex;
  justify-content: center;
}

.confirm-btn {
  padding: 12px 32px;
  border: none;
  border-radius: 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px;
}

.confirm-btn:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .wallet-task-rule-modal {
    padding: 10px;
  }
  
  .modal-container {
    max-width: 100%;
    max-height: 90vh;
  }
  
  .modal-header {
    padding: 16px;
  }
  
  .modal-title {
    font-size: 16px;
  }
  
  .modal-content {
    padding: 16px;
  }
  
  .rule-content {
    font-size: 13px;
  }
  
  .rule-content :deep(h3) {
    font-size: 15px;
  }
  
  .modal-footer {
    padding: 16px;
  }
  
  .confirm-btn {
    padding: 10px 24px;
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .modal-container {
    border-radius: 12px;
  }
  
  .modal-header {
    padding: 12px;
  }
  
  .modal-content {
    padding: 12px;
  }
  
  .rule-content {
    font-size: 12px;
  }
  
  .rule-content :deep(h3) {
    font-size: 14px;
    margin: 12px 0 6px 0;
  }
  
  .rule-content :deep(.notice) {
    padding: 8px;
    font-size: 12px;
  }
  
  .modal-footer {
    padding: 12px;
  }
}

/* 滚动条样式 */
.modal-content::-webkit-scrollbar {
  width: 6px;
}

.modal-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.modal-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.modal-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
