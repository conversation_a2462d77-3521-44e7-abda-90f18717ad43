import { ALL_APP_SOURCE_CONFIG } from "./Config";
import { DEEP_INDEXZ, E_CHANEL_TYPE, E_FUND_TYPE, UI_PATH_DIC } from "./GlobalConstant";
import Global from "./GlobalScript";
import <PERSON>ICom<PERSON> from "./component/UICommon";
import { GEETEST_TYPE, GeetestMgr } from "./geetest/GeetestMgr";
import { uiManager } from "./mgr/UIManager";
import HttpUtils from "./net/HttpUtils";
import utils from "./utils/utils";

const { ccclass, property } = cc._decorator;
export const chooseWallet = "chooseWallet"
export function showChooseWallet(walletData, fundType) {
    uiManager.instance.showDialog(UI_PATH_DIC.ChooseWallet, [{ walletData, fundType }],null,DEEP_INDEXZ.PRIVACY_TIPS)
}
export enum E_MODIFY_TYPE {
    ADD,
    MODIFY
}
@ccclass
export default class ChooseWallet extends UICommon {
    @property(cc.Label)
    title: cc.Label = null;
    @property(cc.Label)
    tipAccount: cc.Label = null;

    @property(cc.EditBox)
    firstNameEb: cc.EditBox = null;

    @property(cc.EditBox)
    lastNameEb: cc.EditBox = null;

    @property(cc.EditBox)
    accountEb: cc.EditBox = null;

    @property(cc.EditBox)
    birthDay: cc.EditBox = null;

    @property(cc.EditBox)
    birthMonth: cc.EditBox = null;

    @property(cc.EditBox)
    birthYear: cc.EditBox = null;

    @property(cc.Label)
    lbError1: cc.Label = null;
    @property(cc.Label)
    lbError2: cc.Label = null;
    @property(cc.Label)
    lbError3: cc.Label = null;

    userAccountInfo: any[] = []
    allAccountCodes: any[] = []
    curAccountInfo: any = null;
    _firstName: string = ""
    _middleName: string = ""
    _lastName: string = ""
    _account: string = ""

    _birthDay: string = ""
    _birthMonth: string = ""
    _birthYear: string = ""

    _accountId: string = "";
    curPayCode: string = '';
    curFundType: E_FUND_TYPE;
    // _verifyCode: String = ""
    m_t: number = 120;
    _modifyType: E_MODIFY_TYPE;
    curPayPath: string = '';


    start() {
        super.start()
        if (this.userAccountInfo.length > 0) {
            this.curAccountInfo = this.userAccountInfo[0];
            this.curPayCode = this.curAccountInfo.pay_code
            this.showPayAccountInfo(this.curAccountInfo)
            this._modifyType = E_MODIFY_TYPE.MODIFY;
        } else {
            this.curPayCode = this.allAccountCodes[0].pay_code
            this._modifyType = E_MODIFY_TYPE.ADD;
        }

        //EditBox暂时先禁用
        this.firstNameEb.enabled = false;
        this.lastNameEb.enabled = false;
        this.accountEb.enabled = false;
        this.birthDay.enabled = false;
        this.birthMonth.enabled = false;
        this.birthYear.enabled = false;

        if(ALL_APP_SOURCE_CONFIG.channel == E_CHANEL_TYPE.WEB){
            //web 更改一下 title
            this.title.string = 'KYC Details';
        }

        let page = this.node.getChildByName("page").getComponent(cc.PageView);
        page.node.on('page-turning', ()=>{
            let curIndex = page.getCurrentPageIndex();
            if (curIndex != 1) {
                this.node.destroy();
            }
        }, this);
        let tempnode = this.node.getChildByName("temp");
        page.insertPage(cc.instantiate(tempnode),0);
        page.scrollToPage(1,0.01);
    }
    
    init(args: { walletData, fundType: E_FUND_TYPE }) {
        // this.par = par;
        this.curFundType = args.fundType
        this.userAccountInfo = args.walletData.user_account_info;
        this.allAccountCodes = args.walletData.all_account_code
        switch (args.fundType) {
            // case E_FUND_TYPE.Gcash:
            //     this.curPayPath = "GCash"
            //     this.title.string = "Add GCash Account"
            //     this.tipAccount.string = "GCash Account No. (09xx xxx xxxx)"
            //     this.accountEb.placeholder = "Enter GCash phone number here"
            //     break;
            case E_FUND_TYPE.Maya:
                this.curPayPath = "Maya"
                this.title.string = "Personal details (KYC)"//Add Maya Account
                this.tipAccount.string = "Phone number"//"Maya Account No. (09xx xxx xxxx)"

                this.firstNameEb.placeholder = "";
                this.lastNameEb.placeholder = "";
                this.accountEb.placeholder = "";//"Enter Maya phone number here"

                this.birthDay.placeholder = "";
                this.birthMonth.placeholder = "";
                this.birthYear.placeholder = "";
                break;
            // case E_FUND_TYPE.GrabPay:
            //     this.curPayPath = "GrabPay"
            //     this.title.string = "Add GrabPay Account"
            //     this.tipAccount.string = "GrabPay Account No. (09xx xxx xxxx)"
            //     this.accountEb.placeholder = "Enter GrabPay phone number here"
            //     break;
            // case E_FUND_TYPE.BankCard:
            //     break;

            default:
                break;
        }
    }
    //添加提现账号
    add_withdraw_no(ret?){
        let gee_guard = ret?.geetest_guard || '';
        let uInfo = ret?.userInfo || '';
        let gee_captcha = ret?.geetest_captcha || '';
        let data = {
            account: this._account,
            firstName: this._firstName,
            middleName: this._middleName,
            lastName: this._lastName,
            type: this.curPayPath,
            sucFunc: () => {
                HttpUtils.getInstance().post(3, 3, this, "api/add/withdraw/no", {
                    token: Global.getInstance().token,
                    account_no: this._account,
                    first_name: this._firstName,
                    middle_name: this._middleName,
                    last_name: this._lastName,
                    pay_code: this.curPayCode,
                    type: this.curFundType,
                    geetest_guard:gee_guard,
                    userInfo:uInfo,
                    geetest_captcha:gee_captcha,
                    buds:ret?.buds || '64',
                    // verification_code: this._verifyCode
                }, (response) => {
                    if (response.data) {
                        Global.getInstance().showSimpleTip("Your fund account has been added successfully")
                        cc.director.emit("modifyWithdrawNoSuc", response.data)
                        this.closeAction()
                    }
                }, (response) => {
                    if (response && response.code) {
                        if (response.code == 1) {
                            Global.getInstance().showSimpleTip(response.msg)
                        } else {
                            let str = response.msg;
                            Global.getInstance().showSimpleTip(str)
                        }
                    }
                });
            }
        }
        uiManager.instance.showDialog(UI_PATH_DIC.AccountConfirmation, [data])
    }
    //修改提现账号
    change_withdram_no(ret?){
        let gee_guard = ret?.geetest_guard || '';
        let uInfo = ret?.userInfo || '';
        let gee_captcha = ret?.geetest_captcha || '';
        let data = {
            account: this._account,
            firstName: this._firstName,
            middleName: this._middleName,
            lastName: this._lastName,
            type: this.curPayPath,
            sucFunc: () => {
                HttpUtils.getInstance().post(3, 3, this, "api/update/account/info", {
                    token: Global.getInstance().token,
                    account_no: this._account,
                    account_id: this._accountId,
                    first_name: this._firstName,
                    middle_name: this._middleName,
                    last_name: this._lastName,
                    type: this.curFundType,
                    pay_code: this.curPayCode,
                    geetest_guard:gee_guard,
                    userInfo:uInfo,
                    geetest_captcha:gee_captcha,
                    buds:ret?.buds || '64',
                    // verification_code: this._verifyCode
                }, (response) => {
                    if (response.data) {
                        Global.getInstance().showSimpleTip("Your fund account has been added successfully")
                        cc.director.emit("modifyWithdrawNoSuc", response.data)
                        this.closeAction()
                    }
                }, (response) => {
                    if (response && response.code) {
                        if (response.code == 1) {
                            Global.getInstance().showSimpleTip(response.msg)
                        } else {
                            let str = response.msg;
                            Global.getInstance().showSimpleTip(str)
                        }
                    }
                });
            }
        }
        uiManager.instance.showDialog(UI_PATH_DIC.AccountConfirmation, [data])

    }
    onClickOK() {
        if (this._modifyType == E_MODIFY_TYPE.ADD) {
            if (!this._firstName) {
                this.lbError1.string = "Please enter first name."
                return;
            } else {
                this.lbError1.string = ''
            }
            if (!this._lastName) {
                this.lbError2.string = "Please enter last name."
                return;
            } else {
                this.lbError2.string = ''
            }
            if (!this._account) {
                this.lbError3.string = "Please enter " + this.curPayPath + " account no."
                return;
            } else {
                this.lbError3.string = ''
            }
            if (!utils.isPhilippinePhoneNumber(this._account)) {
                this.lbError3.string = "Please enter the correct account number."
                return;
            }
            GeetestMgr.instance.geetest_device(GEETEST_TYPE.bind_withdraw_account,(succ)=>{
                if(succ){
                    let ret = {}
                    if(Object.getPrototypeOf(succ) !== Boolean.prototype){
                        ret = succ
                    }
                    this.add_withdraw_no(ret);
                }
            })
        } else if (this._modifyType == E_MODIFY_TYPE.MODIFY) {
            if (!this._firstName) {
                this.lbError1.string = "Please enter first name."
                return;
            }
            if (!this._lastName) {
                this.lbError2.string = "Please enter last name."
                return;
            }
            if (!this._account) {
                this.lbError3.string = "Please enter " + this.curPayPath + " account no."
                return;
            }
            if (!utils.isPhilippinePhoneNumber(this._account)) {
                this.lbError3.string = "Please enter the correct account number"
                return;
            }
            GeetestMgr.instance.geetest_device(GEETEST_TYPE.change_withdraw_account,(succ)=>{
                if(succ){
                    let ret = {}
                    if(Object.getPrototypeOf(succ) !== Boolean.prototype){
                        ret = succ
                    }
                    this.change_withdram_no(ret);
                }
            })
        }

    }
    closeAction() {
        this.hide()
    }
    onEditBegin(editbox: cc.EditBox) {
        Global.getInstance().editParentMove(editbox, this.lay,0);//这里其实没有用到 用来展示kyc 固定信息不可编辑
        editbox.placeholderLabel.node.name = editbox.placeholder
        editbox.placeholder = ""

    }
    onEditEnd(editbox) {
        if (Global.getInstance().needScreenUp()) {
            // this.node.y = this.node.y - 400
            this.lay.y = 0
        }
        editbox.placeholder = editbox.placeholderLabel.node.name

    }
    onEditFirstName(text) {
        this._firstName = text;
        if (text != '') {
            this.lbError1.string = ''
        }
    }
    onEditLastName(text) {
        this._lastName = text;
        if (text != '') {
            this.lbError2.string = ''
        }
    }
    onEditMiddleName(text) {
        this._middleName = text;
    }
    onEditAccount(text, eb) {
        this._account = text.replace(/[^0-9]/g, '');
        eb.string = this._account;
        if (text != '') {
            this.lbError3.string = ''
        }
    }

    onEditBirthDay(text) {
        this._birthDay = text;
    }

    onEditBirthMonth(text) {
        this._birthMonth = text
    }

    onEditBirthYear(text) {
        this._birthYear = text;
    }

    clearPayAccountInfo() {
        this.curAccountInfo = null;
        this.firstNameEb.string = ""
        this.lastNameEb.string = ""
        this.accountEb.string = ""
        this.birthDay.string = ""
        this.birthMonth.string = ""
        this.birthYear.string = ""
        this._firstName = ''
        this._lastName = ''
        this._middleName = ''
        this._account = ''

        this._birthDay = ''
        this._birthMonth = ''
        this._birthYear = ''
    }
    //response = {{"first_middle_name":"rr"
    // ,"last_name":"ooo","phone":"**********","day":"3","month":"5","year":"2001"}
    // null}
    showPayAccountInfo(info) {
        if (!info) return;
        this.firstNameEb.string = info.first_middle_name || ""
        this._firstName = info.first_middle_name || ""
        this.lastNameEb.string = info.last_name || ""
        this._lastName = info.last_name || ""
        if (info.phone) {
            this.accountEb.string = this.formatPhoneNumber(info.phone) || ""
        }
        this._account = info.phone || ""
        this.birthDay.string = info.day || ""
        this.birthMonth.string = this.convertMonth(info.month);
        this.birthYear.string = info.year || ""
    }

    convertMonth(month: string = '1'){
        let m = ["January","February","March","April","May","June","July","August","September","October","November","December"]
        return m[parseInt(month)-1]
    }

    formatPhoneNumber(phoneNumber: string) {
        if (phoneNumber.length == 10) {
            phoneNumber = "0"+phoneNumber;
        }

        if (phoneNumber.length !== 11) {
            return;
        }
        //保留前三位和后四位，中间用****替换
        return `${phoneNumber.slice(0, 4)}****${phoneNumber.slice(8)}`;
    }
}
