import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { useHallStore } from '@/stores/hallStore'

// 路由配置
const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Home',
    component: () => import('@/views/Home.vue'),
    meta: {
      title: '大厅',
      requiresAuth: false,
      transition: 'fade'
    }
  },
  {
    path: '/hall',
    name: 'Hall',
    component: () => import('@/views/Hall.vue'),
    meta: {
      title: '游戏大厅',
      requiresAuth: false,
      transition: 'fade'
    }
  },
  {
    path: '/games',
    name: 'AllGames',
    component: () => import('@/views/AllGames.vue'),
    meta: {
      title: '所有游戏',
      requiresAuth: false,
      transition: 'slide-left'
    }
  },
  {
    path: '/game/:gameId',
    name: 'Game',
    component: () => import('@/views/Game.vue'),
    meta: {
      title: '游戏',
      requiresAuth: true,
      transition: 'slide-left'
    }
  },
  {
    path: '/transactions',
    name: 'Transactions',
    component: () => import('@/views/Transactions.vue'),
    meta: {
      title: '交易记录',
      requiresAuth: true,
      transition: 'slide-left'
    }
  },
  {
    path: '/activities',
    name: 'Activities',
    component: () => import('@/views/Activities.vue'),
    meta: {
      title: '活动中心',
      requiresAuth: false,
      transition: 'slide-left'
    }
  },
  {
    path: '/activity/:activityId',
    name: 'Activity',
    component: () => import('@/views/ActivityDetail.vue'),
    meta: {
      title: '活动详情',
      requiresAuth: false,
      transition: 'slide-left'
    }
  },
  {
    path: '/spin-wheel',
    name: 'SpinWheel',
    component: () => import('@/views/SpinWheel.vue'),
    meta: {
      title: '幸运转盘',
      requiresAuth: true,
      transition: 'scale'
    }
  },
  {
    path: '/leaderboard',
    name: 'LeaderBoard',
    component: () => import('@/views/LeaderBoard.vue'),
    meta: {
      title: '排行榜',
      requiresAuth: false,
      transition: 'slide-left'
    }
  },
  {
    path: '/mail',
    name: 'Mail',
    component: () => import('@/views/Mail.vue'),
    meta: {
      title: '邮件',
      requiresAuth: true,
      transition: 'slide-left'
    }
  },
  {
    path: '/settings',
    name: 'Settings',
    component: () => import('@/views/Settings.vue'),
    meta: {
      title: '设置',
      requiresAuth: false,
      transition: 'slide-left'
    }
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: {
      title: '登录',
      requiresAuth: false,
      transition: 'slide-up'
    }
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('@/views/Register.vue'),
    meta: {
      title: '注册',
      requiresAuth: false,
      transition: 'slide-up'
    }
  },
  {
    path: '/profile',
    name: 'Profile',
    component: () => import('@/views/Profile.vue'),
    meta: {
      title: '个人中心',
      requiresAuth: true,
      transition: 'slide-left'
    }
  },
  {
    path: '/deposit',
    name: 'Deposit',
    component: () => import('@/views/Deposit.vue'),
    meta: {
      title: '充值',
      requiresAuth: true,
      transition: 'slide-up'
    }
  },
  {
    path: '/withdraw',
    name: 'Withdraw',
    component: () => import('@/views/Withdraw.vue'),
    meta: {
      title: '提现',
      requiresAuth: true,
      transition: 'slide-up'
    }
  },
  {
    path: '/notice',
    name: 'Notice',
    component: () => import('@/views/Notice.vue'),
    meta: {
      title: '公告',
      requiresAuth: false,
      transition: 'slide-left'
    }
  },
  {
    path: '/help',
    name: 'Help',
    component: () => import('@/views/Help.vue'),
    meta: {
      title: '帮助中心',
      requiresAuth: false,
      transition: 'slide-left'
    }
  },
  {
    path: '/about',
    name: 'About',
    component: () => import('@/views/About.vue'),
    meta: {
      title: '关于我们',
      requiresAuth: false,
      transition: 'slide-left'
    }
  },
  // 404页面
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/NotFound.vue'),
    meta: {
      title: '页面未找到',
      requiresAuth: false,
      transition: 'fade'
    }
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
  scrollBehavior(to, from, savedPosition) {
    // 路由切换时的滚动行为
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 全局前置守卫
router.beforeEach(async (to, from, next) => {
  const hallStore = useHallStore()
  
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 游戏大厅`
  }
  
  // 检查是否需要登录
  if (to.meta.requiresAuth && !hallStore.isLoggedIn) {
    // 保存原始路径，登录后跳转
    next({
      name: 'Login',
      query: { redirect: to.fullPath }
    })
    return
  }
  
  // 如果已登录且访问登录页，重定向到大厅
  if (hallStore.isLoggedIn && (to.name === 'Login' || to.name === 'Register')) {
    next({ name: 'Hall' })
    return
  }
  
  next()
})

// 全局后置钩子
router.afterEach((to, from) => {
  // 路由切换完成后的处理
  console.log(`Route changed from ${from.path} to ${to.path}`)
  
  // 可以在这里添加页面访问统计
  // analytics.trackPageView(to.path)
})

// 路由错误处理
router.onError((error) => {
  console.error('Router error:', error)
  // 可以在这里添加错误上报
})

export default router
