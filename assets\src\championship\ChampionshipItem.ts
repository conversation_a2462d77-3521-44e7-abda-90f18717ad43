import { DEEP_INDEXZ, UI_PATH_DIC } from "../GlobalConstant";
import ItemRender from "../listView/ItemRender";
import { uiManager } from "../mgr/UIManager";
import utils from "../utils/utils";

const { ccclass, property } = cc._decorator;

@ccclass
export default class LevelItem extends ItemRender {
    /**数据改变时调用 */
    public dataChanged() {
        const info = this.data;

        this.node.active = true;

        this.setLabel("txt_rank", info.rank);
        this.setLabel("txt_userid", utils.formatPlayerId(info.user_id));
        this.setLabel("txt_betamout", utils.formatNumberWithCommas(info.score, 0));
        this.setLabel("txt_award", utils.formatNumberWithCommas(info.bonus, 0));
        this.node.getChildByName("ic_me").active = info.bSelf;
        utils.loadAvatar(utils.seekFromRootByName(this.node, "ic_avatar").getComponent(cc.Sprite), info.avatar);

        this.addButtonClick(this.node.getChildByName("btn_go"), () => {
            uiManager.instance.showDialog(UI_PATH_DIC.ChampionshipDetail, [info.params],null,DEEP_INDEXZ.ACTIVITYS);
        })
    }
}
