import { DEEP_INDEXZ, UI_PATH_DIC } from "./GlobalConstant";
import Global from "./GlobalScript";
import <PERSON>ICom<PERSON> from "./component/UICommon";
import { GEETEST_TYPE, GeetestMgr } from "./geetest/GeetestMgr";
import { Md5 } from "./libs/Md5";
import { uiManager } from "./mgr/UIManager";
import HttpUtils from "./net/HttpUtils";

const { ccclass, property } = cc._decorator;

@ccclass
export default class Password extends UICommon {
    @property(cc.Node)
    lay3: cc.Node = null;
    @property(cc.Label)
    lbError: cc.Label = null;
    @property(cc.EditBox)
    edb: cc.EditBox = null;
    @property(cc.Node)
    pointArr: cc.Node[] = []

    finalPwd: string = ''
    passwords: string[] = [];
    passType: string = '';

    init(type: string) {
        this.passType = type;
    }
    protected onLoad(): void {
        this.lay3.active = true;
        //this.lay1.active = false;
        this.pointArr.forEach(nd => nd.active = false);
        
        this.scheduleOnce(()=>{
            let ed = this.lay3.getComponentsInChildren(cc.EditBox)[0];
            ed.focus();
        },0.5)
    }
    onEditBegin(event) {
        event.string = ""
        if (Global.getInstance().needScreenUp()) {
            Global.getInstance().editParentMove(event, this.lay,200);
        }
    }
    onEditPassword(text, event) {
        if (isNaN(text) || !text) {
            return
        }
        if (cc.sys.isNative) {
            this.finalPwd = text;
            let str = ''
            for (let i = 0; i < text.length; i++) {
                str = str + "*"
            }
            event.string = str;
        } else if (cc.sys.isBrowser) {
            let idx = event.node.parent.children.indexOf(event.node)
            event.string = "·"
            this.passwords[idx] = text;
            if (idx < 5) {
                let ed = event.node.parent.children[idx + 1].getComponent(cc.EditBox)
                ed.string = ''
                ed.focus()
            } else {
                event.blur()
            }
        }
    }
    onEditEnd(event) {
        if (Global.getInstance().needScreenUp()) {
            this.lay.y = 0
        }
        if (cc.sys.isBrowser) {
            if (this.passwords.length == 6) {
                this.onClickConfirm()
            } else {
                this.lbError.string = "Please enter 6 numbers."
            }
        } else if (cc.sys.isNative) {
            if (this.finalPwd.length == 6) { this.onClickConfirm() }
            else {
                this.lbError.string = "Please enter 6 numbers."
            }
        }
        this.edb.string = ''
    }
    onEditBeginTest(event) {
        event.string = ""
        this.pointArr.forEach(nd => nd.active = false)
        this.finalPwd = ""
    }
    onEditPasswordTest(text, event) {
        if (isNaN(text)) {
            return
        }
        this.pointArr.forEach(nd => nd.active = false)
        for (let i = 0; i < text.length; i++) {
            this.pointArr[i].active = true
        }
        this.finalPwd = text;
        if (this.finalPwd.length == 6) {
            event.blur()
            this.onClickConfirm()
        }
    }
    onEditEndTest(event) {
        if (this.finalPwd.length == 6) {
           // this.onClickConfirm()
        } else {
            this.lbError.string = "Please enter 6 numbers."
        }
    }
     //修改提现密码 req
     change_pay_pass(){
        console.log('--------这里开始执行 密码是否错误')
        HttpUtils.getInstance().post(3, 3, this, "/common/api/confirm/withdraw/password", {
            token: Global.getInstance().token,
            withdraw_password: Md5.hashStr(this.finalPwd).toString(),
        }, (response) => {
            if (response && response.code) {
                let data = { passType: this.passType }
                cc.director.emit("password_suc", data)
            }
            this.closeAction()
        }, (response) => {
            if (response && response.code) {
                if (response.code == 1) {
                    this.lbError.string = response.msg;
                }
                if (response.code == "102008") {//输入次数达到上限
                    this.closeAction();
                    uiManager.instance.showDialog(UI_PATH_DIC.PayPasswordLimit, [response],null,DEEP_INDEXZ.PASSWORD_LIMIT);
                }
                if (response.code == "102004") {
                    if(response.msg) Global.getInstance().showSimpleTip(response.msg);
                }
                if (this.lay.active) {
                    this.lay.getComponentInChildren(cc.EditBox).string = ''
                }
            }
        });
    }
    onClickConfirm() {
        // if (cc.sys.isBrowser) {
        //     this.finalPwd = this.passwords.join("")
        // }

        if (this.finalPwd.length < 6) {
            // Global.getInstance().showSimpleTip("Passwords error")
            this.lbError.string = "Please enter 6 numbers."
            return;
        }
        this.change_pay_pass();
    }
    closeAction() {
        // let rootNode = this.node.getChildByName("bg");

        // let anim = rootNode.getComponent(cc.Animation);
        // anim.play("passworddown");
        // anim.once("finished", () => {
        //     this.node.destroy()
        // })
        this.unscheduleAllCallbacks()
        this.hide()
    }
}
