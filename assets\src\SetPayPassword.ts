import Global from "./GlobalScript";
import RoundRectMask from "./RoundRectMask";
import UICommon from "./component/UICommon";
import { GEETEST_TYPE, GeetestMgr } from "./geetest/GeetestMgr";
import { Md5 } from "./libs/Md5";
import HttpUtils from "./net/HttpUtils";
import utils from "./utils/utils";

const { ccclass, property } = cc._decorator;

@ccclass
export default class SetPayPassword extends UICommon {

    @property(cc.Node)
    lay1: cc.Node = null;

    @property(cc.Node)
    lay2: cc.Node = null;
    @property(cc.Node)
    lay3: cc.Node = null;

    @property(cc.Node)
    lay4: cc.Node = null;

    @property(cc.Label)
    lbError: cc.Label = null;
    @property(cc.Label)
    lbError2: cc.Label = null;
    @property(cc.Node)
    pointArr: cc.Node[] = []
    @property(cc.Node)
    pointArr2: cc.Node[] = []

    @property(cc.Label)
    setPayTitle: cc.Label = null;
    @property(cc.Label)
    resetPayTitle: cc.Label = null;

    @property(cc.Node)
    normalBox: cc.Node[] = []
    @property(cc.Node)
    redBox: cc.Node[] = []
    @property(cc.Node)
    btnInitAll: cc.Node = null;

    passwords: string[] = []
    repeatPwd: string[] = []
    finalPwd: string = ''
    finalRepeatPwd: string = ''

    protected onLoad(): void {
        this.initWidget();
    }

    start(): void {
        this.scheduleOnce(()=>{
            let roundret = utils.getChildByPath(this.node, "main").getComponent(RoundRectMask);
            roundret.radius = 0.07;
        },0.01)
    }

    initWidget() {
        this.lay2.active = false;
        this.lay1.active = false;
        this.lay3.active = true;
        this.lay4.active = false;
        this.btnInitAll.active = false;
        this.setPayTitle.node.active = true;
        this.resetPayTitle.node.active = false;
        this.pointArr.forEach(nd => nd.active = false)
        this.pointArr2.forEach(nd => nd.active = false)
        this.redBox.forEach(nd => nd.active = false)
        this.lbError2.string = "";
        this.scheduleOnce(() => {
            let ed = this.lay3.getComponentsInChildren(cc.EditBox)[0]
            ed.focus()
        }, 0.5)
    }

    onEditBegin(editbox: cc.EditBox) {
        editbox.string = ""
        Global.getInstance().editParentMove(editbox, this.lay,800);
        editbox.placeholderLabel.node.name = editbox.placeholder
        editbox.placeholder = ""
    }
    onEditPassword(text, event) {
        if (isNaN(text) || !text) {
            return
        }
        if (cc.sys.isNative) {
            this.finalPwd = text;
            let str = ''
            for (let i = 0; i < text.length; i++) {
                str = str + "*"
            }
            event.string = str;
        } else {
            let idx = event.node.parent.children.indexOf(event.node)
            event.string = "·"
            this.passwords[idx] = text;
            if (idx < 5) {
                let ed = event.node.parent.children[idx + 1].getComponent(cc.EditBox)
                ed.string = ''
                ed.focus()
                ed.string = ''
            } else {
                event.blur()
                this.lbError.string = ''
            }
        }
    }
    onEditEnd(editbox: cc.EditBox) {
        if (Global.getInstance().needScreenUp()) {
            const widght = this.lay.getComponent(cc.Widget)
            widght.bottom = -90;
            widght.updateAlignment();
        }
        editbox.placeholder = editbox.placeholderLabel.node.name
    }


    onEditBeginTest(event) {
        event.string = ""
        this.pointArr.forEach(nd => nd.active = false)
        this.finalPwd = ""
    }
    onEditPasswordTest(text, event) {
        if (isNaN(text)) {
            return
        }
        this.pointArr.forEach(nd => nd.active = false)
        for (let i = 0; i < text.length; i++) {
            this.pointArr[i].active = true
        }
        this.finalPwd = text;
        if (this.finalPwd.length == 6) {
            event.blur()
        }
    }
    onEditEndTest(event) {
        if (this.finalPwd.length == 6) {
            this.lbError2.string = ""
            this.lay3.active = false;
            this.lay4.active = true;
            this.normalBox.forEach(nd => nd.active = true);
            this.setPayTitle.node.active = false;
            this.resetPayTitle.node.active = true;
            this.scheduleOnce(() => {
                let ed = this.lay4.getComponentsInChildren(cc.EditBox)[0]
                ed.focus()
            }, 0.5)
        }
    }


    onEditBeginRepeatTest(event) {
        event.string = ""
        this.pointArr2.forEach(nd => nd.active = false)
        this.finalRepeatPwd = ""
        //===
        this.normalBox.forEach(nd => nd.active = true)
        this.redBox.forEach(nd => nd.active = false)
        this.lbError2.node.active = false;
    }
    onEditPasswordRepeatTest(text, event) {
        if (isNaN(text)) {
            return
        }
        this.pointArr2.forEach(nd => nd.active = false)
        for (let i = 0; i < text.length; i++) {
            this.pointArr2[i].active = true
        }
        this.finalRepeatPwd = text;
        if (this.finalRepeatPwd.length == 6) {
            event.blur()
        }
    }
    onEditEndRepeatTest(event) {
        if (this.finalRepeatPwd.length == 6) {
            this.lbError2.string = ""
            //第二次输入完自动请求校验
            this.onClickConfirm();
        } else {
            this.lbError2.node.active = true;
            this.lbError2.string = "Client error message"//Please enter 6 numbers.
            //==red框提示
            this.normalBox.forEach(nd => nd.active = false);
            this.redBox.forEach(nd => nd.active = true);
        }
    }

    onEditRepeatBegin(event) {
        event.string = ""
        cc.log("onEdittingDidBegin ==>", this.node.y)
        if (Global.getInstance().needScreenUp()) {
            this.node.y = this.node.y + 310
        }
    }
    onEditRepeat(text, event) {
        if (isNaN(text) || !text) {
            return
        }
        if (cc.sys.isNative) {
            this.finalRepeatPwd = text
            let str = ''
            for (let i = 0; i < text.length; i++) {
                str = str + "*"
            }
            event.string = str;
        } else if (cc.sys.isBrowser) {
            let idx = event.node.parent.children.indexOf(event.node)
            event.string = "·"
            this.repeatPwd[idx] = text;
            if (idx < 5) {
                let ed = event.node.parent.children[idx + 1].getComponent(cc.EditBox)
                ed.string = ''
                ed.focus()
                ed.string = ''
            } else {
                event.blur()
                this.lbError2.string = ''
            }
        }
    }
    onEditRepeatEnd(event) {
        if (Global.getInstance().needScreenUp()) {
            this.node.y = this.node.y - 310
        }
        if (cc.sys.isBrowser) {
            if (this.repeatPwd.length == 6 && this.passwords.length == 6) {
                this.onClickConfirm()
            }
        } else if (cc.sys.isNative) {
            if (this.finalRepeatPwd.length == 6 && this.finalPwd.length == 6) this.onClickConfirm()
        }
    }


    onClickConfirm() {
        if (this.finalPwd != this.finalRepeatPwd) {
            // Global.getInstance().showSimpleTip("Passwords do not match")
            this.lbError2.node.active = true;
            this.lbError2.string = "The passwords entered are inconsistent"
            this.finalPwd = '';
            this.finalRepeatPwd = '';
            this.passwords = [];
            this.repeatPwd = []
            if (this.lay3.active) {
                // this.lay3.getComponentsInChildren(cc.EditBox).forEach(eb => eb.string = '')
                // this.lay4.getComponentsInChildren(cc.EditBox).forEach(eb => eb.string = '')
                this.pointArr.forEach(nd => nd.active = false)
                this.pointArr2.forEach(nd => nd.active = false)
            } else {
                this.lay1.getComponentInChildren(cc.EditBox).string = ''
                this.lay2.getComponentInChildren(cc.EditBox).string = ''
            }
            this.btnInitAll.active = true;
            this.lay4.active = true;
            this.normalBox.forEach(nd => nd.active = false);
            this.redBox.forEach(nd => nd.active = true);
            return;
        }
        let geeid = GEETEST_TYPE.first_pay_password
        if(Global.getInstance().userdata.withdraw_password){
            geeid = GEETEST_TYPE.change_pay_password
        }
        //加个验证
        GeetestMgr.instance.geetest_device(geeid,(succ)=>{
            if(succ){
                let ret = {}
                if(Object.getPrototypeOf(succ) !== Boolean.prototype){
                    ret = succ
                }
                this.change_pay_pass(ret);
            }
        })
        
    }
    //修改提现密码 req
    change_pay_pass(ret?){
        let gee_guard = ret?.geetest_guard || '';
        let uInfo = ret?.userInfo || '';
        let gee_captcha = ret?.geetest_captcha || '';
        //设置提现密码req
        HttpUtils.getInstance().post(3, 3, this, "/common/api/set/withdraw/password", {
            token: Global.getInstance().token,
            withdraw_password: Md5.hashStr(this.finalRepeatPwd).toString(),
            geetest_guard:gee_guard,
            userInfo:uInfo,
            geetest_captcha:gee_captcha,
            buds:ret?.buds || '64',
        },(response) => {
            Global.getInstance().userdata.withdraw_password = 1;
            Global.getInstance().showSimpleTip("Payment password set successfully")
            this.closeAction();
            cc.director.emit("setPasswordSuc")
        }, (err)=>{
            cc.log("response ===>",JSON.stringify(err))
            Global.getInstance().showSimpleTip(err?.msg);
        });
    }
    closeAction() {
        let mainNode = this.node.getChildByName("main");
        cc.tween(mainNode).to(0.1,{position:new cc.Vec3(0,-550)}).call(()=>{
            this.node.destroy();
        }).start();
    }
}
