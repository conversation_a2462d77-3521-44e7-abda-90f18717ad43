# Hall 模块页面业务逻辑详解

## 📖 概述

本文档详细解释 Hall 模块中各个页面的具体业务逻辑，包括用户操作流程、数据处理机制、状态管理等核心业务规则。

## 🏠 Hall 主页面 - 核心控制中心

### 📋 页面初始化流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant Hall as Hall主页
    participant Global as 全局数据
    participant Server as 服务器

    User->>Hall: 进入大厅
    Hall->>Hall: onLoad() 初始化
    Hall->>Global: 检查登录状态
    alt 已登录
        Hall->>Server: 获取用户数据
        Hall->>Hall: 显示余额和用户信息
    else 未登录
        Hall->>Hall: 显示登录界面
    end
    Hall->>Hall: start() 启动各种服务
    Hall->>Server: 获取活动数据
    Hall->>Server: 获取Banner数据
    Hall->>Hall: 初始化游戏列表
```

### 🎮 游戏列表业务逻辑

#### 游戏分类系统

```typescript
// 游戏分类定义
typeList = [
  "Casino", // 娱乐场游戏（默认选中）
  "Slots", // 老虎机游戏
  "Poker", // 扑克类游戏
  "Bingo", // 宾果游戏
  "Arcade", // 街机游戏
  "Sports", // 体育博彩
  "Like", // 收藏游戏
  "History", // 历史记录
];
```

#### 动态加载机制

**滚动监听逻辑**:

```typescript
refreshGmaeType() {
    let endOffset = this.parentScrollView.getScrollOffset();
    let maxoffy = this.parentScrollView.getMaxScrollOffset().y;

    // 根据滚动位置自动切换游戏分类
    for (let index = 0; index < gameTypes.length; index++) {
        let offended = this.scorllEndOffSetsList[type] + this.gameScrollOffset;
        let offstart = this.scorllOffSetsList[type] + this.gameScrollOffset;

        if (endOffset.y <= offended && endOffset.y > offstart) {
            this.gameTypeButtonChanged(element.id);
            return;
        }
    }
}
```

#### 游戏状态管理

**状态枚举**:

```typescript
ICON_STATUS = {
  NORMAL: 1, // 正常状态
  MAINTENANCE: 2, // 维护状态
};

GAME_TAGS = {
  REGULAR: 0, // 普通游戏
  HOT: 1, // 热门游戏
  NEW: 2, // 新游戏
};
```

**状态处理逻辑**:

- **正常状态**: 用户可以正常点击进入游戏
- **维护状态**: 显示维护图标，点击时提示维护中
- **热门标签**: 在游戏图标上显示 HOT 标识
- **新游戏标签**: 在游戏图标上显示 NEW 标识

### 💰 余额和金币系统

#### 余额更新机制

```typescript
updateUserInfo(data?) {
    // 余额上限处理
    if (Global.getInstance().balanceTotal >= 9999999999) {
        this.m_moneny.string = "9,999,999,999.00";
    } else {
        this.m_moneny.string = utils.formatNumberWithCommas(Global.getInstance().balanceTotal);
    }

    // 余额变化动画效果
    cc.Tween.stopAllByTarget(this.m_moneny.node);
    cc.tween(this.m_moneny.node)
        .to(0.05, { opacity: 0 })
        .to(0.1, { opacity: 255 })
        .start();
}
```

#### 金币动画系统

**动画触发条件**:

- 用户获得奖励时
- 充值成功时
- 活动奖励发放时
- 游戏获胜时

**动画实现**:

```typescript
showGoldAni(data) {
    let goldAniNode = Global.getInstance().popNode.getChildByName("goldAniNode");
    if (!goldAniNode) {
        goldAniNode = cc.instantiate(this.m_goldani_prefab);
        goldAniNode.parent = Global.getInstance().popNode;
        goldAniNode.zIndex = 1499;
        goldAniNode.name = "goldAniNode";
    }

    const comp = goldAniNode.getComponent(CoinRewardEffect);
    if (comp) {
        comp.playAnimation(data);
    }
}
```

### 🎯 活动和功能按钮

#### 转盘按钮逻辑

```typescript
getSpinInfo(force?, cb?) {
    // 获取转盘配置信息
    HttpUtils.getInstance().get(1, 0, this, "/open/api/activity/spin/config", parms, (response) => {
        let spindata = response?.data;
        Global.getInstance().spinInfo = spindata;

        // 判断是否显示转盘按钮
        if (spindata && spindata.is_start && spindata.is_start == 1) {
            btn_spin.active = true;
            let gamescene = cc.director.getScene().getComponentInChildren(GameControl);
            gamescene.mTabbar.isShowwheel_btn();
        } else {
            btn_spin.removeFromParent();
        }

        // 更新剩余次数显示
        if (spindata && spindata.left_times >= 0 && token_) {
            if (spindata.left_times == 0) {
                spin_count.active = false;
            } else {
                spin_count.active = true;
                spin_count.getChildByName("lab_count").getComponent(cc.Label).string = spindata.left_times;
            }
        }
    });
}
```

#### 红包按钮逻辑

```typescript
initEnvelope() {
    let btn_envelope = Global.getInstance().popNode.getChildByName("btn_envelope");
    if (!btn_envelope) return;

    let envelopedata = Global.getInstance().envelopeInfo;
    // 首充引导红包显示条件
    if (parseInt(envelopedata.first_deposit_bonus_guide_is_start) == 1 && !envelopedata.is_first_recharge) {
        btn_envelope.active = true;
    } else {
        btn_envelope.active = false;
    }
}
```

## 🎮 AllGameView - 游戏展示页面

### 🔍 游戏搜索和筛选系统

#### 搜索业务流程

```mermaid
flowchart TD
    A[用户进入游戏页面] --> B[选择游戏分类]
    B --> C[加载对应游戏列表]
    C --> D{是否有搜索条件?}
    D -->|是| E[执行搜索筛选]
    D -->|否| F[显示全部游戏]
    E --> G[显示筛选结果]
    F --> G
    G --> H[用户选择游戏]
    H --> I[检查登录状态]
    I -->|已登录| J[进入游戏]
    I -->|未登录| K[跳转登录页面]
```

#### 游戏数据加载

```typescript
gameDataList(currentType, cb?) {
    this.currentType = currentType;
    if (this.currentType == "Hot") {
        this.currentType = this.typeList[0]; // Hot类型映射到Casino
    }
    this.homeCb = cb;

    // 切换到对应页面
    for (let index = 0; index < this.typeList.length; index++) {
        const element = this.typeList[index];
        if (element == this.currentType) {
            this.pageView.scrollToPage(index, 0.2);
            this.initGameListDataByFilter();
            this.refreshHistoryListList("History");
            this.refreshHistoryListList("Like");
            this.prepareShowListView();
        }
    }
}
```

### 🎯 特殊游戏类型处理

#### 特殊游戏分类

```typescript
SPECIAL_TYPE = {
  BACCARAT: "baccarat", // 百家乐专区
  ROULETTE: "roulette", // 轮盘专区
  BLACKJACK: "blackjack", // 21点专区
};
```

#### 历史记录和收藏功能

**History 页面逻辑**:

- 显示用户最近玩过的游戏
- 按时间倒序排列
- 支持清空历史记录

**Like 页面逻辑**:

- 显示用户收藏的游戏
- 支持添加/移除收藏
- 收藏状态持久化存储

## 💰 Transations - 交易记录页面

### 📊 交易记录分类系统

#### 记录类型定义

```typescript
const RECORD_TYPE = {
  ADJUSTMENT: "Adjustment", // 调整记录
  MAYAPAY: "mayapay", // Maya支付
  MAYA_WEB: "mayawebpay", // Maya网页支付
  GCASH_WEB: "gcashwebpay", // GCash网页支付
  DEPOSIT: "Deposit", // 充值
  WITHDRAWAL: "Withdrawal", // 提现
  BATCH_WITHDRAWAL: "Batch Withdrawal", // 批量提现
  REWARD: "Reward", // 奖励
  TRANSFER: "Transfer", // 转账
};
```

#### 状态管理系统

**充值状态枚举**:

```typescript
enum RECHARGE_WEB_STATUS {
  SUCCESS = 1, // 成功
  PENDING = 2, // 等待
  FAILED = 3, // 失败
  CANCEL = 4, // 取消
  WAITING = 5, // 等待处理
  OPERATION = 6, // 人工处理
  WAITING_PAYMENT = 7, // 等待支付
  WAITING_CHANGE_BALANCE = 8, // 等待余额变更
}
```

**提现状态枚举**:

```typescript
enum WITHDRAW_STATUS {
  PENDING = 1, // 待到账
  SUCCESS = 2, // 成功
  FAILURE = 3, // 失败
  OPERATIONAL = 4, // 人工审核
  PENDING_APPROVAL = 5, // 待审核
}
```

### 📅 时间筛选功能

#### 筛选选项

```typescript
enum FILTER_DATA {
  TODAY, // 今天
  YESTERDAY, // 昨天
  LAST_THREEDAYS, // 最近3天
  LAST_SEVENTDAYS, // 最近7天
}
```

#### 筛选实现逻辑

```typescript
filterTimeResult(timeResult) {
    // 根据选择的时间范围设置筛选条件
    if (this.filterTimeResult[0] == DATE_TYPE.TODAY)
        this.filterTimeData = FILTER_DATA.TODAY;
    else if (this.filterTimeResult[0] == DATE_TYPE.YESTERDAY)
        this.filterTimeData = FILTER_DATA.YESTERDAY;
    else if (this.filterTimeResult[0] == DATE_TYPE.LAST_THREEDAYS)
        this.filterTimeData = FILTER_DATA.LAST_THREEDAYS;
    else if (this.filterTimeResult[0] == DATE_TYPE.LAST_SEVENTDAYS)
        this.filterTimeData = FILTER_DATA.LAST_SEVENTDAYS;

    // 刷新交易列表
    this.refreshTransListView();
}
```

### 📋 记录详情展示

#### 充值记录处理

```typescript
initDepositItem(item, deposit, index, k, tagRecord) {
    let numlab = item.getChildByName("lab_num").getComponent(cc.Label);
    let sp = item.getChildByName("sp_icon").getComponent(cc.Sprite);

    // 设置图标
    sp.spriteFrame = this.spriteFreams[0];

    // 处理不同支付渠道
    if (deposit[index][k].pay_channel == RECORD_TYPE.ADJUSTMENT) {
        item.getChildByName("lab_recordtype").getComponent(cc.Label).string = RECORD_TYPE.TRANSFER;
        sp.spriteFrame = this.spriteFreams[1];
    } else if (deposit[index][k].pay_channel == RECORD_TYPE.MAYAPAY ||
               deposit[index][k].pay_channel == RECORD_TYPE.MAYA_WEB ||
               deposit[index][k].pay_channel == RECORD_TYPE.GCASH_WEB) {
        // 设置对应的支付方式图标和文本
    }

    // 设置金额显示
    numlab.string = "+" + utils.formatNumberWithCommas(deposit[index][k].amount / GOLD_RATIO);
    numlab.node.color = this.font_color[3];

    // 绑定详情点击事件
    item.getChildByName("btn_detial").on(cc.Node.EventType.TOUCH_END, (event) => {
        this.showDetials(deposit[index][k], tagRecord);
    }, this);
}
```

#### 提现记录处理

```typescript
initWithdrawItem(item, withdraw, index, k, tagRecord) {
    // 处理批量提现
    if (withdraw[index][k].quantity > 1) {
        // 显示批量提现标识
        item.getChildByName("btn_detial").on(cc.Node.EventType.TOUCH_END, (event) => {
            this.showBatch(withdraw[index][k], frame_with);
        }, this);
    } else {
        // 单笔提现处理
        item.getChildByName("btn_detial").on(cc.Node.EventType.TOUCH_END, (event) => {
            this.showDetials(withdraw[index][k], tagRecord);
        }, this);
    }
}
```

#### 奖励记录处理

```typescript
initAwardItem(item, award, index, k, tagRecord) {
    item.getChildByName("lab_recordtype").getComponent(cc.Label).string = RECORD_TYPE.REWARD;
    let sp = item.getChildByName("sp_icon").getComponent(cc.Sprite);
    sp.spriteFrame = this.spriteFreams[1];

    let update_type = award[index][k].update_type;
    let labStatus = item.getChildByName("lab_status").getComponent(cc.Label);

    // 根据奖励类型设置显示文本
    if (update_type == AWARD_UPDATE_TYPE.FREE_REGISTRATION) {
        let register_str = `Free ₱${award[index][k].amount / GOLD_RATIO} Registration Bonus`;
        Global.instance.init_enum_typestr(update_type, '', register_str, labStatus);
    }

    // 设置奖励金额
    let numlab = item.getChildByName("lab_num").getComponent(cc.Label);
    let award_num = award[index][k].amount;
    if (award_num >= 0) {
        numlab.string = "+" + utils.formatNumberWithCommas(award_num / GOLD_RATIO);
        numlab.node.color = this.font_color[3];
    } else {
        numlab.string = utils.formatNumberWithCommas(award_num / GOLD_RATIO);
        numlab.node.color = this.font_color[2];
    }
}
```

## 🎯 Promo - 活动促销页面

### 🎪 活动类型管理系统

#### 活动分类枚举

```typescript
export enum ACTIVITY_TYPE {
  SIGNBIND = 1, // 签到绑定活动
  FIRSTDEPOSIT = 2, // 首次充值活动
  CASHBACK = 3, // 返水活动
  VIPCASHBACK = 4, // VIP返水活动
  RANK = 5, // 排行榜活动
  SPIN = 6, // 转盘活动
  RANKJILI = 7, // JILI排行榜
  VALENTINE = 8, // 情人节活动
  WEEKLY = 9, // 周薪活动
  PP_DAILY_WINS = 10, // PP每日获胜
  LATE_NIGHT_CASHBACK = 11, // 深夜返水
  FC_FREE_SPIN = 12, // FC免费旋转
  GET_EASTER_BONUS = 13, // 复活节奖励
  YELLOW_BAT = 14, // 黄蝙蝠活动
  JILI_FREE_SPIN = 15, // JILI免费旋转
  FC_FREE_SPIN2 = 16, // FC免费旋转2
  CASHBACK_DETIALS = 100, // 返水详情
  VIP_CASHBACK_DETIALS = 101, // VIP返水详情
}
```

### ⏰ 活动时间控制机制

#### 活动有效性验证

```typescript
// 活动筛选逻辑
for (let index = 0; index < data.length; index++) {
  let element = data[index];
  let rightChannel = false;
  let rightPage = false;
  let rightTime = false;

  // 渠道验证
  let channels = element.channel.split(",");
  if (channels.indexOf(now_channel) != -1) {
    rightChannel = true;
  }

  // 页面验证
  let pages = element.page.split(",");
  if (pages && pages.length > 0) {
    for (let pageIndex = 0; pageIndex < pages.length; pageIndex++) {
      if (pages[pageIndex] == "Promo") {
        rightPage = true;
        break;
      }
    }
  }

  // 时间验证
  let starttime = utils.timeToTimestamp(element.start_at);
  let endtime = utils.timeToTimestamp(element.end_at);
  if (endtime > Global.getInstance().now() && starttime < Global.getInstance().now()) {
    rightTime = true;
  }

  // 综合判断活动是否有效
  let promoAct = rightChannel && rightPage && rightTime;
  if (promoAct) {
    newList.push(element);
  }
}
```

### 🎁 活动奖励发放系统

#### 活动参与流程

```mermaid
flowchart TD
    A[用户进入活动页面] --> B[查看活动列表]
    B --> C[选择参与活动]
    C --> D[验证活动条件]
    D -->|满足条件| E[参与活动]
    D -->|不满足| F[显示条件提示]
    E --> G[完成活动任务]
    G --> H[系统验证完成度]
    H -->|完成| I[发放奖励]
    H -->|未完成| J[更新进度]
    I --> K[记录奖励历史]
```

#### 活动按钮点击处理

```typescript
// 活动项点击事件
btnPromo.on(
  cc.Node.EventType.TOUCH_END,
  () => {
    let timegap = (Global.getInstance().now() - this.lastRefreshTime) / 1000;
    if (timegap < 1) {
      return; // 防止重复点击
    }
    this.lastRefreshTime = Global.getInstance().now();

    // 跳转到活动详情或执行活动逻辑
    Global.getInstance().bannerJumpView(element);
  },
  this
);
```

## 🤝 MGM 邀请系统

### 👥 邀请业务核心流程

#### 邀请页面状态管理

```typescript
export enum invitePage {
  page_none = 0,
  page_home = 1, // 邀请主页
  page_help = 2, // 帮助说明
  page_detail = 3, // 邀请详情
  page_member = 4, // 成员列表
  page_rules = 5, // 邀请规则
  page_count = 6, // 统计数据
}
```

#### 邀请码绑定逻辑

```typescript
bindInvite(inviteCode: string) {
    HttpUtils.getInstance().post(3, 3, this, "/api/sponsor/argument", {
        token: Global.getInstance().token,
        inviteCode: inviteCode,
    }, (response) => {
        // 绑定成功处理
        cc.director.emit("BindInviteSuc");
        Global.getInstance().userdata["invite_user_id"] = inviteCode;

        // 发放绑定奖励
        let coinsNum = Global.getInstance().mgm.invitee_reward;
        let itemdata = {
            spf: null,
            count: coinsNum,
            info: "Congratulations on getting the binding bonus 5 coins!"
        };
        showExchangeSucceed(itemdata);
    }, (res) => {
        // 绑定失败处理
        if (!res) return;
        if (res["code"]) {
            let strTip = Global.getInstance().getLabel("invitation_text_" + res["code"]);
            Global.getInstance().showSimpleTip(strTip);
        } else {
            if (res.msg) Global.getInstance().showSimpleTip(res.msg);
        }
    });
}
```

### 🎖️ 邀请奖励计算系统

#### 邀请检查逻辑

```typescript
checkInvite() {
    // 检查邀请功能是否开启
    let isOpen = Global.getInstance().getActivityDataWithType(SOURCE_INVITE_FRIENDS);
    if (!isOpen) {
        return 0; // 功能未开启
    }

    // 检查是否为游客用户
    if (InviteManager.checkGuest()) {
        return 0; // 游客用户不能参与邀请
    }

    // 检查是否已绑定邀请码
    let isInviteBind = Global.getInstance().isInviteBind();
    let now = Math.floor(Global.getInstance().now() / 1000);
    let createTime = Global.getInstance().userdata.created_at;
    let isOver24 = (now - createTime) > 24 * 3600; // 是否超过24小时

    // 检查剪贴板中的邀请码
    let strPasteboard = Global.getInstance().getPasteboard();

    if (!isInviteBind && !isOver24 && strPasteboard) {
        return 1; // 显示输入邀请码界面
    } else if (!isInviteBind && !isOver24) {
        return 2; // 显示绑定邀请界面
    }

    return 0; // 不显示邀请相关界面
}
```

## 🎰 SpinWheel - 转盘活动

### 🎯 转盘状态管理

#### 转盘可用性检查

```typescript
// 转盘配置检查
if (Global.getInstance().token && Global.getInstance().spinInfo.is_start == 1 && Global.getInstance().spinInfo.real_left_times > 0) {
  this.showSpinView(); // 显示转盘界面
}
```

#### 转盘次数管理

```typescript
// 更新转盘剩余次数
if (spindata && spindata.left_times >= 0 && token_) {
  if (spindata.left_times == 0) {
    spin_count.active = false; // 隐藏次数显示
  } else {
    spin_count.active = true;
    spin_count.getChildByName("lab_count").getComponent(cc.Label).string = spindata.left_times;
  }
}
```

### 🎁 转盘奖励机制

#### 奖励类型定义

- **金币奖励**: 直接增加用户余额
- **免费游戏次数**: 特定游戏的免费机会
- **充值优惠券**: 充值时可使用的优惠
- **特殊道具**: 游戏内特殊物品

## 📧 Mail - 邮件系统

### 📬 邮件处理业务逻辑

#### 邮件列表更新

```typescript
onMailUpdate(mailList: Readonly<Object[]>) {
    // 过滤系统邮件
    let _mailList = mailList.filter(mail => {
        return mail["from_user_id"] == 0;
    });

    if (_mailList.length > 0) {
        this.tip.active = false; // 隐藏无邮件提示

        // 创建邮件列表项
        for (let i = 0; i < _mailList.length; i++) {
            let item = cc.instantiate(this.itemPfb);
            item.parent = this.itemView.content;
            item.name = _mailList[i]["id"] + "";

            let mailItem = item.getComponent(MailItem);
            mailItem.setData(_mailList[i], i, this);
        }
    } else {
        this.tip.active = true; // 显示无邮件提示
    }

    this.hall.readAll(); // 标记所有邮件为已读
}
```

### 🎁 邮件附件奖励系统

#### 奖励领取流程

1. **邮件点击**: 用户点击邮件项
2. **显示详情**: 展示邮件内容和附件
3. **检查奖励**: 验证是否有可领取的附件奖励
4. **领取确认**: 用户确认领取奖励
5. **奖励发放**: 系统发放奖励到用户账户
6. **状态更新**: 更新邮件和奖励状态

## 🏆 LeaderBoard - 排行榜系统

### 📊 排行榜数据管理

#### 排行榜类型

```typescript
// 排行榜时间维度
const LEADERBOARD_TYPE = {
  TODAY: 1, // 今日排行
  YESTERDAY: 2, // 昨日排行
};
```

#### 排行榜数据获取

```typescript
geTodaytRankData() {
    HttpUtils.getInstance().post(1, 3, this, "/avt/api/rank/casino", {
        token: Global.getInstance().token,
        type: 1 // 今日排行
    }, (response) => {
        if (response.code == 0) {
            // 处理今日排行榜数据
            this.initTodayRankList(response.data);
        }
    });
}

getYesterdaytRankData() {
    HttpUtils.getInstance().post(1, 3, this, "/avt/api/rank/casino", {
        token: Global.getInstance().token,
        type: 2 // 昨日排行
    }, (response) => {
        if (response.code == 0) {
            // 处理昨日排行榜数据
            this.initYesterdayRankList(response.data);
        }
    });
}
```

### 🎖️ 排行榜奖励发放

#### 奖励计算规则

- **第 1 名**: 最高奖励金额
- **第 2-3 名**: 次级奖励金额
- **第 4-10 名**: 普通奖励金额
- **参与奖**: 所有参与用户的基础奖励

#### 奖励发放时机

- **每日结算**: 每天凌晨自动结算前一天排行榜
- **实时更新**: 排行榜数据实时更新
- **奖励延迟**: 奖励发放可能有 1-2 小时延迟

## 🔄 页面间数据流转机制

### 📡 事件驱动系统

#### 核心事件定义

```typescript
// 全局事件列表
const GLOBAL_EVENTS = {
  UPDATE_GOLD: "update_gold", // 金币余额更新
  REFRESH_PROMO: "refresh_promo", // 活动数据刷新
  SHOW_GOLD_ANI: "showGoldAni", // 播放金币动画
  UPDATE_HOME_WIDGET: "update_home_widget", // 首页组件更新
  REFRESH_MAIL: "refresh_mail", // 邮件数据刷新
  UPDATE_LEADERBOARD: "update_leaderboard", // 排行榜更新
};
```

#### 事件监听和触发

```typescript
// 事件监听注册
cc.director.on("update_gold", this.updateUserInfo, this);
cc.director.on("refresh_promo", this.refreshPromoData, this);
cc.director.on("showGoldAni", this.showGoldAnimation, this);

// 事件触发
cc.director.emit("update_gold", newBalance);
cc.director.emit("showGoldAni", { type: GOLDANI_TYPE.TOTARGET, target: targetNode });
```

### 🗄️ 数据持久化策略

#### 本地存储管理

```typescript
// 本地存储键定义
const STORAGE_KEYS = {
  USER_SETTINGS: "user_settings", // 用户设置
  GAME_HISTORY: "game_history", // 游戏历史
  ACTIVITY_STATUS: "activity_status", // 活动状态
  MAIL_READ_STATUS: "mail_read_status", // 邮件已读状态
  INVITE_STATUS: "invite_status", // 邀请状态
};

// 数据存储和读取
Global.getInstance().setStoreageData(STORAGE_KEYS.USER_SETTINGS, settingsData);
let settings = Global.getInstance().getStoreageData(STORAGE_KEYS.USER_SETTINGS, defaultSettings);
```

#### 服务器数据同步

- **用户余额**: 实时同步，确保数据一致性
- **交易记录**: 定期同步，支持离线查看
- **活动进度**: 实时更新，防止作弊
- **邀请关系**: 永久存储，支持历史查询

---

_文档版本：v1.0_
_最后更新：2025-08-07_
_维护者：开发团队_
