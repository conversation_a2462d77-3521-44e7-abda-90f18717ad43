import ExchangeSucceed from "./ExchangeSucceed";
import { DEEP_INDEXZ, UI_PATH_DIC } from "./GlobalConstant";
import Global from "./GlobalScript";
import RoundRectMask from "./RoundRectMask";
import { showLoginPassword } from "./SetLoginPassword";
import UICom<PERSON> from "./component/UICommon";
import { GEETEST_TYPE, GeetestMgr } from "./geetest/GeetestMgr";
import { uiManager } from "./mgr/UIManager";
import HttpUtils from "./net/HttpUtils";
import { GlobalEnum } from "./room/GlobalEnum";
import utils from "./utils/utils";

export enum PN_VERIFY_TYPE {
    SetPhoneNumber = 0,
    ForgetPassword,
    ChangePhoneNumber,
    AddWithdrawAccount,
    ChangeWithdrawAccount,
}

export function showPhoneNumber(verify_way: number = PN_VERIFY_TYPE.SetPhoneNumber, phone?: string, cb?, panelzIndex?:any) {
    uiManager.instance.showDialog(UI_PATH_DIC.SetPhoneNumber, [{ verify_way: verify_way, phone: phone, cb: cb }], null, panelzIndex || DEEP_INDEXZ.VERIFY_CODE)
}

const { ccclass, property } = cc._decorator;

const TITLESTRS = ["Change Login Password", "Set Phone Number", "Change Phone Number"]

const PN_TITLE = {
    SetPhoneNumber:"Set Phone Number",
    ForgetPassword:"Change Login Password",
    ChangePhoneNumber:"Change Phone Number",
    AddWithdrawAccount:"Add Fund Account",
    ChangeWithdrawAccount:"Change Fund Account"
}

@ccclass
export default class PhoneNumber extends UICommon {
    @property(cc.Label)
    labTitle: cc.Label = null;

    @property(cc.Node)
    phoneNum: cc.Node = null;

    @property(cc.Node)
    uiBox: cc.Node = null;

    @property(cc.Node)
    m_get_btn: cc.Node = null;

    @property(cc.Label)
    m_time: cc.Label = null;

    //提交按钮 label
    @property(cc.Label)
    btn_label: cc.Label = null;

    @property(cc.Label)
    lbError2: cc.Label = null;

    @property(cc.Node)
    ndRed2: cc.Node = null

    @property(cc.EditBox)
    code_edit: cc.EditBox = null;

    //验证码 node 
    @property(cc.Node)
    yzmNode: cc.Node = null
    //手机号 node 
    @property(cc.Node)
    iphoneNode: cc.Node = null

    m_phone: string = "";
    m_code: string = "";
    m_t: number = 120;

    m_verifyWay: number = PN_VERIFY_TYPE.SetPhoneNumber
    m_oldPhone: string = '';

    callBackFunc = null;
    smsType = 1;

    onLoad(): void {
        cc.director.emit("hideHallLive");//关闭直播视频
        this.checkCountdown();
    }

    start(): void {
       
    }

    init(args: { verify_way: number, phone: string , cb?}) {
        this.m_verifyWay = args.verify_way;
        this.callBackFunc = args.cb;
        if (this.m_verifyWay == PN_VERIFY_TYPE.SetPhoneNumber) {
            let userData = Global.getInstance().userdata;
            if (userData && userData.phone) {
                this.m_phone = userData.phone
            }
        } else if (this.m_verifyWay == PN_VERIFY_TYPE.ForgetPassword) {
            let phone
            if (args.phone) {
                phone = args.phone
            } else {
                phone = Global.getInstance().getStoreageData(Global.GLOBAL_STORAGE_KEY.PHONE, "");
            }
            if (phone.length > 0) {
                this.m_phone = phone
                if (this.phoneNum) this.phoneNum.getComponent(cc.Label).string = this.formatPhoneNumber(this.m_phone);
            }
        } else if (this.m_verifyWay == PN_VERIFY_TYPE.ChangePhoneNumber) {
            this.m_oldPhone = args.phone || Global.getInstance().userdata.phone
        } else if (this.m_verifyWay == PN_VERIFY_TYPE.AddWithdrawAccount || this.m_verifyWay == PN_VERIFY_TYPE.ChangeWithdrawAccount) {
            let userData = Global.getInstance().userdata;
            if (userData && userData.phone) {
                this.m_phone = userData.phone
            }
        }
        //初始化
        this.iphoneNode.active = true;
        this.yzmNode.active = false;

        //忘记密码、添加提现账号、编辑提现账号 直接进行短信验证
        if (this.m_verifyWay == PN_VERIFY_TYPE.ForgetPassword || this.m_verifyWay == PN_VERIFY_TYPE.AddWithdrawAccount || this.m_verifyWay == PN_VERIFY_TYPE.ChangeWithdrawAccount) {
            this.iphoneNode.active = false;
            this.yzmNode.active = true;
            if (this.yzmNode.active) {
                //phone
                let mphone = this.yzmNode.getChildByName("phone");
                let phone = this.m_phone;
                if (this.m_verifyWay == PN_VERIFY_TYPE.AddWithdrawAccount && Global.getInstance().userdata) {
                    phone = Global.getInstance().userdata.phone;
                }

                if (mphone) mphone.getComponent(cc.Label).string = this.formatPhoneNumber(phone);
            }
        }

        //Title标题 根据情况做判定
        if (this.m_verifyWay == PN_VERIFY_TYPE.SetPhoneNumber) this.labTitle.string = PN_TITLE.SetPhoneNumber;
        else if(this.m_verifyWay == PN_VERIFY_TYPE.ChangePhoneNumber) this.labTitle.string = PN_TITLE.ChangePhoneNumber;
        else if(this.m_verifyWay == PN_VERIFY_TYPE.ForgetPassword) this.labTitle.string = PN_TITLE.ForgetPassword;
        else if (this.m_verifyWay == PN_VERIFY_TYPE.AddWithdrawAccount) this.labTitle.string = PN_TITLE.AddWithdrawAccount;
        else if (this.m_verifyWay == PN_VERIFY_TYPE.ChangeWithdrawAccount) this.labTitle.string = PN_TITLE.ChangeWithdrawAccount;
    }

    formatPhoneNumber(phoneNumber: string) {
        //保留前两位和后四位，中间用****替换
        return `${phoneNumber.slice(0, 2)}****${phoneNumber.slice(6)}`;
    }

    onCodeText(text, editbox, customEventData) {
        if (this.iphoneNode.active) {
            this.m_phone = text;
        }
    }
    //发送验证码之前先 geetest
    checkPhoneIsRegister(){
        let gtype = ''
        switch (this.m_verifyWay) {
            case PN_VERIFY_TYPE.SetPhoneNumber:
                gtype = GEETEST_TYPE.bind_pt_phone_code;
                break;
            case PN_VERIFY_TYPE.ChangePhoneNumber:
                gtype = GEETEST_TYPE.change_pt_phone_code;
                break;
            case PN_VERIFY_TYPE.AddWithdrawAccount:
                gtype = GEETEST_TYPE.bind_withdraw_account_code;
                break;
            case PN_VERIFY_TYPE.ChangeWithdrawAccount:
                gtype = GEETEST_TYPE.change_withdraw_account_code;
                break;
            case PN_VERIFY_TYPE.ForgetPassword:
                gtype = GEETEST_TYPE.forget_password_code;
                break;
            default:
                break;
        }
        GeetestMgr.instance.geetest_device(gtype,(succ)=>{
            if(succ){
                let ret = {}
                if(Object.getPrototypeOf(succ) !== Boolean.prototype){
                    ret = succ
                }
                this.checkPhoneIsRegister_true(ret);
            }
        })
    }
    //发送验证码
    checkPhoneIsRegister_true(ret?) {
        let gee_guard = ret?.geetest_guard || '';
        let uInfo = ret?.userInfo || '';
        let gee_captcha = ret?.geetest_captcha || '';
        let params;
        params = {
            phone: this.m_phone,
            telephoneCode: "+63",
            type: this.m_verifyWay,
            geetest_guard:gee_guard,
            userInfo:uInfo,
            geetest_captcha:gee_captcha,
            buds:ret?.buds || '64',
        }
        if (this.m_verifyWay == PN_VERIFY_TYPE.SetPhoneNumber || this.m_verifyWay == PN_VERIFY_TYPE.ChangePhoneNumber || 
            this.m_verifyWay == PN_VERIFY_TYPE.AddWithdrawAccount || this.m_verifyWay == PN_VERIFY_TYPE.ChangeWithdrawAccount) {
            if (this.m_verifyWay == PN_VERIFY_TYPE.AddWithdrawAccount) {
                params["type"] = GlobalEnum.SMS_TYPE.BIND_WITHDRAW_ACCOUNT;
            }
            if (this.m_verifyWay == PN_VERIFY_TYPE.ChangeWithdrawAccount) {
                params["type"] = GlobalEnum.SMS_TYPE.UPDATE_WITHDRAW_ACCOUNT;
            }
            if (this.m_verifyWay == PN_VERIFY_TYPE.ChangePhoneNumber) {
                params["type"] = GlobalEnum.SMS_TYPE.UPDATE_PHONE;
            }
            if (this.m_verifyWay == PN_VERIFY_TYPE.SetPhoneNumber) {
                params["type"] = GlobalEnum.SMS_TYPE.BIND_PHONE;
            }
            this.smsType = params["type"];
            HttpUtils.getInstance().post(3, 3, this, "/common/api/sms/send/short/msg", params, () => {
                Global.getInstance().showSimpleTip("Verification code sent successfully")
            }, (response) => {
                if (response && response.code) {
                    if (response && response["code"] == 600) {
                        Global.getInstance().showSimpleTip(Global.getInstance().getLabel("tipword23"));
                    } else {
                        if(response?.msg){
                            Global.getInstance().showSimpleTip(response.msg);
                        }
                    }    
                } else {
                    if(response?.msg){
                        Global.getInstance().showSimpleTip(response.msg);
                    }
                }
                return;
            });
        } else {
            //次数获取验证码既可以绑定手机号也可以修改密码
            let params: object = {
                type: this.m_verifyWay,
                geetest_guard:gee_guard,
                userInfo:uInfo,
                geetest_captcha:gee_captcha,
                buds:ret?.buds || '64',
            }
            params["phone"] = this.m_phone
            params["telephoneCode"] = "+63"
            let isForgetPw = this.m_verifyWay == PN_VERIFY_TYPE.ForgetPassword
            params["isBind"] = isForgetPw ? 1 : 0 //需要已经绑定填1  不需要绑定发0
            // params["type"] = 3 //修改密码
            //未绑定手机 需要请求绑定
            if (params["isBind"] == 0) {
                HttpUtils.getInstance().post(3, 3, this, "/common/api/is/bind/send", params, (response) => {
                    Global.getInstance().showSimpleTip("Verification code sent successfully")
                }, (res) => {
                    if (res && res.code) {
                        if (res && res["code"] == 600) {
                            Global.getInstance().showSimpleTip(Global.getInstance().getLabel("tipword23"));
                        } else {
                            Global.getInstance().showSimpleTip(Global.getInstance().getLabel("php_code_" + res["code"]));
                        }
                    } else {
                        Global.getInstance().showSimpleTip(res.msg);
                    }
                })
            } else {
                this.smsType = GlobalEnum.SMS_TYPE.UPDATE_LOGIN_PASSWORD;
                HttpUtils.getInstance().post(3, 3, this, "/common/api/sms/send/short/msg", {
                    phone: this.m_phone,
                    telephoneCode: "+63",
                    type: GlobalEnum.SMS_TYPE.UPDATE_LOGIN_PASSWORD,
                    geetest_guard:gee_guard,
                    userInfo:uInfo,
                    geetest_captcha:gee_captcha,
                    buds:ret?.buds || '64',
                }, () => {
                    Global.getInstance().showSimpleTip("Verification code sent successfully")
                }, (response) => {
                    if (response && response.code) {
                        if (response && response["code"] == 600) {
                            Global.getInstance().showSimpleTip(Global.getInstance().getLabel("tipword23"));
                        } else {
                            Global.getInstance().showSimpleTip(response.msg);
                        }
                    } else {
                        Global.getInstance().showSimpleTip(response.msg);
                    }
                    return;
                });
            }
        }

        this.m_get_btn.active = false;
        this.m_time.node.parent.active = true;
        let m_t = 60;
        this.m_time.string = m_t + "s";
        const endTime = Date.now() + m_t * 1000;
        //保存结束时间到本地存储
        Global.getInstance().setStoreageData(Global.GLOBAL_STORAGE_KEY.COUNTDOWN_ENDTIME, endTime);
        this.schedule(this.timer, 1);
        this.lbError2.string = ""
    }

    //短信接口不用再发了,防止短信接口暴漏
    getCode() {
        
    }

    reSetCode() {
        this.unschedule(this.timer);
        this.m_get_btn.active = true;
        this.m_time.node.parent.active = false;
    }

    close() {
        // this.hide()
        cc.tween(this.uiBox).to(0.1,{position:new cc.Vec3(0,-1200)}).call(()=>{
            this.node.destroy();
        }).start();
    }

    onEdittingDidBegin(editbox: cc.EditBox) {
        Global.getInstance().editParentMove(editbox, this.uiBox,800);
        editbox.placeholderLabel.node.name = editbox.placeholder
        editbox.placeholder = ""
    }

    onEdittingDidEnd(editbox: cc.EditBox) {
        if (Global.getInstance().needScreenUp()) {
            const widght = this.uiBox.getComponent(cc.Widget)
            widght.bottom = -96;
            widght.updateAlignment();
        }
        editbox.placeholder = editbox.placeholderLabel.node.name
        editbox.string = this.m_phone;
    }

    onEdittingCodeDidBegin(editbox: cc.EditBox) {
        Global.getInstance().editParentMove(editbox, this.uiBox,800);
        this.lbError2.string = ""
        this.ndRed2.active = false
        editbox.placeholderLabel.node.name = editbox.placeholder
        editbox.placeholder = ""
    }

    onEdittingCodeDidEnd(editbox: cc.EditBox) {
        if (Global.getInstance().needScreenUp()) {
            const widght = this.uiBox.getComponent(cc.Widget)
            widght.bottom = -96;
            widght.updateAlignment();
        }
        editbox.placeholder = editbox.placeholderLabel.node.name

    }
    verifyPhone(){
        let phone_edit = this.iphoneNode.getChildByName('editbox1').getComponent(cc.EditBox)
        let errorlabel = this.iphoneNode.getChildByName('errorlabel').getComponent(cc.Label)
        let red_line = this.iphoneNode.getChildByName("editbox1").getChildByName('redline')
        let pstr = utils.stringTure(phone_edit.string)
        if(pstr == ''){
            errorlabel.node.active = true;
            errorlabel.string = "Phone number can not be empty"
            red_line.active = true
            return;
        }
        if (!utils.isPhilippinePhoneNumber(pstr)) {
            // Global.getInstance().showSimpleTip("Wrong phone number");
            errorlabel.node.active = true;
            errorlabel.string = "Wrong phone number"
            red_line.active = true
            return
        }

        this.m_phone = pstr;
        red_line.active = false;
        errorlabel.string = "";
        this.iphoneNode.active = false;
        this.yzmNode.active = true;
        this.btn_label.string = 'Done'
        let phone_l = this.yzmNode.getChildByName('phone').getComponent(cc.Label)
        phone_l.string = this.formatPhoneNumber(this.m_phone);
    }
    confirmClick(event, data) {
        if (this.iphoneNode.active) {
            this.verifyPhone();
            return;
        }
        let cstr = utils.stringTure(this.code_edit.string)
        if(cstr.length != 6){
            this.lbError2.node.active = true;
            this.lbError2.string = "Code Error, Please Try Again";
            this.ndRed2.active = true
            return Global.getInstance().showSimpleTip("Code Error,Please Try Again");
        }
        this.lbError2.string = ""
        this.ndRed2.active = false
        this.m_code = cstr;
        let userData = Global.getInstance().userdata;
        if (this.m_verifyWay == PN_VERIFY_TYPE.ForgetPassword) {
            //已经绑定, 修改密码 暂时没有这个功能2024-7-23 已增加此功能2024-07-24
            this.checkCode()
        } else if (this.m_verifyWay == PN_VERIFY_TYPE.SetPhoneNumber) {
            
            //加个验证
            GeetestMgr.instance.geetest_device(GEETEST_TYPE.bind_pt_phone,(succ)=>{
                if(succ){
                    let ret = {}
                    if(Object.getPrototypeOf(succ) !== Boolean.prototype){
                        ret = succ
                    }
                    this.bindPhone(ret);
                }
            })
        } else if (this.m_verifyWay == PN_VERIFY_TYPE.ChangePhoneNumber) {
            //加个验证
            GeetestMgr.instance.geetest_device(GEETEST_TYPE.change_pt_phone,(succ)=>{
                if(succ){
                    let ret = {}
                    if(Object.getPrototypeOf(succ) !== Boolean.prototype){
                        ret = succ
                    }
                    this.changePhone(ret);
                }
            })
        } else if (this.m_verifyWay == PN_VERIFY_TYPE.AddWithdrawAccount || this.m_verifyWay == PN_VERIFY_TYPE.ChangeWithdrawAccount) {
            //添加提现账号 进行OTP验证
            this.checkCode();
        }
    }

    checkCode() {
        if (!this.m_code) return Global.getInstance().showSimpleTip("Code Error,Please Try Again");
        HttpUtils.getInstance().post(3, 3, this, "/common/api/sms/verify/short/msg/code", {
            phone: this.m_phone,
            telephoneCode: "+63",
            code: this.m_code,
            type: this.smsType
        }, (response) => {
            this.hide()
            this.verrifySucEvent()
        }, () => {
            Global.getInstance().showSimpleTip("Verification Error,Please Try Again");
        });
    }

    verrifySucEvent() {
        if (this.m_verifyWay == PN_VERIFY_TYPE.ForgetPassword) {
            showLoginPassword(this.m_phone,this.m_code)
        } else if (this.m_verifyWay == PN_VERIFY_TYPE.AddWithdrawAccount || this.m_verifyWay == PN_VERIFY_TYPE.ChangeWithdrawAccount) {
            if (this.callBackFunc) {
                this.callBackFunc();
            } else {
                uiManager.instance.showDialog(UI_PATH_DIC.WithdrawAccount, [{type:"select_payment"}], null, DEEP_INDEXZ.ADD_WITHDRAW)
            }
        }else {
            //加个验证
            GeetestMgr.instance.geetest_device(GEETEST_TYPE.bind_pt_phone,(succ)=>{
                if(succ){
                    let ret = {}
                    if(Object.getPrototypeOf(succ) !== Boolean.prototype){
                        ret = succ
                    }
                    this.bindPhone(ret);
                }
            })
        }
    }
    changePhone(ret?) {
        let gee_guard = ret?.geetest_guard || '';
        let uInfo = ret?.userInfo || '';
        let gee_captcha = ret?.geetest_captcha || '';
        //todo
        HttpUtils.getInstance().post(3, 0, this, "/common/api/update/bind/phone", {
            token: Global.getInstance().token,
            old_phone: this.m_oldPhone,
            phone: this.m_phone,
            verifyCode: this.m_code,
            telephoneCode: "+63",
            geetest_guard:gee_guard,
            userInfo:uInfo,
            geetest_captcha:gee_captcha,
            buds:ret?.buds || '64',
        }, (response) => {
            Global.getInstance().userdata = response["data"]["user_info"];
            cc.director.emit("phoneBindSuc");
            Global.getInstance().showSimpleTip("Phone number set successfully.");
            this.hide()
        }, (response) => {
            let errMsg = ""
            if (response["msg"] && response["msg"] != "") {
                errMsg = response["msg"]
                // Global.getInstance().showSimpleTip(errMsg);
                this.lbError2.node.active = true;
                this.lbError2.string = errMsg
                this.ndRed2.active = true
            } else if (response["code"]) {
                errMsg = response.msg
                // Global.getInstance().showSimpleTip(errMsg);
                this.lbError2.node.active = true;
                this.lbError2.string = errMsg
                this.ndRed2.active = true
            }
        });
    }
    bindPhone(ret?) {
        let gee_guard = ret?.geetest_guard || '';
        let uInfo = ret?.userInfo || '';
        let gee_captcha = ret?.geetest_captcha || '';
        HttpUtils.getInstance().post(3, 0, this, "/common/api/player/add-bind", {
            token: Global.getInstance().token,
            appPackageName: Global.getInstance().getAppBundleId(),
            deviceId: Global.getInstance().getDeviceId(),
            appVersion: Global.getInstance().getAppVersion(),
            phone: this.m_phone,
            verifyCode: this.m_code,
            telephoneCode: "+63",
            geetest_guard:gee_guard,
            userInfo:uInfo,
            geetest_captcha:gee_captcha,
            buds:ret?.buds || '64',
        }, (response) => {
            Global.getInstance().userdata = response["data"]["user_info"];
            cc.director.emit("bind_callback");
            Global.getInstance().showSimpleTip("Phone number set successfully.");
        }, (response) => {
            let errMsg = ""
            if (response["msg"] && response["msg"] != "") {
                errMsg = response["msg"]
                // Global.getInstance().showSimpleTip(errMsg);
                this.lbError2.node.active = true;
                this.lbError2.string = errMsg
                this.ndRed2.active = true
            } else if (response["code"]) {
                errMsg = response.msg
                // Global.getInstance().showSimpleTip(errMsg);
                this.lbError2.node.active = true;
                this.lbError2.string = errMsg
                this.ndRed2.active = true
            }
        });
    }

    timer() {
        //开始倒计时
        this.updateCountdown();
    }

    updateCountdown(){
        const eTime = Global.getInstance().getStoreageData(Global.GLOBAL_STORAGE_KEY.COUNTDOWN_ENDTIME, 0);
        const currentTime = Date.now();
        const remainingTime = Math.max(0, eTime - currentTime);
        // 每秒更新一次
        this.m_time.string = Math.ceil(remainingTime / 1000) + "s";
        //修复倒计时bug <= 0
        if (remainingTime <= 0) {
            this.reSetCode();
        }
    }

    checkCountdown() {
        const endTime = Global.getInstance().getStoreageData(Global.GLOBAL_STORAGE_KEY.COUNTDOWN_ENDTIME, 0);
        const currentTime = Date.now();
        const remainingTime = Math.max(0, endTime - currentTime);
        if (remainingTime > 0) {
            // 倒计时未结束
            this.m_get_btn.active = false;
            this.m_time.node.parent.active = true;
            this.m_time.string = Math.ceil(remainingTime / 1000) + "s";
            this.schedule(()=>{this.updateCountdown()},1);
        } else {
            // 倒计时结束
            this.reSetCode();
        }
    }

    protected onDestroy(): void {
        // cc.director.emit("DestroyQueuePopup")
    }
}
