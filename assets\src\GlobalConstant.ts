import BeforeWithdrawal from "./hall/BeforeWithdrawal";
import MayaDownloadPop from "./hall/MayaDownloadPop";
import MultiTogProviders from "./hall/MultiTogProviders";
import TransationsBatch from "./hall/TransationsBatch";
import KycTips from "./KYC/KycTips";
import ProfileNode from "./ProfileNode";
import SelectWithdrawAcc from "./SelectWithdrawAcc";

export const BTN_SAFE_TIME = 0.1
export const UI_HIDE_TIME = 0.1
export const UI_SHOW_TIME = 0.1
export const UI_POP_OUT_TIME = 0.2
export const GOLD_RATIO = 100;

// start update_type 字段字典配置
export const BU_GAME = 1;                  //游戏结算
export const SOURCE_INVITE_FRIENDS = 36;       //邀请人送金币

export const SOURCE_ACTIVITY_FIRST_CHARGE = 109; //首充
export const SOURCE_ACTIVITY_WEEKLY_SIGNIN = 110; //周签到活动
export const SOURCE_ACTIVITY_DAILY_REBATE = 111;  //每日投注反水活动
export const SOURCE_ACTIVITY_PAYDAY = 112;  //发薪日活动
export const SOURCE_ACTIVITY_SPORT = 113;  //体育包赔活动
export const SOURCE_ACTIVITY_MAYA_VIP = 114;  //mayavip活动
export const SOURCE_ACTIVITY_REDEEMPROMISE = 117; //提现承诺
/*
 * 账变类型 ；后面如果有新的需求，就在后面继续增加就可以，比如 2,3,4 。。。
 */
export const TYPE_ACTIVITY_BALANCE_CHANGE = 1;
export const TYPE_BALANCE_CHANGE = 4;
export const TYPE_BALANCE_RECHARGE = 5;
/*
 * 有公告消息 ；
 */
export const TYPE_NOTICE = 2;
/*
 * 有新的邮件来了；
 */
export const TYPE_MAIL = 3;

export enum E_BALANCE_UPDATE {
    BU_GAME = 1,                  //游戏结算
    INIT_BET = 2,                 // 入场费（底注）
    SOURCE_RECHARGE = 11,         // 充值
    SOURCE_RECHARGE_CASHBACK = 12,// 充值赠送
    SOURCE_WITHDRAW = 21,         // 提现
    SOURCE_WITHDRAW_FAIL = 22,    // 提现失败
    SOURCE_INVITE_FRIENDS = 36,       //邀请人送金币
    SOURCE_INVITE_FIVE_FRIENDS = 37,  //邀请满5人送金币
    SOURCE_INVITEE_FRIENDS = 38,      //被邀请人送金币
    SOURCE_MAIL = 51,             // 邮件领取
    SOURCE_ADMIN = 41,            // 管理员添加(backend使用)
    SOURCE_BONUS = 62,
    SOURCE_USER_REGISTER = 65,    // 注册送金币
    SOURCE_SIGN_IN_CASHBACK = 91, // 每日签到送金币
    SOURCE_BIND_PHONE_CASHBACK = 94, // 绑定手机号
    SOURCE_ACTIVITY_FIRST_CHARGE = 109, //首充
    SOURCE_ACTIVITY_WEEKLY_SIGNIN = 110, //周签到活动
    SOURCE_ACTIVITY_DAILY_REBATE = 111,  //每日投注反水活动
    SOURCE_ACTIVITY_PAYDAY = 112,  //发薪日活动
    SOURCE_ACTIVITY_SPORT = 113,  //体育包赔活动
    SOURCE_ACTIVITY_MAYA_VIP = 114,  //mayavip活动
    SOURCE_CURRENT_BALANCE = 9001,       //玩家当前可用余额查询

    SOURCE_ADMIN_GAME = 201,      // 管理员添加-游戏记录(backend使用)
    SOURCE_ADMIN_RECHARGE = 211,  // 管理员添加-充值记录(backend使用)
    SOURCE_ADMIN_WITHDRAW = 221,  // 管理员添加-提现记录(backend使用)
    SOURCE_ADMIN_BIND_PHONE_CASHBACK = 294,   // 管理员添加-绑定手机号(backend使用)
    SOURCE_ADMIN_USER_REGISTER = 265,         // 管理员添加-注册送金币(backend使用)
    SOURCE_ADMIN_ACTIVITY_WEEKLY_SIGNIN = 310,// 管理员添加-周签到活动(backend使用)
    SOURCE_ADMIN_ACTIVITY_DAILY_REBATE = 311, // 管理员添加-每日投注反水活动(backend使用)
    SOURCE_ADMIN_ACTIVITY_PAYDAY = 312,       // 管理员添加-发薪日活动(backend使用)
    SOURCE_ADMIN_ACTIVITY_SPORT = 313,        // 管理员添加-体育包赔活动(backend使用)
    SOURCE_ADMIN_ACTIVITY_BACKEDN_CHANGE = 314, // 管理员调整-修改反水活动产生的用户金额(backend使用)

    SOURCE_ADMIN_INVITE_FRIENDS = 236,        // 管理员添加-邀请人送金币(backend使用)
    SOURCE_ADMIN_INVITE_FIVE_FRIENDS = 237,   // 管理员添加-邀请满5人送金币(backend使用)
    SOURCE_ADMIN_INVITEE_FRIENDS = 238,       // 管理员添加-被邀请人送金币(backend使用)
    SOURCE_ADMIN_FIRST_DEPOSIT_BONUS = 239,       // 管理员添加-First deposit bonus送金币(backend使用)

}

export const MAINTENANCETIPCODE = 102121
export const MAINTENANCE_GAME = 102041//游戏维护 错误码
export const TOKENEXPIRE_CODE = 102042//登录的时候 token 过期 令牌过期 gcash用到
export const LOCK_ACCOUNT = 102008
export const POPBANNERS = "popBanners_"
export const MAN_MADE_LOGIN = "man_made_login"

export enum UI_INDEX {
    REWARD = 100,
    NEW_USER = 1000
}
export enum E_SCENE_TYPE {
    LOGIN = 1,
    HALL = 2,
    GAME = 3
}
//目前就用到两个
export enum E_PAGE_TYPE {
    HALL,
    OTHER
}
export enum E_GAME_TYPE {
    NO_ROOM_LIST,
    WITH_ROOM_LIST,
    THIRD_GAME_LIST,
    THIRD_GAME,
}

export enum E_THIRD_COMPANY_ID {
    PlayZone,
    JILI,
    PG,
    EVO,
    RTG,
    FC,
    Galaxsys,
    Pinnacle,
    BetConstruct,
    SAGaming,
    PP,
    JDB,
    PM,
    PS,
    YB,
    PlayTech = 20,
}

export const E_THIRD_PROVIDER = {
    JL: "JILI",
    LP: "Larong Pinoy",
    FC: "FaChai",
    PT: "PlayTime",
    PG: "PG Soft",
    PP: "Pragmatic Play",
    YB: "Yellow Bat",
    EG: "EG Soft",
    EVO: "Evolution",
    JDB: "JDB",
    PINNACLE: "Pinnacle",
    PLAYSTAR: "PlayStar",
    CQ9: "CQ9",
}

export enum E_CHANEL_TYPE {
    GOOGLE_PLAY = "GP",
    G_CASH = 'Gcash',
    H5 = 'H5',
    WEB = 'Web',
    MAYA = 'Maya',
    IOS = 'iOS',//增加iOS渠道
}
export enum CHANEL_PARAM {
    "GP" = 1,
    'H5' = 2, // otherApp
    'iOS' = 4,
    'Gcash' = 8,
    'Maya' = 64,
    'Web' = 128,
}
export const METHODS = {
    GCASH: "gcash",
    GCASH_WEB: "gcashwebpay",
    GOOGLE_PAY: "googlepay",
    MAYA_PAY: "mayapay",
    PAYCOOLS: "paycools",
    BANK_CARD: "paycoolsbank",
    MAYA_WEB: "mayawebpay"
}
export enum E_FUND_TYPE {
    Gcash = 3,
    Maya = 4,
    GrabPay = 5,
    BankCard = 6,
    MAYA_MINI = 7,
    MAYA_WEB = 11,
    GCASH_WEB = 12
}

export enum ACTIVITY_TYPE {
    SIGNBIND = 1,
    FIRSTDEPOSIT = 2,
    CASHBACK = 3,
    VIPCASHBACK = 4,
};
//所有层的 深度
export const enum DEEP_INDEXZ {
    CUSTOM = 1,//基础页面
    WalletTask = 96,//钱包
    BUTN_SPIN = 97,//转盘 首页按钮
    BTUN_ENVELOPE = 98,//红包首页button
    POPBANNER = 99,//弹出banner
    RANK = 100,//排行
    TASK = 101,//任务
    KYC = 102,//kyc
    ACTIVITYS = 103,//活动弹窗 比如 返奖等
    SPINWHEEL = 104,//转盘打开
    ENVELOPE = 105,//首存红包 弹窗
    ALL_GAME = 106,//全部游戏
    ALL_GAME_MUTIPROVIDER = 107,//选择厂商
    HEAD_IMG = 108,//头像
    NOTICE = 109,//通知
    ACTIVITY_CONTENT = 200,//活动内容页
    VEDIO_PLAYER = 201,//全屏视频
    DEPOSIT = 880,//充值
    WITHDRAW = 881,//提现
    BEFORE_WITHDRAW = 882,//提现前置条件
    TRANSATIONS = 883,//
    BETORDER = 884,//
    SEL_WITHDRAW = 888,//添加提现账号
    ADD_WITHDRAW = 889,//添加提现账号
    TIPS_21 = 890,//21岁提示
    PRECONDITIONS = 891,//提示没有手机号 或者密码之类的
    CHANGE_NAME = 892,//更改名字
    WITHDRAW_OK = 893,//二次确认提现框
    LOGIN = 894,//登录页面
    VIPTIPS = 895,//vip 提示框
    PASSWORD_LIMIT = 896,//密码多次输入限制弹窗
    PASSWORD = 897,//密码输入框
    KYC_TIPS = 898,//kyc弹窗
    VERIFY_CODE = 899,//验证码弹窗 手机号验证
    COMMON_TIPS = 900,//普通提示
    POP_BLOCK_TIP = 901,
    POP_BLOCK_INSTRUCTION = 902,
    BroadCastDetail = 903,//
    PRIVACY_TIPS = 997,//隐私协议层
    TOAST = 998,//toast
    LOADING = 999,//加载
    MAX = 1000,
};

export enum E_TRANSACTION_TYPE {
    recharge,
    redeem
}

export enum PRODUCT_TYPE {
    ITEM = 1,
    CASH = 2,
    GCASH = 3,
    MAYA = 4,
    GRAB_PAY = 5,
    BANK_CARD = 6,
    MAYA_MINI = 7,
    MAYA_WEB = 9,//Maya充值
    GCASH_WEB = 10,//GCash充值
    WITHDRAW_MAYA_WEB = 11,//Maya提现
    WITHDRAW_GCASH_WEB = 12,//GCash提现
    GCASH_MINI = 13,//gcash mini提现
}
//测试帐号字段type: T-0 测试无 Recharge 和 Withrawal 权限。 T-1 Recharge权限，T-2 Withrawal权限，T-3 Withrawal 和 Recharge
export const E_CAPABILITY_TYPE = {
    NONE: "T-0",
    ONLY_RECHARGE: "T-1",
    ONLY_WITHDRAWAL: "T-2",
    BOTH: "T-3"
}
export const PASS_TYPE = {
    RedeemSuc: "RedeemSuc",
    AddAccount: "AddAccount",
    ResetAccount: "ResetAccount",
    ConfirmPwSuc: "ConfirmPwSuc"
}
export const VERIFY_CODE_TYPE = {
    SetPaymentPwd: "Set Payment Password",
    ChangePaymentPwd: "Change Payment Password",
    SetLoginPwd: "Set Login Password",
    ChangeLoginPwd: "Change Login Password",
    ChangePhoneNum: "Change Phone Number",
    AddWithdrawAccount: "Add Withdraw Account"
}

export const OPEN_BROADCAST_TYPE = {
    Hall: "Hall",//大厅
    AllGame: "AllGame",//AllGameView搜索页
    Promo: "Promo",//活动页
    News: "News",//新闻页
    Deposit: "Deposit",//充值页面
    Withdraw: "Withdraw",//提现页面
}

export const UI_PATH_DIC = {
    Hall: "prefab/hall",
    SetPayPassword: "prefab/setPayPassword",
    Password: "prefab/password",
    Shop: "prefab/newShopUI",
    Setting: "prefab/setting",
    GameRecords: "prefab/gameRecords",
    FundAccount: "prefab/fundAccount",
    ShopItemDetail: "prefab/shopItemDetail",
    Mail: "prefab/hall/Notice",
    AccountList: "prefab/accountList",
    VerifyCode: "prefab/verifyCode",
    UserProfileSetting: "prefab/hall/UserProfileSetting",
    ActivityRebate: "prefab/simpleActivity/ActivityRebate",
    ActivityMayaVip: "prefab/simpleActivity/ActivityMayaVip",
    ActivityWeekly: "prefab/simpleActivity/ActivityWeekly",
    ActivityPayday: "prefab/simpleActivity/ActivityPayday",
    ActivityFirstRecharge: "prefab/simpleActivity/FirstRecharge",
    ActivitySports: "prefab/simpleActivity/ActivitySports",
    CustomerService: "prefab/hall/CustomerService",
    PhoneBind: "prefab/hall/PhonePassword",
    SetLoginPassword: "prefab/setLoginPassword",
    ChangeName: "prefab/GCashAccounts/ChangeName",
    SecurityCenter: "prefab/securityCenter",
    PrivacyUI: "prefab/PrivacyUI",
    OpenCameraUI: "prefab/OpenCameraUI",
    ChangeOrientationTip: "prefab/ChangeOrientationTip",
    Commontip: "prefab/commontip",
    Commontip3: "prefab/commontip3",
    SetPhoneNumber: "prefab/setPhoneNumber",
    AccountConfirmation: "prefab/AccountConfirmation",
    ChooseBankCard: "prefab/chooseBankCard",
    ChooseWallet: "prefab/chooseWallet",
    ExchangeSucceed: "prefab/exchangeSucceed",
    SecurityRequirement1: "prefab/hall/sr1/securityRequirements",
    SecurityRequirement2: "prefab/hall/sr2/securityRequirements",
    SecurityRequirement3: "prefab/hall/sr3/securityRequirements",
    ThirdPay: "prefab/thirdPay",
    TipsRedeemSuc: "prefab/tipsRedeemSuc",
    FullScreenTip: "prefab/FullScreenTip",
    phonePasswordLogin: "prefab/phonePasswordLogin",
    MoreGameList: "prefab/MoreGame/MoreGameList",
    Maintenancetip: "prefab/Maintenancetip",
    LockTip: "prefab/lockTip",
    PopupBanners: "prefab/PopupBanners",
    PleaseRecharge: "prefab/simpleActivity/PleaseRecharge",
    WalletLimitPop: "prefab/WalletLimitPop",
    MayavipApplyTip: "prefab/MayavipApplyTip",
    RedeemPromise: "prefab/simpleActivity/RedeemPromise",
    FlopCard: "prefab/hilogame/filpcard",
    HelpView: "prefab/hilogame/gamerules",
    HistoryView: "prefab/hilogame/history",
    ScoreCard: "prefab/hilogame/scorecard",
    GameResult: "prefab/hilogame/gameresult",
    CashOutWin: "prefab/hilogame/cashoutwin",
    CardRecord: "prefab/hilogame/card_record",
    Notice: "prefab/hall/Notice",
    Deposit: "prefab/Deposit",
    WithDraw: "prefab/Withdraw",
    BetOrder: "prefab/BetOrder",
    BetDetail: "prefab/BetDetails",
    Transations: "prefab/Transations",
    TransationsDetail: "prefab/TransationsDetial",
    TransationsBatch: "prefab/TransationsBatch",
    MultiTogProviders: "prefab/common/MultiTogProviders",
    MultiToggles: "prefab/common/MultiToggles",
    SingleToggle: "prefab/common/singletoggle",
    AllGameView: "prefab/allgame",
    HallGameItem: "prefab/HallGameItem",
    HallGameBigItem: "prefab/HallGameBigItem",
    NewView: "prefab/hall/news",
    NewViewDetails: "prefab/hall/newsDetails",
    Promo: "prefab/hall/promo",
    PromoDetial: "prefab/hall/promodetial",
    PayPasswordLimit: "prefab/PayPasswordLimit",
    Preconditions: "prefab/Preconditions",
    SpecialCasino: "prefab/SpecialCasino",
    PopupBanner: "prefab/PopupBanners",
    KYCVerification: "prefab/KYC/KYCVerification",//KYC 页面 lemoon
    KycTips: "prefab/KYC/KycTips",//验证的弹窗
    PrivacyTip: "prefab/PrivacyTip",
    WithdrawAccount: "prefab/WithdrawAccount",
    SelectWithdrawAcc: "prefab/SelectWithdrawAcc",
    LogoutTips: "prefab/common/logoutTips",//目前只有退出提示的时候用到
    ProfileNode: "prefab/ProfileNode",//用户选择头像
    BroadCastDetail: "prefab/BroadCast/BroadCastDetail",//系统广播
    TipOf21: "prefab/hall/Tips21Old",
    Vip: "prefab/VIP/Vip",//VIP页面
    VipDetail: "prefab/VIP/VipDetail",//VIP详情页
    VipTip: "prefab/VIP/VipTip",//晋级VIP弹窗
    LoadinViewPre: "prefab/preview/loadingpre",
    withDeposit: "prefab/withdrawDepositDialog",
    ActivityBonusTip: "prefab/hall/ActivityBonus",//返奖活动弹窗
    Task: "prefab/hall/Task",//新手任务
    SpinView: "prefab/hall/spinwheel",
    SpinDetial: "prefab/hall/spinDetials",
    Rank: "prefab/hall/LeaderBoard",
    RankPop: "prefab/hall/LeaderBoardPop",
    RankPopJili: "prefab/hall/LeaderBoardPopJili",
    RankRule: "prefab/hall/LeaderBoardRule",
    RankJili: "prefab/hall/LeaderBoardJILI",
    RankRuleJili: "prefab/hall/LeaderBoardRuleJili",
    Valentine: "prefab/hall/Valentine",
    WeeklyPayday: "prefab/hall/WeeklyPayday",
    WeeklyRule: "prefab/hall/WeeklyRule",
    PPDailyWins: "prefab/hall/PPDailyWins",//PP Daily Wins
    LateNightCashback: "prefab/hall/LateNightCashback",//Late Night Cashback
    PromoCommonDetail: "prefab/Promo/PromoCommonDetail",//通用活动详情页
    CustomEventDetail: 'prefab/Promo/CustomEventDetail',//自定义活动
    FCFreeSpinDetail: "prefab/Promo/FCFreeSpinDetail",//FC Free Spin
    FCFreeSpinDetail2: "prefab/Promo/FCFreeSpinDetail2",//FC Free Spin
    YellowBatDetail: "prefab/Promo/YellowBatDetail",//Yellow Bat
    BeforeWithdrawal: "prefab/BeforeWithdrawal",//free 15/1st 1倍流水功能
    JILIFreeSpinDetail: "prefab/Promo/JILIFreeSpinDetail",//JILI Free Spin
    Envelope: "prefab/Envelope",//首充红包弹窗
    VideoPlayer: "prefab/VideoPlayer/VideoPlayer",//直播 横屏 播放器
    ChampionshipBoard: "prefab/championship/ChampionshipBoard",
    ChampionshipDetail: "prefab/championship/ChampionshipDetail",
    ChampionshipBoardRule: "prefab/championship/ChampionshipBoardRule",
    ChampionshipBoardTip: "prefab/championship/ChampionshipTip",
    WalletTask: "prefab/Wallet/WalletTask",
    WalletTaskRule: "prefab/Wallet/WalletTaskRule",
    PopBlockTip: "prefab/hall/PopBlockTip",
    PopBlockInstruction: "prefab/hall/PopBlockInstruction",
    MayaDownloadPop: "prefab/hall/MayaDownloadPop",
}

export const EVENT = {
    SHOW_COUNTRY_LIMIT: "SHOW_COUNTRY_LIMIT",
    CHAMPIONSHIP_DETAIL: "CHAMPIONSHIP_DETAIL",
    CHAMPIONSHIP_RANK: "CHAMPIONSHIP_RANK",
    PLAYER_GAME_DISTRIBUTION: "PLAYER_GAME_DISTRIBUTION",
    PLAYER_BETTING_SUMMARY: "PLAYER_BETTING_SUMMARY",
    UPDATE_WALLETTASKINFO: "UPDATE_WALLETTASKINFO",
    UPDATE_DOWNLOAD_TIP: "UPDATE_DOWNLOAD_TIP",
}

export interface CLIENTLOG {
    key: string,  //用来定义日志类型的
    userId?: string,
    deviceId: string,
    deviceName: string,
    uploadTime: string,
    formatUpTime?: string,
    duration?: number,
    extraData?: any, //用于扩展
}

export const DOWNLOAD_CHANNEL = {
    GOOGLE: "Google",
    XIAOMI: "Xiaomi",
    OPPO: "Oppo",
    VIVO: "Vivo",
    HUAWEI: "Huawei",
    SANXING: "Samsung",
    CHUANYIN: "Chuanyin",
    IOS: "iOS",
    GCASH: "GCash",
    MAYA: "Maya",
    gcashdownloadapp: "gcashdownloadapp",
    mayadownloadapp: "mayadownloadapp",
    appdownload: "appdownload",
    WEB_H5: "web"
}

export const GOLDANI_TYPE = {
    TOTARGET: "toTarget",
    FULLSCREEN: "fullscreen"
}

export const AVATAR_LOCAL = {
    // male
    "images/786d8c65d99336971d7c12d42b12203b.png": "male_1",
    "images/d5360c4f29a174375b83beb535468cf5.png": "male_2",
    "images/48386cd8ee0f61c8fc589b3e35d5215a.png": "male_3",
    "images/1f89e2016f51a5e6279e52cf7965f1a5.png": "male_4",
    "images/b3569333bd912ce31db075606aeeb669.png": "male_5",
    "images/8992235a05161635b98236e09d16c9ec.png": "male_6",
    "images/40981820c0dc3d7c3911847b64f033b6.png": "male_7",
    "images/6f50210a3fb548441526c5e150893f3f.png": "male_8",
    "images/e7c85e667d027d510739ce9156132f08.png": "male_9",
    "images/9e92e20c41060a96ebe203c3971cc8d3.png": "male_10",
    "images/ee33947017b6c27d40b8ae492dca356a.png": "male_11",
    "images/6942e2bb3d380c32b1ea3d7d595a967a.png": "male_12",
    "images/1621eae88b001fd6b944ba0ba8abdad6.png": "male_13",
    "images/63fe677eff3e5e45b8a59b9f8e845fc9.png": "male_14",
    "images/9b66e8efdf4cf483950f7bf6f33e2d8e.png": "male_15",

    // female
    "images/be9171004a4c729473bac2e333768c0c.png": "female_1",
    "images/48011316ef3f9ba10756849ae324144e.png": "female_2",
    "images/07159c495a65de0117fb8f1cedd1fb36.png": "female_3",
    "images/cdb8c7d0879ab87ffbacc158f92ebab4.png": "female_4",
    "images/72c735eef6095b5c1ba15943de00c2b1.png": "female_5",
    "images/38441c19a785edac53ace52c45f662e7.png": "female_6",
    "images/bae2cac7d7282e94472ae48a6f180a08.png": "female_7",
    "images/32578310197b940cc989f301c141b2a5.png": "female_8",
    "images/06cf822104d17fced853e7aa013bc15e.png": "female_9",
    "images/ee6617036d0a595e86a049795ab432d5.png": "female_10",
    "images/81e20f15bdca9e449865c222642d5ba0.png": "female_11",
    "images/763bd85edbe83047ee0d6d6565a9c1fc.png": "female_12",
    "images/0278cd232b5a5e328445e48f0b6e6d72.png": "female_13",
    "images/8d3eae0b7f3142383579d8d7bcf7f78c.png": "female_14",
    "images/aefc329c3724bdd566d2fbc2971a75cb.png": "female_15",
}

export const CHAMPIONSHIP_STATUS = {
    WAITING: 0,
    ONGOING: 1,
    ENDED: 2
}

export const EMIT_PARAMS = {
    APSPORTS_DATA_CHANGE: 'apsport_data_change',//拳皇直播数据变动 通知改变
    APSPORTS_GOPLAY: 'apsport_goplay',//拳皇直播 进入第三方 通知
    GOOGLE_SIGN_IN_SUCCESS: 'google_signin_success',//google 登录成功通知
    GOOGLE_SIGN_IN_ERROR: 'google_signin_error',//google 登录失败通知
    FACEBOOK_SIGN_IN_SUCCESS: 'Facebook_signin_success',//Facebook 登录成功通知
    FACEBOOK_SIGN_IN_ERROR: 'Facebook_signin_error',//Facebook 登录失败通知
}

export const WALLETTASK_STATUS = {
    LOCK: 1,
    ONGOING: 2,
    FINISHED: 3,
    EXPIRED: 4,
    DELETE: 5
}

export const JUMP_TYPE = {
    GAME: 1,
    DEPOSIT: 2,
    PROMO: 3,
    LINK: 4,
}

export const ACCOUNT_TYPE = {
    GCASH: 12,
    MAYA: 11
}