cc.Class({
    extends: cc.Component,

    properties: {
        curData: null
    },

    initComboBox(cb, data) {
        this.cb = cb;
        this.curData = data;
        this.node.children[0].getComponent(cc.Label).string = data.name;
    },
    itemBtn(event) {
        // 子项点击后改变下拉按钮上的文本
        this.cb.comboLabel.string = event.target.children[0].getComponent(cc.Label).string;
        // 选择后改变小三角和下拉框显示
        this.cb.comboboxClicked(this.curData.pay_code);
    },

    // update (dt) {},
});
