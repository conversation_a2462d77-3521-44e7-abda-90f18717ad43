import { ALL_APP_SOURCE_CONFIG } from "../Config";
import GameControl from "../GameControl";
import { AVATAR_LOCAL, DEEP_INDEXZ, E_CHANEL_TYPE, JUMP_TYPE, UI_PATH_DIC } from "../GlobalConstant";
import Global from "../GlobalScript";
import { uiManager } from "../mgr/UIManager";
import MoreGameManager from "../MoreGameManager";
import Resource = require("../Resource");
import PopBlockTip from "../hall/PopBlockTip";
import { LOGIN_WAY } from "../login/phonePasswordLogin";

const allBytes = {
    'A': 18,
    'B': 18,
    'C': 18,
    'D': 18,
    'E': 18,
    'F': 18,
    'G': 18,
    'H': 18,
    'I': 7,
    'J': 16,
    'K': 18,
    'L': 18,
    'M': 24,
    'N': 18,
    'O': 20,
    'P': 18,
    'Q': 20,
    'R': 20,
    'S': 18,
    'T': 18,
    'U': 18,
    'V': 18,
    'W': 24,
    'X': 18,
    'Y': 18,
    'Z': 18,
    'a': 16,
    'b': 16,
    'c': 16,
    'd': 16,
    'e': 16,
    'f': 12,
    'g': 16,
    'h': 16,
    'i': 6,
    'j': 8,
    'k': 16,
    'l': 6,
    'm': 22,
    'n': 16,
    'o': 16,
    'p': 16,
    'q': 16,
    'r': 12,
    's': 16,
    't': 16,
    'u': 16,
    'v': 16,
    'w': 22,
    'x': 16,
    'y': 16,
    'z': 16,
    '1': 10,
    '2': 16,
    '3': 16,
    '4': 16,
    '5': 16,
    '6': 16,
    '7': 16,
    '8': 16,
    '9': 16,
    '0': 16,
    '-': 12,
    '·': 6,
    '.': 12,
    '•': 16
}

export default class utils {
    static getFileSize(size) {
        if (!size) return "";

        var num = 1024.0; //byte
        if (size < num) return size + "B";
        if (size < Math.pow(num, 2)) return (size / num).toFixed(2) + "K"; //kb
        if (size < Math.pow(num, 3))
            return (size / Math.pow(num, 2)).toFixed(2) + "M"; //M
        if (size < Math.pow(num, 4))
            return (size / Math.pow(num, 3)).toFixed(2) + "G"; //G
        return (size / Math.pow(num, 4)).toFixed(2) + "T"; //T
    }
    static numFormat(num, digits = 2) {
        if (typeof num == "number") {
            num = num.toFixed(digits);
        } else if (typeof num == "string") {
            num = num + ".00";
        }

        if (num > 0 && num < 1000000) {
            var result = [],
                counter = 0;
            num = num.toString().split("");
            for (let i = num.length - 1; i >= 0; i--) {
                counter++;
                result.unshift(num[i]);
                if (
                    ((digits == 2 && counter > 3) || digits == 0) &&
                    !((counter - 3) % 3) &&
                    i != 0
                ) {
                    result.unshift(",");
                }
            }
            return result.join("");
        }
        var si = [
            { value: 1, symbol: "" },
            //{ value: 1E3, symbol: "K" },
            { value: 1e6, symbol: "M" },
            { value: 1e9, symbol: "B" },
        ];
        var rx = /\.0+$|(\.[0-9]*[1-9])0+$/;
        var i;
        for (i = si.length - 1; i > 0; i--) {
            if (num >= si[i].value) {
                break;
            }
        }
        return (num / si[i].value).toFixed(digits).replace(rx, "$1") + si[i].symbol;
    }
    static numFormat2(num, digits = 2) {
        // num = (num.toFixed(digits))
        var si = [
            { value: 1, symbol: "" },
            { value: 1e3, symbol: "K" },
            { value: 1e6, symbol: "M" },
            { value: 1e9, symbol: "B" },
        ];
        var rx = /\.0+$|(\.[0-9]*[1-9])0+$/;
        var i;
        for (i = si.length - 1; i > 0; i--) {
            if (num >= si[i].value) {
                break;
            }
        }
        return (num / si[i].value).toFixed(digits).replace(rx, "$1") + si[i].symbol;
    }

    /**
     * 使用K,M分隔数字
     * @param num
     * @param point 小数保留位，兼容原来的数字
     * @param force 默认false 10000直接返回(true 都分隔成K M B 数字)
     * @param containSymbol 是否带,分隔(默认false 不带)
     */
    static transNumberToKM(num, point = 2, force = false, containSymbol = false) {
        num = !num ? 0 : num;
        let numStr = num.toString();
        let integer = 0;
        let decimal = 0;
        let unit = '';
        if (!force && numStr.length < 5) { // 10000以内直接返回
            if (containSymbol) {
                return utils.formatLocalNum(numStr);
            } else {
                return numStr;
            }
        }
        // else if (numStr.length > 15) {
        //     integer = parseInt((num * 0.000000000000001).toString());
        //     unit = 'Q';
        // } else if (numStr.length > 12) {
        //     integer = num * 0.000000000001;
        //     unit = 'T';
        // } else if (numStr.length > 9) {
        //     integer = parseInt((num * 0.000000001).toString());
        //     unit = 'B';
        // }
        else if (numStr.length >= 8) {//当数值>=10000000 转换为M为单位
            integer = parseInt((num * 0.000001).toString());
            unit = 'M';
        } else if (numStr.length >= 5) {//当数值>=10000 转换为K为单位
            integer = parseInt((num * 0.001).toString());
            unit = 'K';
        } else {
            if (containSymbol) {
                return utils.formatLocalNum(numStr);
            } else {
                return numStr;
            }
        }
        if (point) {
            let aLen = integer.toString().length;
            decimal = numStr.substring(aLen, aLen + point);
        }
        if (0 == decimal) {
            if (containSymbol) {
                return utils.formatLocalNum(integer) + unit;
            } else {
                return integer + unit;
            }
        } else {
            return parseFloat(integer + '.' + decimal) + unit;
        }
    }

    /**
     * 数字使用逗号分隔
     * @param str 
     * @param isIncrease 是否带+号
     * @returns 
     */
    static formatLocalNum(str, isIncrease = false) {
        str = !str ? 0 : Number(str);
        let num = str;
        str = Math.abs(str);
        str = str.toFixed(0).toString();
        let newStr = "";
        let count = 0;
        for (let i = str.length - 1; i >= 0; i--) {
            if (count % 3 === 0 && count !== 0) newStr = str.charAt(i) + "," + newStr;
            else newStr = str.charAt(i) + newStr;
            count++;
        }
        isIncrease && num >= 0 && (newStr = "+" + newStr);
        num < 0 && (newStr = "-" + newStr);
        return newStr;
    }
    static formatNumberWithCommas(number: number, digits: number = 2, bZeroFormat: boolean = true): string {
        let parts = number.toString().split('.');
        let integerPart = parts[0];
        let decimalPart = "";
        if (digits > 0) {
            if (parts[1]) {
                if (parts[1].length >= digits) {
                    // 截取字符串的前两位
                    decimalPart = "." + parts[1].substring(0, digits);
                } else {
                    decimalPart = "." + parts[1].padEnd(digits, '0');
                }
            } else {
                decimalPart = bZeroFormat ? "." + "".padEnd(digits, '0') : '';
            }
        } else {
            if (parts.length > 1) {
                decimalPart = "." + parts[1];
            }
        }
        let formatted = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ",");
        return formatted + decimalPart;
    }

    static formatNumberDigits(number: number, digits = 2): string {
        let parts = number.toString().split('.');
        let integerPart = parts[0];
        let decimalPart = "";
        if (digits > 0) {
            if (parts[1]) {
                if (parts[1].length >= digits) {
                    // 截取字符串的前两位
                    decimalPart = "." + parts[1].substring(0, digits);
                } else {
                    decimalPart = "." + parts[1].padEnd(digits, '0');
                }
            } else {
                decimalPart = "." + "".padEnd(digits, '0');
            }
        } else {
            decimalPart = "." + (parts.length > 1 ? parts[1] : "");
        }
        return integerPart + decimalPart;
    }

    /**
     * 保留两位小数
     * @param number 
     * @returns 
     */
    static toTwoDecimalPlacesWithoutRounding(number: number): number {
        return Math.floor(number * 100) / 100;
    }

    static getTimeStamp() {
        return new Date().valueOf();
    }
    static isTimeLessDate(time1) {
        let s1 = new Date(time1 + " 00:00:00").getTime();
        let now = new Date().getTime();
        return now < s1;
    }
    static isTimeToday(time1) {
        let s1 = new Date(time1 + " 00:00:00").getTime();
        let now = new Date().getTime();
        let tomorrow = s1 + 24 * 60 * 60 * 1000; //当前时间戳（毫秒） + 1天毫秒数 = 明天时间戳
        return now >= s1 && now < tomorrow;
    }

    static isToday(timestamp: number): boolean {
        const currentDate = new Date();
        const inputDate = new Date(timestamp); // Convert Unix timestamp to milliseconds
        return (
            inputDate.getDate() === currentDate.getDate() &&
            inputDate.getMonth() === currentDate.getMonth() &&
            inputDate.getFullYear() === currentDate.getFullYear()
        );
    }

    static isTimeBeyondDate(time1) {
        let s1 = new Date(time1 + " 00:00:00").getTime();
        let now = new Date().getTime();
        let tomorrow = s1 + 24 * 60 * 60 * 1000; //当前时间戳（毫秒） + 1天毫秒数 = 明天时间戳
        return now > tomorrow;
    }
    static timestampToTime(timestamp) {
        // 时间戳为10位需*1000，时间戳为13位不需乘1000
        var date = new Date(timestamp * 1000);
        var Y = date.getFullYear() + "-";
        var M =
            (date.getMonth() + 1 < 10
                ? "0" + (date.getMonth() + 1)
                : date.getMonth() + 1) + "-";
        var D = (date.getDate() < 10 ? "0" + date.getDate() : date.getDate()) + " ";
        var h =
            date.getHours() < 10
                ? "0" + date.getHours() + ":"
                : date.getHours() + ":";
        var m =
            date.getMinutes() < 10
                ? "0" + date.getMinutes() + ":"
                : date.getMinutes() + ":";
        var s =
            date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds();
        return Y + M + D + h + m + s;
    }

    /**
     * 将时间转化为年/月/日 格式
     */
    static timestampToTime2(timestamp) {
        // 时间戳为10位需*1000，时间戳为13位不需乘1000
        var date = new Date(timestamp * 1000);
        var Y = date.getFullYear() + "/";
        var M =
            (date.getMonth() + 1 < 10
                ? "0" + (date.getMonth() + 1)
                : date.getMonth() + 1) + "/";
        var D = (date.getDate() < 10 ? "0" + date.getDate() : date.getDate());
        return Y + M + D;
    }

    /**
     * 将时间转化为年.月.日 格式
     */
    static timestampToTime3(timestamp) {
        // 时间戳为10位需*1000，时间戳为13位不需乘1000
        var date = new Date(timestamp * 1000);
        var Y = date.getFullYear() + ".";
        var M =
            (date.getMonth() + 1 < 10
                ? "0" + (date.getMonth() + 1)
                : date.getMonth() + 1) + ".";
        var D = (date.getDate() < 10 ? "0" + date.getDate() : date.getDate());
        return Y + M + D;
    }

    /**
     * 将时间转化为月/日/年 00:00:00格式
     */
    static timestampToTime4(timestamp) {
        // 时间戳为10位需*1000，时间戳为13位不需乘1000
        var date = new Date(timestamp);
        var Y = date.getFullYear() + " ";
        var M =
            (date.getMonth() + 1 < 10
                ? "0" + (date.getMonth() + 1)
                : date.getMonth() + 1) + "/";
        var D = (date.getDate() < 10 ? "0" + date.getDate() : date.getDate()) + "/";
        var h =
            date.getHours() < 10
                ? "0" + date.getHours() + ":"
                : date.getHours() + ":";
        var m =
            date.getMinutes() < 10
                ? "0" + date.getMinutes() + ":"
                : date.getMinutes() + ":";
        var s =
            date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds();
        return M + D + Y + h + m + s;
    }

    /**
     * 2024-05-13 00:00:00格式的时间转换为时间戳
     * @param time 
     * @returns 
     */
    static timeToTimestamp(time) {
        let tempstr = time + ''
        let timearr = tempstr.slice(0, 19).replace(/\-/g, "/");//有得浏览器不支持  lemoon 修复
        let timestamp = Date.parse(timearr);
        //timestamp = timestamp / 1000; //时间戳为13位需除1000，时间戳为10位不需除1000
        //console.log(time + "的时间戳为：" + timestamp);
        return timestamp;
    }

    static copyFromiOSWeb(str: string) {
        const textString = str + "";
        let input = document.querySelector("#copy-input");
        if (!input) {
            input = document.createElement("input");
            input.id = "copy-input";
            // 防止ios聚焦触发键盘事件
            // @ts-ignore
            input.readOnly = true;
            // @ts-ignore
            input.style.position = "absolute";
            // @ts-ignore
            input.style.left = "-1000px";
            // @ts-ignore
            input.style.zIndex = "-1000";
            document.body.appendChild(input);
        }
        // ios必须先选中文字且不支持
        // @ts-ignore
        input.value = textString;
        // @ts-ignore
        input.select();
        selectText(input, 0, textString.length);
        console.log(document.execCommand("copy"), "execCommand");
        if (document.execCommand("copy")) {
            document.execCommand("copy");
        }
        // input 自带的 select() 方法在苹果端无法进行选择，所以需要自己去写一个类似的方法
        // @ts-ignore
        input.blur();
        // 选择文本。createTextRange(setSelectionRange)是 input 方法
        function selectText(textbox, startIndex, stopIndex) {
            if (textbox.createTextRange) {
                const range = textbox.createTextRange();
                range.collapse(true);
                // 起始光标
                range.moveStart("character", startIndex);
                // 结束光标
                range.moveEnd("character", stopIndex - startIndex);
                range.select();
            } else {
                textbox.setSelectionRange(startIndex, stopIndex);
                textbox.focus();
            }
        }
    }
    /**
     * 判断对象是否为数组
     * @param obj
     * @returns
     */
    private static IsArray(obj: any) {
        return obj && typeof obj == "object" && obj instanceof Array;
    }

    /**
     * 对象深拷贝
     * @param tSource
     * @returns
     */
    public static DeepClone<T>(tSource: T, tTarget?: Record<string, any> | T): T {
        if (this.IsArray(tSource)) {
            tTarget = tTarget || [];
        } else {
            tTarget = tTarget || {};
        }
        for (const key in tSource) {
            if (Object.prototype.hasOwnProperty.call(tSource, key)) {
                if (typeof tSource[key] === "object" && typeof tSource[key] !== null) {
                    //@ts-ignore
                    tTarget[key] = this.IsArray(tSource[key]) ? [] : {};
                    this.DeepClone(tSource[key], tTarget[key]);
                } else {
                    tTarget[key] = tSource[key];
                }
            }
        }
        return tTarget as T;
    }

    /**
     * 对象浅拷贝
     * @param tSource
     * @returns
     */
    public static SimpleClone<T>(
        tSource: T,
        tTarget?: Record<string, any> | T
    ): T {
        if (this.IsArray(tSource)) {
            tTarget = tTarget || [];
        } else {
            tTarget = tTarget || {};
        }
        for (const key in tSource) {
            if (Object.prototype.hasOwnProperty.call(tSource, key)) {
                tTarget[key] = tSource[key];
            }
        }
        return tTarget as T;
    }

    /**生成json文件 */
    public static createJsonFile(data: any, fileName: string) {
        var content = JSON.stringify(data);
        this.saveForBrowser(content, fileName);
    }

    /**
     * 存字符串内容到文件。
     * @param textToWrite  - 要保存的文件内容
     * @param fileNameToSaveAs - 要保存的文件名
     */
    public static saveForBrowser(textToWrite, fileNameToSaveAs) {
        if (cc.sys.isBrowser) {
            cc.log("浏览器");
            let textFileAsBlob = new Blob([textToWrite], {
                type: "application/json",
            });
            let downloadLink = document.createElement("a");
            downloadLink.download = fileNameToSaveAs;
            downloadLink.innerHTML = "Download File";
            if (window.webkitURL != null) {
                downloadLink.href = window.webkitURL.createObjectURL(textFileAsBlob);
            }
            downloadLink.click();
        }
    }

    public static addZero(num) {
        var t = (num + "").length,
            s = "";
        for (var i = 0; i < 9 - t; i++) {
            s += "0";
        }
        return s + num;
    }
    /**
* 日期1988-09 获取 年龄大小 by lemoon
* @param year month day
* @returns 
*/
    static getYearsoldWithDate(year, month, day) {
        let now = new Date();
        let nowYear = now.getFullYear();
        let nowMonth = now.getMonth() + 1;
        let nowDay = now.getDate();
        let retYear = nowYear - year;
        if (nowMonth > month) { return retYear }
        else if (nowMonth < month) { return retYear - 1 }
        else {
            if (nowMonth > month) { return retYear }
            else if (nowMonth < month) { return retYear - 1 }
            else {
                if (nowDay >= day) { return retYear }
                else {
                    return retYear - 1;
                }
            }
        }
    }
    //脱敏 格式 开始几位 结束几位
    static formatDesensitization(dstr: string, start_num: number, end_num: number) {
        if (dstr.length <= start_num + end_num) return dstr;
        //保留前两位和后四位，中间用****替换
        return `${dstr.slice(0, start_num)}***${dstr.slice(dstr.length - end_num)}`;
    }
    //获取长度 下面getGameTitleString子方法
    static get_str_length_true(y_str: string) {
        let relen = 0;
        for (let index = 0; index < y_str.length; index++) {
            const ss0 = y_str.slice(index, index + 1)
            if (allBytes.hasOwnProperty(ss0)) {
                relen += allBytes[ss0];
            } else {
                relen += 12
            }
        }
        return relen
    }
    //获取最长的字符串带... sizi是倍率
    static get_maxstr_with3dot(y_str: string, width_xy: number, size = 1) {
        if (utils.get_str_length_true(y_str) * size <= width_xy) return y_str;
        for (let index = y_str.length; index >= 0; index--) {
            const ss0 = y_str.slice(0, index)
            if (utils.get_str_length_true(ss0) * size <= width_xy - 18) {
                return ss0 + '...'
            }
        }
        return y_str;
    }
    /**
     * string 游戏标题格式 两行 固定区域显示 多的地方...代替 by lemoon
     * @param y_str 
     * @returns 
     */
    static getGameTitleString_new(y_str: string, width_xy: number) {
        let tempstr = y_str.replace("|", " | ");
        let allarr = tempstr.split(" ");//所有单词数组
        let first_str = "";//第一行的字符串
        let index1 = 0
        for (let index = 0; index < allarr.length; index++) {
            const element = allarr[index];
            let son_s = first_str + " " + element;
            if (first_str == '') { son_s = element }
            if (utils.get_str_length_true(son_s) <= width_xy) {
                if (element == "|") {
                    allarr[index] = "";
                    index1 = index;
                    index = allarr.length;
                } else {
                    if (first_str == '') { first_str = element } else {
                        first_str = first_str + " " + element;
                    }
                }
            } else if (index == 0) {
                first_str = element;
            } else {
                if (element == "|") {
                    allarr[index] = "";
                }
                index1 = index;
                index = allarr.length;
            }
        }
        if (index1 == 0) {
            let retss = first_str != "" ? first_str : y_str;
            return utils.get_maxstr_with3dot(retss, width_xy);
        }
        let second_str = "";//第2行的字符串
        for (let index = index1; index < allarr.length; index++) {
            let element = allarr[index];
            if (element == "|") {
                element = ""
            }
            let son_s = second_str + " " + element;
            if (second_str == '') { son_s = element }
            if (utils.get_str_length_true(son_s) <= width_xy) {
                if (element != "") {
                    if (second_str == '') { second_str = element } else {
                        second_str = second_str + " " + element;
                    }
                }
            } else {
                second_str = second_str + " " + element;
                index = allarr.length;
            }
        }
        if (y_str == '') {
            return y_str;
        }
        first_str = utils.get_maxstr_with3dot(first_str, width_xy)
        second_str = utils.get_maxstr_with3dot(second_str, width_xy)
        return first_str + "\n" + second_str;
    }
    /**
     * string 游戏标题格式 两行 固定区域显示 多的地方...代替 by lemoon
     * @param y_str 
     * @returns 
     */
    static getGameTitleString(y_str: string, num_font: number) {
        let tempstr = y_str.replace("|", " | ");
        let allarr = tempstr.split(" ");//所有单词数组
        let first_str = "";//第一行的字符串
        let index1 = 0
        for (let index = 0; index < allarr.length; index++) {
            const element = allarr[index];
            let son_s = first_str + " " + element;
            if (first_str == '') { son_s = element }
            if (son_s.length <= num_font) {
                if (element == "|") {
                    allarr[index] = "";
                    index1 = index;
                    index = allarr.length;
                } else {
                    if (first_str == '') { first_str = element } else {
                        first_str = first_str + " " + element;
                    }
                }
            } else if (index == 0) {
                first_str = element;
            } else {
                if (element == "|") {
                    allarr[index] = "";
                }
                index1 = index;
                index = allarr.length;
            }
        }
        if (index1 == 0) {
            return first_str != "" ? first_str : y_str;
        }
        let second_str = "";//第2行的字符串
        for (let index = index1; index < allarr.length; index++) {
            let element = allarr[index];
            if (element == "|") {
                element = ""
            }
            let son_s = second_str + " " + element;
            if (second_str == '') { son_s = element }
            if (son_s.length <= num_font) {
                if (element != "") {
                    if (second_str == '') { second_str = element } else {
                        second_str = second_str + " " + element;
                    }
                }
            } else {
                let sy_len = num_font - second_str.length;
                if (sy_len <= 3) {
                    element = element.slice(0, 2) + "...";
                } else {
                    element = element.slice(0, sy_len - 1) + "...";
                }
                second_str = second_str + " " + element;
                index = allarr.length;
            }
        }
        if (y_str == '') {
            return y_str;
        }
        return first_str + "\n" + second_str;
    }
    /**
    * string去除 所有特殊字符空格 转行等 by lemoon
    * @param s 
    * @returns 
    */
    static stringTure(s: string): string {
        var strings = s;
        var retStr = strings.replace(/(^s*)|(s*$)/g, "")
        return retStr;
    }
    //判断两个数组是否相等
    public static compare2arrayEqual(arr1, arr2) {
        if (!arr1 || !arr2) return false
        if (arr1.length != arr2.length) return false
        let arr1_str = JSON.stringify(arr1)
        let arr2_str = JSON.stringify(arr2)
        if (arr1_str == arr2_str) return true
        return false;
    }
    public static isPhilippinePhoneNumber(phoneNumber: string): boolean {
        // 菲律宾手机号码的正则表达式
        const regex = /^(09|9|\+639|8|08)\d{9}$/;

        return regex.test(phoneNumber);
    }
    public static keepTwoDecimalFull(num) {
        var result = parseFloat(num);
        if (isNaN(result)) {
            alert("传递参数错误，请检查！");
            return false;
        }
        result = Math.round(num * 100) / 100;
        var s_x = result.toString(); //将数字转换为字符串

        var pos_decimal = s_x.indexOf("."); //小数点的索引值

        // 当整数时，pos_decimal=-1 自动补0
        if (pos_decimal < 0) {
            pos_decimal = s_x.length;
            s_x += ".";
        }

        // 当数字的长度< 小数点索引+2时，补0
        while (s_x.length <= pos_decimal + 2) {
            s_x += "0";
        }
        return s_x;
    }

    public static getLastSegmentFromUrl(url: string): string | null {
        const matches = url.match(/\/([^\/]+)\/?$/);
        if (matches && matches.length > 1) {
            return matches[1];
        }
        return null;
    }

    public static removeFileExtension(filename: string): string {
        const extensionIndex = filename.lastIndexOf(".png");
        if (extensionIndex !== -1) {
            return filename.substring(0, extensionIndex);
        }
        return filename;
    }
    public static getTime(t: number) {
        let d = new Date();
        d.setTime(t * 1000);
        let y = d.getFullYear();
        let m = d.getMonth() + 1;
        let time = "";
        let day = d.getDate();

        if (day < 10) {
            time += "0" + day + "/";
        } else {
            time += day + "/";
        }

        if (m < 10) {
            time += "0" + m + "/";
        } else {
            time += m + "/";
        }

        time += y + "";
        return time;
    }
    public static getToday() {
        const now = new Date();
        const year = now.getFullYear(); // 年
        const month = now.getMonth() + 1; // 月
        const date = now.getDate(); // 日

        return `${year}-${month}-${date}`; // 2022-5-7
    }

    public static getVIPRefreshTime() {
        const now = new Date();
        let year = now.getFullYear(); // 年
        let month = now.getMonth() + 1; // 月
        let date = now.getDate(); // 日
        if (date < 16) {
            month = month;
            date = 16;
        } else {
            month = month + 1;
            date = 1;
            if (month > 12) {
                month = 1;
                year = year + 1;
            }
        }

        return `${year}/${month}/${date}`
    }

    public static myBrowser() {
        if (!navigator) return null;
        var userAgent = navigator.userAgent.toLocaleLowerCase(); //取得浏览器的userAgent字符串并转换为小写
        //360浏览器，以往检测方式，现在360的usrAgent与谷歌返回一样，无法检测
        /*if( userAgent.indexOf("360ee") > -1 || userAgent.indexOf("360se") > -1 ){
                return "360"
            }*/
        // //新检测，不确定是否100%检测出一定是360浏览器
        // if (utils.is360()) {
        //     return "360"
        // }

        // Global.getInstance().showCommonTip(navigator.userAgent, Global.getInstance().popNode)

        //谷歌浏览器
        if (
            (userAgent.indexOf("chrome") > -1 || userAgent.indexOf("crios") > -1) &&
            userAgent.indexOf("safari") > -1
        ) {
            return "chrome";
        }
        //火狐浏览器
        if (userAgent.indexOf("firefox") > -1) {
            return "firefox";
        }
        //欧朋浏览器
        if (userAgent.indexOf("opera") > -1 || userAgent.indexOf("opr") > -1) {
            return "opera";
        }
        //safari浏览器
        if (
            userAgent.indexOf("safari") > -1 &&
            userAgent.indexOf("chrome") === -1
        ) {
            return "safari";
        }
        //IE11浏览器
        if (
            userAgent.indexOf("trident") > -1 &&
            userAgent.indexOf("rv:11.0") > -1
        ) {
            return "ie11";
        }
        //IE浏览器
        if (
            userAgent.indexOf("compatible") > -1 &&
            userAgent.indexOf("msie") > -1
        ) {
            return "ie";
        }
        //UC浏览器
        if (
            userAgent.indexOf("ucbrowser") > -1 &&
            userAgent.indexOf("ucbrowser") > -1
        ) {
            return "uc";
        }
        //微信浏览器
        if (userAgent.indexOf("micromessenger") > -1) {
            return "wechat";
        }
    }
    // public static is360() {
    //     var mType = navigator.mimeTypes;
    //     for (let i = 0; i < mType.length; i++) {
    //         if (mType[i].type.indexOf("360soft") > -1) {
    //             return 1
    //         } else {
    //             return 0
    //         }
    //     }
    // }
    //判断系统
    public static myOS() {
        if (cc.sys.isNative) {
            if (cc.sys.os == cc.sys.OS_ANDROID) {
                return "android";
            }
            if (cc.sys.os == cc.sys.OS_IOS) {
                return "ios";
            }
        }

        var userAgent = navigator.userAgent.toLocaleLowerCase(); //取得浏览器的userAgent字符串并转换为小写
        // Global.getInstance().showSimpleTip("userAgent: " + userAgent)
        if (
            userAgent.indexOf("compatible") > -1 ||
            userAgent.indexOf("windows") > -1
        ) {
            return "windows";
        } else if (
            userAgent.indexOf("macintosh") > -1 &&
            userAgent.indexOf("intel") > -1
        ) {
            return "macOS";
        } else if (userAgent.indexOf("iphone") > -1) {
            return "ios";
        } else if (userAgent.indexOf("android") > -1) {
            return "android";
        } else if (userAgent.indexOf("ipad") > -1) {
            return "ipad";
        } else {
            return "other";
        }
    }

    /** 是否是 ios 的 safari 浏览器环境 */
    public static isIosSafari() {
        if (MoreGameManager.instance().isBrowserIosDevice() && this.myBrowser() == "safari") {
            return true;
        }
        return false;
    }

    public static isJSON(str) {
        if (typeof str == "string") {
            try {
                var obj = JSON.parse(str);
                if (typeof obj == "object" && obj) {
                    return true;
                } else {
                    return false;
                }
            } catch (e) {
                console.log("error：" + str + "!!!" + e);
                return false;
            }
        }
        console.log("It is not a string!");
    }

    public static containsChinese(text: string): boolean {
        const chineseRegex = /[\u4e00-\u9fff]/;
        return chineseRegex.test(text);
    }

    public static containsValidCharacters(text: string): boolean {
        const validCharactersRegex = /^[a-zA-Z0-9\s]*$/;
        return validCharactersRegex.test(text);
    }

    public static getKeyBoardHeight() {
        if (cc.sys.os == cc.sys.OS_ANDROID && cc.sys.isNative) {
            let keyboardHeght = jsb.reflection.callStaticMethod(
                "org/cocos2dx/javascript/AppActivity",
                "getCurrentkeyboardHeight",
                "()I"
            );
            return keyboardHeght;
        }
        //todo browser目前不需要,IOS后续需要加
        return 0;
    }

    public static getEditBoxOffset(editbox: cc.EditBox) {
        let KeyBoardHeight = utils.getKeyBoardHeight();
        let height = (cc.winSize.height - KeyBoardHeight) / 2 + KeyBoardHeight;
        let editPos = editbox.node.parent.convertToWorldSpaceAR(
            cc.v2(editbox.node.x, editbox.node.y)
        );
        let offsetY = height - editPos.y + 78;
        return offsetY;
    }

    //https://www.jianshu.com/p/e0558470289b
    //deep link android 返回app
    public static deepLinkToApp() {
        if (MoreGameManager.instance().isBrowserDevices()) {
            window.location.href = "myapp://com.atm27.tongitstodo/openwith";
        }
    }

    public static hasNumberAndCharacter(input: string): boolean {
        // 使用正则表达式检查字符串是否同时含有数字和字符
        const hasNumber = /\d/.test(input); // 检查是否含有数字
        const hasCharacter = /[a-zA-Z]/.test(input); // 检查是否含有字符（字母）
        return hasNumber && hasCharacter;
    }

    public static getBitValue(num: number, position: number): number {
        // 确保 num 是32位整数，position在0到31之间
        if (
            Number.isInteger(num) &&
            num >= -2147483648 &&
            num <= 2147483647 &&
            Number.isInteger(position) &&
            position >= 0 &&
            position <= 31
        ) {
            // 右移位数，将目标位移动到最低位，然后与1进行按位与操作
            const shiftedValue = (num >> (position - 1)) & 1;
            return shiftedValue;
        } else {
            console.error("参数不合法");
            return -1; // 返回一个错误值或者其他适当的值
        }
    }

    public static filterJSScript(htmlCode): string {
        // 正则表达式，用于匹配包含在 <script> 标签中的 JavaScript 代码
        var scriptRegex = /<script\b[^>]*>([\s\S]*?)<\/script>/g;

        // 匹配所有的 <script> 标签及其内容
        var matches = htmlCode.match(scriptRegex);

        // 输出匹配到的 JavaScript 代码
        if (matches) {
            // for (var i = 0; i < matches.length; i++) {
            //     console.log("JavaScript Code " + (i + 1) + ":\n", matches[i].match(/<script\b[^>]*>([\s\S]*?)<\/script>/)[1]);
            // }
            let str = matches[0].match(/<script\b[^>]*>([\s\S]*?)<\/script>/)[1];
            return str;
        } else {
            console.log("No JavaScript code found in the HTML.");
            return "";
        }
    }

    //生成唯一不重复ID
    public static generateUuid(length = 8): string {
        return Number(
            Math.random().toString().substring(3, length) + Date.now()
        ).toString(36);
    }

    public static openUrl(url: string) {
        if (!url || url.length == 0) return
        if (Global.getInstance().isNative) {
            const encodestr = btoa(encodeURIComponent(url));
            document.location = 'nustaronlinekey://url=' + encodestr;
            return;
        }
        PopBlockTip.showPopWithCheck(() => {
            setTimeout(() => {
                cc.sys.openURL(url)
            });
        });

    }
    //gcash 游戏返回 获取首页地址
    public static get_home_gcash() {
        if (ALL_APP_SOURCE_CONFIG.channel != E_CHANEL_TYPE.G_CASH) {
            return window.location.href
        }
        let reurl = window.location.protocol + '//';
        reurl += window.location.host + '/?gcash_auth_code='
        let gcash_auth_code = utils.getBrowserValue("gcash_auth_code");
        reurl += gcash_auth_code;
        return reurl;
    }
    //浏览器 当前网址直接替换网页
    public static assignUrl(url: string) {
        if (!url || url.length == 0) return
        if (Global.getInstance().isNative) {
            const encodestr = btoa(encodeURIComponent(url));
            document.location = 'nustaronlinekey://url=' + encodestr;
            return;
        }
        let reurl = utils.get_home_gcash()
        window.history.pushState({ page: reurl }, "NuStar", reurl)
        MoreGameManager.instance().curGameURL = url
        window.location.reload()//这里要重新刷新一下 重新登录
        window.location.assign(url)
    }
    /**
     * 按照传入的路径查找节点
     * @param parentNode 
     * @param childPath 
     */
    static getChildByPath(parentNode: cc.Node, childPath: string) {
        let curNode = parentNode;
        let childPathArray = childPath.split(".");
        for (let index = 0; index < childPathArray.length; index++) {
            if (curNode == null) break;
            curNode = curNode.getChildByName(childPathArray[index]);
        }
        return curNode;
    }

    /**
     * 按照传入的节点名递归查找节点
     * @param parentNode 
     * @param name 
     */
    static seekFromRootByName(parentNode, name) {
        if (parentNode.name === name || name == null) {
            // 先访问根节点, 若找到则返回该节点
            return parentNode;
        }

        // 否则按从左到右的顺序遍历根节点的每一棵子树
        for (let i = 0; i < parentNode.children.length; i++) {
            if (this.seekFromRootByName(parentNode.children[i], name)) {
                // 若找到则返回该节点
                return this.seekFromRootByName(parentNode.children[i], name);
            };
        }

        // 找不到返回 null
        return null;
    }

    /**
     * 绑定按钮点击事件
     * @param button 
     * @param target 
     * @param component 
     * @param callback 
     * @param customEventData 
     */
    static bindClickEvent(button: cc.Node, target: cc.Node, component: string, callback: string, customEventData?: string) {
        let clickEventHandler = new cc.Component.EventHandler();
        clickEventHandler.target = target;
        clickEventHandler.component = component;
        clickEventHandler.handler = callback;
        clickEventHandler.customEventData = customEventData;
        button.getComponent(cc.Button).clickEvents.push(clickEventHandler);
    }

    /**
     * 绑定组件触摸事件
     * @param node 
     * @param target 
     * @param callback 
     */
    static bindTouchEvent(node: cc.Node, target: Object, callback: Function) {
        node.on(cc.Node.EventType.TOUCH_START, callback, target);
        node.on(cc.Node.EventType.TOUCH_MOVE, callback, target);
        node.on(cc.Node.EventType.TOUCH_END, callback, target);
        node.on(cc.Node.EventType.TOUCH_CANCEL, callback, target);
        node.on(cc.Node.EventType.ANCHOR_CHANGED, callback, target);
        node.on(cc.Node.EventType.COLOR_CHANGED, callback, target);
    }

    /**
     * 创建Spine动画
     * @param node 
     * @param skeletonData 
     * @param animateName 
     * @param completeCallback 
     * @param isRetain 
     * @param isLoop 
     * @param timeScale 
     * @param skin 
     */
    // static createSpineAnimation(node:cc.Node, skeletonData:sp.SkeletonData, animateName:string, completeCallback, isRetain, isLoop, timeScale?, skin?) {
    //     let spine:sp.Skeleton = node.getComponent(sp.Skeleton);
    //     spine.skeletonData = skeletonData;
    //     if (skin) spine.setSkin(skin);
    //     spine.timeScale = timeScale || 1;
    //     spine.premultipliedAlpha = false;
    //     spine.setAnimation(0, animateName, isLoop);
    //     if (completeCallback) spine.setEndListener(completeCallback);
    //     return spine;
    // }

    /**
     * 改变node父节点
     * @param node 
     * @param newParent 
     */
    static changeNodeParent(node, newParent) {
        if (node == null || newParent == null) return;
        let worldPos = this.getNodeWorldPosition(node);
        node.parent = newParent;
        this.setNodeWorldPosition(node, worldPos);
    }

    /**
     * 获取node的世界坐标
     * @param node 
     * @returns 
     */
    static getNodeWorldPosition(node) {
        return node.convertToWorldSpaceAR(cc.v2(0, 0));
    }

    /**
     * 设置node的世界坐标
     * @param node 
     * @param worldPos 
     */
    static setNodeWorldPosition(node, worldPos) {
        let parentNode = node.parent;
        if (parentNode == null) {
            node.setPosition(worldPos);
            return;
        }
        let toPos = parentNode.convertToWorldSpaceAR(worldPos);
        node.setPosition(toPos);
    }

    /**
     * 设置文本缩放
     * @param uiText 
     * @param maxWidth 
     * @param isNotScaleY 
     * @param resetScale 
     */
    static autoTextWidth(uiText, maxWidth, isNotScaleY, resetScale) {
        if (resetScale) uiText.setScale(resetScale);
        let textScaleX = uiText.scaleX;
        let textScaleY = uiText.scaleY;
        let textWidth = uiText.getContentSize().width;
        if (textWidth * textScaleX > maxWidth) {
            let newScaleX = maxWidth / textWidth;
            uiText.scaleX = newScaleX;
            if (isNotScaleY) uiText.scaleY = (textScaleY / textScaleX * newScaleX);
            return true;
        }
        return false;
    }

    /**
     * 顺序任务
     * @param taskArr task数组
     * @param onAllExec 所有任务执行完再执行
     * @param execFunc 执行Func
     */
    static sequenceTask(taskArr, onAllExec, execFunc) {
        let co = 0;
        let execTask
        execTask = () => {
            if (co >= taskArr.length) {
                onAllExec();
                return;
            }
            let task = taskArr[co];
            let curCo = co;
            co = co + 1;
            execFunc(task, curCo);
        }
        execTask();
    }

    /**
     * 日期转化为年-月-日格式
     * @param date 
     * @returns 
     */
    static dateToYYYYMMdd(date: Date) {
        let mm = date.getMonth() + 1; // getMonth() is zero-based
        let dd = date.getDate();

        return [
            date.getFullYear(),
            (mm > 9 ? '' : '0') + mm,
            (dd > 9 ? '' : '0') + dd
        ].join('-');
    }

    /**
    * 日期转化为月 日，年格式(May 11, 2024)
    * @param date 
    * @returns 
    */
    static dateToMDY(date: Date) {
        let mm = date.getMonth(); // getMonth() is zero-based 
        let dd = date.getDate().toString();
        if (parseInt(dd) < 10) dd = "0" + dd;
        let t = ["Jan", "Feb", "Mar", "Apr", "May", "June", "Jul", "Aug", "Sept", "Oct", "Nov", "Dec"];
        return t[mm] + " " + dd + ", " + date.getFullYear();
    }

    /**
    * 日期转化为月 日，年 时分秒格式(May 11, 2024 00:00:00)
    * @param date 
    * @returns 
    */
    static dateToMDYHMS(date: Date) {
        let mm = date.getMonth(); // getMonth() is zero-based 
        let dd = date.getDate();
        let hour = date.getHours().toString();
        let minutes = date.getMinutes().toString();
        let seconds = date.getSeconds().toString();
        if (parseInt(hour) < 10) hour = "0" + hour;
        if (parseInt(minutes) < 10) minutes = "0" + minutes;
        if (parseInt(seconds) < 10) seconds = "0" + seconds;
        let t = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sept", "Oct", "Nov", "Dec"];
        return t[mm] + " " + dd + "," + date.getFullYear() + " " + hour + ":" + minutes + ":" + seconds;
    }

    /**
     * 日期转化为年-月格式
     * @param date 
     * @returns 
     */
    static dateToYYYYMM(date: Date) {
        var mm = date.getMonth() + 1; // getMonth() is zero-based
        var dd = date.getDate();

        return [
            date.getFullYear(),
            (mm > 9 ? '' : '0') + mm
        ].join('-');
    }

    /**
     * 获取当前月份有多少天(这个方法适用于所有月份，无论是 28 天的 2 月还是 30、31 天的其他月份)
     * @param year 
     * @param month 
     */
    static getDaysInMonth(year: number, month: number): number {
        //创建下一个月的第一天，然后减去一天，得到当前月份的最后一天
        const date = new Date(year, month, 0);// month: 0 是1月,1是2月,依次类推
        return date.getDate();//获取最后一天的日期(即天数)
    }

    /**
     * 随机整数
     * @param startNumber 
     * @param endNumber 
     * @returns 
     */
    static randomInt(startNumber, endNumber) {
        let choice = endNumber - startNumber + 1;
        return Math.floor(Math.random() * choice + startNumber);
    }

    /**
     * string转Int类型
     * @param s 
     * @returns 
     */
    static stringToInt(s: string): number {
        var n = parseInt(s.replace(/\./g, '').replace(/,/g, ''));
        if (isNaN(n)) n = 0;
        return n;
    }
    /**获取浏览器 参数 */
    static getBrowserValue(value) {
        var query = window.location.search.substring(1);
        var vars = query.split("&");
        for (var i = 0; i < vars.length; i++) {
            var pair = vars[i].split("=");
            if (pair[0] == value) {
                return pair[1];
            }
        }
        return null;
    }
    /**转换为 Cocos Creator 支持的 <color> 标签 */
    static convertHtmlToRichText(htmlString: string): string {
        // 使用正则表达式匹配带有color样式的span标签
        const colorRegex = /<span style="color:\s*rgb\((\d+),\s*(\d+),\s*(\d+)\);?">/gi;

        // 将匹配到的rgb颜色值转换为16进制颜色值
        htmlString = htmlString.replace(colorRegex, (match, r, g, b) => {
            const hexColor = `#${parseInt(r).toString(16).padStart(2, '0')}${parseInt(g).toString(16).padStart(2, '0')}${parseInt(b).toString(16).padStart(2, '0')}`;
            return `<color=${hexColor}>`;
        });

        // 替换掉span结束标签为<color>结束标签
        htmlString = htmlString.replace(/<\/span>/gi, '</color>');

        return htmlString;
    }

    /**判断链接是否是图片 */
    static isPicsWithUrl(urlstr: string) {
        let url_str = urlstr;
        if (url_str.length < 4) return false
        let has_pics = ['.png', '.jpg', '.jpeg', '.gif']
        for (let index = 0; index < has_pics.length; index++) {
            const element = has_pics[index];
            let tempstr = url_str.slice(-element.length);
            tempstr = tempstr.toLowerCase()
            if (tempstr == element) return true;
        }
        return false
    }

    /*获取文件后缀 根据链接 */
    static getPicsextWithUrl(urlstr: string) {
        let url_str = urlstr;
        if (url_str.length < 4) return ''
        let has_pics = ['.png', '.jpg', '.jpeg', '.gif']
        for (let index = 0; index < has_pics.length; index++) {
            const element = has_pics[index];
            let tempstr = url_str.slice(-element.length);
            tempstr = tempstr.toLowerCase()
            if (tempstr == element) return element;
        }
        return ''
    }

    static getRandomInt(min: number, max: number): number {
        min = Math.ceil(min);
        max = Math.floor(max);
        return Math.floor(Math.random() * (max - min + 1)) + min;
    }

    /**
    * 得到一个节点的世界坐标
    * node的原点在中心
    * @param {*} node 
    */
    static localConvertWorldPointAR(node) {
        if (node) {
            return node.convertToWorldSpaceAR(cc.v2(0, 0));
        }
        return null;
    }

    /**
     * 把一个世界坐标的点，转换到某个节点下的坐标
     * 原点在node中心
     * @param {*} node 
     * @param {*} worldPoint 
     */
    static worldConvertLocalPointAR(node, worldPoint) {
        if (node) {
            return node.convertToNodeSpaceAR(worldPoint);
        }
        return null;
    }

    /**
     *  * 把一个节点的本地坐标转到另一个节点的本地坐标下
     * @param {*} node 
     * @param {*} targetNode 
     */
    static convetOtherNodeSpaceAR(node, targetNode) {
        if (!node || !targetNode) {
            return null;
        }
        //先转成世界坐标
        let worldPoint = this.localConvertWorldPointAR(node);
        return this.worldConvertLocalPointAR(targetNode, worldPoint);
    }

    static loadAvatar(sp: cc.Sprite, avatar: string) {
        avatar = Global.getInstance().formatAvatar(avatar);

        const localAvatar = AVATAR_LOCAL[avatar];
        if (localAvatar) {
            const src = "avatar/";
            const arr = localAvatar.split("_");
            Resource.loadSpriteFrame_from_res(src + arr[0] + "/avatar_" + arr[1], (spriteFrame: cc.SpriteFrame) => {
                if (spriteFrame) sp.spriteFrame = spriteFrame;
            })
        }
        else {
            if (!/^http/.test(avatar)) {
                avatar = ALL_APP_SOURCE_CONFIG.remote_assets_prod[Global.DEBUG] + avatar;
            }
            Global.getInstance().download_img(avatar, sp);
        }
    }

    static loadWebImage(sp: cc.Sprite, img: string) {
        if (!/^http/.test(img)) {
            img = ALL_APP_SOURCE_CONFIG.remote_assets_prod[Global.DEBUG] + img;
        }
        Global.getInstance().download_img(img, sp);
    }

    static formatPlayerId(playerId: string | number) {
        if (typeof playerId == 'number') playerId = playerId.toString();

        if (playerId.length > 5) {
            return playerId.substring(0, 2) + "***" + playerId.substring(playerId.length - 3, playerId.length);
        }
        return playerId;
    }

    static cutContent(content: string | number, maxCount: number) {
        if (typeof content == 'number') content = content.toString();

        if (content?.length > maxCount) {
            return content.substring(0, maxCount - 3) + "...";
        }
        return content;
    }

    static linkJump(arg) {
        const jumpType = arg.jumpType;
        const gameType = arg.gameType;
        const providerList = arg.providerList;
        const url = arg.url;

        if (jumpType == JUMP_TYPE.GAME) {
            uiManager.instance.hideAllDialog();

            const bAllProvider = Global.instance.isAllProvider(providerList);
            const gamescene = cc.director.getScene().getComponentInChildren(GameControl);

            if (bAllProvider) {
                gamescene.mTabbar.goHall_showCatogry(gameType);
            }
            else {
                gamescene.mTabbar.showAllGames_containProviders(providerList);
            }
        }
        else if (jumpType == JUMP_TYPE.DEPOSIT) {//3 => Deposit(跳转至充值页)
            if (!Global.getInstance().token) {
                //未登录 跳转登录页
                uiManager.instance.showDialog(UI_PATH_DIC.phonePasswordLogin, [{ way: LOGIN_WAY.PhoneCode }])
                return;
            }

            if (ALL_APP_SOURCE_CONFIG.channel == E_CHANEL_TYPE.G_CASH) {
                //gcash 直接弹窗提示 是否跳转充值
                Global.getInstance().back_mini_buy_tips();
                return;
            }
            uiManager.instance.showDialog(UI_PATH_DIC.Deposit,null,null,DEEP_INDEXZ.DEPOSIT);
        }
        else if (jumpType == JUMP_TYPE.LINK) {//4 => Internal URL(内部框架打开对应网址)
            if (cc.sys.os == cc.sys.OS_IOS) {
                utils.assignUrl(url);
            } else {
                utils.openUrl(url)
            }
        }
        else if (jumpType == JUMP_TYPE.PROMO) {//5 => 跳转到promo
            uiManager.instance.hideAllDialog();

            let gamescene = cc.director.getScene().getComponentInChildren(GameControl);
            gamescene.mTabbar.tap_bar(null, "promos");
        }
    }

    static awaitTime(time: number) {
        return new Promise((resolve, reject) => {
            setTimeout(() => {
                resolve(time);
            }, time * 1000);
        });
    }

    static addButtonClick(node: cc.Node, callback: Function) {
        if (!node) {
            cc.error(`addButtonClick error, node is not exist`);
            return;
        }

        node.off("click");
        node.on("click", (target) => {
            const clickTime = node["clickTime"] || 0;
            const now = Date.now();
            if (now - clickTime < 500) return;

            node["clickTime"] = now;

            cc.log("addButtonClick ", node.name);
            if (callback) callback(target);
        });
    }
}
