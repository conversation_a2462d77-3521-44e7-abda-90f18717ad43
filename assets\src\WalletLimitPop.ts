import { DEEP_INDEXZ, E_FUND_TYPE, E_TRANSACTION_TYPE, PRODUCT_TYPE, UI_PATH_DIC } from "./GlobalConstant";
import Global from "./GlobalScript";
import { showMayavipApplyTip } from "./MayavipApplyTip";
import NewShop from "./NewShopUI";
import { MayaVipManager } from "./UI/activity/ActivityMayaVip";
import UICommon from "./component/UICommon";
import { uiManager } from "./mgr/UIManager";
import utils from "./utils/utils";

const { ccclass, property } = cc._decorator;

export function showWalletLimitPop(payType: number, ransactionType: number, amount: number) {
    uiManager.instance.showDialog(UI_PATH_DIC.WalletLimitPop, [{ payType: payType, ransactionType: ransactionType, amount: amount }],null,DEEP_INDEXZ.POP_BLOCK_TIP)
}


@ccclass
export default class WalletLimitPop extends UICommon {
    @property(cc.Label)
    word: cc.Label = null;

    @property(cc.Node)
    ndOtherPay: cc.Node = null;

    @property(cc.Node)
    ndReedemMe: cc.Node = null

    @property(cc.Node)
    ndUpgradeMaya: cc.Node = null

    @property(cc.Node)
    ndIncreaseWL: cc.Node = null

    @property(cc.Node)
    ndLayout: cc.Node = null

    @property(cc.Node)
    ndBottonBg: cc.Node = null

    @property(cc.Node)
    ndMainBg: cc.Node = null

    mPayType = E_FUND_TYPE.Gcash | PRODUCT_TYPE.GCASH
    mRansactionType = E_TRANSACTION_TYPE.recharge
    mAmount = 0
    onLoad(): void {
        this.setSys()
    }

    init(data) {
        if (!data) return
        this.mPayType = data.payType
        this.mRansactionType = data.ransactionType
        this.mAmount = data.amount
        this.ndUpgradeMaya.active = false
        let isRecharge = this.mRansactionType == E_TRANSACTION_TYPE.recharge
        this.ndOtherPay.active = isRecharge
        this.ndReedemMe.active = !isRecharge
        if (this.mPayType == E_FUND_TYPE.Maya && Global.getInstance().mayaMode) {
            this.ndUpgradeMaya.active = true
            setTimeout(() => {
                this.ndBottonBg.height = this.ndLayout.height + 60
            }, 20);
            this.ndMainBg.height = this.ndMainBg.height + 60
            this.word.string = "You have  exceeded the Maya wallet limit."
        } else if (this.mPayType == E_FUND_TYPE.Gcash) {
            this.word.string = "You have  exceeded the Gcash wallet limit."
        }
    }

    onDestroy(): void {

    }

    closeDialog() {
        this.closeCommonTips()
    }

    closeCommonTips() {
        this.node.destroy()
    }

    setSys() {
        this.node.zIndex = 1000;
    }

    onClickOtherPay() {
        if (this.mPayType == E_FUND_TYPE.Maya) {
            // Global.getInstance().showShop(0, { amount: this.mAmount, payWay: E_FUND_TYPE.Gcash });

            let dialog = uiManager.instance.getVisDialog(UI_PATH_DIC.Shop)
            let extraData = { payType: E_TRANSACTION_TYPE.recharge, amount: this.mAmount, payWay: E_FUND_TYPE.Gcash }
            if (dialog) {
                dialog.getComponent(NewShop).execAutoOpen(extraData)
            } else {
                Global.getInstance().showShop(0, extraData);
            }
        } else if (this.mPayType == E_FUND_TYPE.Gcash) {
            // Global.getInstance().showShop(0, { amount: this.mAmount, payWay: E_FUND_TYPE.Maya });

            let dialog = uiManager.instance.getVisDialog(UI_PATH_DIC.Shop)
            let extraData = { payType: E_TRANSACTION_TYPE.recharge, amount: this.mAmount, payWay: E_FUND_TYPE.Maya }
            if (dialog) {
                dialog.getComponent(NewShop).execAutoOpen(extraData)
            } else {
                Global.getInstance().showShop(0, extraData);
            }
        }
        this.closeDialog()
    }

    onClickRedeemMe() {
        if (this.mPayType == PRODUCT_TYPE.MAYA) {
            let dialog = uiManager.instance.getVisDialog(UI_PATH_DIC.Shop)
            let extraData = { payType: E_TRANSACTION_TYPE.redeem, amount: this.mAmount, payWay: PRODUCT_TYPE.GCASH }
            if (dialog) {
                dialog.getComponent(NewShop).execAutoOpen(extraData)
            } else {
                Global.getInstance().showShop(1, extraData);

            }
        } else if (this.mPayType == PRODUCT_TYPE.GCASH) {
            let dialog = uiManager.instance.getVisDialog(UI_PATH_DIC.Shop)
            let extraData = { payType: E_TRANSACTION_TYPE.redeem, amount: this.mAmount, payWay: PRODUCT_TYPE.MAYA }
            if (dialog) {
                dialog.getComponent(NewShop).execAutoOpen(extraData)
            } else {
                Global.getInstance().showShop(1, extraData);
            }
        }
        this.closeDialog()
    }

    onClickUpgradeMaya() {
        if (this.mPayType == E_FUND_TYPE.Maya) {
            if (MayaVipManager.isCanApplay()) {
                showMayavipApplyTip()
            } else {
                MayaVipManager.showMayavipApplyVipUI()
            }
        }
        this.closeDialog()
    }

    onClickIncreaseWL() {
        if (this.mPayType == E_FUND_TYPE.Maya) {
            utils.openUrl("https://www.maya.ph/account-limits")
        } else if (this.mPayType == E_FUND_TYPE.Gcash) {
            utils.openUrl("https://help.gcash.com/hc/en-us/articles/************-GCash-Wallet-and-Transaction-Limits")
        }
        this.closeDialog()
    }
}
