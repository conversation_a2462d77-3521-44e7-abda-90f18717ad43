// Hall模块类型定义
export interface HallGameIcon {
  company_id: number;
  content: string;
  game_id: string | number;
  game_name: string;
  game_type: number; // 0代表没有房间的 1代表有房间列表的 2代表分类
  group: number;
  icon_url: string; // 图片的远程地址
  id: number;
  is_animation: boolean; // true是动画 false是静态图
  order_num: number;
  rule: string;
  status: number;
}

export interface ConfigType {
  pos: { x: number; y: number };
  k: number;
  index: number;
}

export interface ConfigK {
  node: any;
  iindex: number; // 滑动到的index
  type: string; // 标识poker这种
}

export enum LiveTag {
  LIVE1,
  LIVE2,
  LIVE3
}

export enum LoginWay {
  PhoneCode,
  Password
}

// 游戏分类
export const GAME_TYPES = [
  'Casino',
  'Slots', 
  'Poker',
  'Bingo',
  'Arcade',
  'Sports',
  'Like',
  'History',
  'Baccarat',
  'Roulette',
  'Blackjack'
] as const;

export type GameType = typeof GAME_TYPES[number];

// 游戏标签
export const GAME_TAGS = {
  REGULAR: 0,
  HOT: 1,
  NEW: 2
} as const;

// 特殊游戏类型
export const SPECIAL_TYPE = {
  BACCARAT: 'baccarat',
  ROULETTE: 'roulette',
  BLACKJACK: 'blackjack'
} as const;

// 图标状态
export const ICON_STATUS = {
  NORMAL: 1,
  MAINTENANCE: 2
} as const;

// 交易记录类型
export const RECORD_TYPE = {
  ADJUSTMENT: 'Adjustment',
  MAYAPAY: 'mayapay',
  MAYA_WEB: 'mayawebpay',
  GCASH_WEB: 'gcashwebpay',
  DEPOSIT: 'Deposit',
  WITHDRAWAL: 'Withdrawal',
  BATCH_WITHDRAWAL: 'Batch Withdrawal',
  REWARD: 'Reward',
  TRANSFER: 'Transfer'
} as const;

// 筛选数据类型
export enum FilterData {
  TODAY,
  YESTERDAY,
  LAST_THREEDAYS,
  LAST_SEVENTDAYS
}

// 充值状态
export enum RechargeStatus {
  SUCCESS = 1, // 成功
  PENDING = 2, // 等待
  FAILURE = 3, // 失败
  OPERATIONAL = 4 // 人工审核
}

// 充值网页状态
export enum RechargeWebStatus {
  SUCCESS = 1, // 成功
  PENDING = 2, // 等待
  FAILED = 3, // 失败
  CANCEL = 4, // 取消
  WAITING = 5, // 等待处理
  OPERATION = 6, // 人工处理
  WAITING_PAYMENT = 7, // 等待支付
  WAITING_CHANGE_BALANCE = 8 // 等待余额变更
}

// 提现状态
export enum WithdrawStatus {
  PENDING = 1, // 待到账
  SUCCESS = 2, // 成功
  FAILURE = 3, // 失败
  OPERATIONAL = 4, // 人工审核
  PENDING_APPROVAL = 5 // 待审核
}

// 记录标签
export enum RecordTag {
  DEPOSIT = 1,
  WITHDRAW = 2,
  AWARD = 3
}

// 奖励更新类型
export enum AwardUpdateType {
  FIRST_RECHARGE = 12,
  FREE_REGISTRATION = 65,
  CASHBACK = 111,
  VIP_CASHBACK_119 = 119,
  BING_PHONE = 294,
  SIGN_UP_BONUS = 265,
  WEEKLY_SIGNIN = 310,
  DAILY_BETTING = 311
}

// Banner数据接口
export interface BannerData {
  id: number;
  title: string;
  image_url: string;
  link_url?: string;
  sort: number;
  updated_at: string;
  activity_type?: number;
}

// 交易记录接口
export interface TransactionRecord {
  id: number;
  type: string;
  amount: number;
  status: number;
  created_at: string;
  updated_at: string;
  pay_channel?: string;
  quantity?: number;
  update_type?: number;
}

// 用户余额接口
export interface UserBalance {
  total: number;
  available: number;
  frozen: number;
}

// 活动数据接口
export interface ActivityData {
  id: number;
  name: string;
  type: number;
  start_at: string;
  end_at: string;
  channel: string;
  page: string;
  content: string;
}
