import UICommon from "../component/UICommon";
import { DEEP_INDEXZ, UI_PATH_DIC } from "../GlobalConstant";
import Global from "../GlobalScript";
import { uiManager } from "../mgr/UIManager";
import HttpUtils from "../net/HttpUtils";
import utils from "../utils/utils";

const {ccclass, property} = cc._decorator;
@ccclass
export default class VipDetail extends UICommon {

    @property(cc.Label)
    labSlotsCashBack:cc.Label = null;

    @property(cc.Label)
    labBingoCashBack:cc.Label = null;

    @property(cc.Label)
    labSportsCashBack:cc.Label = null;

    @property(cc.Label)
    labPokerCashBack:cc.Label = null;
    
    @property(cc.Label)
    labCasinoCashBack:cc.Label = null;

    @property(cc.Label)
    labFishingCashBack:cc.Label = null;

    @property(cc.Label)
    labCashback:cc.Label = null;

    @property(cc.Node)
    bonusNode:cc.Node = null;

    @property(cc.Node)
    mainNode: cc.Node = null;

    onLoad () {
        this.initCashBackData();
        let scrollview = this.mainNode.getChildByName("scrollview");
        scrollview.on("scrolling", ()=>{this.title_layout_opacity(scrollview.getComponent(cc.ScrollView))}, this);
    }

    initCashBackData() {
        HttpUtils.getInstance().post(1, 3, this, "/avt/api/activity/rebate-conf", {
            token: Global.getInstance().token,
            config_type: 4,//1:普通返水 4:vip返水
        }, (response) => {
            if (response.data && response.data.config && response.data.config.length > 0) {
                let config = response.data.config[0];
                let config_cashback = response.data.config[0][1];
                this.labCashback.string = "Highest cashback "+config_cashback.rate+"%"; 
                let status = response.data.status;
                for(let k = 1; k < config.length; k++) {
                    let rebate = utils.getChildByPath(this.bonusNode, "lab_"+config[k].game_type.toLowerCase()).getComponent(cc.Label);
                    if (status != 1) {
                        if(rebate) rebate.string = "";
                    } else {
                        if(rebate) rebate.string = config[k].rate+"%";
                    }
                }
            }
        });
    }

    start () {
        let page = this.node.getChildByName("page").getComponent(cc.PageView);
        page.node.on('page-turning', ()=>{
            let curIndex = page.getCurrentPageIndex();
            if (curIndex != 1) {
                this.node.destroy();
            }
        }, this);
        let tempnode = this.node.getChildByName("temp");
        page.insertPage(cc.instantiate(tempnode),0);
        page.scrollToPage(1,0.01);
    }

    /**点击进入VIP页*/
    onClickDetailBack() {
        uiManager.instance.showDialog(UI_PATH_DIC.Vip,null,null,DEEP_INDEXZ.ACTIVITYS);
    }

    // update (dt) {}
    title_layout_opacity(node:cc.ScrollView) {
        let opacity = node.getScrollOffset().y
        if (opacity > 255 ) opacity = 255;
        let layou = utils.getChildByPath(this.mainNode,"titlelayout.bg");
        layou.opacity = opacity;
        let btn_back = utils.getChildByPath(this.mainNode,"titlelayout.btn_back.img");
        if (opacity > 0) {
            btn_back.color = cc.color(255-opacity/255*180,255-opacity/255*168,255-opacity/255*151);
        } else {
            btn_back.color = cc.color(255,255,255);
        }
        
    }
}
