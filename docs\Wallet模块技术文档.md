# 钱包任务模块技术文档

## 📖 概述

钱包任务模块是游戏平台中的核心功能模块，负责管理用户的任务系统，包括任务展示、进度跟踪、奖励发放等功能。该模块采用 Cocos Creator 框架开发，使用 TypeScript 语言实现。

## 📁 模块结构

```
assets/src/Wallet/
├── WalletTask.ts           # 钱包任务主界面组件
├── WalletTaskConst.ts      # 钱包任务数据接口定义
└── WalletTaskRule.ts       # 钱包任务规则弹窗组件
```

## 🏗️ 架构设计

### 核心组件

1. **WalletTask** - 主界面控制器
2. **WalletTaskConst** - 数据模型定义
3. **WalletTaskRule** - 规则展示组件

### 依赖关系

```mermaid
graph TD
    A[WalletTask] --> B[WalletTaskConst]
    A --> C[WalletTaskRule]
    A --> D[UIComponent]
    A --> E[Global]
    A --> F[GameData]
    A --> G[UIManager]
```

## 📋 数据结构

### WalletTaskInfo 接口

```typescript
export interface WalletTaskInfo {
    id: string;                      // 任务唯一标识
    wallet_task_id: string;          // 钱包配置信息
    user_id: string;                 // 用户ID
    bonus: string;                   // 钱包任务奖金
    task_status: number;             // 任务状态
    created_at: string;              // 任务发放时间
    updated_at: string;              // 任务状态变更时间
    task_name: string;               // 任务名称
    task_rule: string;               // 任务规则
    bet_num: string;                 // 当前注单金额
    bet_target_value: number;        // 任务目标投注额
    recharge_done: boolean;          // 充值是否完成
    recharge_target_value: string;   // 任务目标充值额
    expire_time: number;             // 任务有效时间（小时）
    game_list: string;               // 游戏列表
    game_type: string[];             // 游戏类别
    provider_list: string[];         // 厂商列表
}
```

### 任务状态枚举

```typescript
export const WALLETTASK_STATUS = {
    LOCK: 1,        // 未解锁
    ONGOING: 2,     // 进行中
    FINISHED: 3,    // 已完成
    EXPIRED: 4,     // 已过期
    DELETE: 5       // 已删除
}
```

## 🎮 主要功能

### 1. 弹窗显示控制

#### showWalletTaskPop 函数
- **功能**：动态加载并显示钱包任务弹窗
- **参数**：args - 可选参数
- **逻辑**：
  1. 检查弹窗是否已存在
  2. 动态加载预制体
  3. 限制只能在大厅页面弹出
  4. 设置自动弹窗标记

```typescript
export function showWalletTaskPop(args?: any) {
    let parent = Global.getInstance().popNode
    if (!parent) return
    let gameNd = parent.getChildByName("WalletTask")
    if (gameNd) return
    
    // 加载预制体逻辑...
}
```

### 2. 引导机制

#### 首次使用引导
- **存储键**：`Global.GLOBAL_STORAGE_KEY.WALLET_TASK`
- **触发条件**：首次使用且有任务数据
- **流程**：
  1. 检查本地存储
  2. 显示引导界面
  3. 用户确认后切换到正常界面

### 3. 任务列表管理

#### 任务过滤与排序
```typescript
// 过滤过期任务
const bExpired = (info: WalletTaskInfo) => {
    const status = info.task_status;
    const expireTime = info.expire_time;
    const createdTime = Number(info.created_at);
    const now = Global.getInstance().now() / 1000;
    let diffTime = expireTime * 3600 + createdTime - now;
    
    return (expireTime != 0 && diffTime <= 0) || 
           status == WALLETTASK_STATUS.EXPIRED || 
           status == WALLETTASK_STATUS.DELETE;
}

// 按状态排序
this.walletTaskData.sort((a, b) => b.task_status - a.task_status);
```

### 4. 进度显示系统

#### 投注进度条
- **显示条件**：`betTotal > 0 && status != FINISHED`
- **计算公式**：`betCur / betTotal`
- **格式化**：使用千分位分隔符

#### 充值进度条
- **显示条件**：`rechargeTotal > 0 && status != FINISHED`
- **状态显示**：完成/未完成二元状态

### 5. 倒计时功能

#### 实时倒计时
```typescript
const updateTime = () => {
    if (diffTime < 0) {
        // 任务过期处理
        this.gameData.reduceWalletTask(taskId);
        return;
    }
    
    const hour = Math.floor((diffTime / 3600) % 24).toString().padStart(2, '0');
    const minute = Math.floor((diffTime / 60) % 60).toString().padStart(2, '0');
    const second = Math.floor(diffTime % 60).toString().padStart(2, '0');
    this.setLabel("txt_time", `${hour}:${minute}:${second}`, parent);
    
    diffTime--;
}
```

### 6. 按钮交互逻辑

#### 解锁按钮
- **限制**：同时只能有一个进行中的任务
- **检查**：`this.gameData.existOngoingTask()`
- **操作**：调用 `httpProxy.receiveWalletTask(taskId)`

#### 进行中按钮
- **功能**：跳转到对应游戏
- **参数**：游戏类型、厂商列表
- **调用**：`utils.linkJump({ jumpType: JUMP_TYPE.GAME, gameType, providerList })`

#### 领取按钮
- **功能**：领取任务奖励
- **流程**：
  1. 调用API领取奖励
  2. 更新余额
  3. 播放金币动画
  4. 刷新界面

## 🔄 业务流程

### 任务生命周期

```mermaid
stateDiagram-v2
    [*] --> LOCK: 任务创建
    LOCK --> ONGOING: 用户解锁
    ONGOING --> FINISHED: 完成条件
    ONGOING --> EXPIRED: 超时过期
    FINISHED --> [*]: 领取奖励
    EXPIRED --> [*]: 自动清理
```

### 数据流转

1. **初始化**：从服务器获取任务数据
2. **过滤**：移除过期和无效任务
3. **排序**：按状态优先级排序
4. **渲染**：动态生成任务列表项
5. **更新**：实时更新进度和倒计时
6. **交互**：处理用户操作
7. **同步**：与服务器同步状态变更

## 🎨 UI 特性

### 界面布局
- **PageView**：支持页面滑动
- **ScrollView**：任务列表滚动
- **动态列表**：根据数据动态生成

### 视觉反馈
- **状态指示**：不同状态使用不同颜色和图标
- **进度条**：实时显示任务进度
- **倒计时**：动态更新剩余时间
- **动画效果**：金币领取动画

### 响应式设计
- **自适应**：根据任务数量调整布局
- **状态切换**：平滑的状态转换动画
- **加载状态**：预制体动态加载

## 🔗 系统集成

### 事件系统
```typescript
// 监听事件
this.addEvent(EVENT.UPDATE_WALLETTASKINFO, this.refreshUI);

// 发送事件
cc.director.emit("showGoldAni", { 
    type: GOLDANI_TYPE.TOTARGET, 
    target: this.nodeAtNode("node_balance") 
});
```

### 网络通信
- **领取任务**：`httpProxy.receiveWalletTask(taskId)`
- **领取奖励**：`httpProxy.receiveWalletTaskReward(taskId)`

### 数据管理
- **GameData**：任务数据管理
- **Global**：全局状态管理
- **本地存储**：引导状态记录

## 🔧 配置说明

### 常量配置
```typescript
// UI路径配置
UI_PATH_DIC.WalletTask
UI_PATH_DIC.WalletTaskRule
UI_PATH_DIC.Transations

// 跳转类型
JUMP_TYPE.GAME
JUMP_TYPE.PROMO

// 动画类型
GOLDANI_TYPE.TOTARGET
```

### 存储键
```typescript
Global.GLOBAL_STORAGE_KEY.WALLET_TASK  // 引导状态
```

## 🚀 使用示例

### 显示钱包任务弹窗
```typescript
import { showWalletTaskPop } from "./Wallet/WalletTask";

// 显示弹窗
showWalletTaskPop();

// 带参数显示
showWalletTaskPop([param1, param2]);
```

### 更新任务数据
```typescript
// 设置任务数据
GameData.instance.setWalletTaskData(taskList);

// 触发界面更新
cc.director.emit(EVENT.UPDATE_WALLETTASKINFO);
```

## 📝 注意事项

1. **性能优化**：任务列表使用对象池模式，避免频繁创建销毁
2. **内存管理**：及时清理定时器和事件监听
3. **网络异常**：处理网络请求失败的情况
4. **数据校验**：对服务器返回的数据进行有效性检查
5. **用户体验**：提供加载状态和错误提示

## 🔮 扩展建议

1. **任务类型扩展**：支持更多任务类型
2. **奖励多样化**：支持非金币奖励
3. **社交功能**：任务分享和排行榜
4. **个性化推荐**：基于用户行为推荐任务
5. **离线处理**：支持离线状态下的任务进度计算

---

*文档版本：v1.0*  
*最后更新：2025-08-07*  
*维护者：开发团队*
