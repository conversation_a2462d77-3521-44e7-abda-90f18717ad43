/** 下载引导信息 */
export interface DownloadTipInfo {
    button_text: string; // 按钮文案
    close_type: number; // 1 允许关闭 2 不允许关闭
    icon: string; // 图标
    slogan: string; // 描述
    download_url: string; // 下载链接
    pop_title: string; // 下载链接
    pop_info: string; // 下载链接
    status: string;
}

export interface RechargeConfig {
    id: number;
    account_type: number;
    channel_id: number;
    type: number;
    min: number;
    max: number;
    name: string;
    icon: string;
    tags: string[];
    first_restriction: number;
    channel_name: string;
    pid: number;
}

export interface WithdrawConfig {
    id: number;
    account_type: number;
    channel_id: number;
    type: number;
    min: number;
    max: number;
    name: string;
    icon: string;
    tags: string[];
    first_restriction: number;
    channel_name: string;
    pid: number;
}

export interface rechargeWithdrawConfig {
    recharge: RechargeConfig[];
    withdraw: WithdrawConfig[];
}