import { ALL_APP_SOURCE_CONFIG } from "./Config";
import { E_CHANEL_TYPE, E_SCENE_TYPE, UI_PATH_DIC } from "./GlobalConstant";
import Global from "./GlobalScript";
import MoreGameManager from "./MoreGameManager";
import BackgroundAdapter, { FIT_POLICY } from "./component/BackgroundAdapter";
import BetLevelManager from "./hall/BetLevelManager";
import Hall, { LOGIN_WAY } from "./hall/Hall";
import News from "./hall/News";
import Promo from "./hall/Promo";
import UserSetting from "./hall/UserSetting";
import Tabbar from './tabbar/Tabbar'
import { uiManager } from "./mgr/UIManager";
import { SocketUtils } from "./net/SocketUtils";
import { serviceMgr } from "./mgr/serviceMgr";
import { poolManager } from "./mgr/poolManager";

const { ccclass, property } = cc._decorator;
export enum E_DAY_NIGHT {
    DAY = 1,
    NIGHT = 2
}
@ccclass
export default class GameControl extends cc.Component {
    @property(cc.ProgressBar)
    proLoading: cc.ProgressBar = null;

    @property(cc.Sprite)
    spSmallman: cc.Sprite = null;

    @property(cc.Label)
    lbMemory: cc.Label = null;

    @property(cc.Label)
    lbProgress: cc.Label = null;

    @property(cc.Node)
    game_node: cc.Node = null;

    @property(cc.Camera)
    mainCamera:cc.Camera = null;

    //解决滑动卡 的bug
    @property(cc.Prefab)
    game_hall_item:cc.Prefab = null;

    m_prefab: Object = {};
    m_currentNode: cc.Node = null;
    // LIFE-CYCLE CALLBACKS:
    m_bg: cc.Node = null
    private _curHallType: number;
    private isTransferring: boolean = false;
    private popTime: number = 0;
    currentGameListPage = 1;
    mHall:Hall = null;
    mPromos:Promo = null;
    mNews:News = null;
    mAccount:UserSetting = null;
    mTabbar:Tabbar = null;
    
    onLoad() {
        cc.director.setClearColor(new cc.Color(0,0,0, 0));
        cc.director.on('noticeShowPrefab', this.noticeShowPrefab, this);
        cc.director.on('closeExchangeSucceed', this.closeExchangeSucceedEvent, this);
        cc.director.on('reWidgetScene',this.reWidget,this);
        this.m_prefab["hall"] = null;
        this.m_bg = this.node.getChildByName("bg");
        Global.getInstance().popNode = this.node.getChildByName("popNode")
        Global.getInstance().popNode.zIndex = 999
        Global.getInstance().sceneRoot = this.node;
        this.mainCamera.backgroundColor = cc.Color.TRANSPARENT;
        // this.proLoading.node.width = cc.view.getVisibleSize().width
        // this.proLoading.totalLength = cc.view.getVisibleSize().width
        // let bar = this.proLoading.node.getChildByName("bar")
        // bar.x = -cc.view.getVisibleSize().width / 2
        
        // this.schedule(this.updateCountDown, 1)
        // this.lbMemory.node.zIndex = 9999
        if (MoreGameManager.instance().isBrowserDevices()) {
            this.node.on(cc.Node.EventType.TOUCH_START, this.mouseStart, this);
            cc.director.on("MoreGameFullScreen", this.mouseStart, this)
        }

        let height = cc.sys.getSafeAreaRect().height;
        let width = cc.sys.getSafeAreaRect().width;
        // if (cc.winSize.height/cc.winSize.width<16/9) {
        if(height/width < 14/9){
            this.node.getComponent(cc.Canvas).fitHeight = true;
            this.node.getComponent(cc.Canvas).fitWidth = false;
        } else {
            this.node.getComponent(cc.Canvas).fitHeight = false;
            this.node.getComponent(cc.Canvas).fitWidth = true;
        }
        serviceMgr.inner_ing = false;//修复一个 顶号的bug

        //预先加载21个 备用 防止主页卡顿
        poolManager.instance.prePool(this.game_hall_item,21);
    }
    //重新适配一下
    reWidget(){
        // return;
        let height = cc.sys.getSafeAreaRect().height;
        let width = cc.sys.getSafeAreaRect().width;
        // if (cc.winSize.height/cc.winSize.width<16/9) {
        if(height/width < 14/9){
            this.node.getComponent(cc.Canvas).fitHeight = true;
            this.node.getComponent(cc.Canvas).fitWidth = false;
        } else {
            this.node.getComponent(cc.Canvas).fitHeight = false;
            this.node.getComponent(cc.Canvas).fitWidth = true;
        }
        if (cc.sys.isBrowser) {
            cc.view.resizeWithBrowserSize(true);
            cc.view.setResizeCallback(() => {
                // Global.getInstance().showSimpleTip("screen size resize")
                cc.game.emit("screenResize")
            })
        }
    }
    mouseStart(event) {
        cc.director.emit("HallBgClickedEvent")
        // if (!document.fullscreenElement) {
        //     if (MoreGameManager.instance().isOpeningUI) return
        //     cc.view.enableAutoFullScreen(false)
        // }
    }
    protected start(): void {
        //加载tabbar 
    }
 
    protected onDestroy(): void {
        cc.director.off('noticeShowPrefab', this.noticeShowPrefab, this);
        cc.director.off('closeExchangeSucceed', this.closeExchangeSucceedEvent, this);
        this.node.off(cc.Node.EventType.TOUCH_START, this.mouseStart, this);
        cc.director.off("MoreGameFullScreen", this.mouseStart, this)
        cc.director.off('reWidgetScene');
    }

    // updateCountDown(dt) {
    //     let assets = cc.assetManager.assets
    //     let allMemory = 0
    //     assets.forEach((obj, key: string) => {
    //         if (obj && (obj["_native"] == ".png" || obj["_native"] == ".jpg")) {
    //             // cc.log("============>",)
    //             allMemory = allMemory + (obj.width * obj.height * 32) / 8
    //         }
    //     })
    //     let nMemory = (Math.floor(allMemory / (1024 * 1024)) + 20)
    //     let mMery = "UseMemory:" + nMemory + "MB"
    //     if (this.lbMemory.string != mMery) {
    //         cc.log("all memory ======>", mMery)
    //         this.lbMemory.string = mMery
    //     }

    // }

    noticeShowPrefab(prefabPath: string, cb?) {
        let parent = Global.getInstance().popNode
        if (!parent) return
        let gameNd = parent.getChildByName(prefabPath)
        if (gameNd) {
            return
        }
        cc.resources.load(prefabPath, (err, prefab: cc.Prefab) => {
            if (!prefab) return
            let gameNd = parent.getChildByName(prefabPath)
            if (gameNd) {
                return
            }
            if (err) return
            let ndPop = cc.instantiate(prefab)
            ndPop.name = prefabPath
            ndPop.zIndex = 999
            ndPop.parent = parent
            if (cb) cb();
        })
    }

    update(dt: number) {
        // if (this.isTransferring) return;
        // let hour = new Date(Global.getInstance().now()).getHours()
        // let tempType = hour >= 6 && hour < 18 ? E_DAY_NIGHT.DAY : E_DAY_NIGHT.NIGHT;
        // if (tempType != this._curHallType) {
        //     this._curHallType = tempType == E_DAY_NIGHT.DAY ? E_DAY_NIGHT.NIGHT : E_DAY_NIGHT.DAY;
        //     this.startTransfer()
        //     cc.director.emit("transferDayNight")
        // }
        if(Global.getInstance().loadingName){
            if(Global.getInstance().loadingName.length == 0)return;
        }
        this.popTime += dt;
        if (this.popTime > 5) {
            let n = Global.getInstance().popNode;

            if (n.childrenCount == 0) {
                // this.emittingHallEvent = true;
                this.popTime = 0
                cc.director.emit("inHallEvent")
                return;
            }
            let show = true
            n.children.forEach(nd => {
                if (nd.active) show = false
            })
            if (show) {
                // this.emittingHallEvent = true;
                this.popTime = 0
                cc.director.emit("inHallEvent")
                return;
            }

        }

        // }
    }
 
    goToGame(gameName: string, comPleteFun: Function, gameId: any = null) {
        if (Global.getInstance().getSceneId() == E_SCENE_TYPE.HALL && !!comPleteFun) {
            Global.getInstance().closeBgAudio();
            Global.getInstance().hideShowLoading();
            SocketUtils.getInstance().unregisterMessages(this.m_currentNode.getComponent(Hall));
        }
        this.loadBundle(gameName, comPleteFun, gameId);
    }

    preLoadPrefabs(cb?) {
        let prefabs = [
            UI_PATH_DIC.LoadinViewPre,
            // UI_PATH_DIC.SpinView,
            // UI_PATH_DIC.phonePasswordLogin,
            UI_PATH_DIC.PopupBanner,
            // UI_PATH_DIC.NewView,
            // UI_PATH_DIC.Promo,
            // UI_PATH_DIC.HallGameItem,
            // UI_PATH_DIC.UserProfileSetting,
        ]
        cc.resources.preload(prefabs,(finish, total, item) =>{
            this.proLoading.node.active = true;
            let rate = finish / total * 0.95;
            if (rate > this.proLoading.progress) {
                this.proLoading.progress = rate
                this.lbProgress.string = Math.floor(rate*100) + "%";
            }
        },(error, items) =>{
            if (error) {
                return;
            }
            if (cb) {
                cb();
            }
        });
        
        this.currentGameListPage = 1;
        
        // let param = "";
        // let types = Global.getInstance().getGameType();
        // if (types && types.length > 0) {
        //     param = types.map(types => types.id).join(',');
        // }
        // MoreGameManager.instance().doQueryFirstGameList(()=>{
        //     cc.director.emit("RefreshGameListThird");
        // })
        //Global.getInstance().reqBannerData();
    }

    reqNextPage() {
        let gameList = MoreGameManager.instance().getAllThirdGameData();
        if (gameList.length==this.currentGameListPage*100 && gameList.length%100==0) {
            this.currentGameListPage = this.currentGameListPage+1;
            let params = {
                token: Global.getInstance().token,
                page: this.currentGameListPage
            };
            MoreGameManager.instance().doQueryMoreGameList(()=>{
                this.reqNextPage();
            },params,true)
        } else {
            cc.director.emit("RefreshGameListThird");
        }
    }

    loadPrefab(name: string, comPleteFun: (node: cc.Node) => void = null) {
        if (this.m_prefab[name] == null) {
            let tnamear = name.split('/') 
            let tname = name;
            if(tnamear.length > 0){
                tname = tnamear[tnamear.length - 1]
            }
            cc.resources.load("prefab/" + name, cc.Prefab, (finish, total, item) => {
                this.proLoading.node.active = true;
                let rate = finish / total * 0.05;
                if (rate+0.95 > this.proLoading.progress) {
                    this.proLoading.progress = rate + 0.95;
                    this.lbProgress.string = Math.floor((rate + 0.95)*100) + "%";
                }
            }, (err, prefab: any) => {
                if (err) {
                    return;
                }
                this.node.getChildByName("layer").active = false;
                this.proLoading.node.active = false;
                this.m_prefab[tname] = prefab;
                let node = cc.instantiate(this.m_prefab[tname]);

                node.parent = this.game_node;
                node.name = tname;
                this.releaseRes();
                this.m_currentNode = node;
                // this.spSmallman.node.active = false
                if (this.m_bg) this.m_bg.active = false;
                if (comPleteFun) comPleteFun(node);
            });
        }
        else {
            let node = cc.instantiate(this.m_prefab[name]);
            node.parent = this.node;
            node.name = name;
            this.releaseRes();
            this.m_currentNode = node;
            if (comPleteFun) comPleteFun(node);
        }
    }

    loadBundle(name: string, comPleteFun: Function, gameId: any = null, isMiniGame: boolean = false) {
        var bundleName = "";
        const options = {
            version: "",
        }

        // if (cc.sys.isMobile && !cc.sys.isBrowser && Global.DEBUG != Global.DEBUG_MODE.TEST) {
        //     if ((name == "teen" || name == "andar" || name == "truco" || isMiniGame) &&
        //         ((Global.getInstance().gameVersion["remote"] && !Global.getInstance().gameVersion["remote"][name]) ||
        //             (!Global.getInstance().gameVersion["remote"] && !Global.getInstance().gameVersion["local"][name]))) {
        //         bundleName = name + 'bundle';
        //     } else {
        //         options.version = Global.getInstance().gameVersion["local"][name];
        //         bundleName = ALL_APP_SOURCE_CONFIG.app_hosts.REMOTE_URL + name + 'bundle';
        //     }
        // } else {
        bundleName = name + 'bundle';
        // }

        let bundle = cc.assetManager.getBundle(bundleName);
        if (bundle) {
            let prefabName = "";
            if (name == "carrom") {
                prefabName = name + 'prefab/' + name + gameId;
                if (!gameId) {
                    return;
                }
            } else {
                prefabName = name + 'prefab/' + name;
            }

            if (isMiniGame) {
                Global.getInstance().showLoading("enterGame");
                bundle.load(prefabName, cc.Prefab, (err, prefab: cc.Prefab) => {
                    Global.getInstance().hideShowLoading("enterGame");
                    if (!err) {
                        let node = cc.instantiate(prefab);
                        node.parent = this.node;
                        node.name = name;
                    }
                });
                return;
            }

            if (comPleteFun) {
                Global.getInstance().showLoading("enterGame");
                bundle.load(prefabName, cc.Prefab, (err, prefab: cc.Prefab) => {
                    Global.getInstance().hideShowLoading("enterGame");
                    if (!err) {
                        let node = cc.instantiate(prefab);
                        node.parent = this.game_node;
                        node.name = name;
                        this.releaseRes();
                        this.m_currentNode = node;
                        comPleteFun.call(node);
                    }
                });
            } else {
                bundle.preload(prefabName, cc.Prefab);
            }
        } else {
            Global.getInstance().showLoading("enterGame");
            cc.assetManager.loadBundle(bundleName, options,
                (err, bundle) => {
                    Global.getInstance().hideShowLoading("enterGame");
                    if (!err) {
                        let prefabName = name + 'prefab/' + name;
                        if (isMiniGame) {
                            Global.getInstance().showLoading("enterGame");
                            bundle.load(prefabName, cc.Prefab, (err, prefab: cc.Prefab) => {
                                Global.getInstance().hideShowLoading("enterGame");
                                if (!err) {
                                    let node = cc.instantiate(prefab);
                                    node.parent = this.game_node;
                                    node.name = name;
                                }
                            });
                            return;
                        }

                        if (comPleteFun) {
                            Global.getInstance().showLoading("enterGame");
                            bundle.load(prefabName, cc.Prefab, (err, prefab: cc.Prefab) => {
                                Global.getInstance().hideShowLoading("enterGame");
                                if (!err) {
                                    let node = cc.instantiate(prefab);
                                    node.parent = this.game_node;
                                    node.name = name;
                                    this.releaseRes();
                                    this.m_currentNode = node;
                                    comPleteFun.call(node);
                                }
                            });
                        } else {
                            bundle.preload(prefabName, cc.Prefab);
                        }
                    }
                });
        }
    }

    releaseRes() {
        if (this.m_currentNode) {
            let bundle = cc.assetManager.getBundle(this.m_currentNode.name + 'bundle');
            if (bundle != null) {
                // 释放所有属于 Asset Bundle 的资源
                //bundle.releaseAll();
                cc.assetManager.removeBundle(bundle);
            }

            this.m_currentNode.destroy();
            this.m_currentNode = null;
        }
    }

    closeWebView() {
        cc.director.emit("CloseMoreGameWebView")
    }

    closeExchangeSucceedEvent() {
        BetLevelManager.instance().checkBetLevelUp()
        // this.emittingHallEvent = false
    }
}
