import { UI_PATH_DIC } from "./GlobalConstant";
import Global from "./GlobalScript";
import { MayaVipManager } from "./UI/activity/ActivityMayaVip";
import UICommon from "./component/UICommon";
import { uiManager } from "./mgr/UIManager";
import HttpUtils from "./net/HttpUtils";

const { ccclass, property } = cc._decorator;

export function showMayavipApplyTip() {
    uiManager.instance.showDialog(UI_PATH_DIC.MayavipApplyTip)//未用到
}

@ccclass
export default class MayavipApplyTip extends UICommon {

    @property(cc.Node)
    ndApplay: cc.Node = null;

    @property(cc.Node)
    ndNoApplyData: cc.Node = null

    @property(cc.Node)
    ndLay: cc.Node = null

    onLoad(): void {
        this.setSys()
        this.initControlUI()
    }

    initControlUI() {
        let isHaveData = MayaVipManager.isHaveApplyData()
        this.ndApplay.active = isHaveData
        this.ndNoApplyData.active = !isHaveData
        if (isHaveData) {
            let mayaData = MayaVipManager.getMayavipData()
            let firstName = this.ndLay.getChildByName("fisrtName").getComponent(cc.Label)
            firstName.string = "First Name:" + mayaData.first_name
            let middleName = this.ndLay.getChildByName("middleName").getComponent(cc.Label)
            if (mayaData.middle_name) {
                middleName.string = "Midddle Name:" + mayaData.middle_name
            } else {
                middleName.node.active = false
            }
            let lastName = this.ndLay.getChildByName("lastName").getComponent(cc.Label)
            lastName.string = "Last Name:" + mayaData.last_name
            let mayaPhone = this.ndLay.getChildByName("mayaPhone").getComponent(cc.Label)
            mayaPhone.string = "Maya Phone Number:" + Global.getInstance().userdata.phone
        }
    }

    onDestroy(): void {

    }

    setSys() {
        this.node.zIndex = 1299;
    }

    confirmClick() {
        HttpUtils.getInstance().post(3, 3, this, "/api/mayavip/apply", {
            token: Global.getInstance().token,
        }, (response) => {
            MayaVipManager.updateAppStatus(1)
            cc.director.emit("MayaVipApplySuc")
            Global.getInstance().showSimpleTip(Global.getInstance().getLabel("php_code_102121"))
            this.closeAction()
        }, (rsp) => {
            if (rsp && rsp.code) {
                Global.getInstance().showSimpleTip(Global.getInstance().getLabel("php_code_" + rsp["code"]));
            } else {
                Global.getInstance().showSimpleTip(rsp.msg);
            }
            this.closeAction()
        })
    }

    closeAction() {
        this.node.removeFromParent(true);
        this.node.destroy();
    }

}
