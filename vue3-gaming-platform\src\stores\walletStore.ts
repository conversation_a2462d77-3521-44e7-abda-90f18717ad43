import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { 
  WalletTaskInfo, 
  WalletTaskFilter, 
  WalletTaskStats,
  TaskProgress,
  TaskCountdown,
  WalletTaskConfig,
  TaskHistory,
  TaskNotificationSettings
} from '@/types'
import { 
  WalletTaskStatus, 
  DEFAULT_WALLET_CONFIG,
  WALLET_STORAGE_KEYS 
} from '@/types'

export const useWalletStore = defineStore('wallet', () => {
  // 状态
  const tasks = ref<WalletTaskInfo[]>([])
  const taskHistory = ref<TaskHistory[]>([])
  const isLoading = ref(false)
  const showGuide = ref(false)
  const selectedTask = ref<WalletTaskInfo | null>(null)
  const filter = ref<WalletTaskFilter>({})
  const config = ref<WalletTaskConfig>(DEFAULT_WALLET_CONFIG)
  const notificationSettings = ref<TaskNotificationSettings>({
    task_unlocked: true,
    task_completed: true,
    task_expiring: true,
    reward_available: true,
    progress_milestone: true,
    new_tasks_available: true,
    sound_enabled: true,
    vibration_enabled: true,
    email_enabled: false,
    push_enabled: true
  })
  const lastRefreshTime = ref<number>(0)
  const error = ref<string>('')

  // 计算属性
  const filteredTasks = computed(() => {
    let filtered = tasks.value

    // 按状态筛选
    if (filter.value.status && filter.value.status.length > 0) {
      filtered = filtered.filter(task => 
        filter.value.status!.includes(task.task_status as WalletTaskStatus)
      )
    }

    // 按时间限制筛选
    if (filter.value.hasTimeLimit !== undefined) {
      filtered = filtered.filter(task => 
        filter.value.hasTimeLimit ? task.expire_time > 0 : task.expire_time === 0
      )
    }

    // 按游戏类型筛选
    if (filter.value.gameTypes && filter.value.gameTypes.length > 0) {
      filtered = filtered.filter(task => 
        task.game_type.some(type => filter.value.gameTypes!.includes(type))
      )
    }

    // 按厂商筛选
    if (filter.value.providers && filter.value.providers.length > 0) {
      filtered = filtered.filter(task => 
        task.provider_list.some(provider => filter.value.providers!.includes(provider))
      )
    }

    // 按奖励金额筛选
    if (filter.value.minReward !== undefined) {
      filtered = filtered.filter(task => Number(task.bonus) >= filter.value.minReward!)
    }
    if (filter.value.maxReward !== undefined) {
      filtered = filtered.filter(task => Number(task.bonus) <= filter.value.maxReward!)
    }

    // 按标签筛选
    if (filter.value.tags && filter.value.tags.length > 0) {
      filtered = filtered.filter(task => 
        task.tags?.some(tag => filter.value.tags!.includes(tag))
      )
    }

    // 排序
    if (filter.value.sortBy) {
      filtered.sort((a, b) => {
        const aValue = getTaskSortValue(a, filter.value.sortBy!)
        const bValue = getTaskSortValue(b, filter.value.sortBy!)
        
        if (filter.value.sortOrder === 'desc') {
          return bValue - aValue
        }
        return aValue - bValue
      })
    } else {
      // 默认排序：已完成 > 进行中 > 未解锁 > 已过期
      filtered.sort((a, b) => {
        const statusPriority = {
          [WalletTaskStatus.FINISHED]: 4,
          [WalletTaskStatus.ONGOING]: 3,
          [WalletTaskStatus.LOCK]: 2,
          [WalletTaskStatus.EXPIRED]: 1,
          [WalletTaskStatus.DELETE]: 0
        }
        
        const aPriority = statusPriority[a.task_status]
        const bPriority = statusPriority[b.task_status]
        
        if (aPriority !== bPriority) {
          return bPriority - aPriority
        }
        
        // 相同状态按优先级排序
        const aPriorityValue = a.priority || 0
        const bPriorityValue = b.priority || 0
        
        if (aPriorityValue !== bPriorityValue) {
          return bPriorityValue - aPriorityValue
        }
        
        // 最后按创建时间排序
        return Number(b.created_at) - Number(a.created_at)
      })
    }

    return filtered
  })

  const taskStats = computed((): WalletTaskStats => {
    const stats: WalletTaskStats = {
      total: tasks.value.length,
      locked: 0,
      ongoing: 0,
      finished: 0,
      expired: 0,
      totalRewards: 0,
      completionRate: 0,
      averageCompletionTime: 0,
      mostPopularGameType: '',
      totalBetAmount: 0,
      totalRechargeAmount: 0
    }

    const completionTimes: number[] = []
    const gameTypeCounts: Record<string, number> = {}

    tasks.value.forEach(task => {
      switch (task.task_status) {
        case WalletTaskStatus.LOCK:
          stats.locked++
          break
        case WalletTaskStatus.ONGOING:
          stats.ongoing++
          stats.totalBetAmount += Number(task.bet_num) || 0
          break
        case WalletTaskStatus.FINISHED:
          stats.finished++
          stats.totalRewards += Number(task.bonus) || 0
          stats.totalBetAmount += Number(task.bet_num) || 0
          stats.totalRechargeAmount += Number(task.recharge_target_value) || 0
          
          // 计算完成时间
          const history = taskHistory.value.find(h => h.task_id === task.id)
          if (history && history.completion_time) {
            completionTimes.push(history.completion_time)
          }
          break
        case WalletTaskStatus.EXPIRED:
          stats.expired++
          break
      }

      // 统计游戏类型
      task.game_type.forEach(type => {
        gameTypeCounts[type] = (gameTypeCounts[type] || 0) + 1
      })
    })

    // 计算完成率
    if (stats.total > 0) {
      stats.completionRate = (stats.finished / stats.total) * 100
    }

    // 计算平均完成时间
    if (completionTimes.length > 0) {
      stats.averageCompletionTime = completionTimes.reduce((a, b) => a + b, 0) / completionTimes.length
    }

    // 找出最受欢迎的游戏类型
    let maxCount = 0
    Object.entries(gameTypeCounts).forEach(([type, count]) => {
      if (count > maxCount) {
        maxCount = count
        stats.mostPopularGameType = type
      }
    })

    return stats
  })

  const hasOngoingTask = computed(() => {
    return tasks.value.some(task => task.task_status === WalletTaskStatus.ONGOING)
  })

  const availableTasks = computed(() => {
    return tasks.value.filter(task => 
      task.task_status !== WalletTaskStatus.DELETE &&
      task.task_status !== WalletTaskStatus.EXPIRED
    )
  })

  const expiringSoonTasks = computed(() => {
    const now = Date.now() / 1000
    const oneHour = 3600
    
    return tasks.value.filter(task => {
      if (task.expire_time === 0 || task.task_status !== WalletTaskStatus.ONGOING) {
        return false
      }
      
      const expireTimestamp = Number(task.created_at) + (task.expire_time * 3600)
      const timeLeft = expireTimestamp - now
      
      return timeLeft > 0 && timeLeft <= oneHour
    })
  })

  const completedTasksToday = computed(() => {
    const today = new Date().toDateString()
    return taskHistory.value.filter(history => {
      if (!history.completed_at) return false
      return new Date(history.completed_at).toDateString() === today
    }).length
  })

  // 方法
  function getTaskSortValue(task: WalletTaskInfo, sortBy: string): number {
    switch (sortBy) {
      case 'created_at':
        return Number(task.created_at)
      case 'bonus':
        return Number(task.bonus)
      case 'expire_time':
        return task.expire_time
      case 'priority':
        return task.priority || 0
      default:
        return 0
    }
  }

  function setTasks(newTasks: WalletTaskInfo[]) {
    // 过滤掉过期和删除的任务
    const validTasks = newTasks.filter(task => {
      if (task.task_status === WalletTaskStatus.DELETE) return false
      
      // 检查是否过期
      if (task.expire_time > 0) {
        const now = Date.now() / 1000
        const expireTimestamp = Number(task.created_at) + (task.expire_time * 3600)
        if (expireTimestamp <= now && task.task_status !== WalletTaskStatus.FINISHED) {
          task.task_status = WalletTaskStatus.EXPIRED
        }
      }
      
      return true
    })

    tasks.value = validTasks
    lastRefreshTime.value = Date.now()
    
    // 缓存任务数据
    cacheTasksData()
  }

  function addTask(task: WalletTaskInfo) {
    const existingIndex = tasks.value.findIndex(t => t.id === task.id)
    if (existingIndex >= 0) {
      tasks.value[existingIndex] = task
    } else {
      tasks.value.push(task)
    }
    cacheTasksData()
  }

  function removeTask(taskId: string) {
    const index = tasks.value.findIndex(task => task.id === taskId)
    if (index >= 0) {
      tasks.value.splice(index, 1)
      cacheTasksData()
    }
  }

  function updateTaskStatus(taskId: string, status: WalletTaskStatus) {
    const task = tasks.value.find(t => t.id === taskId)
    if (task) {
      const oldStatus = task.task_status
      task.task_status = status
      task.updated_at = new Date().toISOString()
      
      // 记录状态变更历史
      addTaskHistory(task, oldStatus, status)
      cacheTasksData()
    }
  }

  function updateTaskProgress(taskId: string, betNum: string, rechargeDone?: boolean) {
    const task = tasks.value.find(t => t.id === taskId)
    if (task) {
      task.bet_num = betNum
      if (rechargeDone !== undefined) {
        task.recharge_done = rechargeDone
      }
      
      // 检查是否完成
      const progress = getTaskProgress(task)
      if (progress.isCompleted && task.task_status === WalletTaskStatus.ONGOING) {
        updateTaskStatus(taskId, WalletTaskStatus.FINISHED)
        
        // 发送完成通知
        if (notificationSettings.value.task_completed) {
          sendTaskNotification('task_completed', task)
        }
      }
      
      cacheTasksData()
    }
  }

  function getTaskProgress(task: WalletTaskInfo): TaskProgress {
    const betCurrent = Number(task.bet_num) || 0
    const betTotal = task.bet_target_value || 0
    const rechargeTarget = Number(task.recharge_target_value) || 0
    
    const betProgress = betTotal > 0 ? Math.min(betCurrent / betTotal, 1) : 1
    const rechargeProgress = rechargeTarget > 0 ? (task.recharge_done ? 1 : 0) : 1
    
    const totalProgress = Math.min(betProgress, rechargeProgress)
    
    return {
      current: betCurrent,
      total: betTotal,
      percentage: totalProgress * 100,
      isCompleted: betProgress >= 1 && rechargeProgress >= 1,
      bet_progress: {
        current: betCurrent,
        target: betTotal,
        percentage: betProgress * 100
      },
      recharge_progress: {
        completed: task.recharge_done,
        target: rechargeTarget
      }
    }
  }

  function getTaskCountdown(task: WalletTaskInfo): TaskCountdown {
    if (task.expire_time === 0) {
      return {
        hours: 0,
        minutes: 0,
        seconds: 0,
        totalSeconds: 0,
        isExpired: false,
        formatted: '无限制'
      }
    }

    const now = Date.now() / 1000
    const expireTimestamp = Number(task.created_at) + (task.expire_time * 3600)
    const diffSeconds = Math.max(0, expireTimestamp - now)

    const hours = Math.floor(diffSeconds / 3600)
    const minutes = Math.floor((diffSeconds % 3600) / 60)
    const seconds = Math.floor(diffSeconds % 60)

    const formatted = hours > 0 
      ? `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
      : `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`

    return {
      hours,
      minutes,
      seconds,
      totalSeconds: diffSeconds,
      isExpired: diffSeconds <= 0,
      formatted
    }
  }

  function setFilter(newFilter: Partial<WalletTaskFilter>) {
    filter.value = { ...filter.value, ...newFilter }
    saveFilterSettings()
  }

  function clearFilter() {
    filter.value = {}
    saveFilterSettings()
  }

  function setSelectedTask(task: WalletTaskInfo | null) {
    selectedTask.value = task
  }

  function setLoading(loading: boolean) {
    isLoading.value = loading
  }

  function setError(errorMessage: string) {
    error.value = errorMessage
  }

  function clearError() {
    error.value = ''
  }

  function setShowGuide(show: boolean) {
    showGuide.value = show
    if (!show) {
      localStorage.setItem(WALLET_STORAGE_KEYS.GUIDE_SHOWN, 'true')
    }
  }

  function setConfig(newConfig: Partial<WalletTaskConfig>) {
    config.value = { ...config.value, ...newConfig }
    localStorage.setItem(WALLET_STORAGE_KEYS.USER_PREFERENCES, JSON.stringify(config.value))
  }

  function setNotificationSettings(settings: Partial<TaskNotificationSettings>) {
    notificationSettings.value = { ...notificationSettings.value, ...settings }
    localStorage.setItem('wallet_notification_settings', JSON.stringify(notificationSettings.value))
  }

  function loadConfig() {
    const saved = localStorage.getItem(WALLET_STORAGE_KEYS.USER_PREFERENCES)
    if (saved) {
      try {
        config.value = { ...DEFAULT_WALLET_CONFIG, ...JSON.parse(saved) }
      } catch (error) {
        console.error('Failed to load wallet config:', error)
      }
    }
  }

  function loadFilterSettings() {
    const saved = localStorage.getItem(WALLET_STORAGE_KEYS.FILTER_SETTINGS)
    if (saved) {
      try {
        filter.value = JSON.parse(saved)
      } catch (error) {
        console.error('Failed to load filter settings:', error)
      }
    }
  }

  function saveFilterSettings() {
    localStorage.setItem(WALLET_STORAGE_KEYS.FILTER_SETTINGS, JSON.stringify(filter.value))
  }

  function loadNotificationSettings() {
    const saved = localStorage.getItem('wallet_notification_settings')
    if (saved) {
      try {
        notificationSettings.value = { ...notificationSettings.value, ...JSON.parse(saved) }
      } catch (error) {
        console.error('Failed to load notification settings:', error)
      }
    }
  }

  function checkGuideStatus(): boolean {
    const guideShown = localStorage.getItem(WALLET_STORAGE_KEYS.GUIDE_SHOWN)
    return guideShown !== 'true' && tasks.value.length > 0
  }

  function cacheTasksData() {
    const cacheData = {
      tasks: tasks.value,
      timestamp: Date.now()
    }
    localStorage.setItem(WALLET_STORAGE_KEYS.CACHED_TASKS, JSON.stringify(cacheData))
  }

  function loadCachedTasks(): boolean {
    const cached = localStorage.getItem(WALLET_STORAGE_KEYS.CACHED_TASKS)
    if (cached) {
      try {
        const cacheData = JSON.parse(cached)
        const now = Date.now()
        
        // 检查缓存是否过期（5分钟）
        if (now - cacheData.timestamp < config.value.cacheExpiration) {
          tasks.value = cacheData.tasks || []
          return true
        }
      } catch (error) {
        console.error('Failed to load cached tasks:', error)
      }
    }
    return false
  }

  function addTaskHistory(task: WalletTaskInfo, oldStatus: WalletTaskStatus, newStatus: WalletTaskStatus) {
    const history: TaskHistory = {
      id: `${task.id}_${Date.now()}`,
      task_id: task.id,
      task_name: task.task_name,
      user_id: task.user_id,
      status: newStatus,
      started_at: oldStatus === WalletTaskStatus.LOCK && newStatus === WalletTaskStatus.ONGOING 
        ? new Date().toISOString() 
        : task.created_at,
      completed_at: newStatus === WalletTaskStatus.FINISHED ? new Date().toISOString() : undefined,
      expired_at: newStatus === WalletTaskStatus.EXPIRED ? new Date().toISOString() : undefined,
      reward_amount: Number(task.bonus) || 0,
      bet_amount: Number(task.bet_num) || 0,
      recharge_amount: Number(task.recharge_target_value) || 0
    }

    // 计算完成时间
    if (newStatus === WalletTaskStatus.FINISHED && history.started_at && history.completed_at) {
      const startTime = new Date(history.started_at).getTime()
      const endTime = new Date(history.completed_at).getTime()
      history.completion_time = Math.floor((endTime - startTime) / 1000)
    }

    taskHistory.value.push(history)
    
    // 限制历史记录数量
    if (taskHistory.value.length > 100) {
      taskHistory.value = taskHistory.value.slice(-100)
    }
  }

  function sendTaskNotification(type: string, task: WalletTaskInfo) {
    // 这里可以集成实际的通知系统
    console.log(`Task notification: ${type}`, task)
  }

  function formatTaskTitle(title: string): string {
    if (title.length > 30) {
      return title.substring(0, 27) + '...'
    }
    return title
  }

  function formatNumber(num: number | string): string {
    const value = typeof num === 'string' ? parseFloat(num) : num
    return value.toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    })
  }

  // 初始化
  function initialize() {
    loadConfig()
    loadFilterSettings()
    loadNotificationSettings()
    
    // 尝试加载缓存的任务数据
    const hasCachedData = loadCachedTasks()
    
    // 检查是否需要显示引导
    showGuide.value = checkGuideStatus()
    
    return hasCachedData
  }

  return {
    // 状态
    tasks,
    taskHistory,
    isLoading,
    showGuide,
    selectedTask,
    filter,
    config,
    notificationSettings,
    lastRefreshTime,
    error,

    // 计算属性
    filteredTasks,
    taskStats,
    hasOngoingTask,
    availableTasks,
    expiringSoonTasks,
    completedTasksToday,

    // 方法
    setTasks,
    addTask,
    removeTask,
    updateTaskStatus,
    updateTaskProgress,
    getTaskProgress,
    getTaskCountdown,
    setFilter,
    clearFilter,
    setSelectedTask,
    setLoading,
    setError,
    clearError,
    setShowGuide,
    setConfig,
    setNotificationSettings,
    loadConfig,
    loadFilterSettings,
    saveFilterSettings,
    loadNotificationSettings,
    checkGuideStatus,
    cacheTasksData,
    loadCachedTasks,
    formatTaskTitle,
    formatNumber,
    initialize
  }
})
