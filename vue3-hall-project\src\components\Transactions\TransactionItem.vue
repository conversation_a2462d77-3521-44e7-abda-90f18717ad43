<template>
  <div class="transaction-item" @click="handleClick">
    <div class="item-icon">
      <i :class="getIconClass()"></i>
    </div>
    
    <div class="item-content">
      <div class="item-header">
        <h4 class="item-title">{{ getTitle() }}</h4>
        <span class="item-amount" :class="getAmountClass()">
          {{ getFormattedAmount() }}
        </span>
      </div>
      
      <div class="item-details">
        <span class="item-time">{{ getFormattedTime() }}</span>
        <span class="item-status" :class="getStatusClass()">
          {{ getStatusText() }}
        </span>
      </div>
      
      <div v-if="showExtraInfo()" class="item-extra">
        <span class="extra-info">{{ getExtraInfo() }}</span>
      </div>
    </div>
    
    <div class="item-arrow">
      <i class="icon-chevron-right"></i>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { TransactionRecord } from '@/types/hall'
import { 
  RECORD_TYPE, 
  RechargeWebStatus, 
  WithdrawStatus, 
  AwardUpdateType 
} from '@/types/hall'

interface Props {
  record: TransactionRecord
  type: string
}

interface Emits {
  (e: 'click', record: TransactionRecord): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 计算属性
const goldRatio = 100 // 金币比例

// 方法
function handleClick() {
  emit('click', props.record)
}

function getIconClass(): string {
  switch (props.type) {
    case 'deposit':
      if (props.record.pay_channel === RECORD_TYPE.ADJUSTMENT) {
        return 'icon-transfer'
      }
      return 'icon-deposit'
    case 'withdraw':
      return 'icon-withdraw'
    case 'award':
      return 'icon-gift'
    default:
      return 'icon-transaction'
  }
}

function getTitle(): string {
  switch (props.type) {
    case 'deposit':
      if (props.record.pay_channel === RECORD_TYPE.ADJUSTMENT) {
        return '余额调整'
      } else if (props.record.pay_channel === RECORD_TYPE.MAYAPAY) {
        return 'Maya支付'
      } else if (props.record.pay_channel === RECORD_TYPE.MAYA_WEB) {
        return 'Maya网页支付'
      } else if (props.record.pay_channel === RECORD_TYPE.GCASH_WEB) {
        return 'GCash网页支付'
      }
      return '充值'
    case 'withdraw':
      if (props.record.quantity && props.record.quantity > 1) {
        return '批量提现'
      }
      return '提现'
    case 'award':
      return getAwardTitle()
    default:
      return '交易记录'
  }
}

function getAwardTitle(): string {
  const updateType = props.record.update_type
  switch (updateType) {
    case AwardUpdateType.FREE_REGISTRATION:
      return '注册奖励'
    case AwardUpdateType.FIRST_RECHARGE:
      return '首充奖励'
    case AwardUpdateType.CASHBACK:
      return '返水奖励'
    case AwardUpdateType.VIP_CASHBACK_119:
      return 'VIP返水'
    case AwardUpdateType.BING_PHONE:
      return '绑定手机奖励'
    case AwardUpdateType.SIGN_UP_BONUS:
      return '注册奖金'
    case AwardUpdateType.WEEKLY_SIGNIN:
      return '每周签到'
    case AwardUpdateType.DAILY_BETTING:
      return '每日投注返利'
    default:
      return '奖励'
  }
}

function getFormattedAmount(): string {
  const amount = props.record.amount / goldRatio
  const prefix = amount >= 0 ? '+' : ''
  return `${prefix}₱${Math.abs(amount).toLocaleString('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })}`
}

function getAmountClass(): string {
  return props.record.amount >= 0 ? 'positive' : 'negative'
}

function getFormattedTime(): string {
  const date = new Date(props.record.created_at)
  return date.toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

function getStatusText(): string {
  const status = props.record.status
  
  switch (props.type) {
    case 'deposit':
      return getDepositStatusText(status)
    case 'withdraw':
      return getWithdrawStatusText(status)
    case 'award':
      return '已发放'
    default:
      return '未知状态'
  }
}

function getDepositStatusText(status: number): string {
  switch (status) {
    case RechargeWebStatus.SUCCESS:
      return '成功'
    case RechargeWebStatus.PENDING:
      return '等待中'
    case RechargeWebStatus.FAILED:
      return '失败'
    case RechargeWebStatus.CANCEL:
      return '已取消'
    case RechargeWebStatus.WAITING:
      return '等待处理'
    case RechargeWebStatus.OPERATION:
      return '人工处理'
    case RechargeWebStatus.WAITING_PAYMENT:
      return '等待支付'
    case RechargeWebStatus.WAITING_CHANGE_BALANCE:
      return '等待余额变更'
    default:
      return '未知状态'
  }
}

function getWithdrawStatusText(status: number): string {
  switch (status) {
    case WithdrawStatus.PENDING:
      return '待到账'
    case WithdrawStatus.SUCCESS:
      return '成功'
    case WithdrawStatus.FAILURE:
      return '失败'
    case WithdrawStatus.OPERATIONAL:
      return '人工审核'
    case WithdrawStatus.PENDING_APPROVAL:
      return '待审核'
    default:
      return '未知状态'
  }
}

function getStatusClass(): string {
  const status = props.record.status
  
  switch (props.type) {
    case 'deposit':
      if (status === RechargeWebStatus.SUCCESS) return 'success'
      if (status === RechargeWebStatus.FAILED || status === RechargeWebStatus.CANCEL) return 'error'
      return 'pending'
    case 'withdraw':
      if (status === WithdrawStatus.SUCCESS) return 'success'
      if (status === WithdrawStatus.FAILURE) return 'error'
      return 'pending'
    case 'award':
      return 'success'
    default:
      return 'pending'
  }
}

function showExtraInfo(): boolean {
  return props.type === 'withdraw' && props.record.quantity && props.record.quantity > 1
}

function getExtraInfo(): string {
  if (props.type === 'withdraw' && props.record.quantity) {
    return `共${props.record.quantity}笔提现`
  }
  return ''
}
</script>

<style scoped>
.transaction-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.transaction-item:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.item-icon {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: #4CAF50;
}

.item-content {
  flex: 1;
  min-width: 0;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 4px;
}

.item-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: white;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.item-amount {
  font-size: 16px;
  font-weight: bold;
  white-space: nowrap;
}

.item-amount.positive {
  color: #4CAF50;
}

.item-amount.negative {
  color: #FF6B6B;
}

.item-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.item-time {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

.item-status {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
}

.item-status.success {
  background: rgba(76, 175, 80, 0.2);
  color: #4CAF50;
}

.item-status.error {
  background: rgba(255, 107, 107, 0.2);
  color: #FF6B6B;
}

.item-status.pending {
  background: rgba(255, 193, 7, 0.2);
  color: #FFC107;
}

.item-extra {
  margin-top: 4px;
}

.extra-info {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.5);
  font-style: italic;
}

.item-arrow {
  flex-shrink: 0;
  color: rgba(255, 255, 255, 0.4);
  font-size: 14px;
  transition: transform 0.3s ease;
}

.transaction-item:hover .item-arrow {
  transform: translateX(2px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .transaction-item {
    padding: 12px;
    gap: 10px;
  }
  
  .item-icon {
    width: 36px;
    height: 36px;
    font-size: 16px;
  }
  
  .item-title {
    font-size: 14px;
  }
  
  .item-amount {
    font-size: 14px;
  }
  
  .item-time, .item-status {
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .item-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .item-details {
    flex-direction: column;
    align-items: flex-start;
    gap: 2px;
  }
}
</style>
