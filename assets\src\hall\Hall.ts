import Global from "../GlobalScript";
import GameControl from "../GameControl";
import HttpUtils from "../net/HttpUtils";
import { SocketUtils } from "../net/SocketUtils";
import { ALL_APP_SOURCE_CONFIG } from "../Config";
import utils from "../utils/utils";
import { CHANEL_PARAM, DEEP_INDEXZ, E_CHANEL_TYPE, E_GAME_TYPE, E_PAGE_TYPE, EVENT, GOLD_RATIO, GOLDANI_TYPE, JUMP_TYPE, MAINTENANCETIPCODE, OPEN_BROADCAST_TYPE } from "../GlobalConstant";
import { E_SCENE_TYPE, UI_PATH_DIC } from "../GlobalConstant";
import HallGameItem from "../HallGameItem";
import MoreGameManager from "../MoreGameManager";
import BetLevelManager from "./BetLevelManager";
import { uiManager } from "../mgr/UIManager";
import AllGameView from "./AllGameView";
import Banners from "../hall/Banners"
import { GlobalEnum } from "../room/GlobalEnum";
import RoundRectMask from "../RoundRectMask";
import { INGSME_TYPE, KycMgr } from "../KYC/KycMgr";
import { SERVICE_TYPE, serviceMgr } from "../mgr/serviceMgr";
import { poolManager } from "../mgr/poolManager";
import { AutoPopMgr } from "../mgr/AutoPopMgr";
import ScrollNumber from "../component/ScrollNumber";
import LiveGame from "../player/LiveGame";
import Resource = require("../Resource");
import GoldAni from "../room/GoldAni";
import CoinRewardEffect from "../component/CoinRewardEffect";
import HttpProxy from "../net/HttpProxy";
import { GameData } from "../data/GameData";
import EventComponent from "../customComponent/EventComponent";
import { NetRspObject, NetRspMsg } from "../net/Http";
import { DownloadTipInfo } from "../data/GameDataConst";
export interface MessageImpl {
    protoIds(): number[];
    protoEncode(protoid: number, data: Object): Uint8Array;
    protoDecode(protoid: number, buffer: Uint8Array): Object;
    onProtoMessage(message: Object): void;
}

export interface HallGame_Icon {
    company_id: number;
    content: string;
    game_id: string | number;
    game_name: string;
    game_type: number; //0代表没有房间的  1带边有房间列表的 2 代表分类
    group: number;
    icon_url: "" //图片的远程地址
    id: number;
    is_animation: false  //true 是动画 false 是静态图
    order_num: number;
    rule: string;
    status: number;
}
//用于保存所有 游戏配置信息
interface config_type {
    pos: cc.Vec2,
    k: number,
    index: number
}
//用于保存所有 游戏配置信息
interface config_k {
    node: any,
    iindex: number,//滑动到的index
    type: string,//标识 poker 这种
}

const { ccclass, property } = cc._decorator;

enum LIVE_TAG {
    LIVE1,
    LIVE2,
    LIVE3
}

export enum LOGIN_WAY {
    PhoneCode,
    Password
}

@ccclass
export default class Hall extends EventComponent implements MessageImpl {
    @property(cc.Node)
    topBalanceNode: cc.Node = null;

    @property(cc.Node)
    loginNode: cc.Node = null;


    @property(cc.ScrollView)
    parentScrollView: cc.ScrollView = null;

    @property(cc.Node)
    gameScrollView: cc.Node = null;

    //侧边栏 list
    @property(cc.Node)
    gameTypeListNode: cc.Node = null;

    @property(cc.Node)
    gameTypeNodeTemp: cc.Node = null;

    @property(cc.PageView)
    bannerPageView: cc.PageView = null;

    @property(cc.Label)
    m_moneny: cc.Label = null;

    @property(cc.Node)
    btnInbox: cc.Node = null;

    //客服按钮
    @property(cc.Node)
    btnCoustom: cc.Node = null;

    @property(cc.Node)
    typeAnimBg: cc.Node = null;

    @property(cc.Node)
    policyNode: cc.Node = null;

    //新手任务按钮
    @property(cc.Node)
    promoTaskNode: cc.Node = null;


    //预留的视频区域的node
    @property(cc.Node)
    livePageView: cc.Node = null;
    @property(cc.Node)
    liveNode: cc.Node = null;

    @property(cc.Prefab)
    m_goldani_prefab: cc.Prefab = null;

    @property(cc.Node)
    goldAniTarget: cc.Node = null;

    @property(cc.Node)
    walletTaskBtn: cc.Node = null;

    @property(cc.Node)
    downloadTipNode: cc.Node = null;

    //3张视频 预览图 缓存使用 最多3张
    vedio3SpriteFrames: cc.SpriteFrame[] = [null, null, null];

    itemPrefab = null;
    currentGameType = null;
    gameListByType: [] = [];
    scorllOffSetsList: [] = [];
    scorllEndOffSetsList: [] = [];

    mNoticePrefab: cc.Prefab = null;
    mPopBanner: cc.Prefab = null;
    isLoadComplete = [false, false, false];//这个是 视频的是否加载完成 
    mBroadCastPrefab: cc.Prefab = null;
    mBroadCastView: cc.Node = null;



    lastTimeGetInboxMsg = 0;
    currentGameListPage = 1;
    gameScHeight = 0;
    GAME_TAGS = {
        "REGULAR": 0,
        "HOT": 1,
        "NEW": 2
    }
    ICON_STATUS = {
        NORMAL: 1,
        MAINTENANCE: 2
    }

    isScrolling = false;//标记变量，表示是否在处理滑动
    startPos = null;

    /**刷新时间，单位s */
    private updateTimer: number = 0;
    /**刷新间隔，单位s */
    private updateInterval: number = 0.1;
    /**是否滚动容器 */
    private bScrolling: boolean = false;
    /**是否滚动容器左边大分类滚动 */
    private bScroll_ani: boolean = false;
    /**存放列表项实例的数组 */
    private itemList: Array<cc.Node> = [];
    //======================= 计算参数 ==========================
    /**上一次content的Y值，用于和现在content的Y值比较，得出是向上还是向下滚动 */
    private lastContentPosY: number = 0;
    //所有游戏icon 对应的位置
    private all_configs: config_type[] = []
    //所有大分类游戏节点
    private all_item_nodes: config_k[] = []

    //broadmsgData
    broadmsgData = null;//保存待播放的跑马灯数据

    gameScrollOffset = 0;
    lastReshLocation = 0;
    scrollGameList = false;
    lastReqTime = 0;//间隔请求
    lastReqSpinInfoTime = 0;//间隔请求
    lastReqBanlanceTime = 0;//间隔请求

    isDragging = false;
    draggeoffset = null;
    _longPressTimer = null;
    originPosDragY = null;
    // dragBtnNames = ["promoTaskNode","btn_spin"]
    dragBtnNames = ["btn_spin", "btn_envelope"]

    get gameData() { return GameData.instance; }

    onLoad() {
        super.onLoad();

        let gamescene = cc.director.getScene().getComponentInChildren(GameControl);
        gamescene.mHall = this;
        // cc.director.setClearColor(new cc.Color(0,0,0,0));
        cc.Camera.main.backgroundColor = cc.color(0, 0, 0, 0);
        Global.getInstance().prefabNode = this.node;
        this.currentGameType = "Casino";
        cc.director.on('ShowMoreGameEvent', this.ShowMoreGameEvent, this);
        cc.director.on('CloseMoreGameEvent', this.CloseMoreGameEvent, this);
        cc.director.on("inHallEvent", this.onInHallEvent, this)
        cc.director.on("showAllGameSearchView", this.showAllGamesSerchView, this)
        cc.director.on("Promo_ShowAllGameSearchView", this.Promo_ShowAllGameSearchView, this)
        cc.director.on("RefreshGameListThird", this.RefreshGameListThird, this)
        cc.director.on("update_gold", this.updateUserInfo, this);
        cc.director.on("update_home_widget", this.updateWidget, this);
        cc.director.on("ReturnToHome2", this.showLiveVideo, this);
        cc.director.on("hidelivevideo", this.hideLiveVideo, this);
        cc.director.on("hideHallLive", this.hideHallLive, this);
        //注册一下 检查kyc 状态
        cc.director.on("checkKycState_login", this.checkKycState_login, this);
        cc.director.on("update_spin_time", this.getSpinInfo, this);
        //检查是否弹出 转盘
        cc.director.on("check_show_spin", this.popTargetView, this);

        cc.director.on("showGoldAni", this.showGoldAni, this);

        if (cc.sys.isBrowser) {
            let canvas = document.getElementById('GameCanvas');
            canvas.addEventListener("webglcontextlost", function (event) {
                event.preventDefault();
                console.log("WebGL context lost?");
                setTimeout(() => {
                    location.reload(); // 刷新页面
                }, 100);
            });
        }


        Global.getInstance().hadLogined = true
        if (!Global.getInstance().token) Global.getInstance().hadLogined = false;
        if (cc.sys.isBrowser) {
            // 监听 window.onpopstate 事件
            window.addEventListener('popstate', function (event) {
                // Global.getInstance().showSimpleTip("popstate Event")
                if (cc.sys.OS_IOS === cc.sys.os) {
                    if (Global.getInstance().mayaMode) {
                        window.location.assign(window.location.href)
                    }
                }
            });
        }

        this.parentScrollView.node.on(cc.Node.EventType.TOUCH_MOVE, this.onInnerScroll, this);
        // this.gameScrollView.node.on(cc.Node.EventType.TOUCH_MOVE, this.onInnerScroll, this);
        this.parentScrollView.node.on("scrolling", this._event_update_opacity, this);
        this.parentScrollView.content.on(cc.Node.EventType.CHILD_REMOVED, this._event_update_opacity, this);
        this.parentScrollView.content.on(cc.Node.EventType.CHILD_REORDER, this._event_update_opacity, this);
        //判定token存在
        if (!!Global.getInstance().token) {
            this.loginNode.active = false;
            this.topBalanceNode.active = true;
            let userId = Global.getInstance().userdata.user_id
            Global.getInstance().setStoreageData(Global.GLOBAL_STORAGE_KEY.LASTLOGINUSERID, userId);
            Global.getInstance().updateBalanceAfterGame()
        } else {//token没有 用户未登录
            this.topBalanceNode.active = false;
            this.loginNode.active = true;
        }

        this.parentScrollView.content.height = this.gameScrollView.parent.height + this.gameScrollOffset + 20;
        cc.game.on("screenResize", () => {
            this.resize()
        })
        this.policyNode.opacity = 1;//防止隐私区域提前显示
        this.init21_gameitems();
        this.reload_friends_icon();//先刷新一下 底部
        //提前第一次获取 维护 隐藏 游戏列表
        this.refreshGamelistPer30(true)


        // this.addButtonDragListener(this.promoTaskNode); 
        this.initSpin()
        this.initEnvelope()

        this.initDefaultUI();
    }

    initDefaultUI() {
        this.walletTaskBtn.getChildByName("nd_wallet_tip").active = false;

        this.updateDownloadTip();
    }

    addEvents(): void {
        this.addEvent(EVENT.UPDATE_WALLETTASKINFO, this.updateWalletTaskBtnInfo);
        this.addEvent(EVENT.UPDATE_DOWNLOAD_TIP, this.updateDownloadTip);
    }

    addButtonDragListener(node) {
        node.on(cc.Node.EventType.TOUCH_START, this.onDraggableTouchStart, this);
        node.on(cc.Node.EventType.TOUCH_MOVE, this.onDraggableTouchMove, this);
        node.on(cc.Node.EventType.TOUCH_END, this.onDraggableTouchEnd, this);
        node.on(cc.Node.EventType.TOUCH_CANCEL, this.onDraggableTouchEnd, this);
    }

    onDraggableTouchStart(event: cc.Event.EventTouch) {
        this.originPosDragY = event.target.y;
        this.isDragging = false;
        let touchLoc = event.getLocation();
        let nodeLoc = event.target.convertToNodeSpaceAR(touchLoc);
        this.draggeoffset = nodeLoc;
        this._longPressTimer = Global.getInstance().now();
    }

    onDraggableTouchMove(event: cc.Event.EventTouch) {
        if (this.isDragging) {
            let touched = event.getLocation();
            let touchLoc = new cc.Vec3(touched.x, touched.y)
            let nodeLoc = event.target.parent.convertToNodeSpaceAR(touchLoc);
            event.target.position = nodeLoc.sub(this.draggeoffset);
        } else if (this._longPressTimer && (Global.getInstance().now() - this._longPressTimer >= 100)) {
            this.isDragging = true;
        }
        event.target.y = Math.min(cc.winSize.height / 2 - 10, Math.max(-cc.winSize.height / 2 + 200, event.target.y));
    }

    onDraggableTouchEnd(event: cc.Event.EventTouch) {
        if (this.isDragging) {
            this.isDragging = false;
        } else {
            this.hideLiveVideo();
            let btn_spin = Global.getInstance().popNode.getChildByName("btn_spin");
            if (btn_spin) btn_spin.zIndex = -1;
            let btn_envelope = Global.getInstance().popNode.getChildByName("btn_envelope");
            if (btn_envelope) btn_envelope.zIndex = -1;
            switch (event.target.name) {
                case "promoTaskNode":
                    uiManager.instance.showDialog(UI_PATH_DIC.Task, null, null, DEEP_INDEXZ.TASK);//任务
                    break;
                case "btn_spin":
                    this.showSpinView();
                    break;
                case "btn_envelope":
                    //首存红包弹窗
                    uiManager.instance.showDialog(UI_PATH_DIC.Envelope, null, null, DEEP_INDEXZ.ENVELOPE);
                    break;
                default:
                    break;
            }
        }
        this._longPressTimer = null;

        let x = event.target.position.x;
        let y = event.target.position.y;
        if (y > (this.node.height / 2 - event.target.height / 2)) {
            y = this.node.height / 2 - event.target.height / 2;
        } else if (y < (-this.node.height / 2 + event.target.height / 2 + 170)) {
            y = -this.node.height / 2 + event.target.height / 2 + 170;
        }

        if (x >= 0) {
            x = this.parentScrollView.node.width / 2 - event.target.width / 2;
        } else if (x < 0) {
            x = event.target.width / 2 - this.parentScrollView.node.width / 2;
        }


        let compareArr = [];
        for (let index = 0; index < this.dragBtnNames.length; index++) {
            let element = this.dragBtnNames[index];
            if (element != event.target.name) {
                compareArr.push(element);
            }
        }
        compareArr.sort((a, b) => {
            let node1 = this.node.getChildByName(a)
            let node2 = this.node.getChildByName(b)
            if (a == "btn_spin" || a == "btn_envelope") {
                node1 = Global.getInstance().popNode.getChildByName(a)
            }
            if (b == "btn_spin" || b == "btn_envelope") {
                node2 = Global.getInstance().popNode.getChildByName(b)
            }
            if (!node1 || !node2) {
                return;
            }
            return node1.y - node2.y;
        })
        let toPosY = event.target.y;
        let bigerz = false;
        if (toPosY > 0) {
            bigerz = true;
            compareArr = compareArr.reverse();
        }
        for (let index = 0; index < compareArr.length; index++) {
            let element = compareArr[index];
            if (element != event.target.name) {
                let temp = this.node.getChildByName(element);
                if (element == "btn_spin" || element == "btn_envelope") {
                    temp = Global.getInstance().popNode.getChildByName(element);
                }
                if (temp && temp.isValid && toPosY < (temp.y + temp.height / 2 + 40)
                    && toPosY > (temp.y - temp.height / 2 - 40) && ((temp.x > 0 && event.target.x > 0) || (temp.x < 0 && event.target.x < 0))) {
                    if (bigerz) {
                        toPosY = temp.y - temp.height / 2 - 50;
                    } else {
                        toPosY = temp.y + temp.height / 2 + 50;
                    }
                }
            }
        }

        y = toPosY;
        event.target.position = new cc.Vec3(x, y);
    }
    //重置一下底部 友商icon显示区域
    reload_friends_icon() {
        let self = this;
        let icons_parent = this.policyNode.getChildByName('content').getChildByName('all_icons');
        let icon_module = icons_parent.getChildByName('pm')
        icon_module.active = false;
        Global.getInstance().getGameProvider((provider: any) => {
            let providerItemsList = provider;
            for (let index = 0; index < providerItemsList.length; index++) {
                let element = providerItemsList[index];
                // {"id": 3,"provider": "Evo","short_name": "","icon_home": "images\/daef73b18c6e52061a2c614ee0a3f9fb.png",
                //     "icon_other": "images\/63d11ff4929c045a20228906748c0caa.png",
                //     "game_type": [ "10001","10004","10006"],"status": 1,"content": ""},
                if (element.status != 3) {
                    let csprite = cc.instantiate(icon_module);
                    csprite.parent = icons_parent;
                    csprite.active = true;
                    this.load_provider_icon(csprite, element.id, element.short_name, element.icon_home);
                }
            }
        });
    }
    reset_friends_rect() {
        let self = this;
        setTimeout(() => {
            let theight = self.policyNode.getChildByName('content').height;
            self.policyNode.height = theight;
            let masknode = self.policyNode.getChildByName('white_bg')
            masknode.height = theight;
            let roundsrc = masknode.getComponent(RoundRectMask)
            roundsrc.radius = 55;

            //每刷一次在这里重置一下 防止底部跑偏
            self.gameScrollOffset = Math.abs(self.gameScrollView.parent.y);
            self.parentScrollView.content.height = self.gameScrollView.parent.height + self.gameScrollOffset;
            self.policyNode.y = -self.gameScrollView.parent.height + self.policyNode.height / 2.0 + 200;
        }, 100);
    }
    //从本地动态读取 游戏厂商icon
    load_provider_icon(sprite_cs, csid = 'no', short = '', icon = '') {
        let src_pre = 'MoreGameIcons/provider_home'
        let localFilePath = 'p' + csid
        //优先加载本地的,本地没有从网络下载
        Resource.loadSpriteFrame(src_pre, localFilePath, (spriteFrame: cc.SpriteFrame) => {
            if (!cc.isValid(sprite_cs)) return
            if (spriteFrame) {
                sprite_cs.getChildByName('sp').getComponent(cc.Sprite).spriteFrame = spriteFrame
            } else {
                sprite_cs.getChildByName('name').getComponent(cc.Label).string = short;
            }
            let image = icon;
            if (image != "") {
                if (!/^http/.test(image)) {
                    image = ALL_APP_SOURCE_CONFIG.remote_assets_prod[Global.DEBUG] + image;
                }
                Global.getInstance().download_img(image, sprite_cs.getChildByName('sp').getComponent(cc.Sprite), (isture) => {
                    this.reset_roundrecd(sprite_cs)
                    if (isture) sprite_cs.getChildByName('name').getComponent(cc.Label).string = '';
                    this.reset_friends_rect()
                })
            }
        })
    }
    //下一帧重置圆角
    reset_roundrecd(sp) {
        if (sp && cc.isValid(sp)) {
            let parent_sp = sp;
            setTimeout(() => {
                let prsrc = parent_sp.getComponent(RoundRectMask)
                prsrc.radius = 18;
            }, 10);
        }
    }
    //初始化21个游戏icon
    init21_gameitems() {
        for (let index = 0; index < 21; index++) {
            let cb = () => {
                let node = poolManager.instance.getNode(this.itemPrefab, this.gameScrollView)
                node.position = cc.v2(0, -1000000);
                let moreGameItem = node.getComponent(HallGameItem);
                if (moreGameItem) {
                    moreGameItem.reset()
                    //把这21个 加入到数组 以后滑动只用到这21个相互变换位置
                    this.itemList[this.itemList.length] = moreGameItem;
                }
            };
            this.loadGameItemPrefab(cb)
        }

    }
    //30秒更新一次 是否维护状态 是否隐藏状态 统一使用一个接口
    refreshGamelistPer30(isfirst = false) {
        // if(true)return;
        let isload = false;
        HttpUtils.getInstance().post(1, 3, this, "/vdr/api/maintenance/game/list", { is_new: 1 }, (response) => {
            let ac_data = response?.data?.maintenance
            if (ac_data && ac_data.length > 0) {
                Global.getInstance().all_maintain_game_list = ac_data;
            } else {
                Global.getInstance().all_maintain_game_list = []
            }
            let hide_data = response?.data?.hide
            let is_same = utils.compare2arrayEqual(hide_data, Global.getInstance().all_hide_game_list)
            if (hide_data && hide_data.length > 0) {
                Global.getInstance().all_hide_game_list = hide_data;
            } else {
                Global.getInstance().all_hide_game_list = []
            }
            //第一次 刷新 维护 隐藏接口 不用执行对应逻辑 目前还没显示出来
            if (!isfirst) {
                cc.director.emit('update_maintenance')
                if (!is_same) {
                    //不同的时候才触发 重新排序 游戏列表 隐藏需要隐藏的
                    let gameList = MoreGameManager.instance().getFirstThirdGameData();
                    this.refreshGameList(gameList);
                    cc.director.emit('refresh_gamelist');//这里刷新一下 更多游戏
                }
            } else {
                isload = true;
                this.getThridGameList();
            }
        }, function (response) {
            //修复bug 如果第一次没有加载出来 数据 则自动加载所有游戏列表
            if (isfirst) {
                isload = true;
                this.getThridGameList();
            }
        });
        if (isfirst) {
            //设置3秒超时 防止上面接口 没刷新出来 不加载游戏列表
            let self = this;
            setTimeout(() => {
                if (!isload) {
                    self.getThridGameList();
                }
            }, 3000);
            setTimeout(() => {
                self.show_refresh_view();
            }, 6000);
        }
        let self = this;
        setTimeout(() => {
            self.refreshGamelistPer30()
        }, 30000);
    }
    //显示 刷新按钮
    show_refresh_view() {
        let gameList = MoreGameManager.instance().getFirstThirdGameData();

        //只有没加载出来的时候 才显示刷新
        let refresh_btn = this.gameScrollView.getChildByName('btn_refresh');
        if (gameList.length == 0) {
            if (refresh_btn && refresh_btn.parent) {
                refresh_btn.active = true;
            }
            return true;
        } else {
            if (refresh_btn && refresh_btn.parent) {
                refresh_btn.active = false;
            }
        }
        return false;
    }
    //添加 刷新按钮 如果游戏列表 超时 加载不出来的时候显示 
    tap_refresh_gamelist() {
        let self = this;
        Global.getInstance().showLoading('tap_refresh_gamelist');
        setTimeout(() => {
            Global.getInstance().hideShowLoading('tap_refresh_gamelist');
            self.getThridGameList();
        }, 1000);
    }
    /* ***************自定义事件*************** */
    private _event_update_opacity(): void {
        this.bScrolling = true;
    }
    getThridGameList() {
        let firstData = MoreGameManager.instance().getFirstThirdGameData();
        if (!firstData || firstData.length == 0) {
            let param = "";
            let types = Global.getInstance().getGameType();
            if (types && types.length > 0) {
                param = types.map(types => types.id).join(',');
            }
            // MoreGameManager.instance().doQueryGameListByTag(()=>{
            //     let gameList = MoreGameManager.instance().getAllThirdGameData();
            //     this.refreshGameList(gameList);
            // },param)
            MoreGameManager.instance().doQueryFirstGameList(() => {
                let gameList = MoreGameManager.instance().getFirstThirdGameData();
                this.refreshGameList(gameList);
            })
        } else {
            this.refreshGameList(firstData);
        }
    }

    /**点击Register/Login按钮*/
    onClickLoginBtn() {
        uiManager.instance.showDialog(UI_PATH_DIC.phonePasswordLogin, [{ way: LOGIN_WAY.PhoneCode, curPromoPage: false }])
    }

    /**
     * 大厅新增搜索按钮
     */
    onClickSearchBtn() {
        MoreGameManager.instance().queryHistoryGameList(() => {
            //记录用户最后一次打开的游戏 跳转至最后一次打开的游戏类型的all games页面
            let historyList = MoreGameManager.instance().getHistoruGameData();
            if (historyList && historyList.length > 0) {
                let gameId = historyList[0].game_id;
                let gameType = Global.getInstance().getGameType();
                for (let i = 0; i < gameType.length; i++) {
                    if (gameId == gameType[i].id) {
                        let game_type = gameType[i].game_type;
                        let type = game_type.toLowerCase();
                        let game_tpye = type.charAt(0).toUpperCase() + type.slice(1)
                        cc.director.emit("showAllGameSearchView", game_tpye);
                    }
                }
            } else {
                //交互 点击跳转到 All game页面，默认为casino
                cc.director.emit("showAllGameSearchView", "Casino");
            }
        });

    }

    onTouchLiveStart(event) {
        if (!this.isScrolling) {
            this.startPos = event.getLocation();//记录初始位置
        }
    }

    onTouchLiveEnd(event) {
        this.startPos = null;//重置初始位置
    }

    resize() {
        if (!this.node || !cc.isValid(this.node)) return
        this.node.getComponent(cc.Widget).updateAlignment();
        this.node.children.forEach(node => {
            node.getComponent(cc.Widget)?.updateAlignment();
        });
        this.parentScrollView.content.height = this.gameScrollView.parent.height + this.gameScrollOffset + 20;
    }

    start() {
        let self = this;
        setTimeout(() => {
            self.get_all_activity_bonus();
        }, 100);

        setTimeout(() => {
            self.reqBanners();
            self.getRankPopData();
            self.getRankJiliPopData();
            // //LiveGame视频区
            //加载视频数据
            let script = self.liveNode.getComponent(LiveGame);
            script.initData();
            self.getSpinInfo()
            let gamescene = cc.director.getScene().getComponentInChildren(GameControl);
            gamescene.mTabbar.show_spin_auto = true;//防止 第一次弹出
            self.getEnvelopeInfo();
        }, 500);
        this.updateWidget();
        this.reqCashbackYesterday();

        if (cc.sys.isBrowser) {
            let htmlButton = document.getElementById('backButton');
            if (htmlButton) {
                htmlButton.remove();
            }
            let serviceButton = document.getElementById('serviceButton');
            if (serviceButton) {
                serviceButton.remove();
            }
        }

        Global.getInstance().setSceneId(E_SCENE_TYPE.HALL);
        Global.getInstance().setPageId(E_PAGE_TYPE.HALL);

        AutoPopMgr.autoPopupDialog();//这里是21岁弹窗 马上执行 kyc马上执行
        cc.director.emit('change_tabbar_state');//加载出来之后再刷新tabbar状态
        Global.getInstance().start_get_livedata();
    }

    update(dt) {
        let scrolY = utils.getNodeWorldPosition(this.gameScrollView).y
        if (scrolY > 1700 / 1920 * cc.winSize.height) {
            this.gameTypeListNode.parent = this.parentScrollView.node;
            this.gameTypeListNode.y = - this.gameTypeListNode.height / 2 - 20;
        } else {
            this.gameTypeListNode.parent = this.gameScrollView.parent;
            this.gameTypeListNode.y = -this.gameTypeListNode.height / 2 - 20;
        }
        this.gameTypeListNode.getComponent(RoundRectMask).radius = 0.2;
        let endOffset = this.parentScrollView.getScrollOffset();
        let videoPosY = this.livePageView.y - 20;
        let topPercent = Math.abs(videoPosY / cc.winSize.height) - endOffset.y / cc.winSize.height;
        cc.director.emit("LiveGame_TopPos", topPercent);//发送事件更新video的top位置

        if (this.scrollGameList) {
            this.refreshGmaeType();
        }
        /*  以下是自动 重置位置*  */
        if (!this.bScrolling) {
            return;
        }

        if (this.bScroll_ani) return;

        if (this.itemList.length == 0) return;

        this.updateTimer += dt;
        if (this.updateTimer < this.updateInterval) {
            return;
        }
        this.updateTimer = 0;
        this.bScrolling = false;
        this.update_game_list();
        //没有加载出来游戏列表的时候 下不显示隐私区域
        if (this.gameScrollView.height > 1550) {
            this.policyNode.opacity = 255;
        }
        //解决bug 如果banner 区域没出来的时候 再出来 直接重置
        if (this.gameScrollOffset != Math.abs(this.gameScrollView.parent.y)) {
            this.gameScrollOffset = Math.abs(this.gameScrollView.parent.y);
            this.parentScrollView.content.height = this.gameScrollView.parent.height + this.gameScrollOffset;
            this.policyNode.y = -this.gameScrollView.parent.height + this.policyNode.height / 2.0 + 200;
        }
    }
    update_game_list() {
        let isUp = this.parentScrollView.content.y > this.lastContentPosY;
        this.lastContentPosY = this.parentScrollView.content.y;
        let max_index = 0;
        let min_index = this.all_configs.length;
        for (let index = 0; index < this.itemList.length; index++) {
            let element: any = this.itemList[index];
            if (element.i_index > max_index) { max_index = element.i_index }
            if (element.i_index < min_index) { min_index = element.i_index }
        }
        //这里加一个 最上面 和最下面的逻辑 防止 多删除
        if (isUp) {
            if ((max_index + 1) >= this.all_configs.length) return;
        } else {
            if ((min_index - 1) < 0) return;
        }

        //是否有删除的 有得话 就补充一行
        let remove_true = false;
        this.itemList = this.itemList.filter((element: any) => {
            let viewPos = this.getPositionInView(element.node);
            if (isUp) {
                if (viewPos.y > 2200) {
                    Global.instance.removeFromQueue(element.data.images)
                    poolManager.instance.putNode(element.node);
                    remove_true = true;
                    return false
                } else {
                    return true
                }
            } else {
                if (viewPos.y < -100) {
                    Global.instance.removeFromQueue(element.data.images)
                    poolManager.instance.putNode(element.node);
                    remove_true = true;
                    return false
                } else {
                    return true
                }
            }
        })
        //如果没有 21个补充到21个
        if (remove_true) {
            if (isUp) {
                //item上滑时，超出了scrollView上边界，将item移动到下方复用，item移动到下方的位置必须不超过content的下边界
                //滑动 过快 会越来越少
                for (let index = 0; index <= 22 - this.itemList.length; index++) {
                    this.add_item_game(max_index + 1 + index)
                }
            } else {
                //item下滑时，超出了scrollView下边界，将item移动到上方复用，item移动到上方的位置必须不超过content的上边界
                for (let index = 0; index <= 22 - this.itemList.length; index++) {
                    this.add_item_game(min_index - 1 - index)
                }
            }
        }
    }
    //增加一个item game
    add_item_game(iindex: number) {
        if (iindex < 0) return;
        if (iindex >= this.all_configs.length) return;
        let a_config = this.all_configs[iindex];
        let node = poolManager.instance.getNode(this.itemPrefab, this.gameScrollView);
        node.position = a_config.pos;
        let moreGameItem = node.getComponent(HallGameItem);
        let tempList = this.get_list_game_type_str();
        let tempname: string = tempList[a_config.k];
        let emlist: [] = this.gameListByType[tempname];
        if (moreGameItem) {
            let data = emlist[a_config.index]
            moreGameItem.reset()
            moreGameItem.i_index = iindex;
            moreGameItem.init(data)
            moreGameItem.dataChanged();
            //把这21个 加入到数组 以后滑动只用到这21个相互变换位置
            this.itemList[this.itemList.length] = moreGameItem;
        }
    }
    /**获取item在scrollView的局部坐标 */
    private getPositionInView(item) {
        let worldPos = item.parent.convertToWorldSpaceAR(item.position);

        return worldPos;
    }
    //检测 kyc 两种情况会调用 进入游戏 和 登录成功调用
    checkKycState_login() {
        //验证一下kyc
        KycMgr.instance.verify_kyc(INGSME_TYPE.Login, (isVerity) => {
            if (isVerity) {
            }
            //这里是 认证过之后的 逻辑 其他逻辑会自动 执行 是非对错都要调用
        });
    }

    initSpin() {
        let btn_spin = this.node.getChildByName("btn_spin");
        if (btn_spin) {
            btn_spin.parent = Global.getInstance().popNode;
            btn_spin.zIndex = 1;
            // btn_spin.x = this.prefabNode.height/2-btn_spin.height/2;
            this.addButtonDragListener(btn_spin);
        }
    }

    initEnvelope() {
        let btn_envelope = this.node.getChildByName("btn_envelope");
        if (btn_envelope) {
            btn_envelope.parent = Global.getInstance().popNode;
            btn_envelope.zIndex = 1;
            this.addButtonDragListener(btn_envelope);
        }
    }

    /**隐藏大厅Live直播视频 */
    hideHallLive() {
        this.livePageView.stopAllActions();
        this.hideLiveVideo();
        Global.getInstance().setPageId(E_PAGE_TYPE.OTHER);
    }
    reqNextPage() {
        let gameList = MoreGameManager.instance().getAllThirdGameData();
        if (gameList.length == this.currentGameListPage * 100 && gameList.length % 100 == 0) {
            this.currentGameListPage = this.currentGameListPage + 1;
            let params = {
                token: Global.getInstance().token,
                page: this.currentGameListPage
            };
            MoreGameManager.instance().doQueryMoreGameList(() => {
                this.reqNextPage();
            }, params, true)
        } else {
            this.refreshGameList(gameList);
        }
    }

    reqInboxMsg() {
        if ((Global.getInstance().now() - this.lastTimeGetInboxMsg) / 1000 < 30) {
            return;
        }
        this.lastTimeGetInboxMsg = Global.getInstance().now();
        if (!!Global.getInstance().token) {//判定token存在
            let inboxPoint = this.btnInbox.getChildByName("redpoint");
            if (Global.getInstance().unreadMarks?.inbox) {
                inboxPoint.active = true;
            } else {
                inboxPoint.active = false;
            }
        }
    }

    reqWalletTaskMsg() {
        if (Global.getInstance().token) {
            HttpProxy.instance.getWalletTaskInfo().then((rsp: NetRspObject) => {
                if (rsp.msg == NetRspMsg.Success && cc.isValid(this.node)) AutoPopMgr.autoPopupDialog();
            });
        }
    }

    updateWalletTaskBtnInfo() {
        const amount = this.gameData.getWalletTaskRewardAmount();
        this.walletTaskBtn.getChildByName("nd_wallet_tip").active = amount > 0;
        this.walletTaskBtn.getChildByName("nd_wallet_tip").getChildByName("lb_wallet_balance").getComponent(cc.Label).string = `${amount} Bonus Available,Claim Now!`;

        const spineName = amount > 0 ? "claim" : "idle";
        const name = this.walletTaskBtn.getChildByName("spine_wallet").getComponent(sp.Skeleton).animation;
        if (spineName != name) this.walletTaskBtn.getChildByName("spine_wallet").getComponent(sp.Skeleton).setAnimation(0, spineName, true);
    }

    updateDownloadTip() {
        const downloadTipData: DownloadTipInfo = this.gameData.downloadTipData;
        this.downloadTipNode.active = downloadTipData != null && downloadTipData.status == "1";

        if (!this.downloadTipNode.active) return;

        const downloadText = downloadTipData.button_text;
        const closeType = downloadTipData.close_type;
        const icon = downloadTipData.icon;
        const slogan = downloadTipData.slogan;
        let url = downloadTipData.download_url;

        this.downloadTipNode.getChildByName("btn_download").getChildByName("txt_download").getComponent(cc.Label).string = downloadText;

        const contentNode = this.downloadTipNode.getChildByName("txt_content");
        const iconNode = this.downloadTipNode.getChildByName("icon");
        const closeBtn = this.downloadTipNode.getChildByName("btn_close");
        const downloadBtn = this.downloadTipNode.getChildByName("btn_download");

        const existIcon = icon && icon.length > 0;
        contentNode.width = existIcon ? 530 : 660;
        const maxCount = existIcon ? 42 : 52;
        iconNode.active = existIcon;
        if (existIcon) utils.loadWebImage(iconNode.getComponent(cc.Sprite), icon);
        contentNode.getComponent(cc.Label).string = utils.cutContent(slogan, maxCount);
        this.downloadTipNode.getChildByName("btn_close").active = closeType == 1;

        const jumpDownload = () => {
            if (ALL_APP_SOURCE_CONFIG.channel == E_CHANEL_TYPE.MAYA) {
                uiManager.instance.showDialog(UI_PATH_DIC.MayaDownloadPop, null, null, DEEP_INDEXZ.COMMON_TIPS);
            }
            else {
                const bApkDownloadAddress = url.indexOf(".apk") != -1;
                if (!bApkDownloadAddress) url = `${url}?channel=${CHANEL_PARAM[ALL_APP_SOURCE_CONFIG.channel]}`;

                utils.openUrl(url);
            }
        }

        utils.addButtonClick(closeBtn, () => {
            jumpDownload();

            this.downloadTipNode.active = false;
            Global.getInstance().setStoreageData(Global.GLOBAL_STORAGE_KEY.DOWNLOAD_TIP, true);
        });

        utils.addButtonClick(downloadBtn, jumpDownload);


    }

    protected onEnable(): void {
        this.get_all_activity_bonus();
    }
    //获取所有 返奖 活动奖励 弹窗 修复bug hall加载两次 这个只在测试的时候出现
    get_all_activity_bonus() {
        let token_ = Global.getInstance().token;
        if (!token_) return;
        let gap = (Global.getInstance().now() - Global.getInstance().lastReqTime_bonus) / 1000;
        if (gap < 30) {
            return;
        }
        Global.getInstance().lastReqTime_bonus = Global.getInstance().now()
        HttpUtils.getInstance().post(1, 3, this, "/avt/api/activity/bonus_list", { token: token_ }, (response) => {
            let ac_data = response?.data?.list
            if (ac_data && ac_data.length > 0) {
                Global.getInstance().popActivityBonus = Global.getInstance().popActivityBonus.concat(ac_data);
            }
            AutoPopMgr.autoPopupDialog();
        }, function (response) {
            AutoPopMgr.autoPopupDialog();
        });
        //拉取所有领奖枚举接口
        Global.getInstance().actAdjustmentData();
        //调账 发奖的请求 新增
        HttpUtils.getInstance().get(1, 3, this, "/open/api/activity/adjustment_bonus_record/list", { token: token_ }, (response) => {
            let ac_data = response?.data?.list
            if (ac_data && ac_data.length > 0) {
                //concat 返回的新数组 需要接受一下
                Global.getInstance().popActivityBonus = Global.getInstance().popActivityBonus.concat(ac_data);
            }
            AutoPopMgr.autoPopupDialog();
        }, function (response) {
            AutoPopMgr.autoPopupDialog();
        });
        return true
    }
    reqBanners() {
        //let bannerData = Global.getInstance().getBannerData();
        //let popBannerData = Global.getInstance().getPopBannerData();
        let bannerData;
        let popBannerData;
        HttpUtils.getInstance().post(1, 3, this, "/avt/api/banner/activity/list", { token: Global.getInstance().token }, (response) => {
            if (response.data) this.gameData.setBannerData(response.data);

            if (response.data && response.data.banner && response.data.banner.length > 0) {
                bannerData = response.data.banner;
                Global.getInstance().setBannerData(bannerData);//保存缓存一份 popbanner 跳转使用
            }
            if (response.data && response.data.pop_banner && response.data.pop_banner.length > 0) {
                popBannerData = response.data.pop_banner;
            }
            if (bannerData && bannerData.length > 0) {
                let banner_src = this.bannerPageView.getComponent(Banners)
                banner_src.set_banner(bannerData)
            } else {
                this.bannerPageView.node.active = false;
            }
            if (popBannerData && popBannerData.length > 0) {
                let ttpdata = this.filter_popBanner(popBannerData)
                console.log('---------ttpdata:', JSON.stringify(ttpdata))
                Global.getInstance().popBanners = ttpdata;
            }
            AutoPopMgr.autoPopupDialog();
        }, function (response) {
            AutoPopMgr.autoPopupDialog();
        });
    }

    filter_popBanner(data) {
        let ppdata = data.filter((item) => {
            let element = item.channel.toLocaleLowerCase()
            let now_channel = ALL_APP_SOURCE_CONFIG.channel.toLocaleLowerCase()
            if (element.indexOf(now_channel) != -1) {
                //这里添加一个开始结束时间 如果在这时间段内 才展示
                const start_time = new Date(item.start_at).getTime();
                const end_time = new Date(item.end_at).getTime();
                // 获取当前时间戳（毫秒级）
                const timestamp = Date.now();
                if (timestamp >= start_time && timestamp <= end_time) {
                    let idUniq = "_popBanners_" + item.id;
                    if (Global.getInstance().userdata && Global.getInstance().userdata.user_id) {
                        idUniq = Global.getInstance().userdata.user_id + "_popBanners_" + item.id;
                    }
                    let p = Global.getInstance().getStoreageData(idUniq, "");
                    if (p != '') {
                        let p_pop = p.split('_');
                        if (p_pop[1] !== utils.getToday()) {
                            if (item.image != '') return true //这里排出一下 gcash mini的banner
                        } else {
                            if (parseInt(p_pop[0]) <= item.show_count) {
                                if (item.image != '') return true //这里排出一下 gcash mini的banner
                            }
                        }
                    } else {
                        if (item.image != '') return true //这里排出一下 gcash mini的banner
                    }
                }
                return false;
            } else {
                return false
            }
        })
        return ppdata
    }
    getRankPopData() {
        if (!Global.getInstance().token) {
            return;
        }
        HttpUtils.getInstance().post(1, 3, this, "/avt/api/rank/casino", {
            token: Global.getInstance().token,
            type: 2
        }, (response) => {
            if (!this.node || !this.node.isValid) {
                return;
            }
            if (response && response.data && response.data.list && response.data.list.rank) {
                let rank = response.data.list.rank;
                if (rank[0] && rank[0].player_id && rank[0].player_id != "--") {
                    Global.getInstance().yesterdayRankInfo = true;
                    AutoPopMgr.autoPopupDialog();
                }
            }
        });
    }
    getRankJiliPopData() {
        if (!Global.getInstance().token) {
            return;
        }
        HttpUtils.getInstance().post(1, 3, this, "/avt/api/rank/slot", {
            token: Global.getInstance().token,
            type: 2
        }, (response) => {
            if (!this.node || !this.node.isValid) {
                return;
            }
            if (response && response.data && response.data.list && response.data.list.rank) {
                let rank = response.data.list.rank;
                if (rank[0] && rank[0].player_id && rank[0].player_id != "--") {
                    Global.getInstance().yesterdayRankInfoJili = true;
                    AutoPopMgr.autoPopupDialog();
                }
            }
        });
    }
    getEnvelopeInfo(cb?) {
        let btn_envelope = Global.getInstance().popNode.getChildByName("btn_envelope");
        if (!btn_envelope) return;
        let envelopedata = Global.getInstance().envelopeInfo;
        if (parseInt(envelopedata.first_deposit_bonus_guide_is_start) == 1 && !envelopedata.is_first_recharge) {
            btn_envelope.active = true;
        } else {
            btn_envelope.active = false;
        }
    }
    //获取转盘次数
    getSpinInfo(force?, cb?) {
        let gap = (Global.getInstance().now() - this.lastReqSpinInfoTime) / 1000;
        if (gap < 10 && !force) {
            if (cb) {
                cb();
            }
            return;
        }
        this.lastReqSpinInfoTime = Global.getInstance().now()

        let token_ = Global.getInstance().token;
        let btn_spin = Global.getInstance().popNode.getChildByName("btn_spin");
        if (!btn_spin) return;
        let spin_count = btn_spin.getChildByName("spin_count");
        let lab_count = spin_count.getChildByName("lab_count").getComponent(cc.Label);
        let parms = {};
        if (token_) {
            parms = { token: token_ };
            HttpUtils.getInstance().get(1, 0, this, "/open/api/activity/spin/config", parms, (response) => {
                let spindata = response?.data;
                Global.getInstance().spinInfo = spindata;
                if (spindata && spindata.is_start && spindata.is_start == 1) {
                    btn_spin.active = true;
                    let gamescene = cc.director.getScene().getComponentInChildren(GameControl);
                    gamescene.mTabbar.isShowwheel_btn();
                } else {
                    btn_spin.removeFromParent();
                }

                if (spindata && spindata.left_times >= 0 && token_) {
                    if (spindata.left_times == 0) {
                        spin_count.active = false;
                    } else {
                        spin_count.active = true;
                        lab_count.string = spindata.left_times;
                    }
                } else {
                    spin_count.active = true;
                    lab_count.string = "5";
                }

                if (spindata && spindata.total_prize >= 0 && token_) {
                    this.showTotalAnim(spindata.total_prize);
                } else {
                    let lab_total_award = utils.getChildByPath(btn_spin, "totoalprize.lab_total_award").getComponent(cc.Label);
                    lab_total_award.string = "--";
                }
                if (cb) {
                    cb();
                }
                AutoPopMgr.autoPopupDialog();
            }, function (response) {
                AutoPopMgr.autoPopupDialog();
            });
        } else {
            //未登录的时候 显示转盘按钮
            btn_spin.active = true;
            let gamescene = cc.director.getScene().getComponentInChildren(GameControl);
            gamescene.mTabbar.isShowwheel_btn();
            spin_count.active = false;
            let lab_total_award = utils.getChildByPath(btn_spin, "totoalprize.lab_total_award").getComponent(cc.Label);
            lab_total_award.string = "--";
            AutoPopMgr.autoPopupDialog();
        }
    }
    reqCashbackYesterday() {
        let gamescene = cc.director.getScene().getComponentInChildren(GameControl);
        gamescene.mTabbar.setPromos_redpoint()
        if (!!Global.getInstance().token) {
            HttpUtils.getInstance().post(1, 3, this, "/avt/api/activity/daily-rebate-yesterday", {
                token: Global.getInstance().token,
            }, (response) => {
                if (response.data) {
                    let status = response.data.status;
                    if (Number(response.data.yesterday_user_status)) {//0:普通用户 1:VIP用户(昨日身份)
                        if (parseInt(status) == 1) {
                            gamescene.mTabbar.setPromos_redpoint(true)
                            //Global.getInstance().setStoreageData("PROMO_VIPBONUS_CAN_GET", 1);
                            Global.getInstance().unreadMarks.cashback = 1;
                        } else {
                            gamescene.mTabbar.setPromos_redpoint()
                            //Global.getInstance().setStoreageData("PROMO_VIPBONUS_CAN_GET", 0);
                            Global.getInstance().unreadMarks.cashback = 0;
                        }
                    } else {
                        if (parseInt(status) == 1) {
                            gamescene.mTabbar.setPromos_redpoint(true)
                            //Global.getInstance().setStoreageData("PROMO_BONUS_CAN_GET", 1);
                            Global.getInstance().unreadMarks.cashback = 1;
                        } else {
                            gamescene.mTabbar.setPromos_redpoint()
                            //Global.getInstance().setStoreageData("PROMO_BONUS_CAN_GET", 0);
                            Global.getInstance().unreadMarks.cashback = 0;
                        }
                    }
                }
            });
        }
    }
    showSpinView() {
        if (!Global.getInstance().token) {
            uiManager.instance.showDialog(UI_PATH_DIC.phonePasswordLogin, [{ way: LOGIN_WAY.PhoneCode }])
            return
        }
        let spin = Global.getInstance().popNode.getChildByName("SpinView");
        if (spin && spin.isValid) {
            spin.active = true;
            return;
        }

        uiManager.instance.loadPrefabByLoading(UI_PATH_DIC.SpinView, cc.Prefab, () => {
        }, (err, prefab: any) => {
            if (err) {
                return;
            }
            let spin = Global.getInstance().popNode.getChildByName("SpinView");
            if (spin && spin.isValid) {
                return;
            }
            let node = cc.instantiate(prefab);
            node.name = "SpinView"
            node.zIndex = DEEP_INDEXZ.SPINWHEEL;
            node.position = new cc.Vec3(0, 0);
            node.parent = Global.getInstance().popNode;
        });
    }
    popTargetView() {
        let cb = () => {
            if (Global.getInstance().token && Global.getInstance().spinInfo.is_start == 1 && Global.getInstance().spinInfo.real_left_times > 0) {
                this.showSpinView();
            }
        }
        this.getSpinInfo(false, cb);
    }

    showGoldAni(data) {
        let goldAniNode = Global.getInstance().popNode.getChildByName("goldAniNode");
        if (!goldAniNode) {
            goldAniNode = cc.instantiate(this.m_goldani_prefab);
            goldAniNode.parent = Global.getInstance().popNode;
            goldAniNode.zIndex = 1499;
            goldAniNode.name = "goldAniNode";
        }

        const comp = goldAniNode.getComponent(CoinRewardEffect);
        if (comp) {
            const type = data.type;
            if (type == GOLDANI_TYPE.TOTARGET) {
                const en = data.target || this.goldAniTarget;
                const balance = Global.getInstance().balanceTotal;
                const sp = cc.v2(0, -150);
                const ep = utils.convetOtherNodeSpaceAR(utils.seekFromRootByName(en, "ic_coin"), Global.getInstance().popNode);
                const cb = data.cb;
                comp.playAnim(en, sp, ep, balance, cb);
            }
            else if (type == GOLDANI_TYPE.FULLSCREEN) {
                comp.playFullScreenCoinAni();
            }
        }
    }

    //刷新第三方游戏数据列表
    initGameListData(gameList) {
        // this.gameListByType["Hot"] = [];//hot先隐藏
        this.gameListByType["Casino"] = [];
        this.gameListByType["Slots"] = [];
        this.gameListByType["Poker"] = [];
        this.gameListByType["Bingo"] = [];
        this.gameListByType["Arcade"] = [];
        this.gameListByType["Sports"] = [];
        this.gameListByType["Like"] = [];
        this.gameListByType["History"] = [];
        for (let index = 0; index < gameList.length; index++) {
            const item = gameList[index];
            let is_hide = false
            if (Global.getInstance().all_hide_game_list.length > 0) {
                if (Global.getInstance().all_hide_game_list.indexOf(item.id) != -1) {
                    is_hide = true
                }
            }
            if (item.home_page_label && item.home_page_label != "" && !is_hide) {
                let tagarrs = JSON.parse(item.home_page_label);
                for (let k = 0; k < tagarrs.length; k++) {
                    const element = tagarrs[k];
                    if (element == "Hot" && item.table_group == 0) {
                        // this.gameListByType["Hot"].push(item);//hot先隐藏
                    } else if (element == "Casino") {
                        this.gameListByType["Casino"].push(item);
                    } else if (element == "Slots") {
                        this.gameListByType["Slots"].push(item);
                    } else if (element == "Poker") {
                        this.gameListByType["Poker"].push(item);
                    } else if (element == "Bingo") {
                        this.gameListByType["Bingo"].push(item);
                    } else if (element == "Arcade") {
                        this.gameListByType["Arcade"].push(item);
                    } else if (element == "Sports") {
                        this.gameListByType["Sports"].push(item);
                    }
                    if (item.is_like) {
                        this.gameListByType["Like"].push(element);
                    }
                }
            }
        }
        let sortFunc = (a, b) => {
            if (parseInt(a.tags) === 0 && parseInt(b.tags) !== 0) return 1;
            if (parseInt(a.tags) !== 0 && parseInt(b.tags) === 0) return -1;
            //排序tags 1>3>2
            const aint = parseInt(a.tags)
            const bint = parseInt(b.tags)
            if (aint !== bint) {
                if (aint == 1) return -1;
                if (bint == 1) return 1;
                if (aint == 3) return -1;
                if (bint == 3) return 1;
            }
            if (parseInt(a.balance_record) !== parseInt(b.balance_record)) return parseInt(b.balance_record) - parseInt(a.balance_record);
            if (parseInt(a.sort) !== parseInt(b.sort)) return parseInt(b.sort) - parseInt(a.sort);
            if (a.name && b.name) {
                return a.name.localeCompare(b.name);
            }
        }
        // this.gameListByType["Hot"].sort(sortFunc);//hot先隐藏
        this.gameListByType["Casino"].sort(sortFunc);
        this.gameListByType["Slots"].sort(sortFunc);
        this.gameListByType["Poker"].sort(sortFunc);
        this.gameListByType["Bingo"].sort(sortFunc);
        this.gameListByType["Arcade"].sort(sortFunc);
        this.gameListByType["Sports"].sort(sortFunc);
        this.gameListByType["Like"].sort(sortFunc);

        // console.log('------------item:',JSON.stringify(this.gameListByType["Slots"][0]))
        //给每个区域添加一个more game  项
        // for (const key in this.gameListByType) {
        //     if (Object.prototype.hasOwnProperty.call(this.gameListByType, key)) {
        //         let element = this.gameListByType[key];
        //         this.addMoreGameItem(element,key);
        //     }
        // }
    }
    //根据类型 添加more game 按钮
    addMoreGameItem(arr_itmes: any, ctype: string) {
        if (arr_itmes.length == 0) return;
        let more_item = {
            balance_record: "",
            big_images: "",
            big_images_set: 0,
            company_id: ctype,
            game_id: 0,
            game_type: 0,
            home_page_label: "",
            id: "0",
            images: "0",
            is_jump: 0,
            is_landscape: 0,
            is_like: false,
            max_display_amount: 0,
            min_display_amount: 0,
            name: "More Game",
            sort: 0,
            status: 0,
            table_group: 0,
            tags: 0,
            third_party_game_id: ctype
        }
        if (arr_itmes.length < 3) {
            arr_itmes.push(more_item)
        } else {
            arr_itmes.splice(2, 0, more_item);
        }
    }
    //获取list game type string
    get_list_game_type_str() {
        let gameTypes = Global.getInstance().getGameType();
        let tempList = []
        for (let index = 0; index < gameTypes.length; index++) {
            let element = gameTypes[index];
            let str = element.game_type;
            if (str && str.length > 0) {
                let type = str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
                tempList[tempList.length] = type;
            }
        }
        return tempList;
    }
    //先隐藏所有分类
    hide_all_catogry() {
        let parent = this.gameTypeListNode;
        let btn_casino = utils.getChildByPath(parent, "layout.btn_casino");
        let btn_slots = utils.getChildByPath(parent, "layout.btn_slots");
        let btn_poker = utils.getChildByPath(parent, "layout.btn_poker");
        let btn_bingo = utils.getChildByPath(parent, "layout.btn_bingo");
        let btn_fishing = utils.getChildByPath(parent, "layout.btn_arcade");
        let btn_sports = utils.getChildByPath(parent, "layout.btn_sports");
        btn_bingo.active = false;
        btn_casino.active = false;
        btn_slots.active = false;
        btn_poker.active = false;
        btn_fishing.active = false;
        btn_sports.active = false;
    }
    //通过数据 刷新游戏列表界面 lemoon
    initGameListView() {
        let tempList = [];
        let parent = this.gameTypeListNode.getChildByName('layout');
        this.hide_all_catogry();
        let gameTypes = Global.getInstance().getGameType();
        let btn_casino = utils.getChildByPath(parent, "btn_casino");
        btn_casino.opacity = 255;
        let lab_casino = utils.getChildByPath(btn_casino, "Background.Label");
        lab_casino.color = cc.color(34, 34, 34);
        let bntLoc = [400, 240, 80, -80, -240, -400];
        //修复如果 少于6个 适配 dev环境是5个 一般都是6个
        if (gameTypes.length == 5) {
            bntLoc = [320, 160, 0, -160, -320];
        } else if (gameTypes.length == 4) {
            bntLoc = [240, 80, -80, -240];
        } else if (gameTypes.length == 3) {
            bntLoc = [160, 0, -160];
        } else if (gameTypes.length == 2) {
            bntLoc = [80, -80];
        } else if (gameTypes.length == 1) {
            bntLoc = [0];
        }
        let gamescene = cc.director.getScene().getComponentInChildren(GameControl);
        let sec_pr = gamescene.mTabbar.re_page_params
        // gameTypes = [{"id":10000,"game_type":"SLOTS","sort":"5"},{"id":10006,"game_type":"CASINO","sort":"6"},
        // {"id":10001,"game_type":"POKER","sort":"4"},{"id":10004,"game_type":"BINGO","sort":"3"},
        // {"id":10002,"game_type":"FISHING","sort":"2"},{"id":10005,"game_type":"SPORTS","sort":"1"}]
        let show_light = null;
        let to_id = '10000'
        for (let index = 0; index < gameTypes.length; index++) {
            let element = gameTypes[index];
            let str = element.game_type;
            if (str && str.length > 0) {
                let type = str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
                tempList[tempList.length] = type;
                let btn_tag = parent.getChildByName("btn_" + str.toLowerCase());
                btn_tag.active = true;
                btn_tag.y = bntLoc[index];
                if (index == 0) {
                    show_light = btn_tag;
                    to_id = element.id
                    //默认第一个 高亮
                    // btn_tag.getChildByName('Background').opacity = 255;
                }
                if (element.id == sec_pr) {
                    show_light = btn_tag;
                    to_id = element.id
                    // btn_tag.getChildByName('Background').opacity = 255;
                }
                //把所有标签恢复未点击状态
                btn_tag.getChildByName('Background').opacity = 0;
            }
        }
        if (show_light) {
            show_light.getChildByName('Background').opacity = 255;
        }
        // this.scrollToTop();
        parent.getComponent(cc.Layout).updateLayout();
        this.gameTypeListNode.height = 160 * tempList.length;
        parent.height = 160 * tempList.length;
        let posy = 0;
        let height = 0;

        //先吧游戏从节点池删除 防止下面被释放
        this.itemList.forEach((element: any) => {
            poolManager.instance.putNode(element.node)
        });
        this.itemList = [];

        for (let z = 0; z < this.gameScrollView.children.length; z++) {
            const element = this.gameScrollView.children[z];
            if (element.name != 'btn_refresh') {
                element.removeFromParent();
            }
        }

        //几个分类 more games 标题
        this.all_item_nodes.forEach((element: any) => {
            poolManager.instance.putNode(element.node)
        });
        this.all_item_nodes = []
        //先清空所有 游戏坐标信息 
        this.all_configs = []
        let peradd = 0;//一共加载21个 7行
        let self = this;
        for (let k = 0; k < tempList.length; k++) {
            let elemt: string = tempList[k];
            let tempname: string = elemt;
            let emlist: [] = this.gameListByType[tempname];
            let temp = poolManager.instance.getNode(this.gameTypeNodeTemp, this.gameScrollView)
            let title = temp.getChildByName("type_title").getComponent(cc.Label);
            // title.string = tempname;
            title.string = elemt.toUpperCase();
            this.all_item_nodes[k] = { node: temp, iindex: peradd, type: elemt };
            let btn_more = temp.getChildByName("btn_moregame");
            // 方法2：使用 click 事件（推荐）
            btn_more.on('click', (event: cc.Event.EventCustom) => {
                console.log("点击事件触发", tempname);
                cc.director.emit("showAllGameSearchView", tempname);
            });
            //先隐藏more game
            // btn_more.node.active = false;

            // temp.height = 50+Math.ceil((emlist.length)/3)*360;//去掉了see all 重新计算height
            // temp.x = 0;
            // temp.y = height;
            temp.setPosition(0, -posy)
            temp.height = 60;
            let temp_height = 60 + Math.ceil((emlist.length) / 3) * 420;//去掉了see all 重新计算height
            height = height + temp_height;

            if (emlist.length == 0) {
                this.scorllOffSetsList[elemt] = posy;
                this.scorllEndOffSetsList[tempname] = posy;
                temp.active = false;//没有游戏的时候隐藏掉gameTypeNodeTemp
            } else {
                if (k == tempList.length - 1) {
                    this.scorllOffSetsList[elemt] = posy - 420;
                    posy = posy + temp_height + 30;
                    this.scorllEndOffSetsList[tempname] = posy;
                } else {
                    this.scorllOffSetsList[elemt] = posy + 30;
                    posy = posy + temp_height + 30;
                    this.scorllEndOffSetsList[tempname] = posy;
                }
                temp.active = true;//有游戏显示gameTypeNodeTemp
                for (let index = 0; index < emlist.length; index++) {
                    this.all_configs[peradd] = {
                        pos: cc.v2(-280 + index % 3 * 292, -290 - Math.floor(index / 3) * 420 + temp.y),
                        k: k,
                        index: index
                    }
                    peradd++;
                }
            }

        }

        height = height + 500;
        this.gameScrollView.height = height > 1555 ? height : 1555;
        this.gameScrollView.parent.height = this.gameScrollView.height + this.policyNode.height + 200
        this.parentScrollView.content.height = this.gameScrollView.parent.height;
        this._event_update_opacity();
        setTimeout(() => {
            self.gameScrollOffset = Math.abs(self.gameScrollView.parent.y);
            self.parentScrollView.content.height = self.gameScrollView.parent.height + self.gameScrollOffset;
            self.policyNode.y = -self.gameScrollView.parent.height + self.policyNode.height / 2.0 + 200;
        }, 1000);
        //跳转问题

        let type_1 = gamescene.mTabbar.re_page
        let game_type = [
            "10000", "10001", "10002", "10003", "10004", "10005", "10006"
        ]
        if (sec_pr == 'service') {
        } else if (sec_pr && game_type.indexOf(sec_pr) != -1) {
            //这里跳转对应的分类
            //gcash跳转到对应的
            setTimeout(() => {
                self.gameTpyeButtonClickListener(null, sec_pr)
            }, 1000);
        } else {
            setTimeout(() => {
                self.bScrolling = false
                self.bScroll_ani = true
                self.readd_itemlist(0)
                self.scrollGameList = false;
            }, 200);
            setTimeout(() => {
                //这里先不执行 update 
                self.bScroll_ani = false;
            }, 500);
        }
        this.show_refresh_view()//如果没有数据 或者报错 显示刷新按钮
    }
    loadGameItemPrefab(cb) {
        if (!this.itemPrefab) {
            let gamescene = cc.director.getScene().getComponentInChildren(GameControl);
            this.itemPrefab = gamescene.game_hall_item;
            cb();
        } else {
            cb();
        }
    }
    updateUserInfo(data?) {
        if (Global.getInstance().balanceTotal >= 9999999999) {
            this.m_moneny.string = "9,999,999,999.00";
        } else {
            this.m_moneny.string = utils.formatNumberWithCommas(Global.getInstance().balanceTotal);
        }
        cc.Tween.stopAllByTarget(this.m_moneny.node);
        cc.tween(this.m_moneny.node).to(0.05, { opacity: 0 }).to(0.1, { opacity: 255 }).start();
    }
    updateWidget() {
        let totalNews = Global.getInstance().getStoreageData("NEWS_UNREAD_TOTAL_NUMNER", 0);
        let totalInbox = Global.getInstance().unreadMarks?.inbox;//站内信红点
        let promobonus = Global.getInstance().unreadMarks?.cashback;//Promo红点
        let totalTask = Global.getInstance().unreadMarks?.task_num;//Task新手任务红点
        let weeklyBonus;
        if (!!Global.getInstance().token) {
            weeklyBonus = Global.getInstance().getStoreageData("PROMO_WEEKLY_CAN_GET" + Global.getInstance().userdata.user_id, 0);//Weekly payday红点
        }
        // let redpointtask = this.promoTaskNode.getChildByName("redpoint");
        let inboxPoint = this.btnInbox.getChildByName("redpoint");
        let gamescene = cc.director.getScene().getComponentInChildren(GameControl);
        let redpointcustom = this.btnCoustom.getChildByName("redpoint1")
        // if(totalTask > 0){
        //     redpointtask.active = true;
        // }else{
        //     redpointtask.active = false;
        // }
        if (Global.getInstance().custom_msg > 0) {
            redpointcustom.active = true
        } else {
            redpointcustom.active = false
        }
        if (totalInbox > 0) {
            inboxPoint.active = true;
        } else {
            inboxPoint.active = false;
        }
        gamescene.mTabbar.setNews_redpoint(totalNews > 0)
        gamescene.mTabbar.setPromos_redpoint(promobonus > 0 || weeklyBonus > 0)
    }
    onDestroy() {
        super.onDestroy();

        cc.director.off('ShowMoreGameEvent', this.ShowMoreGameEvent, this);
        cc.director.off("inHallEvent", this.onInHallEvent, this)
        cc.director.off("showAllGameSearchView", this.showAllGamesSerchView, this)
        cc.director.off("RefreshGameListThird", this.RefreshGameListThird, this)
        cc.director.off("update_gold", this.updateUserInfo, this);
        cc.director.off("update_home_widget", this.updateWidget, this);
        cc.director.off("ReturnToHome2", this.showLiveVideo, this);
        cc.director.off("hidelivevideo", this.hideLiveVideo, this);
        cc.director.off("hideHallLive", this.hideHallLive, this);
        cc.director.off("checkKycState_login", this.checkKycState_login, this);
        cc.director.off("update_spin_time", this.getSpinInfo, this);
        cc.director.off("check_show_spin", this.popTargetView, this);
        cc.director.off("showGoldAni", this.showGoldAni, this);
    }

    onInnerScroll(event) {
        this.gameScrollView.active = true;
        this.refreshGmaeType();
        this.scrollGameList = true;
    }
    refreshGmaeType() {
        let endOffset = this.parentScrollView.getScrollOffset();
        let maxoffy = this.parentScrollView.getMaxScrollOffset().y;
        let now = Global.getInstance().now();
        if (now - this.lastReshLocation > 500) {
            this.lastReshLocation = Global.getInstance().now();
            let gameTypes = Global.getInstance().getGameType();
            for (let index = 0; index < gameTypes.length; index++) {
                let element = gameTypes[index];
                let str = element.game_type;
                let offended = 0;
                let offstart = 0;
                if (str && str.length > 0) {
                    let type = str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
                    offended = this.scorllEndOffSetsList[type] + this.gameScrollOffset;
                }

                if (index > 0) {
                    let element = gameTypes[index - 1];
                    let str = element.game_type;
                    if (str && str.length > 0) {
                        let type = str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
                        offstart = this.scorllOffSetsList[type] + this.gameScrollOffset;
                    }
                }
                if (endOffset.y <= 20) {
                    this.gameTypeButtonChanged(gameTypes[0].id);
                    return;
                }
                if (endOffset.y > maxoffy - 20) {
                    this.gameTypeButtonChanged(gameTypes[gameTypes.length - 1].id);
                    return;
                }
                if (endOffset.y <= offended && endOffset.y > offstart) {
                    this.gameTypeButtonChanged(element.id);
                    return;
                }
            }
        }
    }
    //游戏标签页滑块的动画显示
    gameTypeButtonChanged(typename) {
        if (this.currentGameType == typename) {
            return
        }
        this.lastReshLocation = Global.getInstance().now();
        this.currentGameType = typename;
        let parent = this.gameTypeListNode;
        // let btn_hot = utils.getChildByPath(parent,"layout.btn_hot");//hot隐藏
        let btn_casino = utils.getChildByPath(parent, "layout.btn_casino.Background");
        let btn_slots = utils.getChildByPath(parent, "layout.btn_slots.Background");
        let btn_poker = utils.getChildByPath(parent, "layout.btn_poker.Background");
        let btn_bingo = utils.getChildByPath(parent, "layout.btn_bingo.Background");
        let btn_fishing = utils.getChildByPath(parent, "layout.btn_arcade.Background");
        let btn_sports = utils.getChildByPath(parent, "layout.btn_sports.Background");

        let lab_casino = utils.getChildByPath(btn_casino, "Label");
        let lab_slots = utils.getChildByPath(btn_slots, "Label");
        let lab_poker = utils.getChildByPath(btn_poker, "Label");
        let lab_bingo = utils.getChildByPath(btn_bingo, "Label");
        let lab_arcade = utils.getChildByPath(btn_fishing, "Label");
        let lab_sports = utils.getChildByPath(btn_sports, "Label");
        // btn_hot.opacity = 0;//hot隐藏
        btn_casino.opacity = 0;
        btn_slots.opacity = 0;
        btn_poker.opacity = 0;
        btn_bingo.opacity = 0;
        btn_fishing.opacity = 0;
        btn_sports.opacity = 0;

        lab_casino.color = cc.color(34, 34, 34);
        lab_slots.color = cc.color(34, 34, 34);
        lab_poker.color = cc.color(34, 34, 34);
        lab_bingo.color = cc.color(34, 34, 34);
        lab_arcade.color = cc.color(34, 34, 34);
        lab_sports.color = cc.color(34, 34, 34);


        let target = null;
        let targetLab = null;
        if (typename == GlobalEnum.GAME_TYPE.CASINO) {
            target = btn_casino;
            targetLab = lab_casino;
        } else if (typename == GlobalEnum.GAME_TYPE.SLOTS) {
            target = btn_slots;
            targetLab = lab_slots;
        } else if (typename == GlobalEnum.GAME_TYPE.POKER) {
            target = btn_poker;
            targetLab = lab_poker;
        } else if (typename == GlobalEnum.GAME_TYPE.BINGO) {
            target = btn_bingo;
            targetLab = lab_bingo
        } else if (typename == GlobalEnum.GAME_TYPE.FISHING) {
            target = btn_fishing;
            targetLab = lab_arcade;
        } else if (typename == GlobalEnum.GAME_TYPE.SPORTS) {
            target = btn_sports;
            targetLab = lab_sports;
        } else if (typename == "hot") {
            // target = btn_hot;//hot隐藏
        }
        if (target) {
            cc.Tween.stopAllByTarget(target);
            cc.Tween.stopAllByTarget(this.typeAnimBg);
            this.typeAnimBg.opacity = 255;
            cc.tween(this.typeAnimBg)
                .to(0.2, { y: target.parent.y }, { easing: "smooth" })
                .call(() => {
                    this.typeAnimBg.y = target.parent.y;
                    targetLab.color = cc.color(34, 34, 34);
                }).start();
            cc.tween(target).delay(0.2).to(0.05, { opacity: 255 }).call(() => {
                this.typeAnimBg.opacity = 0
                btn_casino.opacity = 0;
                btn_slots.opacity = 0;
                btn_poker.opacity = 0;
                btn_bingo.opacity = 0;
                btn_fishing.opacity = 0;
                btn_sports.opacity = 0;
                target.opacity = 255;
            }).start();
        }
    }
    scrollToTarget(gameType) {
        let offset = 0;
        let type_str = ''
        if (gameType == "hot") {
            offset = this.scorllOffSetsList["Hot"];
            type_str = 'Hot'
        } else if (gameType == GlobalEnum.GAME_TYPE.CASINO) {
            offset = this.scorllOffSetsList["Casino"];
            type_str = 'Casino'
        } else if (gameType == GlobalEnum.GAME_TYPE.SLOTS) {
            offset = this.scorllOffSetsList["Slots"];
            type_str = 'Slots'
        } else if (gameType == GlobalEnum.GAME_TYPE.POKER) {
            offset = this.scorllOffSetsList["Poker"];
            type_str = 'Poker'
        } else if (gameType == GlobalEnum.GAME_TYPE.BINGO) {
            offset = this.scorllOffSetsList["Bingo"];
            type_str = 'Bingo'
        } else if (gameType == GlobalEnum.GAME_TYPE.FISHING) {
            offset = this.scorllOffSetsList["Arcade"];
            type_str = 'Arcade'
        } else if (gameType == GlobalEnum.GAME_TYPE.SPORTS) {
            offset = this.scorllOffSetsList["Sports"];
            type_str = 'Sports'
        }
        offset = offset + this.gameScrollOffset;
        // this.parentScrollView.scrollToOffset(new cc.Vec2(0,offset-30),0.2);
        //找到这个位置 对应的i_index  
        let temp_arr = this.all_item_nodes.filter(item => {
            return item.type == type_str
        })
        if (temp_arr.length > 0) {
            let moveindex = temp_arr[0].iindex;
            let min_i = 10000;
            let max_i = 0;
            let hasit = false;
            for (let index = 0; index < this.itemList.length; index++) {
                const element: any = this.itemList[index];
                if (element.i_index < min_i) {
                    min_i = element.i_index
                }
                if (element.i_index > max_i) {
                    max_i = element.i_index
                }
                if (element.i_index == moveindex) { hasit = true }
            }
            if (hasit) {
                this.parentScrollView.scrollToOffset(new cc.Vec2(0, offset - 30), 0.8);
            } else {
                //删除所有的
                this.itemList.forEach((element: any) => {
                    poolManager.instance.putNode(element.node)
                });
                this.itemList = []
                this.bScrolling = false
                this.bScroll_ani = true
                this.readd_itemlist(moveindex)
                this.parentScrollView.scrollToOffset(cc.v2(0, offset - 30), 0.2);
                let self = this;
                setTimeout(() => {
                    //这里先不执行 update 
                    self.bScroll_ani = false;
                }, 249);
            }
        }
    }
    readd_itemlist(start_index: number) {
        let starti = 0
        let endi = 21
        if (start_index > 0) {
            starti = start_index - 3;
            endi = starti + 21
        }

        if (endi > this.all_configs.length - 1) {
            starti = this.all_configs.length - 22
            endi = this.all_configs.length - 1
        }
        for (let index = starti; index <= endi; index++) {
            this.add_item_game(index)
        }
    }
    gameTpyeButtonClickListener(event, userdata) {
        this.gameTypeButtonChanged(userdata);
        this.scrollToTarget(userdata);
        this.scrollGameList = false;
    }
    refreshGameList(showData) {
        this.initGameListData(showData);
        if (this.all_configs.length > 0) {
            this.parentScrollView.scrollToOffset(cc.v2(0, 0), 0.1);
            let self = this;
            setTimeout(() => {
                self.initGameListView();//重新 初始化游戏列表
            }, 100);
        } else {
            this.initGameListView();//重新 初始化游戏列 
        }
    }
    preLoadPrefab() {
        let prefabs = [
            UI_PATH_DIC.NewView,
            UI_PATH_DIC.Promo,
            UI_PATH_DIC.UserProfileSetting,
        ]
        cc.resources.preload(prefabs)
    }
    ShowMoreGameEvent() {
        this.node.active = false
        this.hideLiveVideo();
    }
    CloseMoreGameEvent() {
        this.node.active = true;
        this.showLiveVideo();
    }
    bindClickEvent(sender: cc.Button, customData: HallGame_Icon) {
        //可以根据type 绑定哪个函数 //新加的分类
        var clickEventHandler = new cc.Component.EventHandler();
        if (customData.game_type == 0) {
            //没有房间列表的
            clickEventHandler.handler = "onGameClick"
            //gameName要和 bundlename一致
            clickEventHandler.customEventData = customData.game_name
        } else if (customData.game_type == 1) {
            //有房间列表的
            clickEventHandler.handler = "onTeenClick"
            clickEventHandler.customEventData = customData.game_id + ""
        } else {
            //外接分类的
            clickEventHandler.handler = "onClickJiLi"
            clickEventHandler.customEventData = customData.game_id + ""
        }
        clickEventHandler.target = this.node
        clickEventHandler.component = "Hall";
        sender.clickEvents.push(clickEventHandler)
    }
    showGame(gameNode: any, gameID: any) {
        gameNode.active = true;
        if (!!Global.getInstance().config) {
            if (Global.getInstance().config["game_lobby"]["new"].indexOf(gameID) >= 0) {
                gameNode.getChildByName("new").active = true;
            } else if (Global.getInstance().config["game_lobby"]["hot"].indexOf(gameID) >= 0) {
                gameNode.getChildByName("hot").active = false;
            }
        }
    }
    loadingOperate(gameId: any, isHide: boolean) {
        let loadingName = null;
        if (gameId == 1 || gameId == 4 || gameId == 5) {
            loadingName = "waitGotoTeen";
        } else if (gameId == 2) {
            loadingName = "waitGotoRummy";
        } else if (gameId == 18 || gameId == 19 || gameId == 20) {
            loadingName = "waitGotoGuess";
        } else if (gameId == 13 || gameId == 14 || gameId == 15) {
            loadingName = "waitGotoCarrom";
        } else if (gameId == 25 || gameId == 26 || gameId == 40 || gameId == 41) {
            loadingName = "waitGotoTruco";
        } else if (gameId == 3) {
            loadingName = "waitGotoTexas";
        } else if (gameId == 27) {
            loadingName = "waitGotoCacheta";
        } else if (gameId == base.GAME_ID.GID_TONGITS) {
            loadingName = "waitGotoTongits";
        } else if (gameId == base.GAME_ID.GID_PUSOY) {
            loadingName = "waitGotoPusoy";
        }
        if (loadingName) {
            if (isHide) {
                Global.getInstance().hideShowLoading(loadingName);
            } else {
                Global.getInstance().showLoading(loadingName);
            }
        }
    }
    // MessageImpl
    protoIds(): number[] {
        return [
            MSG_TYPE.PLAYER_LOGOUT_REQ,
            MSG_TYPE.BALANCE_HISTORY_REQ,
            MSG_TYPE.BONUS_HISTORY_REQ,
            MSG_TYPE.PLAYER_BALANCE_REQ,

            MSG_TYPE.PLAYER_LOGOUT_REPLY,
            // MSG_TYPE.BALANCE_HISTORY_REPLY,
            MSG_TYPE.BONUS_HISTORY_REPLY,
            MSG_TYPE.PLAYER_INFO_CHANGE,
        ];
    }
    protoEncode(protoid: number, data: Object): Uint8Array {
        switch (protoid) {
            case MSG_TYPE.BALANCE_HISTORY_REQ:
                return new Uint8Array(0);
            case MSG_TYPE.BONUS_HISTORY_REQ:
                return new Uint8Array(0);
            case MSG_TYPE.PLAYER_BALANCE_REQ:
                return new Uint8Array(0);
            default:
                break;
        }
        return base.RoomListReq.encode(data).finish();
    }
    protoDecode(protoid: number, buffer: Uint8Array): Object {
        switch (protoid) {
            case MSG_TYPE.BONUS_HISTORY_REPLY:
                return base.BonusHistory.decode(buffer);
            case MSG_TYPE.PLAYER_INFO_CHANGE:
                return base.PlayerInfoChange.decode(buffer);
            default:
                break;
        }
        return base.RoomListReq.decode(buffer);
    }
    onProtoMessage(message: Object): void {
        let data = message["data"];

        switch (message["protoid"]) {
            case MSG_TYPE.BONUS_HISTORY_REPLY:
                Global.getInstance().hideShowLoading("waitBonusHistory");
                cc.director.emit("bonusdetaildata_get", (!data ? [] : data["bonuses"]));
                break;
            case MSG_TYPE.PLAYER_INFO_CHANGE:
                Global.getInstance().setStoreageData(Global.GLOBAL_STORAGE_KEY.UPLEVEL_DATA + Global.getInstance().userdata["user_id"], JSON.stringify(data));
                // Global.getInstance().userdata["vip_level"] = data["vipLevel"]
                Global.getInstance().userdata["avatar"] = data["headId"]
                Global.getInstance().userdata["fb_avatar"] = data["headUrl"]
                Global.getInstance().userdata["avatar_frame"] = data["avatarFrame"]
                Global.getInstance().userdata["card_back"] = data["cardBack"]

                BetLevelManager.instance().levelUpEvent(data)
                break;
            default:
                break;
        }
    }
    refreshNotice() {
        if (Global.getInstance().activityData.notice.length > 0) {
            let notice = { type: "system", msg: "" };

            for (let i = 0; i < Global.getInstance().activityData.notice.length; i++) {
                if (notice.msg != "") {
                    notice.msg += "^";
                }
                notice.msg += Global.getInstance().activityData.notice[i].content;
            }

            for (let i = 0; i < Global.getInstance().scrollmessage.data.length; i++) {
                if (Global.getInstance().scrollmessage.data[i].type == "system") {
                    Global.getInstance().scrollmessage.data.splice(i, 1);
                }
            }
            Global.getInstance().scrollmessage.data.unshift(notice);
        }
    }
    getTableId() {
        if (cc.sys.OS_ANDROID === cc.sys.os) {
            let tableId = null;
            if (ALL_APP_SOURCE_CONFIG.app_id > 6009 && cc.sys.isNative) {
                tableId = jsb.reflection.callStaticMethod("org/cocos2dx/javascript/AppActivity", "getTableId", "()Ljava/lang/String;");
            } else {
                tableId = Global.getInstance().getPasteboard();

                if (!!tableId && tableId != "") {
                    let intIndex = tableId.indexOf("tableID=", -1);
                    if (intIndex == - 1) {
                        tableId = "";
                    } else {
                        tableId = tableId.substring(intIndex + 8, intIndex + 14);
                    }
                }
            }
            if (!!tableId && tableId != "" && tableId.length == 6) {
                let roomNumber = Number(tableId);
                if (!isNaN(roomNumber)) {
                    if (roomNumber != Global.getInstance().getStoreageData((<any>window).Global.GLOBAL_STORAGE_KEY.KCREATETABLEID, 0)) {
                        Global.getInstance().showCommonTip(Global.getInstance().getLabel("invitation_text_4") + "," + Global.getInstance().getLabel("invitation_text_5") + tableId, this, false, function () {
                            let gameId = parseInt(tableId.substring(0, 2));
                            SocketUtils.getInstance().send(MSG_TYPE.ENTER_ROOM_REQ, {
                                gameId: gameId,
                                roomId: gameId * 100 + 1,
                                matchType: 1,
                                tableNo: roomNumber
                            });
                            Global.getInstance().setPasteboard("");
                        }, function () {
                            Global.getInstance().setPasteboard("");
                        });
                    }
                }
            }
        }
    }
    showAllGamesSerchView(currentType, type?) {
        if (!Global.getInstance().token) {
            //未登录 跳转登录页
            uiManager.instance.showDialog(UI_PATH_DIC.phonePasswordLogin, [{ way: LOGIN_WAY.PhoneCode, curPromoPage: false }])
            return;
        }
        uiManager.instance.loadPrefabByLoading(UI_PATH_DIC.AllGameView, cc.Prefab, () => {
            this.showLiveVideo();
        }, (err, prefab: any) => {
            if (err) {
                return;
            }
            let node = cc.instantiate(prefab);
            node.parent = Global.instance.popNode;
            node.position = new cc.Vec3(0, 0);
            // node.zIndex = 99999;//设置比弹窗层高 覆盖他们即可
            node.getComponent(AllGameView).gameDataList(currentType, () => {
                this.showLiveVideo();
            });
            if (type) {
                node.getComponent(AllGameView).init(type);
            }
            this.hideLiveVideo();
        });
    }
    Promo_ShowAllGameSearchView(args) {
        if (!Global.getInstance().token) {
            //未登录 跳转登录页
            uiManager.instance.showDialog(UI_PATH_DIC.phonePasswordLogin, [{ way: LOGIN_WAY.PhoneCode, curPromoPage: false }])
            return;
        }
        uiManager.instance.loadPrefabByLoading(UI_PATH_DIC.AllGameView, cc.Prefab, () => {
            this.showLiveVideo();
        }, (err, prefab: any) => {
            if (err) {
                return;
            }
            let node = cc.instantiate(prefab);
            node.parent = Global.instance.popNode;
            node.position = new cc.Vec3(0, 0);
            // node.zIndex = 99999;//设置比弹窗层高 覆盖他们即可
            let currentType = args.type;
            let providerList = args.provider;
            node.getComponent(AllGameView).gameDataList(currentType, () => {
                this.showLiveVideo();
            });
            this.hideLiveVideo();
            if (providerList) {
                node.getComponent(AllGameView).filterGameList(providerList);
            }
        });
    }
    RefreshGameListThird() { }
    hideLiveVideo() {
        //隐藏video
        cc.director.emit("Hide_LiveVideo");
    }

    showLiveVideo() {
        if (!cc.director.getScene()) {
            return;
        }
        let gamescene = cc.director.getScene().getComponentInChildren(GameControl);
        if (gamescene && !gamescene.mTabbar.isHall_show()) {
            return;
        }
        this.node.active = true;
        if (this.gameScrollView && cc.isValid(this.gameScrollView)) {
            this.gameScrollView.active = true;
        }
        cc.director.emit("Show_LiveVideo");//显示当前video
        Global.getInstance().setPageId(E_PAGE_TYPE.HALL);
        this.get_all_activity_bonus();
    }
    addCashClick(event, data) {
        Global.getInstance().loadWithdrawConfig(() => {
            if (ALL_APP_SOURCE_CONFIG.channel == E_CHANEL_TYPE.G_CASH) {
                //gcash 直接弹窗提示 是否跳转充值
                Global.getInstance().back_mini_buy_tips(true);
                return;
            }
            let btn_spin = Global.getInstance().popNode.getChildByName("btn_spin");
            if (btn_spin) btn_spin.zIndex = -1;
            this.hideHallLive();
            uiManager.instance.showDialog(UI_PATH_DIC.Deposit, [{ type: "halltoDeposit" }], null, DEEP_INDEXZ.DEPOSIT);
        }, true);
    }
    /**点击Notice站内信 */
    onClickNoticeBtn() {
        if (!!Global.getInstance().token) {
            uiManager.instance.showDialog(UI_PATH_DIC.Notice, ["hallto_notice"],null,DEEP_INDEXZ.CUSTOM);
            this.hideLiveVideo();
        } else {//未登录 跳转登录页
            uiManager.instance.showDialog(UI_PATH_DIC.phonePasswordLogin, [{ way: LOGIN_WAY.PhoneCode }])
        }
    }
    onClickCustomerBtn() {
        this.hideLiveVideo();
        if (serviceMgr.inner_ing) return;
        serviceMgr.instance.show_achat(SERVICE_TYPE.coustom);
    }
    onComeSoon() {
        Global.getInstance().showSimpleTip(Global.getInstance().getLabel("tipword18"));
    }
    showTotalAnim(total_prize) {
        let btn_spin = Global.getInstance().popNode.getChildByName("btn_spin");
        if (!btn_spin) return;
        let lab_total_award = utils.getChildByPath(btn_spin, "totoalprize.lab_total_award").getComponent(cc.Label);
        let targetNum = parseInt(total_prize)
        this.showNumberScroll(Math.floor(targetNum), 0, true);
        if (parseInt(total_prize) >= 0) {
            lab_total_award.string = "₱";
            let lastNumber = Math.floor(targetNum);
            let lastReq = Global.getInstance().now();
            this.schedule(() => {
                let targetNum = lastNumber + (Global.getInstance().now() - lastReq) / 1000 * utils.getRandomInt(15, 25);
                this.showNumberScroll(Math.floor(targetNum), Math.floor(lastNumber));
                lastNumber = Math.floor(targetNum);
                lastReq = Global.getInstance().now();
            }, 3, cc.macro.REPEAT_FOREVER, 0)
        } else {
            lab_total_award.string = "--";
        }
    }
    showNumberScroll(targetNum, lastNumber, isfirst?) {
        if (targetNum >= 999999999) {//超过9E就不滚动了
            targetNum = 999999999
        }
        if (lastNumber >= 999999999) {
            return;
        }
        let btn_spin = Global.getInstance().popNode.getChildByName("btn_spin");
        if (!btn_spin) return;
        let oldchars: string[] = utils.formatNumberWithCommas(Math.round(lastNumber), 0).split("");
        let chars: string[] = utils.formatNumberWithCommas(Math.round(targetNum), 0).split("");
        chars = chars.reverse();
        oldchars = oldchars.reverse();
        for (let index = 0; index < chars.length; index++) {
            let element = chars[index];
            let element2 = oldchars[index];
            let labNumber = utils.getChildByPath(btn_spin, "totoalprize.scrollnumber" + (index + 1));
            if (labNumber && labNumber.isValid) {
                labNumber.active = true;
                if (isfirst) {
                    if (parseInt(element)) {
                        labNumber.getComponent(ScrollNumber).curNum = parseInt(element);
                        labNumber.getComponent(ScrollNumber).setLabel(0, 9);
                    }
                } else {
                    if (parseInt(element) >= 0) {
                        if (parseInt(element2) == null || (parseInt(element2) == parseInt(element) && index > 3)) {

                        } else {
                            setTimeout(() => {
                                labNumber.getComponent(ScrollNumber).scrollTo(parseInt(element));
                            }, index * 100);
                        }
                    }
                }
            }
        }
    }
    onInHallEvent() {
        // Global.getInstance().updateBalanceAfterGame(() => {
        let msgsBox = Global.getInstance().getAllMsgBox()
        if (msgsBox && msgsBox.length > 0) {
            for (let index = 0; index < msgsBox.length; index++) {
                const element = msgsBox[index];
            }
        }
        // })
        Global.getInstance().setPageId(E_PAGE_TYPE.HALL);
    }
    onClickTermsOfUse() {
        uiManager.instance.showDialog(UI_PATH_DIC.PrivacyTip, [null, "term"], null, DEEP_INDEXZ.PRIVACY_TIPS)
    }
    onClickPrivacyPolicy() {
        uiManager.instance.showDialog(UI_PATH_DIC.PrivacyTip, [null, "privacy"], null, DEEP_INDEXZ.PRIVACY_TIPS)
    }

    onClickToUpdateBanlance() {
        let nowTime = Global.getInstance().now()
        let timegap = (nowTime - this.lastReqBanlanceTime) / 1000;
        if (timegap < 2) {
            cc.warn("updateBalanceAfterGame too frequently")
            return;
        }
        this.lastReqBanlanceTime = nowTime;

        Global.getInstance().updateBalanceAfterGame()
        cc.Tween.stopAllByTarget(this.m_moneny.node);
        cc.tween(this.m_moneny.node).to(0.05, { opacity: 0 }).to(0.1, { opacity: 255 }).start();
    }
    /**点击pagcor */
    onClickPagcorBtn() {
        setTimeout(() => {
            let url = "https://www.pagcor.ph/regulatory/responsible-gaming.php";
            utils.openUrl(url);
        })
    }

    onClickWalletBtn() {
        if (!Global.getInstance().token) {
            //未登录 跳转登录页
            uiManager.instance.showDialog(UI_PATH_DIC.phonePasswordLogin, [{ way: LOGIN_WAY.PhoneCode, curPromoPage: false }])
            return;
        }

        HttpProxy.instance.getWalletTaskInfo().then((rsp: NetRspObject) => {
            if (rsp.msg == NetRspMsg.Success && cc.isValid(this.node)) uiManager.instance.showDialog(UI_PATH_DIC.WalletTask,null,null,DEEP_INDEXZ.WalletTask);
        });
    }
}
