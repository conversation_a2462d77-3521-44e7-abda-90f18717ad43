import { E_MODIFY_TYPE } from "./ChooseWallet";
import { E_FUND_TYPE, UI_PATH_DIC } from "./GlobalConstant";
import Global from "./GlobalScript";
import SideLay from "./SideLay";
import UICommon from "./component/UICommon";
import { uiManager } from "./mgr/UIManager";
import HttpUtils from "./net/HttpUtils";

const { ccclass, property } = cc._decorator;
export const chooseBankCard = "chooseBankCard"
export function showChooseBankCard(bankCard) {
    uiManager.instance.showDialog(UI_PATH_DIC.ChooseBankCard, [bankCard])
}
@ccclass
export default class ChooseBankCard extends UICommon {
    @property(cc.EditBox)
    firstNameEb: cc.EditBox = null;
    @property(cc.EditBox)
    middleNameEb: cc.EditBox = null;
    @property(cc.EditBox)
    lastNameEb: cc.EditBox = null;
    @property(cc.EditBox)
    accountEb: cc.EditBox = null;

    @property(cc.Label)
    lbError0: cc.Label = null;
    @property(cc.Label)
    lbError1: cc.Label = null;
    @property(cc.Label)
    lbError2: cc.Label = null;
    @property(cc.Label)
    lbError3: cc.Label = null;
    userAccountInfo: any[] = []
    allAccountCodes: any[] = []
    curAccountInfo: any = null;
    _firstName: String = ""
    _middleName: String = ""
    _lastName: String = ""
    _account: String = ""

    _accountId: String = "";
    _modifyType: E_MODIFY_TYPE;
    curPayCode: String = '';
    @property(SideLay)
    sideLay: SideLay = null;
    @property(cc.Node)
    ndBank: cc.Node = null;
    curBankName: string = '';
    onLoad() {
        // this.comboBox = this.node.getComponentInChildren("ComboBox")
        // if (this.comboBox) this.comboBox.init(this, "Please select a bank")
        cc.director.on("bank_select", this.onBankSelect, this);

    }
    protected onDestroy(): void {
        cc.director.off("bank_select", this.onBankSelect, this);

    }
    init(args) {
        // this.par = par;
        this.userAccountInfo = args.user_account_info;
        this.allAccountCodes = args.all_account_code;
        // let cardItems = [];
        // for (let i = 0; i < this.allAccountCodes.length; i++) {
        //     const element = this.allAccountCodes[i];
        //     cardItems.push(element)
        // }
        // if (this.comboBox) this.comboBox.initItems(this.allAccountCodes)
    }
    start() {
        super.start()
        if (this.userAccountInfo.length > 0) {
            this.curAccountInfo = this.userAccountInfo[0]
            this.showPayAccountInfo(this.curAccountInfo)
            this.curPayCode = this.curAccountInfo.pay_code
            // this.comboBox.comboLabel.string = this.curAccountInfo.name
            this.ndBank.getChildByName("name").active = true;
            this.ndBank.getChildByName("name").getComponent(cc.Label).string = this.curAccountInfo.name;
            this.ndBank.getChildByName("placeholder").active = false;
            this.curBankName = this.curAccountInfo.name
            this._modifyType = E_MODIFY_TYPE.MODIFY;
        } else {
            // this.curPayCode = this.allAccountCodes[0].pay_code
            this._modifyType = E_MODIFY_TYPE.ADD;
        }
    }
    showCode(code) {
        if (code) {
            // this.clearPayAccountInfo();
            this.curPayCode = code
            // for (let i = 0; i < this.userAccountInfo.length; i++) {
            //     const element = this.userAccountInfo[i];
            //     if (element.pay_code == code) {
            //         this.showPayAccountInfo(element)
            //     }
            // }
        }
    }
    clearPayAccountInfo() {
        this.curAccountInfo = null;
        this.firstNameEb.string = ""
        this.middleNameEb.string = ""
        this.lastNameEb.string = ""
        this.accountEb.string = ""
        this._firstName = ''
        this._lastName = ''
        this._middleName = ''
        this._account = ''
    }
    showPayAccountInfo(info) {
        if (!info) return;
        this.firstNameEb.string = info.first_name || ""
        this._firstName = info.first_name || ""
        this.middleNameEb.string = info.middle_name || ""
        this._middleName = info.middle_name || ""
        this.lastNameEb.string = info.last_name || ""
        this._lastName = info.last_name || ""
        this.accountEb.string = info.account_no || ""
        this._account = info.account_no || ""
        this.curAccountInfo = info;
        this.curPayCode = info.pay_code
        this._accountId = info.account_id || "";
    }
    onClickOK() {
        if (this._modifyType == E_MODIFY_TYPE.ADD) {
            // if (!this._firstName || 
            //     !this._lastName || 
            //     !this._account || 
            //     !this.curPayCode) {
            //     return Global.getInstance().showSimpleTip("Account information is incomplete")
            // }
            if (!this.curBankName) {
                this.lbError0.string = "Please select a bank."
                return;
            } else {
                this.lbError0.string = ''
            }
            if (!this._firstName) {
                this.lbError1.string = "Please enter first name."
                return;
            } else {
                this.lbError1.string = ''
            }
            if (!this._lastName) {
                this.lbError2.string = "Please enter last name."
                return;
            } else {
                this.lbError2.string = ''
            }
            if (!this._account) {
                this.lbError3.string = "Please enter bank account no."
                return;
            } else {
                this.lbError3.string = ''
            }

            let data = {
                account: this._account,
                firstName: this._firstName,
                middleName: this._middleName,
                lastName: this._lastName,
                type: "Bank",
                sucFunc: () => {
                    HttpUtils.getInstance().post(3, 3, this, "api/add/withdraw/no", {
                        token: Global.getInstance().token,
                        account_no: this._account,
                        first_name: this._firstName,
                        middle_name: this._middleName,
                        last_name: this._lastName,
                        pay_code: this.getPayCode(this.curBankName),
                        type: E_FUND_TYPE.BankCard
                    }, (response) => {
                        if (response.data) {
                            Global.getInstance().showSimpleTip("Your fund account has been added successfully")
                            cc.director.emit("modifyWithdrawNoSuc", response.data)
                            this.closeAction()
                        }
                    }, (response) => {
                        if (response && response.code) {
                            if (response.code == 1) {
                                Global.getInstance().showSimpleTip(response.msg)
                            } else {
                                let str = response?.msg;
                                Global.getInstance().showSimpleTip(str)
                            }
                        }
                    });
                }
            }
            uiManager.instance.showDialog(UI_PATH_DIC.AccountConfirmation, [data])

        } else if (this._modifyType == E_MODIFY_TYPE.MODIFY) {
            if (!this._firstName) {
                this.lbError1.string = "Please enter first name."
                return;
            }
            if (!this._lastName) {
                this.lbError2.string = "Please enter last name."
                return;
            }
            if (!this._account) {
                this.lbError3.string = "Please enter bank account no."
                return;
            }
            let data = {
                account: this._account,
                firstName: this._firstName,
                middleName: this._middleName,
                lastName: this._lastName,
                type: "Bank",
                sucFunc: () => {
                    HttpUtils.getInstance().post(3, 3, this, "api/update/account/info", {
                        token: Global.getInstance().token,
                        account_no: this._account,
                        account_id: this._accountId,
                        first_name: this._firstName,
                        middle_name: this._middleName,
                        last_name: this._lastName,
                        pay_code: this.getPayCode(this.curBankName),
                        type: E_FUND_TYPE.BankCard
                    }, (response) => {
                        if (response.data) {
                            Global.getInstance().showSimpleTip("Your fund account has been added successfully")
                            cc.director.emit("modifyWithdrawNoSuc", response.data)
                            this.closeAction()
                        }
                    }, (response) => {
                        if (response && response.code) {
                            if (response.code == 1) {
                                Global.getInstance().showSimpleTip(response.msg)
                            } else {
                                let str = response.msg
                                Global.getInstance().showSimpleTip(str)
                            }
                        }
                    });
                }
            }
            uiManager.instance.showDialog(UI_PATH_DIC.AccountConfirmation, [data])

        }
    }
    getPayCode(str) {
        for (let i = 0; i < this.allAccountCodes.length; i++) {
            const element = this.allAccountCodes[i];
            if (element.name == str) {
                return element.pay_code
            }

        }
    }
    onClickBank() {
        let cardItems = [];
        for (let i = 0; i < this.allAccountCodes.length; i++) {
            const element = this.allAccountCodes[i];
            element['isCur'] = element.name == this.curBankName
            cardItems.push(element)
        }
        this.sideLay.init(cardItems)
    }
    onBankSelect(data) {
        this.ndBank.getChildByName("placeholder").active = false;
        let name = this.ndBank.getChildByName("name");
        name.active = true;
        name.getComponent(cc.Label).string = data.name
        this.curBankName = data.name
    }
    onEditBegin(editbox: cc.EditBox) {
        Global.getInstance().editParentMove(editbox, this.lay,800);//暂时未用到
        editbox.placeholderLabel.node.name = editbox.placeholder
        editbox.placeholder = ""

    }
    onEditEnd(editbox) {
        if (Global.getInstance().needScreenUp()) {
            // this.node.y = this.node.y - 400
            this.lay.y = 0
        }
        editbox.placeholder = editbox.placeholderLabel.node.name

    }
    onEditFirstName(text) {
        this._firstName = text;
        if (text != '') {
            this.lbError1.string = ''
        }
    }
    onEditLastName(text) {
        this._lastName = text;
        if (text != '') {
            this.lbError2.string = ''
        }
    }
    onEditMiddleName(text) {
        this._middleName = text;
    }
    onEditAccount(text, eb) {
        this._account = text.replace(/[^0-9]/g, '');
        eb.string = this._account;
        if (text != '') {
            this.lbError3.string = ''
        }
    }
    closeAction() {
        this.hide()
    }
}
