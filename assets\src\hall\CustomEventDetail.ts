import UICommon from "../component/UICommon";
import { ALL_APP_SOURCE_CONFIG } from "../Config";
import GameControl from "../GameControl";
import { DEEP_INDEXZ, E_CHANEL_TYPE, UI_PATH_DIC } from "../GlobalConstant";
import Global from "../GlobalScript";
import { LOGIN_WAY } from "../login/phonePasswordLogin";
import { uiManager } from "../mgr/UIManager";
import HttpUtils from "../net/HttpUtils";
import { GlobalEnum } from "../room/GlobalEnum";
import RoundRectMask from "../RoundRectMask";
import utils from "../utils/utils";
import AllGameView from "./AllGameView";

const { ccclass, property } = cc._decorator;

@ccclass
export default class CustomEventDetail extends UICommon {
    @property(cc.ScrollView)
    scrollView: cc.ScrollView = null;
    @property(cc.Node)
    mainNode: cc.Node = null;
    @property(cc.Node)
    detailTitle: cc.Node = null;
    @property(cc.Node)
    detailModel: cc.Node = null;
    @property(cc.Node)
    title: cc.Node = null;

    ctype = 0;//区分活动内容 0是闯关活动
    //1是cashback 活动
    gameIconPosList = {
        p1:[0],
        p2:[-177,177],
        p3:[-346,0,346],
        p4:[-346,0,346,0],
        p5:[-346,0,346,-177,177],
        p6:[-346,0,346,-346,0,346]
    }
    onLoad() {}
   
    start() {
        this.scrollView.node.on("scrolling", this.onScroll, this);
        let page = this.node.getChildByName("page").getComponent(cc.PageView);
        page.node.on('page-turning', ()=>{
            let curIndex = page.getCurrentPageIndex();
            if (curIndex != 1) {
                this.node.destroy();
            }
        }, self);
        let tempnode = this.node.getChildByName("temp");
        page.insertPage(cc.instantiate(tempnode),0);
        page.scrollToPage(1,0.01);
        

        if(this.ctype == 1){
            this.scrollView.getComponent(cc.Widget).bottom = 500;
            this.scrollView.getComponent(cc.Widget).updateAlignment();
            this.show_myperfor_cashback();
        }else{
            this.scrollView.getComponent(cc.Widget).bottom = 580;
            this.scrollView.getComponent(cc.Widget).updateAlignment();
            this.show_myperform();
        }
    }
    init(args){
        if(args){
            let url_detail_title = args.data.picture_details_title;
            let url_pic_details = args.data.picture_details; 
            let lab_title = this.title.getComponent(cc.Label);
            let title = args.data.title;
            lab_title.string = title;
            let details_no = utils.getChildByPath(this.mainNode, "promos_acv_no");
            if(url_detail_title){
                if(utils.isPicsWithUrl(url_detail_title)){
                    if (!/^http/.test(url_detail_title)) {
                        url_detail_title = ALL_APP_SOURCE_CONFIG.remote_assets_prod[Global.DEBUG]+url_detail_title;
                    }
                    let title_sp = this.detailTitle.getComponent(cc.Sprite);
                    let originalWidth;
                    Global.getInstance().loadImgFromUrl_V2(url_detail_title, (loaded,image)=>{
                        if(loaded){
                           details_no.active = false; 
                        }
                        title_sp.spriteFrame = image;
                        originalWidth = image._originalSize.width;
                        if (originalWidth < 1080) {
                            if(title_sp.node) title_sp.node.width = 1080;
                        }
                    },title_sp);
                }
            }
            if(url_pic_details && url_pic_details.length > 0){
                for(let i = 0; i < url_pic_details.length; i++){
                    if(utils.isPicsWithUrl(url_pic_details[i])){
                        let imgModel = cc.instantiate(this.detailModel);
                        let url = url_pic_details[i];
                        if (!/^http/.test(url)) {
                            url = ALL_APP_SOURCE_CONFIG.remote_assets_prod[Global.DEBUG]+url_pic_details[i];
                        }
                        let details_sp = imgModel.getComponent(cc.Sprite);
                        let originalWidth;
                        Global.getInstance().loadImgFromUrl_V2(url, (loaded, image)=>{
                            if(loaded){
                                details_no.active = false;
                            }
                            details_sp.spriteFrame = image;
                            originalWidth = image._originalSize.width;
                            if (originalWidth < 1080) {
                              if(details_sp.node) details_sp.node.width = 1080;
                            }
                            if(details_sp.node) imgModel.width = details_sp.node.width;
                        },details_sp);
                        let content = utils.getChildByPath(this.scrollView.node, "view.content");
                        imgModel.parent = content;
                        imgModel.x = 0;
                    }
                }
            }
            //底部跳转按钮
            let jumpBtnLayout = utils.getChildByPath(this.mainNode, "downLayout");
            // let btn_jump = utils.getChildByPath(this.mainNode, "jumpBtnLayout.btn_jump");
            // let lab_jump = utils.getChildByPath(this.mainNode, "jumpBtnLayout.btn_jump.Background.lab_jump");
            //底部BetNow按钮
            // let betNowBtnLayout = utils.getChildByPath(this.mainNode, "betNowBtnLayout");
            //底部Games节点
            // let gameBtnLayout = utils.getChildByPath(this.mainNode, "gameBtnLayout");
            
            
        }
    }
    init2(args){
        if(args){
            let url_detail_title = args.data.picture_details_title;
            let url_pic_details = args.data.picture_details; 
            let lab_title = this.title.getComponent(cc.Label);
            let title = args.data.title;
            lab_title.string = title;
            let details_no = utils.getChildByPath(this.mainNode, "promos_acv_no");
            if(url_detail_title){
                if(utils.isPicsWithUrl(url_detail_title)){
                    if (!/^http/.test(url_detail_title)) {
                        url_detail_title = ALL_APP_SOURCE_CONFIG.remote_assets_prod[Global.DEBUG]+url_detail_title;
                    }
                    let title_sp = this.detailTitle.getComponent(cc.Sprite);
                    let originalWidth;
                    Global.getInstance().loadImgFromUrl_V2(url_detail_title, (loaded,image)=>{
                        if(loaded){
                           details_no.active = false; 
                        }
                        title_sp.spriteFrame = image;
                        originalWidth = image._originalSize.width;
                        if (originalWidth < 1080) {
                            if(title_sp.node) title_sp.node.width = 1080;
                        }
                    },title_sp);
                }
            }
            if(url_pic_details && url_pic_details.length > 0){
                for(let i = 0; i < url_pic_details.length; i++){
                    if(utils.isPicsWithUrl(url_pic_details[i])){
                        let imgModel = cc.instantiate(this.detailModel);
                        let url = url_pic_details[i];
                        if (!/^http/.test(url)) {
                            url = ALL_APP_SOURCE_CONFIG.remote_assets_prod[Global.DEBUG]+url_pic_details[i];
                        }
                        let details_sp = imgModel.getComponent(cc.Sprite);
                        let originalWidth;
                        Global.getInstance().loadImgFromUrl_V2(url, (loaded, image)=>{
                            if(loaded){
                                details_no.active = false;
                            }
                            details_sp.spriteFrame = image;
                            originalWidth = image._originalSize.width;
                            if (originalWidth < 1080) {
                              if(details_sp.node) details_sp.node.width = 1080;
                            }
                            if(details_sp.node) imgModel.width = details_sp.node.width;
                        },details_sp);
                        let content = utils.getChildByPath(this.scrollView.node, "view.content");
                        imgModel.parent = content;
                        imgModel.x = 0;
                    }
                }
            }
            //底部跳转按钮
            let jumpBtnLayout = utils.getChildByPath(this.mainNode, "jumpBtnLayout");
            let btn_jump = utils.getChildByPath(this.mainNode, "jumpBtnLayout.btn_jump");
            let lab_jump = utils.getChildByPath(this.mainNode, "jumpBtnLayout.btn_jump.Background.lab_jump");
            //底部BetNow按钮
            let betNowBtnLayout = utils.getChildByPath(this.mainNode, "betNowBtnLayout");
            //底部Games节点
            let gameBtnLayout = utils.getChildByPath(this.mainNode, "gameBtnLayout");
            if(args.data.pictures_jump == 0){//0 => None 无跳转按钮
                jumpBtnLayout.active = false;
                betNowBtnLayout.active = false;
                gameBtnLayout.active = false;
            }else if(args.data.pictures_jump == 1){//1 => Game Type(跳转至All Games里对应的type)
                jumpBtnLayout.active = true;
                betNowBtnLayout.active = false;
                gameBtnLayout.active = false;
                //下一帧设置圆角
                setTimeout(()=>{
                    jumpBtnLayout.getComponent(RoundRectMask).radius = 55;
                },100)
                if(lab_jump) lab_jump.getComponent(cc.Label).string = args.data.button_text || '--';
                if(btn_jump){
                    btn_jump.on(cc.Node.EventType.TOUCH_END, (event)=>{
                        if(args.data.game_type != '' && args.data.game_type){
                            if (!Global.getInstance().token) {
                                //未登录 跳转登录页
                                uiManager.instance.showDialog(UI_PATH_DIC.phonePasswordLogin, [{ way: LOGIN_WAY.PhoneCode }])
                                return;
                            }
                            this.hide_spin_btn()
                            //修改 bugs 跳转后返回hall 卡死
                            let gamescene =  cc.director.getScene().getComponentInChildren(GameControl);
                            let arr_provider = JSON.parse(args.data.provider_list); // 将字符串解析为数组
                            gamescene.mTabbar.showAllGames_gtype_providers(args.data.game_type,arr_provider);
                        }
                    })
                }
            }else if(args.data.pictures_jump == 2){//2 => Games(跳转至游戏)
                let bg_games = utils.getChildByPath(this.mainNode, "gameBtnLayout.bg_games");
                jumpBtnLayout.active = false;
                betNowBtnLayout.active = true;
                betNowBtnLayout.opacity = 0;
                let lab_betnow = utils.getChildByPath(this.mainNode, "betNowBtnLayout.btn_betnow.Background.lab_jump");
                if(lab_betnow) lab_betnow.getComponent(cc.Label).string = args.data.button_text || '--';
                let gidList = args.data.gid.replace(/\s+/g, "").replace(/[，,]/g, ",").split(",");//去除所有空格 替换中文逗号为英文逗号
                if(gidList.length > 0){
                    gameBtnLayout.active = true;
                    //下一帧设置圆角
                    setTimeout(()=>{
                        gameBtnLayout.getComponent(RoundRectMask).radius = 55;
                    },100)
                    
                    //先隐藏所有game
                    for(let i = 0; i < 6; i++){
                        let gameNode = utils.getChildByPath(this.mainNode, `gameBtnLayout.bg_games.game_${i}`);
                        if(gameNode) gameNode.active = false;
                    }

                    //显示实际需要显示的game
                    let filter_gidlist;
                    if(gidList.length > 0){
                        filter_gidlist = gidList.filter(Boolean);//filter(Boolean) 会自动过滤掉 false、""、0、null、undefined、NaN 等假值
                    }

                    //适配底背景和圆角
                    if(filter_gidlist.length <= 3){
                        gameBtnLayout.height = gameBtnLayout.height - 400;
                        bg_games.height = gameBtnLayout.height;
                        setTimeout(()=>{
                            gameBtnLayout.getComponent(RoundRectMask).radius = 55;
                        },100)
                    }else{
                        gameBtnLayout.height = 1118;
                        bg_games.height = gameBtnLayout.height;
                        setTimeout(()=>{
                            gameBtnLayout.getComponent(RoundRectMask).radius = 55;
                        },100)
                    }

                    for(let i = 0; i < filter_gidlist.length && i < 6; i++){
                        let gameNode = utils.getChildByPath(this.mainNode, `gameBtnLayout.bg_games.game_${i}`);
                        if(gameNode){
                            gameNode.active = true;
                            gameNode.x = this.gameIconPosList[`p${filter_gidlist.length}`][i];
                            
                            let sprite = gameNode.getChildByName("masknode").getChildByName("game_img").getComponent(cc.Sprite);
                            Global.getInstance().showGameIcon(sprite, filter_gidlist[i]);

                            //移除旧事件，避免重复绑定
                            gameNode.off(cc.Node.EventType.TOUCH_END);
                            gameNode.on(cc.Node.EventType.TOUCH_END, (event)=>{
                                if(parseInt(args.data.maintained) == 1 || parseInt(args.data.hidden) == 1){
                                    Global.getInstance().showSimpleTip("Sorry, the current game does not exist");
                                    return;
                                }
                                Global.getInstance().clickGameIconBtn(filter_gidlist[i]);
                            });
                        }
                    }
                }
            }else if(args.data.pictures_jump == 3){//3 => Deposit(跳转至充值页)
                betNowBtnLayout.active = false;
                gameBtnLayout.active = false;
                jumpBtnLayout.active = true;
                setTimeout(()=>{
                    jumpBtnLayout.getComponent(RoundRectMask).radius = 55;
                },100)
                if(lab_jump) lab_jump.getComponent(cc.Label).string = args.data.button_text || '--';
                if(btn_jump){
                    btn_jump.on(cc.Node.EventType.TOUCH_END, (event)=>{
                        Global.getInstance().loadWithdrawConfig(()=>{
                            if(ALL_APP_SOURCE_CONFIG.channel == E_CHANEL_TYPE.G_CASH){
                                //gcash 直接弹窗提示 是否跳转充值
                                Global.getInstance().back_mini_buy_tips();
                                return;
                            }
                            this.hide_spin_btn();
                            uiManager.instance.showDialog(UI_PATH_DIC.Deposit,null,null,DEEP_INDEXZ.DEPOSIT);                           
                        },true);
                    })
                }
            }else if(args.data.pictures_jump == 4){//4 => Internal URL(内部框架打开对应网址)
                betNowBtnLayout.active = false;
                gameBtnLayout.active = false;
                jumpBtnLayout.active = true;
                setTimeout(()=>{
                    jumpBtnLayout.getComponent(RoundRectMask).radius = 55;
                },100)
                if(lab_jump) lab_jump.getComponent(cc.Label).string = args.data.button_text || '--';
                if(btn_jump){
                    btn_jump.on(cc.Node.EventType.TOUCH_END, (event)=>{
                        if (cc.sys.os == cc.sys.OS_IOS) {
                            utils.assignUrl(args.data.url)
                        } else {
                            utils.openUrl(args.data.url)
                        }
                    })
                }
            }else if(args.data.pictures_jump == 5){//5 => Outside URL(外部浏览器打开对应网址)
                betNowBtnLayout.active = false;
                gameBtnLayout.active = false;
                jumpBtnLayout.active = true;
                setTimeout(()=>{
                    jumpBtnLayout.getComponent(RoundRectMask).radius = 55;
                },100)
                if(lab_jump) lab_jump.getComponent(cc.Label).string = args.data.button_text || '--';
                if(btn_jump){
                    btn_jump.on(cc.Node.EventType.TOUCH_END, (event)=>{
                        if (cc.sys.os == cc.sys.OS_IOS) {
                            utils.assignUrl(args.data.url)
                        } else {
                            utils.openUrl(args.data.url)
                        }
                    })
                }
            }
        }
    }
    //重新 绘制 圆角
    re_round_rect(){
        let jumpBtnLayout = utils.getChildByPath(this.mainNode, "downLayout");
        let down_content = jumpBtnLayout.getChildByName('content');
        if(jumpBtnLayout.height != down_content.height + 50){
            jumpBtnLayout.height = down_content.height + 50;
            setTimeout(() => {
                let round = jumpBtnLayout.getComponent(RoundRectMask)
                round.radius = 60;
            }, 10);
        }
    }
    protected update(dt: number): void {
        this.re_round_rect()
    }
    clickCloseGames(){
        this.showHideGames(false, true);
    }

    clickBetNow(){
        this.showHideGames(true, false);
    }

    showHideGames(active_1:boolean, active_2:boolean){
        let gameBtnLayout = utils.getChildByPath(this.mainNode, "gameBtnLayout");
        let betNowBtnLayout = utils.getChildByPath(this.mainNode, "betNowBtnLayout");
        if(gameBtnLayout){
            gameBtnLayout.active = active_1;
            if(gameBtnLayout.active){
                setTimeout(()=>{
                    gameBtnLayout.getComponent(RoundRectMask).radius = 55;
                },100);
            }
        }
        if(betNowBtnLayout){
            betNowBtnLayout.active = active_2;
            if(betNowBtnLayout.active){
                setTimeout(()=>{
                    betNowBtnLayout.getComponent(RoundRectMask).radius = 55;
                    betNowBtnLayout.opacity = 255;
                },100);
            }
        }
    }

    // 隐藏转盘按钮
    hide_spin_btn(){
        let btn_spin = Global.getInstance().popNode.getChildByName("btn_spin");
        if (btn_spin) btn_spin.zIndex = -1;
    }
    protected onDestroy(): void {
    }
  
    clickClose() {
        //bug第一次进入的时候 没有显示首页
        let gamescene =  cc.director.getScene().getComponentInChildren(GameControl);
        gamescene.mTabbar.rein_home();
        this.node.destroy();
    }

    onScroll() {
        let opacity = this.scrollView.getScrollOffset().y
        if (opacity > 255 ) opacity = 255;
        let layou = utils.getChildByPath(this.mainNode,"titlelayout.bg");
        layou.opacity = opacity;

        // let btn_back = utils.getChildByPath(this.mainNode,"titlelayout.btn_back.img");
        let title = utils.getChildByPath(this.mainNode,"titlelayout.bg.title");
        if (opacity > 0) {
            // btn_back.color = cc.color(255-opacity/255*180,255-opacity/255*168,255-opacity/255*151);
            title.color = cc.color(255-opacity/255*180,255-opacity/255*168,255-opacity/255*151);
        } else {
            //btn_back.color = cc.color(52,48,48);
            title.color = cc.color(255,255,255);
        }
    }

    //跳转tranaction页面
    clickTransionView() {
        if (!Global.getInstance().token) {
            //未登录 跳转登录页
            uiManager.instance.showDialog(UI_PATH_DIC.phonePasswordLogin, [{ way: LOGIN_WAY.PhoneCode, curPromoDetail:true }])//curPromoDetail:true表示当前是活动详情页面
            return;
        }
        uiManager.instance.showDialog(UI_PATH_DIC.Transations,["deposit"],null,DEEP_INDEXZ.TRANSATIONS);
    }
    //这里以下 自定义活动
    //点击detail 按钮 打开详细页面
    open_detail(){
        //根据活动内容 打开详细页面
        if(this.ctype == 0){
            this.show_level_items()
        }else{
            this.show_cashback_items()
        }
    }
    //点击关闭 按钮 折叠详细页面
    close_detail(){
        //根据活动内容 关闭详细页面
        if(this.ctype == 0){
            this.show_myperform()
        }else{
            this.show_myperfor_cashback()
        }
    }
    //添加闯关 类型活动的数据
    show_level_items(){
        let content = utils.getChildByPath(this.mainNode, "downLayout.content");
        let title_node = content.getChildByName('title_node')
        title_node.getChildByName('detail_title').active = true;
        title_node.getChildByName('my_title').active = false;

        let category_node = content.getChildByName('category_node')
        category_node.getChildByName('level_bg_me').active = false
        category_node.getChildByName('bet_up_bg').active = false
        category_node.getChildByName('level_bg').active = true

        let event_detail = content.getChildByName('event_detail')
        event_detail.removeAllChildren();
        event_detail.active = true;
        for (let index = 0; index < 8; index++) {
            let item_me = utils.getChildByPath(this.mainNode, "downLayout.item_level");
            let my_item = cc.instantiate(item_me)
            my_item.parent = event_detail;  
        }
        let cashback = content.getChildByName('cashback')
        cashback.active = false;
    }
    //显示折叠 我的闯关活动信息
    show_myperform(){
        let content = utils.getChildByPath(this.mainNode, "downLayout.content");
        let title_node = content.getChildByName('title_node')
        title_node.getChildByName('detail_title').active = false;
        title_node.getChildByName('my_title').active = true;

        let category_node = content.getChildByName('category_node')
        category_node.getChildByName('level_bg_me').active = true
        category_node.getChildByName('bet_up_bg').active = false
        category_node.getChildByName('level_bg').active = false

        let event_detail = content.getChildByName('event_detail')
        event_detail.removeAllChildren();
        event_detail.active = true;
        let item_me = utils.getChildByPath(this.mainNode, "downLayout.item_level_me");
        let my_item = cc.instantiate(item_me)
        my_item.parent = event_detail;

        let cashback = content.getChildByName('cashback')
        cashback.active = false;
    }

    //显示cashback 折叠活动
    show_myperfor_cashback(){
        let content = utils.getChildByPath(this.mainNode, "downLayout.content");
        let title_node = content.getChildByName('title_node')
        title_node.getChildByName('detail_title').active = false;
        title_node.getChildByName('my_title').active = true;

        let category_node = content.getChildByName('category_node')
        category_node.getChildByName('level_bg_me').active = false
        category_node.getChildByName('bet_up_bg').active = false
        category_node.getChildByName('level_bg').active = false
        category_node.active = false;

        let event_detail = content.getChildByName('event_detail')
        event_detail.removeAllChildren();
        event_detail.active = false;
        // let item_me = utils.getChildByPath(this.mainNode, "downLayout.item_level_me");
        // let my_item = cc.instantiate(item_me)
        // my_item.parent = event_detail;

        let cashback = content.getChildByName('cashback')
        cashback.active = true;
    }

    //添加cashback 类型活动的数据
    show_cashback_items(){
        let content = utils.getChildByPath(this.mainNode, "downLayout.content");
        let title_node = content.getChildByName('title_node')
        title_node.getChildByName('detail_title').active = true;
        title_node.getChildByName('my_title').active = false;

        let category_node = content.getChildByName('category_node')
        category_node.getChildByName('level_bg_me').active = false
        category_node.getChildByName('bet_up_bg').active = true
        category_node.getChildByName('level_bg').active = false
        category_node.active = true;

        let event_detail = content.getChildByName('event_detail')
        event_detail.removeAllChildren();
        event_detail.active = true;
        for (let index = 0; index < 8; index++) {
            let item_me = utils.getChildByPath(this.mainNode, "downLayout.item_bet");
            let my_item = cc.instantiate(item_me)
            my_item.parent = event_detail;  
        }
        let cashback = content.getChildByName('cashback')
        cashback.active = true;
    }
}

