{"name": "vue3-gaming-platform", "version": "1.0.0", "description": "Vue3 Gaming Platform - Hall & Wallet Modules", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "type-check": "vue-tsc --noEmit", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.5", "pinia": "^2.1.7", "axios": "^1.6.0", "@vueuse/core": "^10.5.0", "swiper": "^11.0.0", "animate.css": "^4.1.1", "dayjs": "^1.11.10", "lodash-es": "^4.17.21", "mitt": "^3.0.1"}, "devDependencies": {"@types/node": "^20.8.10", "@types/lodash-es": "^4.17.12", "@vitejs/plugin-vue": "^4.4.0", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.4.0", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "prettier": "^3.0.3", "sass": "^1.69.5", "typescript": "~5.2.0", "vite": "^4.4.11", "vitest": "^0.34.6", "@vitest/ui": "^0.34.6", "vue-tsc": "^1.8.19"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["vue3", "typescript", "gaming", "casino", "hall", "wallet", "pinia", "vite"], "author": "Development Team", "license": "MIT"}