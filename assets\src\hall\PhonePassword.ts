import ExchangeSucceed from "../ExchangeSucceed";
import { UI_PATH_DIC } from "../GlobalConstant";
import Global from "../GlobalScript";
import UICommon from "../component/UICommon";
import { GEETEST_TYPE, GeetestMgr } from "../geetest/GeetestMgr";
import { Md5 } from "../libs/Md5";
import { uiManager } from "../mgr/UIManager";
import HttpUtils from "../net/HttpUtils";
import { GlobalEnum } from "../room/GlobalEnum";
import utils from "../utils/utils";

export function showPhonePassword(force?: boolean, bindType?: string) {
    uiManager.instance.showDialog(UI_PATH_DIC.PhoneBind, [{ force, bindType }])//未用到
}

const { ccclass, property } = cc._decorator;

@ccclass
export default class PhonePassword extends UICommon {
    @property(cc.Node)
    m_get_btn: cc.Node = null;


    @property(cc.Label)
    m_time: cc.Label = null;


    @property(cc.EditBox)
    phone_edit: cc.EditBox = null;

    @property(cc.EditBox)
    password_edit: cc.EditBox = null;

    @property([cc.SpriteFrame])
    frame: Array<cc.SpriteFrame> = [];

    @property(cc.Sprite)
    passwordIsShow: cc.Sprite = null;

    m_phone: string = "";
    m_password: string = "";
    m_code: string = "";
    m_t: number = 120;
    m_force: boolean = false;
    bindType: string = '';
    // LIFE-CYCLE CALLBACKS:

    onLoad(): void {
        let userData = Global.getInstance().userdata;
        if (userData.phone) {
            this.phone_edit.string = userData.phone
            this.m_phone = userData.phone
            this.phone_edit.enabled = false
        }
    }

    // show(args?: any): void {
    //     super.show()
    //     if (args) {
    //         this.init(args.force, args.bindType)
    //     }
    // }
    init(args: { force: boolean, bindType?: string }) {
        this.m_force = args.force;
        if (this.m_force == true) {
            this.lay.getChildByName("back").active = false;
        }
        if (args.bindType) {
            this.bindType = args.bindType;
        }
    }

    onPhoneText(text, editbox, customEventData) {
        this.m_phone = text;
    }

    onPasswordText(text, editbox, customEventData) {
        this.m_password = text;
    }

    onCodeText(text, editbox, customEventData) {
        this.m_code = text;
    }

    checkPhoneIsRegister() {
        if (this.m_phone == "") {
            Global.getInstance().showSimpleTip(Global.getInstance().getLabel("tipword22"));
            return;
        }
        //次数获取验证码既可以绑定手机号也可以修改密码
        let params: object = {}
        params["phone"] = this.m_phone
        params["telephoneCode"] = "+63"
        let userData = Global.getInstance().userdata;
        params["isBind"] = userData.phone ? 1 : 0 //需要已经绑定填1  不需要绑定发0  
        HttpUtils.getInstance().post(3, 3, this, "/api/is/bind/send", params, (response) => {
        }, (res) => {
            if (res && res.code) {
                if (res && res["code"] == 600) {
                    Global.getInstance().showSimpleTip(Global.getInstance().getLabel("tipword23"));
                } else if (res["code"] && res["msg"] != "") {
                    Global.getInstance().showSimpleTip(res.msg);
                }
            } else {
                Global.getInstance().showSimpleTip(res.ms);
            }
            // this.reSetCode();
        })
        this.m_get_btn.active = false;
        this.m_time.node.active = true;
        this.m_t = 60;
        this.m_time.string = Global.getInstance().getLabel("scriptword3") + this.m_t + " s";
        this.schedule(this.timer, 1);
    }

    //短信接口不用再发了,防止短信接口暴漏
    getCode() {
        if (this.m_phone == "") {
            Global.getInstance().showSimpleTip(Global.getInstance().getLabel("tipword22"));
            return;
        }

        HttpUtils.getInstance().post(3, 3, this, "/api/sms/send/short/msg", {
            phone: this.m_phone,
            type: GlobalEnum.SMS_TYPE.BIND_PHONE_SET_PWD,
            // appPackageName: Global.getInstance().getAppBundleId()
            telephoneCode: "+63"
        }, null, (response) => {
            if (response && response["code"] == 600) {
                Global.getInstance().showSimpleTip(Global.getInstance().getLabel("tipword23"));
            } else if (response["code"] && response["msg"] != "") {
                Global.getInstance().showSimpleTip(response.msg);
            }
            // this.reSetCode();
        });

        this.m_get_btn.active = false;
        this.m_time.node.active = true;
        this.m_t = 60;
        this.m_time.string = Global.getInstance().getLabel("scriptword3") + this.m_t + " s";
        this.schedule(this.timer, 1);
    }

    reSetCode() {
        this.unschedule(this.timer);
        this.m_get_btn.active = true;
        this.m_time.node.active = false;
    }

    close() {
        this.hide()
    }

    onEdittingDidBegin(editbox: cc.EditBox) {
        Global.getInstance().editParentMove(editbox, this.lay)
        editbox.placeholderLabel.node.name = editbox.placeholder
        editbox.placeholder = ""
    }

    onEdittingDidEnd(editbox: cc.EditBox) {
        if (Global.getInstance().needScreenUp()) {
            this.lay.y = 0
        }
        editbox.placeholder = editbox.placeholderLabel.node.name
    }

    onEdittingCodeDidBegin(editbox: cc.EditBox) {
        Global.getInstance().editParentMove(editbox, this.lay)
    }

    onEdittingCodeDidEnd() {
        if (Global.getInstance().needScreenUp()) {
            this.lay.y = 0
        }
    }

    confirmClick(event, data) {
        if (this.m_phone == "") {
            Global.getInstance().showSimpleTip(Global.getInstance().getLabel("tipword22"));
            return;
        }

        if (!utils.isPhilippinePhoneNumber(this.m_phone)) {
            Global.getInstance().showSimpleTip("Wrong phone number");
            return
        }

        if (this.m_password == "") {
            Global.getInstance().showSimpleTip(Global.getInstance().getLabel("tipword25"));
            return;
        }

        if (this.m_password.length < 8) {
            Global.getInstance().showSimpleTip("Please enter at least 8 characters");
            return
        }

        if (this.m_code == "") {
            Global.getInstance().showSimpleTip(Global.getInstance().getLabel("tipword26"));
            return;
        }

        let userData = Global.getInstance().userdata;
        if (userData.phone) {
            //已经绑定
            let data = { "phone": this.m_phone, "password": this.m_password, "verifyCode": this.m_code }
            this.resetPassward(data)
        } else {
            //加个验证
            GeetestMgr.instance.geetest_device(GEETEST_TYPE.bind_pt_phone,(succ)=>{
                if(succ){
                    let ret = {}
                    if(Object.getPrototypeOf(succ) !== Boolean.prototype){
                        ret = succ
                    }
                    this.bindPhone(ret);
                }
            })
        }
    }

    resetPassward(data) {
        let params = {
            "phone": data.phone,
            "telephone_code": "+63",
            "verify_code": data.verifyCode,
            "password": Md5.hashStr(data.password).toString()
        };
        HttpUtils.getInstance().post(3, 3, this, "/api/user/update/pwd", params, (response) => {
            this.hide()
            Global.getInstance().hideShowLoading("/api/user/update/pwd");
            Global.getInstance().showSimpleTip("Password Update Successfully")
        }, (response) => {
            if (response && response.code) {
                cc.log("response.code ===>", response.code)
                this.hide()
            }
        });
    }

    bindPhone(ret?) {
        let gee_guard = ret?.geetest_guard || '';
        let uInfo = ret?.userInfo || '';
        let gee_captcha = ret?.geetest_captcha || '';
        HttpUtils.getInstance().post(3, 0, this, "/api/player/add-bind", {
            token: Global.getInstance().token,
            appPackageName: Global.getInstance().getAppBundleId(),
            deviceId: Global.getInstance().getDeviceId(),
            appVersion: Global.getInstance().getAppVersion(),
            phone: this.m_phone,
            password: this.m_password,
            verifyCode: this.m_code,
            telephoneCode: "+63",
            geetest_guard:gee_guard,
            userInfo:uInfo,
            geetest_captcha:gee_captcha,
            buds:ret?.buds || '64',
        }, (response) => {
            Global.getInstance().userdata = response["data"]["user_info"];
            cc.director.emit("bind_callback");
            // Global.getInstance().showSimpleTip(Global.getInstance().getLabel("tipword14"));
            uiManager.instance.loadPrefabByLoading("prefab/exchangeSucceed", cc.Prefab,()=>{}, (err, asset: cc.Prefab) => {
                if (err) return;
                let ui: cc.Node = cc.instantiate(asset)
                ui.parent = this.node;
                let itemdata = { spf: null, count: Global.getInstance().config.user_bind_phone_reward.amount, info: "Congratulations on successfully binding your mobile phone number" };
                // ui.getComponent(RewardItem).init(cfg[this.curDate]["cashback"], () => {
                cc.director.emit("CheckInviteBindEvent");
                cc.director.emit("phoneBindSuc", { bindType: this.bindType });
                ui.getComponent(ExchangeSucceed).setData(itemdata, () => {
                    // ui.getComponent(RewardItem).init(Global.getInstance().config.user_bind_phone_reward.amount, () => {
                    let e = new cc.Event.EventCustom('show_gold_ani', true);
                    e.setUserData([{ sp: cc.v2(0, 0), yibi: false }]);
                    this.node.dispatchEvent(e);
                    this.close();
                })
            })
            // Global.getInstance().showCommonTip("Congratulations on binding your mobile phone number to get " + Global.getInstance().config.user_bind_phone_reward.amount + " gold coins", this, true, () => {
            //     let e = new cc.Event.EventCustom('show_gold_ani', true);
            //     e.setUserData([{ sp: cc.v2(0, 0), yibi: false }]);
            //     this.node.dispatchEvent(e);
            //     this.close();
            // });
        }, (response) => {
            if (response["msg"] && response["msg"] != "") {
                Global.getInstance().showSimpleTip(response["msg"]);
            } else if (response["code"]) {
                Global.getInstance().showSimpleTip(response.msg);
            }
            // this.reSetCode();
        });
    }

    passwordIsShowClick() {
        if (this.passwordIsShow.spriteFrame == this.frame[1]) {
            this.passwordIsShow.spriteFrame = this.frame[0];
            this.password_edit.inputFlag = cc.EditBox.InputFlag.PASSWORD;
        } else {
            this.passwordIsShow.spriteFrame = this.frame[1];
            this.password_edit.inputFlag = cc.EditBox.InputFlag.DEFAULT;

        }
    }

    timer() {
        this.m_t--;
        this.m_time.string = Global.getInstance().getLabel("scriptword3") + this.m_t + " s";
        if (this.m_t <= 0) {
            this.reSetCode();
        }
    }

    protected onDestroy(): void {
        // cc.director.emit("DestroyQueuePopup")

    }
}
