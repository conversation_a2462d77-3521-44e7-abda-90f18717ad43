<template>
  <div class="wallet-task-modal" v-if="isVisible" @click="handleBackdropClick">
    <div class="modal-container" @click.stop>
      <!-- 头部 -->
      <header class="modal-header">
        <h2 class="modal-title">钱包任务</h2>
        <div class="header-actions">
          <div class="balance-display">
            <span class="balance-label">余额</span>
            <span class="balance-amount">₱{{ walletStore.formatNumber(walletStore.userBalance) }}</span>
          </div>
          <button class="close-btn" @click="closeModal">
            <i class="icon-close"></i>
          </button>
        </div>
      </header>

      <!-- 引导页面 -->
      <div v-if="walletStore.showGuide" class="guide-content">
        <div class="guide-illustration">
          <img src="/images/wallet-guide.png" alt="钱包任务引导" />
        </div>
        <div class="guide-text">
          <h3>欢迎使用钱包任务</h3>
          <p>完成任务即可获得丰厚奖励，快来体验吧！</p>
        </div>
        <button class="guide-btn" @click="completeGuide">
          开始任务
        </button>
      </div>

      <!-- 主要内容 -->
      <div v-else class="modal-content">
        <!-- 统计信息 -->
        <div class="stats-section">
          <div class="stat-item">
            <span class="stat-value">{{ walletStore.taskStats.total }}</span>
            <span class="stat-label">总任务</span>
          </div>
          <div class="stat-item">
            <span class="stat-value">{{ walletStore.taskStats.ongoing }}</span>
            <span class="stat-label">进行中</span>
          </div>
          <div class="stat-item">
            <span class="stat-value">{{ walletStore.taskStats.finished }}</span>
            <span class="stat-label">已完成</span>
          </div>
          <div class="stat-item">
            <span class="stat-value">₱{{ walletStore.formatNumber(walletStore.taskStats.totalRewards) }}</span>
            <span class="stat-label">总奖励</span>
          </div>
        </div>

        <!-- 任务列表 -->
        <div class="tasks-section">
          <div v-if="walletStore.isLoading" class="loading-container">
            <div class="loading-spinner"></div>
            <p>加载任务中...</p>
          </div>

          <div v-else-if="walletStore.filteredTasks.length === 0" class="empty-state">
            <div class="empty-icon">
              <i class="icon-task"></i>
            </div>
            <h3>暂无任务</h3>
            <p>当前没有可用的钱包任务</p>
            <button class="refresh-btn" @click="refreshTasks">
              刷新任务
            </button>
          </div>

          <div v-else class="tasks-list">
            <WalletTaskItem
              v-for="task in walletStore.filteredTasks"
              :key="task.id"
              :task="task"
              @unlock="handleUnlockTask"
              @collect="handleCollectReward"
              @continue="handleContinueTask"
              @view-rule="handleViewRule"
            />
          </div>
        </div>

        <!-- Banner区域 -->
        <div v-if="bannerData.length > 0" class="banner-section">
          <BannerCarousel 
            :banners="bannerData"
            :auto-play="true"
            :auto-play-interval="4000"
            @banner-click="handleBannerClick"
          />
        </div>
      </div>

      <!-- 底部操作栏 -->
      <footer class="modal-footer">
        <button class="footer-btn" @click="openTransactionHistory">
          <i class="icon-history"></i>
          <span>奖励记录</span>
        </button>
        <button class="footer-btn" @click="openMoreActivities">
          <i class="icon-gift"></i>
          <span>更多活动</span>
        </button>
      </footer>
    </div>

    <!-- 任务规则弹窗 -->
    <WalletTaskRuleModal
      v-if="showRuleModal"
      :rule-content="selectedTaskRule"
      @close="closeRuleModal"
    />

    <!-- 金币动画容器 -->
    <div ref="goldAnimationContainer" class="gold-animation-container"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useWalletStore } from '@/stores/walletStore'
import WalletTaskItem from './WalletTaskItem.vue'
import WalletTaskRuleModal from './WalletTaskRuleModal.vue'
import BannerCarousel from '@/components/Common/BannerCarousel.vue'
import type { WalletTaskInfo, BannerData } from '@/types/wallet'
import { WalletTaskStatus } from '@/types/wallet'

interface Props {
  isVisible: boolean
  bannerData?: BannerData[]
}

interface Emits {
  (e: 'close'): void
  (e: 'task-unlocked', task: WalletTaskInfo): void
  (e: 'reward-collected', task: WalletTaskInfo, amount: number): void
}

const props = withDefaults(defineProps<Props>(), {
  bannerData: () => []
})

const emit = defineEmits<Emits>()

const router = useRouter()
const walletStore = useWalletStore()

// 响应式数据
const showRuleModal = ref(false)
const selectedTaskRule = ref('')
const goldAnimationContainer = ref<HTMLElement>()
const refreshTimer = ref<NodeJS.Timeout>()

// 方法
function handleBackdropClick() {
  closeModal()
}

function closeModal() {
  emit('close')
}

function completeGuide() {
  walletStore.setShowGuide(false)
  refreshTasks()
}

async function refreshTasks() {
  walletStore.setLoading(true)
  try {
    // TODO: 调用API获取任务数据
    // const tasks = await walletAPI.getTasks()
    // walletStore.setTasks(tasks)
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
  } catch (error) {
    console.error('Failed to refresh tasks:', error)
  } finally {
    walletStore.setLoading(false)
  }
}

async function handleUnlockTask(task: WalletTaskInfo) {
  if (walletStore.hasOngoingTask) {
    // 显示提示：请先完成进行中的任务
    showToast('请先完成进行中的任务')
    return
  }

  try {
    // TODO: 调用API解锁任务
    // await walletAPI.unlockTask(task.id)
    
    walletStore.updateTaskStatus(task.id, WalletTaskStatus.ONGOING)
    emit('task-unlocked', task)
    showToast('任务已解锁')
  } catch (error) {
    console.error('Failed to unlock task:', error)
    showToast('解锁任务失败')
  }
}

async function handleCollectReward(task: WalletTaskInfo) {
  try {
    // TODO: 调用API领取奖励
    // const response = await walletAPI.collectReward(task.id)
    
    const rewardAmount = Number(task.bonus)
    
    // 播放金币动画
    playGoldAnimation(rewardAmount)
    
    // 更新余额
    walletStore.updateBalance(walletStore.userBalance + rewardAmount)
    
    // 移除任务
    walletStore.removeTask(task.id)
    
    emit('reward-collected', task, rewardAmount)
    showToast(`获得奖励 ₱${walletStore.formatNumber(rewardAmount)}`)
  } catch (error) {
    console.error('Failed to collect reward:', error)
    showToast('领取奖励失败')
  }
}

function handleContinueTask(task: WalletTaskInfo) {
  // 跳转到对应游戏
  const gameTypes = task.game_type
  const providerList = task.provider_list
  
  router.push({
    name: 'Games',
    query: {
      types: gameTypes.join(','),
      providers: providerList.join(',')
    }
  })
  
  closeModal()
}

function handleViewRule(task: WalletTaskInfo) {
  selectedTaskRule.value = task.task_rule
  showRuleModal.value = true
}

function closeRuleModal() {
  showRuleModal.value = false
  selectedTaskRule.value = ''
}

function openTransactionHistory() {
  router.push({
    name: 'Transactions',
    query: { tab: 'award' }
  })
  closeModal()
}

function openMoreActivities() {
  router.push({ name: 'Activities' })
  closeModal()
}

function handleBannerClick(banner: BannerData) {
  if (banner.link_url) {
    if (banner.activity_type) {
      router.push({
        name: 'Activity',
        params: { activityId: banner.id }
      })
    } else {
      window.open(banner.link_url, '_blank')
    }
  }
  closeModal()
}

function playGoldAnimation(amount: number) {
  if (!goldAnimationContainer.value) return
  
  // TODO: 实现金币动画
  console.log('Playing gold animation for amount:', amount)
}

function showToast(message: string) {
  // TODO: 实现Toast提示
  console.log('Toast:', message)
}

function startAutoRefresh() {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
  }
  
  refreshTimer.value = setInterval(() => {
    if (props.isVisible && !walletStore.isLoading) {
      refreshTasks()
    }
  }, walletStore.config.autoRefreshInterval)
}

function stopAutoRefresh() {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
    refreshTimer.value = undefined
  }
}

// 监听弹窗显示状态
watch(() => props.isVisible, (visible) => {
  if (visible) {
    walletStore.initialize()
    refreshTasks()
    startAutoRefresh()
  } else {
    stopAutoRefresh()
  }
})

// 生命周期
onMounted(() => {
  if (props.isVisible) {
    walletStore.initialize()
    refreshTasks()
    startAutoRefresh()
  }
})

onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style scoped>
.wallet-task-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modal-container {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  width: 100%;
  max-width: 480px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.modal-title {
  margin: 0;
  color: white;
  font-size: 20px;
  font-weight: bold;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.balance-display {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.balance-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
}

.balance-amount {
  font-size: 16px;
  font-weight: bold;
  color: white;
}

.close-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.guide-content {
  padding: 40px 20px;
  text-align: center;
  color: white;
}

.guide-illustration img {
  width: 200px;
  height: auto;
  margin-bottom: 24px;
}

.guide-text h3 {
  margin: 0 0 12px 0;
  font-size: 24px;
  font-weight: bold;
}

.guide-text p {
  margin: 0 0 32px 0;
  font-size: 16px;
  opacity: 0.8;
}

.guide-btn {
  padding: 12px 32px;
  border: none;
  border-radius: 8px;
  background: #4CAF50;
  color: white;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
}

.guide-btn:hover {
  background: #45A049;
  transform: translateY(-2px);
}

.modal-content {
  flex: 1;
  overflow-y: auto;
  padding: 0 20px;
}

.stats-section {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12px;
  margin-bottom: 20px;
}

.stat-item {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 12px 8px;
  text-align: center;
  backdrop-filter: blur(10px);
}

.stat-value {
  display: block;
  font-size: 18px;
  font-weight: bold;
  color: white;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
}

.tasks-section {
  margin-bottom: 20px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 20px;
  color: rgba(255, 255, 255, 0.8);
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: rgba(255, 255, 255, 0.8);
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-state h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: bold;
}

.empty-state p {
  margin: 0 0 20px 0;
  font-size: 14px;
  opacity: 0.7;
}

.refresh-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.refresh-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.tasks-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.banner-section {
  margin-bottom: 20px;
}

.modal-footer {
  display: flex;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.footer-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 16px 8px;
  border: none;
  background: none;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  transition: all 0.3s ease;
}

.footer-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.footer-btn i {
  font-size: 20px;
}

.footer-btn span {
  font-size: 12px;
}

.gold-animation-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 9999;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .wallet-task-modal {
    padding: 10px;
  }
  
  .modal-container {
    max-width: 100%;
    max-height: 95vh;
  }
  
  .modal-header {
    padding: 16px;
  }
  
  .modal-title {
    font-size: 18px;
  }
  
  .stats-section {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .stat-value {
    font-size: 16px;
  }
}
</style>
