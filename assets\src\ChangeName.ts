import AudioManager from "./AudioManager";
import Global from "./GlobalScript";
import { loadPrefab } from "./Resource";
import RoundRectMask from "./RoundRectMask";
import UICommon from "./component/UICommon";
import HttpUtils from "./net/HttpUtils";
import utils from "./utils/utils";

const { ccclass, property } = cc._decorator;


// export function showChangeName() {
//     let parent = Global.getInstance().popNode
//     if (!parent) return
//     let changeName = parent.getChildByName("ChangeName")
//     if (cc.isValid(changeName)) {
//         return
//     }
//     loadPrefab("resources", "prefab/GCashAccounts/ChangeName", (prefab) => {
//         if (!prefab) return
//         let ndPop = cc.instantiate(prefab)
//         ndPop.name = "ChangeName"
//         ndPop.zIndex = 999
//         ndPop.parent = parent
//     })
// }
@ccclass
export default class ChangeName extends UICommon {

    @property(cc.EditBox)
    account_edit: cc.EditBox = null;
    @property(cc.Button)
    saveBtn: cc.Button = null;

    //内容 node edix 使用
    @property(cc.Node)
    content: cc.Node = null;


    m_userName = ""
    private uiBox = null;

    onPhoneText(text, editbox, customEventData) {
        this.m_userName = text;
        if (text == "") {
            this.saveBtn.node.opacity = 128;
        } else {
            this.saveBtn.node.opacity = 255;
        }
    }

    onLoad() {
        this.saveBtn.node.opacity = 128;
    }

    start() {
        
    }

    closeDialog() {
        this.hide();
    }

    onEdittingDidBegin(editbox: cc.EditBox) {
        Global.getInstance().editParentMove(editbox,this.content,800);
        editbox.placeholderLabel.node.name = editbox.placeholder;
        editbox.placeholder = "";
    }



    onEdittingDidEnd(editbox: cc.EditBox) {
        Global.instance.scrollTo(0,0,100);
        if (Global.getInstance().needScreenUp()) {
            this.content.y -= 800;
        }
        editbox.placeholder = editbox.placeholderLabel.node.name;
    }

    onClickOK() {
        if (this.saveBtn.node.opacity < 255) {
            this.audioPlay("audio/click_disable",false);
            return;
        }

        if (this.m_userName == "") {
            Global.getInstance().showSimpleTip(Global.getInstance().getLabel("tipword95"));
            return
        }

        if (this.m_userName.length < 2) {
            Global.getInstance().showSimpleTip(Global.getInstance().getLabel("tipword96"));
            return
        }

        if (!utils.containsValidCharacters(this.m_userName)) {
            Global.getInstance().showSimpleTip(Global.getInstance().getLabel("tipword97"));
            return
        }

        let params: Object = {};
        params["token"] = Global.getInstance().token;
        params["nickname"] = this.m_userName;
        params["avatar"] = Global.getInstance().getAvatar();

        HttpUtils.getInstance().post(3, 3, this, "/common/api/set/user/info", params,
            (response) => {
                Global.getInstance().userdata = response.data.user_info;
                Global.getInstance().showSimpleTip("Done");
                cc.director.emit("updateUserName")
                if (!this.node || !this.node.isValid) {
                    return;
                }
                this.hide()
            }, (res) => {
                if (res && res.code) {
                    Global.getInstance().showSimpleTip(Global.getInstance().getLabel("php_code_" + res["code"]));
                } else {
                    Global.getInstance().showSimpleTip(res.msg);
                }
                if (!this.node || !this.node.isValid) {
                    return;
                }
                this.hide()
            });
    }

    audioPlay(url,loop) {
        AudioManager.getInstance().playEffect(url, loop);
    }

}
