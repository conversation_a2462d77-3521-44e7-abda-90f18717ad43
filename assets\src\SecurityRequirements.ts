import { DEEP_INDEXZ, PASS_TYPE, UI_PATH_DIC, VERIFY_CODE_TYPE } from "./GlobalConstant";
import Global from "./GlobalScript";
import { showPhoneNumber } from "./SetPhoneNumber";
import { showVerifyCode } from "./VerifyCode";
import UICommon from "./component/UICommon";
import { uiManager } from "./mgr/UIManager";

const { ccclass, property } = cc._decorator;
export function ShowSecurityRequirement(data: any) {
    switch (data.type) {
        case 1:
            uiManager.instance.showDialog(UI_PATH_DIC.SecurityRequirement1, [data])//未用到
            break;
        case 2:
            uiManager.instance.showDialog(UI_PATH_DIC.SecurityRequirement2, [data])//未用到
            break;
        case 3:
            uiManager.instance.showDialog(UI_PATH_DIC.SecurityRequirement3, [data])//未用到
            break;
        default:
            break;
    }
}
@ccclass
export default class SecurityRequirements extends UICommon {
    @property(cc.Label)
    lbTips: cc.Label = null;

    @property(cc.Node)
    ndPhone: cc.Node = null;
    @property(cc.Node)
    ndPayment: cc.Node = null;
    @property(cc.Node)
    ndAccount: cc.Node = null;
    @property(cc.SpriteFrame)
    m_phoneStSps: cc.SpriteFrame[] = []
    @property(cc.SpriteFrame)
    m_paymentStSps: cc.SpriteFrame[] = []
    @property(cc.SpriteFrame)
    m_accountStSps: cc.SpriteFrame[] = []
    @property(cc.SpriteFrame)
    m_arrowStSps: cc.SpriteFrame[] = []
    @property(cc.SpriteFrame)
    m_gouSp: cc.SpriteFrame = null

    m_statesColor: cc.Color[] = [cc.color(62, 97, 176), cc.color(111, 112, 130)]
    accountNotInOrder: boolean = false;
    paymentNotInOrder: boolean = false;


    protected onLoad(): void {
        cc.director.on("password_suc", this.refreshStats, this)
        cc.director.on("setPhoneNum_suc", this.refreshStats, this)
        cc.director.on('modifyWithdrawNoSuc', this.refreshStats, this);
        cc.director.on("setPasswordSuc", this.refreshStats, this)
        this.preLoadPrefab()
    }
    preLoadPrefab() {
        let prefabs = [
            // UI_PATH_DIC.SetPhoneNumber,
            // UI_PATH_DIC.SetPayPassword,
            // UI_PATH_DIC.SetLoginPassword,
        ]
        cc.resources.preload(prefabs)
    }
    protected onDestroy(): void {
        cc.director.off("password_suc", this.refreshStats, this)
        cc.director.off("setPhoneNum_suc", this.refreshStats, this)
        cc.director.off('modifyWithdrawNoSuc', this.refreshStats, this);
        cc.director.off("setPasswordSuc", this.refreshStats, this)

    }
    refreshStats(args) {
        let userData = Global.getInstance().userdata;
        let account
        if (args) {
            account = args["redeemAccount"] || args["account_no"]
            if (args.title) {
                this.lbTips.string = args.title;
            }
        }

        if (!userData.phone) {
            if (this.ndPhone) {
                this.ndPhone.getComponent(cc.Button).interactable = true;
                this.ndPhone.getChildByName("icon").getComponent(cc.Sprite).spriteFrame = this.m_phoneStSps[0]
                this.ndPhone.getChildByName("title").color = this.m_statesColor[0]
                this.ndPhone.getChildByName("jt").getComponent(cc.Sprite).spriteFrame = this.m_arrowStSps[0]
            }
            if (this.ndPayment) {
                this.paymentNotInOrder = true
                this.ndPayment.getComponent(cc.Button).interactable = true;
                this.ndPayment.getChildByName("icon").getComponent(cc.Sprite).spriteFrame = this.m_paymentStSps[1]
                this.ndPayment.getChildByName("title").color = this.m_statesColor[1]
                this.ndPayment.getChildByName("jt").getComponent(cc.Sprite).spriteFrame = this.m_arrowStSps[1]
            }
            if (this.ndAccount) {
                this.accountNotInOrder = true;
                this.ndAccount.getComponent(cc.Button).interactable = true;
                this.ndAccount.getChildByName("icon").getComponent(cc.Sprite).spriteFrame = this.m_accountStSps[1]
                this.ndAccount.getChildByName("title").color = this.m_statesColor[1]
                this.ndAccount.getChildByName("jt").getComponent(cc.Sprite).spriteFrame = this.m_arrowStSps[1]
            }
        } else if (!userData.withdraw_password) {
            if (this.ndPhone) {
                this.ndPhone.getComponent(cc.Button).interactable = false;
                this.ndPhone.getChildByName("icon").getComponent(cc.Sprite).spriteFrame = this.m_phoneStSps[1]
                this.ndPhone.getChildByName("title").color = this.m_statesColor[1]
                this.ndPhone.getChildByName("jt").getComponent(cc.Sprite).spriteFrame = this.m_gouSp
            }
            if (this.ndPayment) {
                this.ndPayment.getComponent(cc.Button).interactable = true;
                this.paymentNotInOrder = false
                this.ndPayment.getChildByName("icon").getComponent(cc.Sprite).spriteFrame = this.m_paymentStSps[0]
                this.ndPayment.getChildByName("title").color = this.m_statesColor[0]
                this.ndPayment.getChildByName("jt").getComponent(cc.Sprite).spriteFrame = this.m_arrowStSps[0]
            }
            if (this.ndAccount) {
                this.accountNotInOrder = true;
                this.ndAccount.getComponent(cc.Button).interactable = true;
                this.ndAccount.getChildByName("icon").getComponent(cc.Sprite).spriteFrame = this.m_accountStSps[1]
                this.ndAccount.getChildByName("title").color = this.m_statesColor[1]
                this.ndAccount.getChildByName("jt").getComponent(cc.Sprite).spriteFrame = this.m_arrowStSps[1]
            }
        } else if (!account) {
            if (this.ndPhone) {
                this.ndPhone.getComponent(cc.Button).interactable = false;
                this.ndPhone.getChildByName("icon").getComponent(cc.Sprite).spriteFrame = this.m_phoneStSps[1]
                this.ndPhone.getChildByName("title").color = this.m_statesColor[1]
                this.ndPhone.getChildByName("jt").getComponent(cc.Sprite).spriteFrame = this.m_gouSp
            }
            if (this.ndPayment) {
                this.ndPayment.getComponent(cc.Button).interactable = false;
                this.ndPayment.getChildByName("icon").getComponent(cc.Sprite).spriteFrame = this.m_paymentStSps[1]
                this.ndPayment.getChildByName("title").color = this.m_statesColor[1]
                this.ndPayment.getChildByName("jt").getComponent(cc.Sprite).spriteFrame = this.m_gouSp
            }
            if (this.ndAccount) {
                this.ndAccount.getComponent(cc.Button).interactable = true;
                this.accountNotInOrder = false;
                this.ndAccount.getChildByName("icon").getComponent(cc.Sprite).spriteFrame = this.m_accountStSps[0]
                this.ndAccount.getChildByName("title").color = this.m_statesColor[0]
                this.ndAccount.getChildByName("jt").getComponent(cc.Sprite).spriteFrame = this.m_arrowStSps[0]
            }
        } else {
            this.hide()
        }
    }
    closeAction() {
        this.hide()
    }
    init(args: any): void {
        this.refreshStats(args)
    }

    onClickPhone() {

        // let data = {
        //     type: VERIFY_CODE_TYPE.,
        //     cb: () => {
        //         uiManager.instance.showDialog(UI_PATH_DIC.SetPayPassword)
        //     }
        // }
        showPhoneNumber(1)
    }
    onClickPayment() {
        if (this.paymentNotInOrder) return Global.getInstance().showSimpleTip("Please set up in order")
        if (Global.getInstance().userdata.withdraw_password) {
            let data = {
                type: VERIFY_CODE_TYPE.ChangePaymentPwd,
                verifyType: 14,
                cb: () => {
                    uiManager.instance.showDialog(UI_PATH_DIC.SetPayPassword,null,null,DEEP_INDEXZ.PASSWORD)
                }
            }
            showVerifyCode(data)
        } else {
            uiManager.instance.showDialog(UI_PATH_DIC.SetPayPassword,null,null,DEEP_INDEXZ.PASSWORD)
        }
    }
    onClickAccount() {
        if (this.accountNotInOrder) return Global.getInstance().showSimpleTip("Please set up in order")
        uiManager.instance.showDialog(UI_PATH_DIC.Password, [PASS_TYPE.AddAccount],null,DEEP_INDEXZ.PASSWORD)

    }
}
