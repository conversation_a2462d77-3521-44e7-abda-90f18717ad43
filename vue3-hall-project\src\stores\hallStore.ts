import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { 
  HallGameIcon, 
  GameType, 
  UserBalance, 
  BannerData, 
  ActivityData,
  TransactionRecord 
} from '@/types/hall'
import { GAME_TYPES } from '@/types/hall'

export const useHallStore = defineStore('hall', () => {
  // 状态
  const isLoggedIn = ref(false)
  const userBalance = ref<UserBalance>({
    total: 0,
    available: 0,
    frozen: 0
  })
  
  const gameList = ref<HallGameIcon[]>([])
  const currentGameType = ref<GameType>('Casino')
  const bannerData = ref<BannerData[]>([])
  const activityData = ref<ActivityData[]>([])
  const transactionRecords = ref<TransactionRecord[]>([])
  
  const isLoading = ref(false)
  const searchText = ref('')
  const filterTimeType = ref(0)
  
  // 计算属性
  const formattedBalance = computed(() => {
    if (userBalance.value.total >= 9999999999) {
      return '9,999,999,999.00'
    }
    return formatNumberWithCommas(userBalance.value.total)
  })
  
  const filteredGameList = computed(() => {
    let filtered = gameList.value
    
    // 按游戏类型筛选
    if (currentGameType.value !== 'Casino') {
      filtered = filtered.filter(game => {
        if (currentGameType.value === 'History') {
          return getGameHistory().includes(game.id)
        }
        if (currentGameType.value === 'Like') {
          return getLikedGames().includes(game.id)
        }
        return game.game_type === getGameTypeId(currentGameType.value)
      })
    }
    
    // 按搜索文本筛选
    if (searchText.value) {
      filtered = filtered.filter(game => 
        game.game_name.toLowerCase().includes(searchText.value.toLowerCase())
      )
    }
    
    return filtered
  })
  
  const validActivities = computed(() => {
    const now = Date.now()
    return activityData.value.filter(activity => {
      const startTime = new Date(activity.start_at).getTime()
      const endTime = new Date(activity.end_at).getTime()
      return startTime <= now && now <= endTime
    })
  })
  
  // 方法
  function formatNumberWithCommas(num: number): string {
    return num.toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    })
  }
  
  function getGameTypeId(type: GameType): number {
    const typeMap: Record<GameType, number> = {
      'Casino': 1,
      'Slots': 2,
      'Poker': 3,
      'Bingo': 4,
      'Arcade': 5,
      'Sports': 6,
      'Like': 0,
      'History': 0,
      'Baccarat': 7,
      'Roulette': 8,
      'Blackjack': 9
    }
    return typeMap[type] || 1
  }
  
  function getGameHistory(): number[] {
    const history = localStorage.getItem('gameHistory')
    return history ? JSON.parse(history) : []
  }
  
  function getLikedGames(): number[] {
    const liked = localStorage.getItem('likedGames')
    return liked ? JSON.parse(liked) : []
  }
  
  function addToHistory(gameId: number) {
    const history = getGameHistory()
    const newHistory = [gameId, ...history.filter(id => id !== gameId)].slice(0, 20)
    localStorage.setItem('gameHistory', JSON.stringify(newHistory))
  }
  
  function toggleLike(gameId: number) {
    const liked = getLikedGames()
    const index = liked.indexOf(gameId)
    if (index > -1) {
      liked.splice(index, 1)
    } else {
      liked.push(gameId)
    }
    localStorage.setItem('likedGames', JSON.stringify(liked))
  }
  
  function setGameType(type: GameType) {
    currentGameType.value = type
  }
  
  function setSearchText(text: string) {
    searchText.value = text
  }
  
  function updateBalance(balance: UserBalance) {
    userBalance.value = balance
  }
  
  function setGameList(games: HallGameIcon[]) {
    gameList.value = games
  }
  
  function setBannerData(banners: BannerData[]) {
    // 按排序和更新时间排序
    banners.sort((a, b) => {
      if (parseInt(a.sort.toString()) < parseInt(b.sort.toString())) return 1
      if (parseInt(a.sort.toString()) > parseInt(b.sort.toString())) return -1
      if (new Date(a.updated_at).getTime() < new Date(b.updated_at).getTime()) return 1
      if (new Date(a.updated_at).getTime() > new Date(b.updated_at).getTime()) return -1
      return 0
    })
    bannerData.value = banners
  }
  
  function setActivityData(activities: ActivityData[]) {
    activityData.value = activities
  }
  
  function setTransactionRecords(records: TransactionRecord[]) {
    transactionRecords.value = records
  }
  
  function setLoading(loading: boolean) {
    isLoading.value = loading
  }
  
  function setLoginStatus(status: boolean) {
    isLoggedIn.value = status
  }
  
  function setFilterTimeType(type: number) {
    filterTimeType.value = type
  }
  
  // 获取筛选后的交易记录
  function getFilteredTransactions() {
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    
    return transactionRecords.value.filter(record => {
      const recordDate = new Date(record.created_at)
      
      switch (filterTimeType.value) {
        case 0: // 今天
          return recordDate >= today
        case 1: // 昨天
          const yesterday = new Date(today)
          yesterday.setDate(yesterday.getDate() - 1)
          return recordDate >= yesterday && recordDate < today
        case 2: // 最近3天
          const threeDaysAgo = new Date(today)
          threeDaysAgo.setDate(threeDaysAgo.getDate() - 3)
          return recordDate >= threeDaysAgo
        case 3: // 最近7天
          const sevenDaysAgo = new Date(today)
          sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)
          return recordDate >= sevenDaysAgo
        default:
          return true
      }
    })
  }
  
  return {
    // 状态
    isLoggedIn,
    userBalance,
    gameList,
    currentGameType,
    bannerData,
    activityData,
    transactionRecords,
    isLoading,
    searchText,
    filterTimeType,
    
    // 计算属性
    formattedBalance,
    filteredGameList,
    validActivities,
    
    // 方法
    setGameType,
    setSearchText,
    updateBalance,
    setGameList,
    setBannerData,
    setActivityData,
    setTransactionRecords,
    setLoading,
    setLoginStatus,
    setFilterTimeType,
    addToHistory,
    toggleLike,
    getFilteredTransactions,
    formatNumberWithCommas
  }
})
