import AudioManager from "./AudioManager";
import { ALL_APP_SOURCE_CONFIG } from "./Config";
import { DEEP_INDEXZ, E_CHANEL_TYPE, E_PAGE_TYPE, E_TRANSACTION_TYPE, MAINTENANCETIPCODE, OPEN_BROADCAST_TYPE, PASS_TYPE, PRODUCT_TYPE, UI_PATH_DIC } from "./GlobalConstant";
import Global from "./GlobalScript";
import { INGSME_TYPE, KycMgr } from "./KYC/KycMgr";
import { showMaintenancetip } from "./Maintenancetip";
import { loadPrefab } from "./Resource";
import RoundRectMask from "./RoundRectMask";
import { showWalletLimitPop } from "./WalletLimitPop";
import UICommon from "./component/UICommon";
import { GEETEST_TYPE, GeetestMgr } from "./geetest/GeetestMgr";
import { uiManager } from "./mgr/UIManager";
import HttpUtils from "./net/HttpUtils";
import utils from "./utils/utils";
import withdrawDepositDialog from "./withdrawDepositDialog";

const { ccclass, property } = cc._decorator;


const TAG_TYPE = {
    AMOUNT_ONE: "amount1",
    AMOUNT_TWO: "amount2",
    AMOUNT_THREE: "amount3",
    AMOUNT_FOUR: "amount4",
    AMOUNT_FIVE: "amount5",
    AMOUNT_SIX: "amount6",
    AMOUNT_SEVEN: "amount7",
    AMOUNT_EIGHT: "amount8",
    AMOUNT_NINE: "amount9",
    AMOUNT_TEN: "amount10",
    AMOUNT_ELEVEN: "amount11",
    AMOUNT_TWELVE: "amount12"
}

const ACCOUNT_WITHDRAW = {
    MAYA_WEB: "11",
    GCASH_WEB: "12"
}


@ccclass
export default class Withdraw extends UICommon {
    @property(cc.Node)
    maya_withdraw: cc.Node = null;
    //钱包余额
    @property(cc.Label)
    wallet_label: cc.Label = null;


    //这里是 所有提现项目 node
    @property(cc.Node)
    labAmount_content = null;
    @property(cc.Node)
    gcash_withdraw: cc.Node = null;
    @property(cc.Node)
    web_withdraw: cc.Node = null;
    @property(cc.EditBox)
    amount_edit: cc.EditBox = null;
    @property(cc.Label)
    mayaAccount: cc.Label = null;
    @property(cc.Label)
    gcashAccount: cc.Label = null;
    @property(cc.Button)
    withdrawAmount: cc.Button[] = [];
    @property(cc.Label)
    minimumTip: cc.Label = null;
    @property(cc.Label)
    maximumTip: cc.Label = null;
    @property(cc.Label)
    insufficientTip: cc.Label = null;
    @property(cc.Button)
    continueBtn: cc.Button = null;
    @property(cc.Node)
    amountBgNode: cc.Node = null;
    @property([cc.SpriteFrame])
    accountFrames: cc.SpriteFrame[] = [];
    
    @property([cc.SpriteFrame])
    btnSpriteFrame: cc.SpriteFrame[] = [];


    @property(cc.Button)
    btnAmount: cc.Button = null;

    //用于上下滑动 判断高度
    @property(cc.Node)
    mainNode: cc.Node = null

    //提现方式 node
    @property(cc.Node)
    account_node: cc.Node = null;

    private uiBox = null;
    private labNum = null;
    private availableBalance = null;
    withdrawList = [];

    _amount: string = "";

    //withdrawData
    withdrawData = null;

    //min max取后台配置
    minAmountNum = null;
    maxAmountNum = null;

    //account_id
    accountID =  null;
    //account_type
    accountType = null;
    //accountData
    accountData = null;
    accountCounts = null;
    //selectIndex
    selectIndex = null;
    selectIdx = null;

    min_amount:any = null;
    max_amount:any = null;
    onLoad() {
        this.minimumTip.node.active = false;
        this.maximumTip.node.active = false;
        this.insufficientTip.node.active = false; 
        KycMgr.instance.in_game_type = -1;//重置一下kyc 进入类型 防止不合理弹窗
        this.maya_withdraw.active = false;
        this.web_withdraw.active = false;
        this.gcash_withdraw.active = false;
        this.account_node.active = false;
        if (ALL_APP_SOURCE_CONFIG.channel == E_CHANEL_TYPE.MAYA) {
            this.maya_withdraw.active = true;
        } else if (ALL_APP_SOURCE_CONFIG.channel == E_CHANEL_TYPE.WEB) {
            this.web_withdraw.active = true;
        } else if (ALL_APP_SOURCE_CONFIG.channel == E_CHANEL_TYPE.G_CASH) {
            this.gcash_withdraw.active = true;
        }
        this.loadAmountConfig();
        //先禁用 有拉到数据再启用
        this.labAmount_content.active = false;

        this.amount_edit.enabled = false;
        this.btnAmount.node.active = true;
    }

    start() {
        let phoneNum = "0"+Global.getInstance().userdata.phone;
        this.mayaAccount.string = this.formatPhoneNumber(phoneNum);
        this.gcashAccount.string = this.formatPhoneNumber(phoneNum);
        Global.getInstance().updateBalanceAfterGame()
        this.availableBalance = Global.getInstance().balanceTotal; 
        
        cc.director.on('password_suc', this.onPasswordVerified, this);
        cc.director.on('select_account', this.initAccounts, this)
        cc.director.on('close_withdrawal', this.closeDialog, this);
        let page = this.node.getChildByName("page").getComponent(cc.PageView);
        page.node.on(cc.Node.EventType.TOUCH_END,this.slideClose,this);
        cc.resources.preload([UI_PATH_DIC.withDeposit])
        this.continueBtn.node.opacity = 155;
        //钱包显示
        this.wallet_label.string = utils.formatNumberWithCommas(this.availableBalance);
    }
    clickCloseBtn() {
        cc.tween(this.mainNode).call(()=>{
            this.mask.active = false;
        }).
        to(0.2,{position:new cc.Vec3(0,-2880)}).call(()=>{
            this.node.destroy();
        }).start();
    }
    slideClose(event) {
        if (event.touch._startPoint.y > this.mainNode.height) {
            this.clickCloseBtn();
        } else {
            let deltaY = event.touch._startPoint.y-event.touch._point.y;
            if (deltaY > this.mainNode.height/3) {
                this.clickCloseBtn();
            }
        }
    }

    /**拉取不到数据或用户未配置/未选择提现账号 点击按钮给出提示信息 */
    onClickAmountBnt() {
        //这里判断 是否开启 这个提现方式
        if (!this.withdrawData || this.withdrawData.length <= 0) {
            Global.getInstance().showSimpleTip(Global.getInstance().getLabel("tipword101"));
            return;
        } else {
            if (!this.accountData || this.accountData.length <= 0) {
                Global.getInstance().showSimpleTip(Global.getInstance().getLabel("tipword99"));
                return;
            }
        }
        let filter_list = this.withdrawData.filter((item)=>{
            return item.account_type == this.accountType
        })
        if(filter_list.length == 0){
            let tips_msg = Global.getInstance().getLabel("tipword102")
            if(this.accountType == ACCOUNT_WITHDRAW.GCASH_WEB){
                tips_msg = Global.getInstance().getLabel("tipword103")
            }
            Global.getInstance().showSimpleTip(tips_msg);
            return;
        }
    }

    loadAmountConfig() {
        HttpUtils.getInstance().post(3, 3, this, "/common/api/global-config/recharge-withdraw", {
            token: Global.getInstance().token,
            appChannel: ALL_APP_SOURCE_CONFIG.channel
        }, (response) => {
            if (!this.node || !this.node.isValid) {
                return;
            }
            if (response.data && response.data.withdraw && response.data.withdraw.length > 0) {
                this.account_node.active = true;
                // this.labAmount_content.active = true;
                // let accountMsg = this.web_withdraw.getChildByName("accountMsg");
                // if (accountMsg.active) this.labAmount_content.active = true;
                this.amount_edit.enabled = true;
                this.btnAmount.node.active = false;
                this.withdrawData = response.data.withdraw;
                for (let index = 0; index < this.withdrawData.length; index++) {
                    this.withdrawData.sort((a,b) => {
                        if (a.sort != b.sort){ //按sort值从大到小
                            return b.sort - a.sort
                        } else { //sort值相同按照首字母从大到小排序
                            if (a.name && b.name) {
                                return b.name.localeCompare(a.name);
                            }
                        }
                    })
    
                    //min max
                    this.minAmountNum = this.withdrawData[index].min;
                    this.maxAmountNum = this.withdrawData[index].max;
                }

                if (Global.instance.is_mini_game()) {
                    this.adaptAmountBg(0);
                    this.setLabAmount(0);
                }
                if (ALL_APP_SOURCE_CONFIG.channel == E_CHANEL_TYPE.WEB) {
                    this.initAccountsListData(()=>{
                        //web 端 显示我选择的账号 如果没有 默认第一个
                        let index_s = 0;
                        let accid = this.accountID;
                        let atype = this.accountType;
                        const sele_id = Global.getInstance().getStoreageData("SELECT_SORTTYPE",'');
                        if(sele_id != ''){
                            const fil_data = this.accountData.filter((item,index)=>{
                                if(sele_id.indexOf(item.account_id) != -1){
                                    index_s = index;
                                    accid = item.account_id;
                                    atype = item.type;
                                }
                            })
                        }
                        this.initAccounts({account_data:this.accountData, select_Index:index_s, id:accid, type:atype});
                    });
                }
            } else {
                this.labAmount_content.active = false;
                this.amount_edit.enabled = false;
                this.btnAmount.node.active = true;
            }
        }, (response)=>{
            this.labAmount_content.node.active = false;
            this.amount_edit.enabled = false;
            this.btnAmount.node.active = true;
        });
    }

    //加一个参数 是否是 选择框触发的请求 
    initAccountsListData(cb?,isSelect = false) {
        HttpUtils.getInstance().post(3, 3, this, "/common/api/get/withdraw/list", {
            token: Global.getInstance().token,
        }, (response) => {
            if (!this.node || !this.node.isValid) {
                return;
            }
            if (response.data.list && response.data.list.length > 0) {
                // this.labAmount_content.active = true;
                if(!isSelect){
                    this.amount_edit.enabled = true;
                    this.btnAmount.node.active = false;
                    this.accountData = response.data.list;
                    this.accountCounts = response.data.total_num;
                    this.accountID = response.data.list[0].account_id;
                    this.accountType = response.data.list[0].type;
                }
            } else {
                //账号列表为空
                this.labAmount_content.active = false;
                this.amount_edit.enabled = false;
                this.btnAmount.node.active = true;
            }
            if (cb) cb();
        }, (response)=>{
            this.labAmount_content.active = false;
            this.amount_edit.enabled = false;
            this.btnAmount.node.active = true;
        });
    }
    protected update(dt: number): void {
        this.resetBG();
    }
    //重置提现框大小并且 画圆角
    resetBG(time = 100){
        if(this.mainNode.getChildByName('all_content').height != this.mainNode.height){
            this.mainNode.height = this.mainNode.getChildByName('all_content').height;
            let roundsrc = this.mainNode.getComponent(RoundRectMask)
            roundsrc.radius = 35;
        }
    }
    initAccounts(args:{account_data:any, select_Index:any, id:any, type:any}) {
        this.accountData = args.account_data;
        this.selectIndex = args.select_Index;
        this.accountID = args.id;
        this.accountType = args.type;
        let accountMsg = this.web_withdraw.getChildByName("accountMsg");
        let account_icon = accountMsg.getChildByName("account_icon");
        let account_type = accountMsg.getChildByName("account_type");
        let account_no = accountMsg.getChildByName("account_no");
        if (this.accountData && this.accountData.length > 0) {
            this.web_withdraw.getChildByName("lab").active = false;
            if (accountMsg) {
                accountMsg.active = true;
                let shadow_bg = utils.getChildByPath(this.web_withdraw, "shadow");
                let account_bg = utils.getChildByPath(this.web_withdraw, "masknode.account_bg");
                if(this.accountType == ACCOUNT_WITHDRAW.MAYA_WEB){
                    if(shadow_bg) shadow_bg.color = cc.color(1,212,106);
                    if(account_bg) account_bg.color = cc.color(1,212,106);
                }else if(this.accountType == ACCOUNT_WITHDRAW.GCASH_WEB){
                    if(shadow_bg) shadow_bg.color = cc.color(72,129,237);
                    if(account_bg) account_bg.color = cc.color(72,129,237);
                }
                //这里判断 是否开启 这个提现方式
                let filter_list = this.withdrawData.filter((item)=>{
                    return item.account_type == this.accountType
                })
                if(filter_list.length == 0){
                    //把支付方式变灰色
                    this.amount_edit.enabled = false;
                    this.btnAmount.node.active = true;
                    if(shadow_bg) shadow_bg.color = cc.color(153,153,153);
                    if(account_bg) account_bg.color = cc.color(153,153,153);
                }else{
                    this.amount_edit.enabled = true;
                    this.btnAmount.node.active = false;
                }
                if (account_icon) {
                    if (this.accountType == ACCOUNT_WITHDRAW.MAYA_WEB) {
                        account_icon.getComponent(cc.Sprite).spriteFrame = this.accountFrames[0];

                    } else if (this.accountType == ACCOUNT_WITHDRAW.GCASH_WEB) {
                        account_icon.getComponent(cc.Sprite).spriteFrame = this.accountFrames[1];
                    }
                }
                if (account_type) {
                    if (this.accountType == ACCOUNT_WITHDRAW.MAYA_WEB) {
                        account_type.getComponent(cc.Label).string = "Maya";
                    } else if (this.accountType == ACCOUNT_WITHDRAW.GCASH_WEB) {
                        account_type.getComponent(cc.Label).string = "GCash";
                    }
                }
                if (account_no) {
                    account_no.getComponent(cc.Label).string = this.formatPhoneNumber(this.accountData[this.selectIndex].account_no);
                }

                for (let idx = 0; idx < this.withdrawData.length; idx++) {
                    if (this.withdrawData[idx].account_type == this.accountType) {
                        this.selectIdx = idx;
                        this.adaptAmountBg(idx);
                        this.setLabAmount(idx);
                    }
                }

                if (this.accountType == null) {
                    this.adaptAmountBg(0);
                    this.setLabAmount(0);
                }
            }
        } else {
            this.web_withdraw.getChildByName("lab").active = true;
            if (accountMsg) accountMsg.active = false;
        }
        if (this._amount && this._amount.trim() != "") {
            this.inputErrTip(this._amount);
            this.setStateContinue();
            this.changeTotalView(parseFloat(this._amount));
        }
    }

    onClickAccountSelect() {
        //拉取一下account list最新数据
        this.initAccountsListData(()=>{
            uiManager.instance.showDialog(UI_PATH_DIC.SelectWithdrawAcc, [{accountdata:this.accountData,withdrawData:this.withdrawData, counts:this.accountCounts}], null, DEEP_INDEXZ.SEL_WITHDRAW);
        },true)
    }

    adaptAmountBg(index) {
        let tags = this.withdrawData[index].tags;
        
        // let bmask = this.amountBgNode.getChildByName('mask')
        // bmask.height = maskview.height + 330;
        // let src = bmask.getComponent(RoundRectMask)
        // src.radius = 26
    }

    setLabAmount(index) {
        if (this.withdrawData.length > 0) {
            this.labAmount_content.active = true;
            let tags = this.withdrawData[index].tags;
            let selectIdx = this.selectIdx || 0;
            let minAmount = parseFloat(this.withdrawData[selectIdx].min);
            let maxAmount = parseFloat(this.withdrawData[selectIdx].max);
            if(this.insufficientTip.node.active)this.insufficientTip.string = "Enter Amount: " + minAmount + " - " + utils.formatNumberWithCommas(maxAmount,0) + "₱";
            this.amount_edit.placeholder = "Enter Amount: " + minAmount + " - " + utils.formatNumberWithCommas(maxAmount,0);//联动起来bug
            //根据后台配置显示
            this.withdrawAmount.forEach((nd, ndIdx) => {
                if (ndIdx > tags.length - 1) nd.node.active = false;
                else nd.node.active = true;
                if (nd.node.active) {
                    let labAmount = nd.node.getChildByName("labnum").getComponent(cc.Label);
                    this.labNum = tags[ndIdx].toString();
                    labAmount.string = utils.formatNumberWithCommas(this.labNum, 0);
                }
            })
            let amount = this.amount_edit.string;
            if(amount.length > 0){
                this.changeTotalView(parseFloat(amount));
            }
        }else{
            this.labAmount_content.active = false;
        }
    }

    formatPhoneNumber(phoneNumber: string) {
        if (phoneNumber.length !== 11) {
            throw new Error('Invalid phone number length. It should be 11 digits.');
        }
        //保留前三位和后四位，中间用****替换
        return `${phoneNumber.slice(0, 4)}****${phoneNumber.slice(8)}`;
    }

    onPhoneText(text, editbox, customEventData) {
        let changetx = text.replace(/[^0-9]/g, '');
        this._amount = changetx;
        if (parseInt(this._amount)) {
            editbox.string = parseInt(this._amount);
            let maxAmount = parseFloat(this.withdrawData[this.selectIdx].max);
            if (parseInt(this._amount) >= maxAmount) {
                this._amount = maxAmount + '';
                editbox.string = maxAmount;
            }
        }
        let impl = editbox['_impl'];
        impl._updateStyleSheet();
        this.inputErrTip(changetx);
        this.setStateContinue();
        this.changeTotalView(parseFloat(this._amount));
    }

    inputErrTip(text) {
        let selectIdx = this.selectIdx || 0;
        let minAmount = parseFloat(this.withdrawData[selectIdx].min);
        let maxAmount = parseFloat(this.withdrawData[selectIdx].max);
        this.min_amount = minAmount;
        this.max_amount = maxAmount;
        let show_err = false;
        if (text == "") {
            this.minimumTip.node.active = false;
            this.maximumTip.node.active = false;
            this.insufficientTip.node.active = true;
            this.insufficientTip.string = "Enter Amount: " + minAmount + " - " + utils.formatNumberWithCommas(maxAmount,0) + "₱";
            this.amount_edit.placeholder = this.insufficientTip.string;//联动起来bug
            show_err = true;
        }else if(text < minAmount) {
            this.minimumTip.node.active = true;
            this.maximumTip.node.active = false;
            this.insufficientTip.node.active = false;
            let amountNum = utils.formatNumberWithCommas(minAmount,0);
            this.minimumTip.string = `The minimum amount is ${amountNum}₱`
            show_err = true;
        } else if (text >= minAmount && text <= maxAmount) {
            this.minimumTip.node.active = false;
            this.maximumTip.node.active = false;
            this.insufficientTip.node.active = false;
            if (this.availableBalance < text) {
                this.continueBtn.node.opacity = 155;
                this.insufficientTip.node.active = true;
                this.minimumTip.node.active = false;
                this.maximumTip.node.active = false;
                this.insufficientTip.string = "Insufficient Balance.";
                show_err = true;
            }
            else if (text == maxAmount) {
                this.maximumTip.node.active = true;
                this.minimumTip.node.active = false;
                this.insufficientTip.node.active = false;
                let amountNum = utils.formatNumberWithCommas(maxAmount);
                this.maximumTip.string = `The maximum allowable input is ${amountNum}₱`
            }
        } else if (text > maxAmount) {
            this.maximumTip.node.active = true;
            this.minimumTip.node.active = false;
            this.insufficientTip.node.active = false;
            let amountNum = utils.formatNumberWithCommas(maxAmount,0);
            this.maximumTip.string = `The maximum amount is ${amountNum}₱`
            if (this.availableBalance < text) {
                this.continueBtn.node.opacity = 155;
                this.insufficientTip.node.active = true;
                this.minimumTip.node.active = false;
                this.maximumTip.node.active = false;
                this.insufficientTip.string = "Insufficient Balance.";
            }
            show_err = true;
        }

        this.maximumTip.node.color = text == maxAmount ? cc.color(192, 192, 192) : cc.color(172, 17, 64);

        // let maskview = this.labAmount_content.getChildByName("view");
        // if(show_err){
        //     let bmask = this.amountBgNode.getChildByName('mask')
        //     bmask.height = maskview.height + 400;
        //     let src = bmask.getComponent(RoundRectMask)
        //     src.radius = 26
        // }else{
        //     let bmask = this.amountBgNode.getChildByName('mask')
        //     bmask.height = maskview.height + 330;
        //     let src = bmask.getComponent(RoundRectMask)
        //     src.radius = 26
        // }
    }

    onEdittingDidBegin(editbox: cc.EditBox) {
        Global.instance.scrollTo(0,0,100);
        Global.getInstance().editParentMove(editbox, this.mainNode.parent,800)
        editbox.placeholderLabel.node.name = editbox.placeholder
        editbox.placeholder = ""
    }

    onEdittingDidEnd(editbox: cc.EditBox) {
        if (Global.getInstance().needScreenUp()) {
            this.mainNode.parent.y = 0
        }
        editbox.placeholder = editbox.placeholderLabel.node.name
    }

    onClickConfigBtn(event, data) {
        let idx = this.selectIdx || 0;
        let tags = this.withdrawData[idx].tags;
        this.amount_edit.string = tags[parseInt(data)].toString();
        this.inputErrTip(tags[parseInt(data)]);
        this._amount = tags[parseInt(data)].toString();
        this.setStateContinue();
        this.changeTotalView(this._amount);
    }

    changeTotalView(withdrawNum) {
        if (!withdrawNum) withdrawNum = 0;
        let idx = this.selectIdx || 0;//bug 默认selectidx null 未设置
        let withdraw_list = this.withdrawData[idx].tags;
        for (let index = 0; index < withdraw_list.length; index++) {
            let element = withdraw_list[index];
            //超过12个会报错
            if(index <= 11){
                if (withdrawNum == element) {
                    this.withdrawAmount[index].target.getComponent(cc.Sprite).spriteFrame = this.btnSpriteFrame[1];
                } else {
                    this.withdrawAmount[index].target.getComponent(cc.Sprite).spriteFrame = this.btnSpriteFrame[0];
                }
            }
        }
    }

    onClickContinue() {
        if (this.continueBtn.node.opacity != 255) {
            // this.audioPlay("audio/click_disable",false);
            return;
        }
        //1倍流水风控
        HttpUtils.getInstance().post(4, 3, this, "/common/api/withdraw/risk", {
            token: Global.getInstance().token,
        }, (response) => {
            if (!this.node || !this.node.isValid) {
                return;
            }
            if(response.data && response.data.status == 0){
                uiManager.instance.showDialog(UI_PATH_DIC.BeforeWithdrawal,[response.data],null,DEEP_INDEXZ.BEFORE_WITHDRAW);
            }else{
                this.click_Continue();
            }
        }, (response)=>{
            this.click_Continue();
        });
    }

    click_Continue(){
        if (Global.instance.is_mini_game()) {
            // if (Global.getInstance().userdata.withdraw_password == 0) {//未设置支付密码
            //     uiManager.instance.showDialog(UI_PATH_DIC.Preconditions,[{type:"maya"}], null, 1);
            // } else {
            //     uiManager.instance.showDialog(UI_PATH_DIC.Password, [PASS_TYPE.ConfirmPwSuc], null, 1)
            // }
            uiManager.instance.loadPrefabByLoading(UI_PATH_DIC.withDeposit, cc.Prefab,(finish, total, item) => {
            }, (err, prefab: cc.Prefab) => {
                if (err) return
                if (!prefab) return
                //let parent = Global.getInstance().popNode
                if (!this.node || !cc.isValid(this.node)) return
                let gameNd = this.node.getChildByName("withDeposit")
                if (gameNd) {
                    return
                }
                // if(Global.getInstance().getPageId() != E_PAGE_TYPE.HALL){return}//控制必须在hall 页面弹出
                let ndPop = cc.instantiate(prefab)
                ndPop.name = "withDeposit"
                ndPop.zIndex = DEEP_INDEXZ.WITHDRAW_OK;
                ndPop.parent = this.node;
                let script = ndPop.getComponent(withdrawDepositDialog);
                let name = 'GCash'
                if(ALL_APP_SOURCE_CONFIG.channel == E_CHANEL_TYPE.MAYA){
                    name = "Maya"
                }
                script.initDate("Withdrawal",this._amount,name,this._amount,()=>{
                    this.reqExchange();
                })
            })
        } else if (ALL_APP_SOURCE_CONFIG.channel == E_CHANEL_TYPE.WEB) {
            //验证一下kyc
            KycMgr.instance.verify_kyc(INGSME_TYPE.withdraw,(isVerity)=>{
                if(isVerity){
                    //这里是 认证过之后的 逻辑 其他逻辑会自动 执行
                    if (Global.getInstance().userdata.withdraw_password == 0) {//未设置支付密码
                        uiManager.instance.showDialog(UI_PATH_DIC.Preconditions,null,null,DEEP_INDEXZ.PRECONDITIONS);
                    } else {
                        uiManager.instance.showDialog(UI_PATH_DIC.Password, [PASS_TYPE.ConfirmPwSuc], null, DEEP_INDEXZ.PASSWORD)
                    }
                }
            });
        }
    }

    //ContinueBtn状态
    setStateContinue() {
        if (this._amount < this.min_amount || this._amount > this.max_amount || this._amount > this.availableBalance) {
            this.continueBtn.node.opacity = 155;
        } else {
            this.continueBtn.node.opacity = 255;
        }
    }

    onPasswordVerified(data) {
        if (data.passType == PASS_TYPE.ConfirmPwSuc) {
            this.reqExchange();
        }
    }
    reqExchange(){
        GeetestMgr.instance.geetest_device(GEETEST_TYPE.withdraw,(succ)=>{
            if(succ){
                let ret = {}
                if(Object.getPrototypeOf(succ) !== Boolean.prototype){
                    ret = succ
                }
                this.reqExchange_true(ret);
            }
        })
    }
    reqExchange_true(ret?) {
        let gee_guard = ret?.geetest_guard || '';
        let uInfo = ret?.userInfo || '';
        let gee_captcha = ret?.geetest_captcha || '';
        let params;
        params = {
            account_id: this.accountID || '', 
            token: Global.getInstance().token,
            product_type: PRODUCT_TYPE.MAYA_MINI,
            name: Global.getInstance().userdata.nickname,
            amount: parseInt(this._amount),
            price: parseInt(this._amount),
            quantity: 1,
            geetest_guard:gee_guard,
            userInfo:uInfo,
            geetest_captcha:gee_captcha,
            buds:ret?.buds || '64',
        }
        if (ALL_APP_SOURCE_CONFIG.channel == E_CHANEL_TYPE.WEB) {
            if (this.accountType == ACCOUNT_WITHDRAW.MAYA_WEB) {
                params["product_type"] = PRODUCT_TYPE.WITHDRAW_MAYA_WEB;
            } else if (this.accountType == ACCOUNT_WITHDRAW.GCASH_WEB) {
                params["product_type"] = PRODUCT_TYPE.WITHDRAW_GCASH_WEB;
            }
        }
        if (ALL_APP_SOURCE_CONFIG.channel == E_CHANEL_TYPE.G_CASH) {
            params["product_type"] = PRODUCT_TYPE.GCASH;
            params["app_package_name"] = 'com.nustargame.gcash'
        }

        HttpUtils.getInstance().post(3, 3, this, "/common/api/exchange", params, (response) => {
            console.log('-----------------oosjfj----',JSON.stringify(response))
            if (Global.getInstance().mayaMode || Global.getInstance().gcashMode) {
                uiManager.instance.showDialog(UI_PATH_DIC.TipsRedeemSuc,null,null,DEEP_INDEXZ.COMMON_TIPS)
            } else {
                let str = "Your withdrawal request has been submitted successfully. You will receive in your account within 10 minutes.";
                Global.getInstance().showCommonTip2({ word: str, confirm: "Done" }, this, true);
            }
            if (!this.node || !this.node.isValid) {
                return;
            }
            let parent = Global.getInstance().popNode
            if (parent) {
                let gameNd = parent.getChildByName("withDeposit")
                if (gameNd) {
                    gameNd.destroy();
                }
            }
            this.hide()
            Global.getInstance().updateBalanceAfterGame(()=>{
                this.wallet_label.string = utils.formatNumberWithCommas(Global.getInstance().balanceTotal);
            });
            //更新余额
            cc.director.emit("updateTotalBalance");
        }, (err) => {
            console.log('--------------err---oosjfj----',JSON.stringify(err));
            if (err.code == MAINTENANCETIPCODE) { //服务器维护
                showMaintenancetip(err?.msg)
                return;
            }
            else if (err.code == "1") {//各种参数错误
                let str = err?.msg;
                Global.getInstance().showCommonTip2({ word: str, confirm: "Done" }, this, true)
            } else {//其他类型错误
                let str = err?.msg;
                Global.getInstance().showCommonTip2({ word: str, confirm: "Done" }, this, true);
            }
            if (!this.node || !this.node.isValid) {
                return;
            }
            let parent = Global.getInstance().popNode
            if (parent) {
                let gameNd = parent.getChildByName("withDeposit")
                if (gameNd) {
                    gameNd.destroy();
                }
            }
            this.hide();
        });
    }

    audioPlay(url,loop) {
        AudioManager.getInstance().playEffect(url, loop);
    }

    closeDialog() {
        this.hide();
    }

    onClickRecordBtn() {
        uiManager.instance.showDialog(UI_PATH_DIC.Transations, [{type:"withdraw",openMark:"withdraw"}],null,DEEP_INDEXZ.TRANSATIONS);
    }

    onDestroy() {
        cc.director.off('password_suc', this.onPasswordVerified, this);
    }
}
