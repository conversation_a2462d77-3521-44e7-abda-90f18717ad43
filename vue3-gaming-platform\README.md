# Vue3 Gaming Platform

## 📖 项目概述

这是一个基于 Vue3 + TypeScript + Pinia 的完整游戏平台项目，整合了Hall（游戏大厅）和Wallet（钱包任务）两大核心模块。项目从 Cocos Creator TypeScript 代码转换而来，实现了现代化的Web游戏平台功能。

## 🚀 技术栈

- **框架**: Vue 3.4+ (Composition API)
- **语言**: TypeScript 5.2+
- **状态管理**: Pinia 2.1+
- **路由**: Vue Router 4.2+
- **构建工具**: Vite 4.4+
- **样式**: SCSS + CSS3
- **动画**: Animate.css
- **工具库**: @vueuse/core, Axios, Lodash-es, Dayjs
- **测试**: Vitest + @vitest/ui
- **代码规范**: ESLint + Prettier

## 📁 项目结构

```
vue3-gaming-platform/
├── public/                     # 静态资源
├── src/
│   ├── api/                   # API 接口层
│   │   ├── hallApi.ts         # 大厅相关API
│   │   ├── walletApi.ts       # 钱包相关API
│   │   └── index.ts           # API统一导出
│   ├── assets/                # 资源文件
│   │   ├── images/            # 图片资源
│   │   ├── icons/             # 图标资源
│   │   └── fonts/             # 字体资源
│   ├── components/            # 组件库
│   │   ├── Common/            # 通用组件
│   │   ├── Hall/              # 大厅相关组件
│   │   ├── Wallet/            # 钱包相关组件
│   │   ├── Layout/            # 布局组件
│   │   └── Debug/             # 调试组件
│   ├── composables/           # 组合式函数
│   ├── directives/            # 自定义指令
│   ├── router/                # 路由配置
│   ├── stores/                # Pinia 状态管理
│   │   ├── index.ts           # 全局状态
│   │   ├── hallStore.ts       # 大厅状态
│   │   └── walletStore.ts     # 钱包状态
│   ├── styles/                # 样式文件
│   │   ├── main.scss          # 主样式文件
│   │   ├── variables.scss     # SCSS变量
│   │   └── mixins.scss        # SCSS混入
│   ├── types/                 # TypeScript 类型定义
│   │   ├── index.ts           # 类型统一导出
│   │   ├── common.ts          # 通用类型
│   │   ├── hall.ts            # 大厅类型
│   │   └── wallet.ts          # 钱包类型
│   ├── utils/                 # 工具函数
│   ├── views/                 # 页面组件
│   ├── App.vue                # 根组件
│   └── main.ts                # 入口文件
├── tests/                     # 测试文件
├── package.json
├── vite.config.ts
├── tsconfig.json
└── README.md
```

## 🎯 核心功能模块

### 1. Hall模块 - 游戏大厅 🎮

#### 主要功能
- **游戏展示**: 支持多种游戏类型的网格展示
- **分类导航**: Casino、Slots、Poker、Bingo等分类
- **搜索筛选**: 实时搜索和多维度筛选
- **Banner轮播**: 活动和推广内容展示
- **用户偏好**: 收藏、历史记录管理
- **响应式设计**: 完美适配移动端和桌面端

#### 核心组件
- `HallMain.vue` - 大厅主页面
- `GameList.vue` - 游戏列表组件
- `GameTypeNav.vue` - 游戏分类导航
- `BannerCarousel.vue` - Banner轮播组件
- `GameItem.vue` - 游戏项组件

#### 状态管理
- 游戏列表管理和筛选
- 用户游戏偏好存储
- Banner和活动数据管理
- 搜索和筛选状态

### 2. Wallet模块 - 钱包任务 💰

#### 主要功能
- **任务管理**: 完整的任务生命周期管理
- **进度跟踪**: 实时任务进度和倒计时
- **奖励系统**: 多种奖励类型和领取机制
- **状态流转**: 未解锁→进行中→已完成的状态管理
- **智能筛选**: 多维度任务筛选和排序
- **通知提醒**: 任务状态变更通知

#### 核心组件
- `WalletTaskModal.vue` - 钱包任务主弹窗
- `WalletTaskItem.vue` - 任务项组件
- `WalletTaskRuleModal.vue` - 任务规则弹窗
- `TaskProgress.vue` - 任务进度组件
- `TaskCountdown.vue` - 任务倒计时组件

#### 状态管理
- 任务列表和状态管理
- 任务进度计算和更新
- 筛选和排序配置
- 用户偏好和通知设置

### 3. 交易系统 💳

#### 主要功能
- **记录分类**: 充值、提现、奖励记录
- **状态跟踪**: 详细的交易状态管理
- **时间筛选**: 灵活的时间范围筛选
- **详情查看**: 完整的交易详情展示

#### 核心组件
- `TransactionList.vue` - 交易记录列表
- `TransactionItem.vue` - 交易记录项
- `TransactionDetails.vue` - 交易详情弹窗

### 4. 通用组件库 🧩

#### 布局组件
- `TabBar.vue` - 底部导航栏
- `Header.vue` - 页面头部
- `Sidebar.vue` - 侧边栏

#### 交互组件
- `GlobalModal.vue` - 全局模态框
- `GlobalToast.vue` - 全局提示
- `GlobalLoading.vue` - 全局加载
- `FloatingActionButton.vue` - 浮动操作按钮

#### 功能组件
- `NetworkStatus.vue` - 网络状态监控
- `DebugPanel.vue` - 开发调试面板

## 🛠️ 开发指南

### 环境要求
```bash
Node.js >= 18.0.0
npm >= 8.0.0
```

### 安装依赖
```bash
npm install
```

### 开发模式
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

### 运行测试
```bash
npm run test
npm run test:ui      # 测试UI界面
npm run test:coverage # 测试覆盖率
```

### 代码检查和格式化
```bash
npm run lint         # 代码检查
npm run format       # 代码格式化
npm run type-check   # 类型检查
```

## 🎨 设计原则

### 1. 组件化设计
- 单一职责原则，每个组件专注特定功能
- 高内聚低耦合，组件间通过props和events通信
- 可复用性，通用组件支持多场景使用

### 2. 类型安全
- 完整的TypeScript类型定义
- 严格的类型检查和智能提示
- 接口和枚举的统一管理

### 3. 响应式设计
- 移动优先的设计策略
- 灵活的断点系统
- 触摸友好的交互设计

### 4. 性能优化
- 组件懒加载和代码分割
- 图片懒加载和压缩
- 虚拟滚动和分页加载
- 合理的缓存策略

## 📱 响应式断点

```scss
// 移动端
@media (max-width: 767px) { }

// 平板端
@media (min-width: 768px) and (max-width: 1023px) { }

// 桌面端
@media (min-width: 1024px) { }

// 大屏幕
@media (min-width: 1440px) { }
```

## 🔧 配置说明

### Vite配置
- 路径别名配置
- 代理服务器设置
- 构建优化配置
- 环境变量管理

### 路由配置
- 嵌套路由结构
- 路由守卫和权限控制
- 页面过渡动画
- 元信息管理

### 状态管理
- Pinia模块化设计
- 持久化存储
- 开发工具集成

## 🚀 部署指南

### 构建
```bash
npm run build
```

### 预览
```bash
npm run preview
```

### 环境变量
```bash
# 开发环境 (.env.development)
VITE_API_BASE_URL=http://localhost:8080/api
VITE_WS_URL=ws://localhost:8080/ws

# 生产环境 (.env.production)
VITE_API_BASE_URL=https://api.example.com
VITE_WS_URL=wss://api.example.com/ws
```

## 🔄 从Cocos Creator转换说明

### 主要转换内容

1. **架构转换**
   - Cocos Creator场景 → Vue3页面组件
   - Cocos组件系统 → Vue3组合式API
   - 全局管理器 → Pinia状态管理

2. **UI系统转换**
   - Cocos UI节点 → HTML/CSS布局
   - 动画系统 → CSS动画 + Animate.css
   - 事件系统 → Vue事件机制

3. **业务逻辑保留**
   - 游戏分类和筛选逻辑
   - 钱包任务状态管理
   - 交易记录处理
   - 用户偏好存储

4. **新增功能**
   - 响应式设计
   - PWA支持
   - 无障碍访问
   - 性能监控
   - 错误追踪

## 📊 性能指标

- **首屏加载时间**: < 2s
- **路由切换时间**: < 300ms
- **组件渲染时间**: < 100ms
- **内存使用**: < 50MB
- **包体积**: < 2MB (gzipped)

## 🧪 测试策略

### 单元测试
- 组件逻辑测试
- 工具函数测试
- 状态管理测试

### 集成测试
- 页面交互测试
- API接口测试
- 路由导航测试

### E2E测试
- 用户流程测试
- 跨浏览器测试
- 性能测试

## 🔒 安全考虑

- XSS防护
- CSRF防护
- 内容安全策略(CSP)
- 敏感数据加密
- API接口鉴权

## 🌐 国际化支持

- 多语言切换
- 本地化格式
- RTL布局支持
- 时区处理

## 📈 监控和分析

- 错误监控
- 性能监控
- 用户行为分析
- 业务指标统计

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

## 📄 许可证

MIT License

## 👥 开发团队

- **前端团队**: Vue3 + TypeScript 开发
- **UI/UX团队**: 界面设计和用户体验
- **后端团队**: API接口和数据服务
- **测试团队**: 质量保证和自动化测试

---

*最后更新: 2025-08-07*

## 🎯 下一步计划

- [ ] 添加更多游戏类型支持
- [ ] 实现实时通知系统
- [ ] 优化移动端性能
- [ ] 添加离线模式支持
- [ ] 集成支付系统
- [ ] 实现社交功能
- [ ] 添加数据可视化
- [ ] 完善无障碍功能
