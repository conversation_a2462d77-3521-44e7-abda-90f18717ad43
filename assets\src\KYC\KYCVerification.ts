import { DEEP_INDEXZ, E_FUND_TYPE, UI_PATH_DIC } from "../GlobalConstant";
import Global from "../GlobalScript";
import UICommon from "../component/UICommon";
import { uiManager } from "../mgr/UIManager";
import HttpUtils from "../net/HttpUtils";
import utils from "../utils/utils";
import EditItem, { E_KYC_EDIT_TYPE, EditDataType } from "./EditItem";
import DateEditItem from "./DateEditItem";
import AddressDown, { ADDR_DOWN_TIP_TYPE } from './AddressDown';
import TakePhoto, { PHOTO_TYPE } from "./TakePhoto";
import AddressTips, { ADDR_TIP_TYPE } from "./AddressTips";
import SingleToggle from "../hall/SingleToggle";
import ShootAndUpload from "./ShootAndUpload";
import { INGSME_TYPE, KycMgr } from "./KycMgr";
import { GEETEST_TYPE, GeetestMgr } from "../geetest/GeetestMgr";
import RoundRectMask from "../RoundRectMask";
const { ccclass, property } = cc._decorator;
export const kycVerification = "kycVerification"
export function showKYCVerification(walletData?, fundType?) {
    uiManager.instance.showDialog(UI_PATH_DIC.KYCVerification, [{ walletData, fundType }],null,DEEP_INDEXZ.KYC);//添加zindex 高一点能 遮挡显示转盘按钮
}
export enum E_MODIFY_TYPE {
    ADD,
    MODIFY
}
enum KYC_STEP{
    KYC_STEP_NAME = 0,//提交名字页面
    KYC_STEP_ADDRESS,//提交地址页面
    KYC_STEP_PHOTO,//上传照片页面
    KYC_STEP_MEDIA,//提交媒体账号
    KYC_STEP_FINISH,//完成页面也是成功页面
    KYC_SUCCESS,//成功页面 用于区分 
    KYC_STEP_SIMPLE,//简化认证页面
}
@ccclass
export default class KYCVerification extends UICommon {
    @property(cc.Label)
    title: cc.Label = null;
    @property(cc.Label)
    tipAccount: cc.Label = null;

    //标题 node
    @property(cc.Node)
    upNode: cc.Node = null;

    //center node
    @property(cc.Node)
    centerNode: cc.Node = null;

    //提交成功标识 node
    @property(cc.Node)
    successNode: cc.Node = null;

    //edit 控件
    @property(cc.Prefab)
    editPrefab: cc.Prefab = null;

    //日期 控件
    @property(cc.Prefab)
    dateEditPrefab: cc.Prefab = null;

    //第二步 提示上部
    @property(cc.Prefab)
    adrrTipsPrefab: cc.Prefab = null;

    //第二步 非官方 选择区域
    @property(cc.Prefab)
    addrDownPrefab: cc.Prefab = null;

    //第3步 拍照页面
    @property(cc.Prefab)
    takephotoPrefab: cc.Prefab = null;

    //3.5步骤 拍照调用相机
    @property(cc.Prefab)
    shootPhotoPrefab:cc.Prefab = null;

    //sroll
    @property(cc.ScrollView)
    m_scroll:(cc.ScrollView) = null;
    //srollcontent 
    @property(cc.Node)
    scroll_content:(cc.Node) = null;
    //srollcontent bg 用于背景 半圆
    @property(cc.Node)
    scroll_content_bg:(cc.Node) = null;

    //继续按钮 
    @property(cc.Node)
    continueNode:(cc.Node) = null;
    shoot_upload_src = null;//拍照层脚本
    continueNum = 0;//第几步 认证

    allEditItem = [];//保存所有输入框
    filterTimePrefab: cc.Prefab = null;
    filterTimeResult = [];//选中的 数组对象
    _kType:E_KYC_EDIT_TYPE = 0;

    userAccountInfo: any[] = []
    allAccountCodes: any[] = []
    curAccountInfo: any = null;
    _firstName: string = ""
    _middleName: string = ""
    _lastName: string = ""
    _account: string = ""

    _birthDay: string = ""
    _birthMonth: string = ""
    _birthYear: string = ""

    _accountId: string = "";
    curPayCode: string = '';
    curFundType: E_FUND_TYPE;
    // _verifyCode: String = ""
    m_t: number = 120;
    _modifyType: E_MODIFY_TYPE;
    curPayPath: string = '';


    start() {
        // super.start()
        cc.director.on("Single_Toggle_Filter_Result",this.filterTime,this);
        this.adaptSize();
        //init 会先传值过来 continuenum
        switch (this.continueNum) {
            case KYC_STEP.KYC_STEP_NAME:
                this.loadUI_Name();
                break;
            case KYC_STEP.KYC_STEP_ADDRESS:
                this.loadUI_Address();
                break;
            case KYC_STEP.KYC_STEP_PHOTO:
                this.loadUI_takephoto();
                break;
            case KYC_STEP.KYC_STEP_MEDIA:
                this.loadUI_Media();
                break;
            case KYC_STEP.KYC_STEP_SIMPLE:
                this.loadUI_Simple();
                break;
            default:
                break;
        }
    }
    protected onLoad(): void {
        cc.director.emit('hideHallLive');//通知大厅 关闭直播流
    }
    //适配scroll 尺寸
    adaptSize(){
        let scroll = this.node.getChildByName('Center').getChildByName('mScroll');
        let scroll_view = scroll.getChildByName('view');
        let scroll_content = scroll_view.getChildByName('content');
        let safeHeight = cc.sys.getSafeAreaRect().height;
        scroll.height = safeHeight - 400;//上下区域各占200
        scroll_view.height = safeHeight - 400;
        scroll_content.height = safeHeight - 400;
        scroll_content.y = scroll_content.height/2.0;
    }
    //重置 半圆背景 和位置 posy是偏移量
    reset_radius_pos(posy = 0){
        //重置一下 半圆背景
        let self = this;
        this.scroll_content_bg.setPosition(0,posy)
        setTimeout(() => {
            let height_content = self.scroll_content.height;
            self.m_scroll.content.height = height_content;
            self.scroll_content_bg.height = height_content + posy;
            let round_src = self.scroll_content_bg.getComponent(RoundRectMask)
            round_src.radius = 25;
        }, 200);
    }
    //加载所有控件 简单验证
    loadUI_Simple(){
        this.continueNum = KYC_STEP.KYC_STEP_SIMPLE;
        this.continueNode.opacity = 155;//未完成 填写状态
        this.continueNode.getChildByName('btn_label').getComponent(cc.Label).string = 'Continue';
        this.scroll_content.removeAllChildren();//删除 所有的子node
        this.allEditItem = [];//清空 输入控件 数组
        this.title.string = 'KYC Verification';
        let round_src = this.scroll_content_bg.getComponent(RoundRectMask)
        round_src.radius = 25;
        this.addTipsItem(ADDR_TIP_TYPE.TIP_SIMPLE,cc.v2(0,-15),this.scroll_content);

        let textArr = [
            {"title":"First Name","placeholder":"Please enter your first name"},
            {"title":"Middle Name (Optional)","placeholder":"Please enter your middle name"},
            {"title":"Last Name","placeholder":"Please enter your last name"},   
        ]

        let first_name = this.add1EditItem(E_KYC_EDIT_TYPE.FIRST_NAME,cc.v2(0,-100),this.scroll_content,textArr[0]);
        this.allEditItem[0] = first_name;
        let middle_name = this.add1EditItem(E_KYC_EDIT_TYPE.MIDDLE_NAME,cc.v2(0,-370),this.scroll_content,textArr[1]);
        this.allEditItem[4] = middle_name;
        let full_last = this.add1EditItem(E_KYC_EDIT_TYPE.FULL_LAST,cc.v2(0,-490),this.scroll_content,textArr[2]);
        this.allEditItem[1] = full_last;

        let birthDay = this.add1DateEditItem(cc.v2(0,-730),this.scroll_content);
        this.allEditItem[3] = birthDay;

        let photo_src = this.addTakePhotoItem(PHOTO_TYPE.UP_ID_PHOTO,cc.v2(0,-1150),this.scroll_content);
        this.allEditItem[2] = photo_src;

        let addr_down = this.addDownItem(ADDR_DOWN_TIP_TYPE.NORMAL,cc.v2(0,-1540),this.scroll_content);
        this.allEditItem[5] = addr_down;

        if (KycMgr.instance.kyc_data) {
            let data = KycMgr.instance.kyc_data;
            if (data.first_middle_name) {
                //增加 中间名字 简版kyc
                let names = data.first_middle_name.split(' ');
                first_name?.setEditboxToText(names[0]);
                if (names[1] && names[1] != "") {
                    middle_name?.setEditboxToText(names[1]);
                } else {
                    middle_name?.setEditboxToText("");
                    middle_name?.setEditboxToPlaceholder("-");
                }
            }
            if (data.last_name) {
                full_last?.setEditboxToText(data.last_name);
            }
            if (data.day && data.month && data.year) {
                let da = data.year + "-" + data.month + "-" + data.day;
                birthDay?.setEditboxToText(da);
            }
        }
        this.reset_radius_pos(-200);
    }
    //获取kyc 的本地存储信息 根据user ID区分
    get_kycdata_uid(){
        let idUniq = Global.GLOBAL_STORAGE_KEY.KYC;
        if (Global.getInstance().userdata && Global.getInstance().userdata.user_id) {
            idUniq = idUniq + '_' + Global.getInstance().userdata.user_id;
        }
        let saveData = Global.getInstance().getStoreageData(idUniq,'')
        let allJsonData = []
        if(saveData != ''){
            allJsonData = JSON.parse(saveData);
        }
        return allJsonData;
    }
    //存储本地 kyc数据根据 uid web端会更换uid
    set_kycdata_uid(allJsonData:any){
        let idUniq = Global.GLOBAL_STORAGE_KEY.KYC;
        if (Global.getInstance().userdata && Global.getInstance().userdata.user_id) {
            idUniq = idUniq + '_' + Global.getInstance().userdata.user_id;
        }
        let saveData = JSON.stringify(allJsonData);
        Global.getInstance().setStoreageData(idUniq,saveData);
    }
    //加载所有控件
    loadUI_Name(){
        this.continueNum = KYC_STEP.KYC_STEP_NAME;
        this.continueNode.opacity = 155;//未完成 填写状态
        this.continueNode.getChildByName('btn_label').getComponent(cc.Label).string = 'Continue';
        this.scroll_content.removeAllChildren();//删除 所有的子node
        this.title.string = 'KYC Verification';
        let textArr = [
            {"title":"First Name","placeholder":"Please enter your first name"},
            {"title":"Middle Name (Optional)","placeholder":"Please enter your middle name"},
            {"title":"Last Name","placeholder":"Please enter your last name"},
            {"title":"Branch","placeholder":"Please select your Branch"},
            {"title":"Nationality","placeholder":"Please select your nationality"}
        ]

        let first_name = this.add1EditItem(E_KYC_EDIT_TYPE.FIRST_NAME,cc.v2(0,-120),this.scroll_content,textArr[0]);
        this.allEditItem[0] = first_name;
        let middle_name = this.add1EditItem(E_KYC_EDIT_TYPE.MIDDLE_NAME,cc.v2(0,-340),this.scroll_content,textArr[1]);
        this.allEditItem[1] = middle_name;
        let last_name = this.add1EditItem(E_KYC_EDIT_TYPE.LAST_NAME,cc.v2(0,-560),this.scroll_content,textArr[2]);
        this.allEditItem[2] = last_name;
        let birthDay = this.add1DateEditItem(cc.v2(0,-780),this.scroll_content);
        this.allEditItem[3] = birthDay;

        let brach = this.add1EditItem(E_KYC_EDIT_TYPE.BRANCH,cc.v2(0,-1020),this.scroll_content,textArr[3]);
        this.allEditItem[4] = brach;
        let nation = this.add1EditItem(E_KYC_EDIT_TYPE.NATIONALITY,cc.v2(0,-1240),this.scroll_content,textArr[4]);
        this.allEditItem[5] = nation;

        let allJsonData = this.get_kycdata_uid()
        if(allJsonData.length > 0){first_name?.setEditboxToText(allJsonData[0])}
        if(allJsonData.length > 1){middle_name?.setEditboxToText(allJsonData[1])}
        if(allJsonData.length > 2){last_name?.setEditboxToText(allJsonData[2])}
        if(allJsonData.length > 3){birthDay?.setEditboxToText(allJsonData[3])}
        if(allJsonData.length > 4){brach?.setEditboxToText(allJsonData[4])}
        if(allJsonData.length > 5){nation?.setEditboxToText(allJsonData[5])
            this.continueNode.opacity = 255;
        }

        if (KycMgr.instance.kyc_data) {
            let data = KycMgr.instance.kyc_data;
            if (data.first_middle_name) {
                let names = data.first_middle_name.split(' ');
                first_name?.setEditboxToText(names[0]);
                if (names[1] && names[1] != "") {
                    middle_name?.setEditboxToText(names[1]);
                } else {
                    middle_name?.setEditboxToText("");
                    middle_name?.setEditboxToPlaceholder("-");
                }
            }
            if (data.last_name) {
                last_name?.setEditboxToText(data.last_name);
            }
            if (data.day && data.month && data.year) {
                let da = data.year + "-" + data.month + "-" + data.day;
                birthDay?.setEditboxToText(da);
            }
        }
        this.reset_radius_pos();
    }
    //加载所有控件_第二页
    loadUI_Address(){
        this.continueNum = KYC_STEP.KYC_STEP_ADDRESS;
        this.continueNode.opacity = 155;//未完成 填写状态
        this.continueNode.getChildByName('btn_label').getComponent(cc.Label).string = 'Continue';
        this.scroll_content.removeAllChildren();//删除 所有的子node
        this.allEditItem = [];//清空 输入控件 数组
        this.title.string = 'KYC Verification';

        this.addTipsItem(ADDR_TIP_TYPE.TIP_NORMAL,cc.v2(0,-150),this.scroll_content);

        let textArr = [
            {"title":"Place of Brith","placeholder":"Please enter your place of birth"},
            {"title":"Current Address","placeholder":"Please enter your current address"},
            {"title":"Permanent Address","placeholder":"Please enter your permanent address"},
            {"title":"Nature of Work","placeholder":"Please enter your nature of work"},
            {"title":"Source of Income","placeholder":"Please select your source of income"}
        ]

        let place_brith = this.add1EditItem(E_KYC_EDIT_TYPE.PLACE_BIRTH,cc.v2(0,-410),this.scroll_content,textArr[0]);
        this.allEditItem[0] = place_brith;
        let curr_addr = this.add1EditItem(E_KYC_EDIT_TYPE.C_ADDRESS,cc.v2(0,-630),this.scroll_content,textArr[1]);
        this.allEditItem[1] = curr_addr;
        let per_addr = this.add1EditItem(E_KYC_EDIT_TYPE.PER_ADDRESS,cc.v2(0,-850),this.scroll_content,textArr[2]);
        this.allEditItem[2] = per_addr;
        let nature_work = this.add1EditItem(E_KYC_EDIT_TYPE.NATURE_WORK,cc.v2(0,-1090),this.scroll_content,textArr[3]);
        this.allEditItem[3] = nature_work;

        let source_income = this.add1EditItem(E_KYC_EDIT_TYPE.S_INCOME,cc.v2(0,-1310),this.scroll_content,textArr[4]);
        this.allEditItem[4] = source_income;

        let addr_down = this.addDownItem(ADDR_DOWN_TIP_TYPE.SELECT,cc.v2(0,-1540),this.scroll_content);
        this.allEditItem[5] = addr_down;

        let allJsonData = this.get_kycdata_uid()
        if(allJsonData.length > 6){place_brith?.setEditboxToText(allJsonData[6])}
        if(allJsonData.length > 7){curr_addr?.setEditboxToText(allJsonData[7])}
        if(allJsonData.length > 8){per_addr?.setEditboxToText(allJsonData[8])}
        if(allJsonData.length > 9){nature_work?.setEditboxToText(allJsonData[9])}
        if(allJsonData.length > 10){source_income?.setEditboxToText(allJsonData[10])
            this.continueNode.opacity = 255;
        }

        this.reset_radius_pos(-300);
    }
    //加载所有控件_第3页
    loadUI_takephoto(){
        this.continueNum = KYC_STEP.KYC_STEP_PHOTO;
        this.continueNode.opacity = 155;//未完成 填写状态
        this.continueNode.getChildByName('btn_label').getComponent(cc.Label).string = 'Continue';
        this.scroll_content.removeAllChildren();//删除 所有的子node
        this.allEditItem = [];//清空 输入控件 数组
        this.title.string = 'Government-Issued ID';

        let textArr = [
            {"title":"Country / Region of Document Issuance","placeholder":"Philippines"},
            {"title":"Select Type of ID","placeholder":"Please select type of ID"},
        ]

        let country_re = this.add1EditItem(E_KYC_EDIT_TYPE.COUNTRY,cc.v2(0,-130),this.scroll_content,textArr[0]);
        this.allEditItem[0] = country_re;
        country_re.setEditboxToText('Philippines');
        let type_id = this.add1EditItem(E_KYC_EDIT_TYPE.S_TYPE_ID,cc.v2(0,-350),this.scroll_content,textArr[1]);
        this.allEditItem[1] = type_id;
        
        let photo_src = this.addTakePhotoItem(PHOTO_TYPE.UP_ID_PHOTO,cc.v2(0,-730),this.scroll_content);
        this.allEditItem[2] = photo_src;

        this.addDownItem(ADDR_DOWN_TIP_TYPE.NORMAL,cc.v2(0,-1180),this.scroll_content);


        let allJsonData = this.get_kycdata_uid()
        if(allJsonData.length > 11){country_re?.setEditboxToText(allJsonData[11])}
        if(allJsonData.length > 12){type_id?.setEditboxToText(allJsonData[12])}
        if(allJsonData.length > 13){photo_src?.setImage(allJsonData[13])
            this.continueNode.opacity = 255;
        }
        this.reset_radius_pos()
    }
    //加载第四页
    loadUI_Media(){
        this.continueNum = KYC_STEP.KYC_STEP_MEDIA;
        this.continueNode.opacity = 155;//未完成 填写状态
        this.continueNode.getChildByName('btn_label').getComponent(cc.Label).string = 'Finish';
        this.scroll_content.removeAllChildren();//删除 所有的子node
        this.title.string = 'Government-Issued ID';
        let textArr = [
            {"title":"Please Select Your Social Media Type","placeholder":"Please select account type"},
            {"title":"Please Enter Your Social Media Account","placeholder":"Please enter your account"},
        ]

        let s_media = this.add1EditItem(E_KYC_EDIT_TYPE.S_MEDIA_TYPE,cc.v2(0,-130),this.scroll_content,textArr[0]);
        this.allEditItem[0] = s_media;
        let media_count = this.add1EditItem(E_KYC_EDIT_TYPE.MEDIA_COUNT,cc.v2(0,-350),this.scroll_content,textArr[1]);
        this.allEditItem[1] = media_count;
        media_count.node.active = false;

        let allJsonData = this.get_kycdata_uid()
        if(allJsonData.length > 14){s_media?.setEditboxToText(allJsonData[14])}
        if(allJsonData.length > 15){media_count?.setEditboxToText(allJsonData[15])
            media_count.node.active = true;
            this.continueNode.opacity = 255;
        }
        this.reset_radius_pos()
    }
    //上传成功之后页面
    submitSuccess(){
        this.continueNum = KYC_STEP.KYC_STEP_FINISH;
        this.continueNode.opacity = 255;//未完成 填写状态
        this.upNode.active = false;
        this.centerNode.active = false;
        this.successNode.active = true;
        this.continueNode.getChildByName('btn_label').getComponent(cc.Label).string = 'Close';
    }

    //添加 一个输入框
    add1EditItem(kType:E_KYC_EDIT_TYPE,pos:cc.Vec2,parent_,data:EditDataType){
        let oneEdit = cc.instantiate(this.editPrefab);
        oneEdit.setPosition(pos);
        oneEdit.parent = parent_;
        let editSrc = oneEdit.getComponent(EditItem);
        editSrc.initData(kType,data,this);

        return editSrc;
    }
    //添加 一个输入自己生日的控件
    add1DateEditItem(pos:cc.Vec2,parent_){
        let oneEdit = cc.instantiate(this.dateEditPrefab);
        oneEdit.setPosition(pos);
        oneEdit.parent = parent_;
        let editSrc = oneEdit.getComponent(DateEditItem);
        editSrc.initData(this);
        return editSrc;
    }
    //添加一个提示控件
    addTipsItem(kType:ADDR_TIP_TYPE,pos:cc.Vec2,parent_){
        let oneEdit = cc.instantiate(this.adrrTipsPrefab);
        oneEdit.setPosition(pos);
        oneEdit.parent = parent_;
        let editSrc = oneEdit.getComponent(AddressTips);
        editSrc.initData(kType);
        return editSrc;
    }
    //添加一个下面选择控件 第二步用到
    addDownItem(kType:ADDR_DOWN_TIP_TYPE,pos:cc.Vec2,parent_){
        let oneEdit = cc.instantiate(this.addrDownPrefab);
        oneEdit.setPosition(pos);
        oneEdit.parent = parent_;
        let editSrc = oneEdit.getComponent(AddressDown);
        editSrc.initData(kType);
        return editSrc;
    }

    //添加一个 拍照的控件
    addTakePhotoItem(kType:PHOTO_TYPE,pos:cc.Vec2,parent_){
        let oneEdit = cc.instantiate(this.takephotoPrefab);
        oneEdit.setPosition(pos);
        oneEdit.parent = parent_;
        let editSrc = oneEdit.getComponent(TakePhoto);
        editSrc.initData(kType,this);
        return editSrc;
    }

    //0/1 0是简化的 1是完整版
    //[{status:0}]
    init(args) {
        if(args.status == 0){
            this.continueNum = KYC_STEP.KYC_STEP_SIMPLE;
        }else{
            let allJsonData = this.get_kycdata_uid()
            if(allJsonData.length < 6){
                this.continueNum = KYC_STEP.KYC_STEP_NAME;
            }else if(allJsonData.length < 11){
                this.continueNum = KYC_STEP.KYC_STEP_ADDRESS;
            }else if(allJsonData.length < 14){
                this.continueNum = KYC_STEP.KYC_STEP_PHOTO;
            }else{
                this.continueNum = KYC_STEP.KYC_STEP_MEDIA;
            }
        }
    }
    onClickOK(){
        let geeid = GEETEST_TYPE.bind_withdraw_account
        if(this._modifyType == E_MODIFY_TYPE.MODIFY){
            geeid = GEETEST_TYPE.change_withdraw_account
        }
        GeetestMgr.instance.geetest_device(geeid,(succ)=>{
            if(succ){
                let ret = {}
                if(Object.getPrototypeOf(succ) !== Boolean.prototype){
                    ret = succ
                }
                this.onClickOK_true(ret);
            }
        })
    }
    onClickOK_true(ret?) {
        let gee_guard = ret?.geetest_guard || '';
        let uInfo = ret?.userInfo || '';
        let gee_captcha = ret?.geetest_captcha || '';
        if (this._modifyType == E_MODIFY_TYPE.ADD) {
            
            let data = {
                account: this._account,
                firstName: this._firstName,
                middleName: this._middleName,
                lastName: this._lastName,
                type: this.curPayPath,
                sucFunc: () => {
                    HttpUtils.getInstance().post(3, 3, this, "api/add/withdraw/no", {
                        token: Global.getInstance().token,
                        account_no: this._account,
                        first_name: this._firstName,
                        middle_name: this._middleName,
                        last_name: this._lastName,
                        pay_code: this.curPayCode,
                        type: this.curFundType,
                        geetest_guard:gee_guard,
                        userInfo:uInfo,
                        geetest_captcha:gee_captcha,
                        buds:ret?.buds || '64',
                        // verification_code: this._verifyCode
                    }, (response) => {
                        if (response.data) {
                            Global.getInstance().showSimpleTip("Your fund account has been added successfully")
                            cc.director.emit("modifyWithdrawNoSuc", response.data)
                            this.closeAction()
                        }
                    }, (response) => {
                        if (response && response.code) {
                            if (response.code == 1) {
                                Global.getInstance().showSimpleTip(response.msg)
                            } else {
                                let str = response.msg
                                Global.getInstance().showSimpleTip(str)
                            }
                        }
                    });
                }
            }
            uiManager.instance.showDialog(UI_PATH_DIC.AccountConfirmation, [data])

        } else if (this._modifyType == E_MODIFY_TYPE.MODIFY) {
           
            let data = {
                account: this._account,
                firstName: this._firstName,
                middleName: this._middleName,
                lastName: this._lastName,
                type: this.curPayPath,
                sucFunc: () => {
                    HttpUtils.getInstance().post(3, 3, this, "api/update/account/info", {
                        token: Global.getInstance().token,
                        account_no: this._account,
                        account_id: this._accountId,
                        first_name: this._firstName,
                        middle_name: this._middleName,
                        last_name: this._lastName,
                        type: this.curFundType,
                        pay_code: this.curPayCode,
                        geetest_guard:gee_guard,
                        userInfo:uInfo,
                        geetest_captcha:gee_captcha,
                        buds:ret?.buds || '64',
                        // verification_code: this._verifyCode
                    }, (response) => {
                        if (response.data) {
                            Global.getInstance().showSimpleTip("Your fund account has been added successfully")
                            cc.director.emit("modifyWithdrawNoSuc", response.data)
                            this.closeAction()
                        }
                    }, (response) => {
                        if (response && response.code) {
                            if (response.code == 1) {
                                Global.getInstance().showSimpleTip(response.msg)
                            } else {
                                let str = response.msg
                                Global.getInstance().showSimpleTip(str)
                            }
                        }
                    });
                }
            }
            uiManager.instance.showDialog(UI_PATH_DIC.AccountConfirmation, [data])

        }

    }
    closeAction() {
        //弹出 退出 确认 框
        // this.hide()//真实退出逻辑 先不调用
        Global.getInstance().showCommonTip3({
            word: "Are you sure you want to cancel your registration?",
            target: this,
            confirmfun: () => {
                this.hide();
                cc.director.emit('checkKycState_login');//通知大厅 重新验证kyc 目标拦截完整版 非kyc用户
                if(KycMgr.instance.in_game_type == INGSME_TYPE.Login){
                    cc.director.emit('ReturnToHome2');//通知大厅 开启直播流
                }
            },
            cancelfun: () => {
            },
            myname: "",
        })
    }

    convertMonth(month: string){
        let m = ["January","February","March","April","May","June","July","August","September","October","November","December"]
        return m[parseInt(month)-1]
    }

    formatPhoneNumber(phoneNumber: string) {
        if (phoneNumber.length == 10) {
            phoneNumber = "0"+phoneNumber;
        }

        if (phoneNumber.length !== 11) {
            return;
        }
        //保留前三位和后四位，中间用****替换
        return `${phoneNumber.slice(0, 4)}****${phoneNumber.slice(8)}`;
    }
    //判断是否可以下一步 
    isCanNext(){
        let retBool = true;
        this.allEditItem.forEach(element => {
            if(!element?.checkError_Edit(true)){retBool = false}
        });
        return retBool;
    }
    //输入完成之后 或者 选择之后 如果都输入没问题 让continue button 变色
    responseContinueColor(){
        let retBool = true;
        this.allEditItem.forEach(element => {
            if(!element?.checkError_Edit()){retBool = false}
        });
        if(retBool){
            this.continueNode.opacity = 255; 
        }else{
            this.continueNode.opacity = 155;//又没有完成的必须项目 则置灰 
        }
    }
    backClick(){
        if(this.continueNum == KYC_STEP.KYC_STEP_SIMPLE){
            //点击退出认证逻辑
            this.closeAction()
            return;
        }
        this.continueNum--;
        if(this.continueNum < 0){
            //点击退出认证逻辑
            this.closeAction()
            return;
        }
        this.showCurrentUI()
    }
    continueClick(){
        //判断是否可以下一步 
        if(this.isCanNext()){
            this.saveEditData();
            this.continueNum++;
            this.showCurrentUI();
        }else{
            //如果错误 定位到错误的地方
            this.m_scroll.scrollToTop(0.5);
        }
    }
    showCurrentUI(){
        if(this.continueNum >= KYC_STEP.KYC_STEP_SIMPLE){
            //点击完成逻辑
            this.continueNum = KYC_STEP.KYC_STEP_SIMPLE;
            this.uploadMyMsg();
            return;
        }
        switch (this.continueNum) {
            case KYC_STEP.KYC_STEP_NAME:
                this.loadUI_Name();
                break;
            case KYC_STEP.KYC_STEP_ADDRESS:
                this.loadUI_Address();
                break;
            case KYC_STEP.KYC_STEP_PHOTO:
                this.loadUI_takephoto();
                break;
            case KYC_STEP.KYC_STEP_MEDIA:
                this.loadUI_Media();
                break;
            case KYC_STEP.KYC_STEP_FINISH:
                this.uploadMyMsg();
                //防止 失败 设置成media 失败后继续 提交可以
                this.continueNum = KYC_STEP.KYC_STEP_MEDIA;
                break;
            case KYC_STEP.KYC_SUCCESS:
                KycMgr.instance.kyc_state = 2;//手动修改成审核中 防止刷新状态 慢重复弹出kyc
                KycMgr.instance.refresh_kycState();//刷新一下状态
                cc.director.emit("upload_KYC_success");//如果是从个人中心 进来的 刷新一下个人中心 状态
                //清除本地缓存信息
                Global.getInstance().setStoreageData(Global.GLOBAL_STORAGE_KEY.KYC,'');
                this.hide();//直接调用 真退出
                if(KycMgr.instance.in_game_type == INGSME_TYPE.Login){
                    cc.director.emit('ReturnToHome2');//通知大厅 开启直播流
                    KycMgr.instance.poping = false;
                    cc.director.emit('DestroyQueuePopup')
                }
                break;
            default:
                break;
        }
    }
    //上传我的个人信息
    uploadMyMsg(){
        let allJsonData = this.get_kycdata_uid()
        let base64Str = '';
        if(this.continueNum == KYC_STEP.KYC_STEP_SIMPLE){
            base64Str = this.allEditItem[2]?.getValue();
        }else{
            if(allJsonData.length > 13){base64Str = allJsonData[13]}  
        }
        if(base64Str == ''){
            Global.getInstance().showSimpleTip("The current network is abnormal, please try again later!");
            return;
        }
        
        //先 传图片
        HttpUtils.getInstance().post(3,0, this, "/common/api/img/upload", {
            token: Global.getInstance().token,
            font_side_url:base64Str,
        }, (response) => {
            if (response) {
                //上传证件成功 执行下一步
                this.uploadMyMsg_2();
            }
        }, (response) => {
            if (response && response.code) {
                if (response.code == 1) {
                    Global.getInstance().showSimpleTip(response.msg)
                } else {
                    let str = response.msg
                    Global.getInstance().showSimpleTip(str)
                }
            }
        });
    }
    uploadMyMsg_2(){
        let first_name = '';
        let middle_name = ''
        let last_name = '';
        let day = '';
        let month = '';
        let year = '';
        let is_goverment = '1';
        let place_brith = '';
        let branch = '';
        let nation = '';
        let c_address = '';
        let per_address = '';
        let work = ''
        let income = ''
        let id_type = ''
        let country = ''
        let acc_type = ''
        let acc_no = ''
        if(this.continueNum == KYC_STEP.KYC_STEP_SIMPLE){
            first_name = this.allEditItem[0]?.getValue();
            middle_name = this.allEditItem[4]?.getValue();//简化版 把中间名字也传一下服务器
            last_name = this.allEditItem[1]?.getValue();
            let datestr = this.allEditItem[3]?.getValue();//第三个是birthday
            let d_arr = datestr.split('-');
            year = d_arr[0];
            month = d_arr[1];
            day = d_arr[2];
        }else{
            let allJsonData = this.get_kycdata_uid()
            if(allJsonData.length != 16){
                //数据不完整 重新填写
                Global.getInstance().showSimpleTip("The current network is abnormal, please try again later!");
                return;
            }
            first_name = allJsonData[0];
            middle_name = allJsonData[1];
            last_name = allJsonData[2];

            let datestr = allJsonData[3]//第4个是birthday
            let d_arr = datestr.split('-');
            year = d_arr[0];
            month = d_arr[1];
            day = d_arr[2];
            is_goverment = '1';
            branch = allJsonData[4]
            nation = allJsonData[5]
            place_brith = allJsonData[6]
            c_address = allJsonData[7]
            per_address = allJsonData[8]
            work = allJsonData[9]
            income = allJsonData[10]
            country = allJsonData[11]
            id_type = allJsonData[12]
            acc_type = allJsonData[14]
            acc_no = allJsonData[15]
        }

        console.log('----这里开始上传--其他数据！');
        HttpUtils.getInstance().post(3, 0, this, "/common/api/sub/info", {
            token: Global.getInstance().token,
            first_name:first_name,
            middle_name:middle_name,
            last_name:last_name,
            day:day,
            month:month,
            year:year,
            is_no_goverment:is_goverment,
            place_of_birth:place_brith,
            branch:branch,
            nationality:nation,
            current_address:c_address,
            permanent_address:per_address,
            work:work,
            income:income,
            id_type:id_type,
            country:country,
            account_type:acc_type,
            account_no:acc_no,
        }, (response) => {
            if (response) {
                this.submitSuccess();
                Global.getInstance().setStoreageData("HAVE_SUBMIT_KYC"+Global.getInstance().userdata.user_id,1);
            }
        }, (response) => {
            // Global.getInstance().showSimpleTip("Upload Failed!Please reTry")
            if (response.msg) {
                let str = response.msg
                Global.getInstance().showSimpleTip(str)
            }
        });
    }
    protected update(dt: number): void {
        // console.log('currenty',this.currentY)
        // this.node.y = this.currentY;
    }
    //本地缓存 输入的数据 一步一步的缓存
    saveEditData(){
        let allJsonData = this.get_kycdata_uid()
        switch (this.continueNum) {
            case KYC_STEP.KYC_STEP_NAME:
                for (let index = 0; index < this.allEditItem.length; index++) {
                    const element = this.allEditItem[index];
                    allJsonData[index] = element?.getValue();
                }
                break;
            case KYC_STEP.KYC_STEP_ADDRESS:
                //如果 前6项丢失 初始化一下 理论上 不会丢失
                if(allJsonData.length == 0){
                    for (let index = 0; index < 6; index++) {
                        allJsonData[index] = ''
                    }
                }
                for (let index = 0; index < 5; index++) {
                    const element = this.allEditItem[index];
                    allJsonData[index + 6] = element?.getValue();
                }
                break;
            case KYC_STEP.KYC_STEP_PHOTO:
                //如果 前11项丢失 初始化一下 理论上 不会丢失
                if(allJsonData.length == 0){
                    for (let index = 0; index < 11; index++) {
                        allJsonData[index] = ''
                    }
                }
                for (let index = 0; index < 3; index++) {
                    const element = this.allEditItem[index];
                    allJsonData[index + 11] = element?.getValue();
                }
                break;
            case KYC_STEP.KYC_STEP_MEDIA:
                //如果 前14项丢失 初始化一下 理论上 不会丢失
                if(allJsonData.length == 0){
                    for (let index = 0; index < 14; index++) {
                        allJsonData[index] = ''
                    }
                }
                for (let index = 0; index < 2; index++) {
                    const element = this.allEditItem[index];
                    allJsonData[index + 14] = element?.getValue();
                }
                break;
            default:
                break;
        }
        // let saveData = JSON.stringify(allJsonData);
        this.set_kycdata_uid(allJsonData)
        // Global.getInstance().setStoreageData(Global.GLOBAL_STORAGE_KEY.KYC,saveData);
    }
    //加载下拉选择框 类型 item数组 标题 已经选择项
    responseSelectDown(kType:E_KYC_EDIT_TYPE,toggleItemsList=[],title = '') {
        this._kType = kType;
        let cb = ()=>{
            this.filterTimeResult[0] = toggleItemsList[0];
            let node = cc.instantiate(this.filterTimePrefab);
            node.getComponent(SingleToggle).setToggleItems(toggleItemsList,this.filterTimeResult,title);
            node.position = new cc.Vec3(0,-960);
            node.parent = this.node;
        }
        if (!this.filterTimePrefab) {
            uiManager.instance.loadPrefabByLoading(UI_PATH_DIC.SingleToggle,cc.Prefab,(finish, total, item) => {
            }, (err, prefab: any) => {
                    if (err) {
                        return;
                    }
                    this.filterTimePrefab = prefab;
                    cb();
            });
        } else {
            cb();
        }
    }

    //下拉框选择之后 逻辑
    filterTime(result) {
        this.filterTimeResult = result;
        let script;
        switch (this._kType) {
            case E_KYC_EDIT_TYPE.BRANCH:
                script = this.allEditItem[4];
                script?.setEditboxToText(result[0]);
                break;
            case E_KYC_EDIT_TYPE.NATIONALITY:
                script = this.allEditItem[5];
                script?.setEditboxToText(result[0]);
                break;
            case E_KYC_EDIT_TYPE.S_INCOME:
                script = this.allEditItem[4];
                script?.setEditboxToText(result[0]);
                break;
            case E_KYC_EDIT_TYPE.COUNTRY:
                script = this.allEditItem[0];
                script?.setEditboxToText(result[0]);
                break;
            case E_KYC_EDIT_TYPE.S_TYPE_ID:
                script = this.allEditItem[1];
                script?.setEditboxToText(result[0]);
                break;
            case E_KYC_EDIT_TYPE.S_MEDIA_TYPE:
                script = this.allEditItem[0];
                this.allEditItem[1].node.active = true;
                script?.setEditboxToText(result[0]);
                this.reset_radius_pos()
                break;
            default:
                break;
        }

        let self = this;
        setTimeout(() => {
            self.responseContinueColor()
        }, 100);
    }


    showShootAndUpload(isOpen = false){
        if(isOpen){
            if(this.shoot_upload_src == null || this.shoot_upload_src == undefined){
                let a_shootNode = cc.instantiate(this.shootPhotoPrefab);
                a_shootNode.parent = this.node;
                a_shootNode.zIndex = 9;
                a_shootNode.position = cc.v3(0,0,0)
                this.shoot_upload_src = a_shootNode.getComponent(ShootAndUpload);
                this.shoot_upload_src.initData(this);
                this.shoot_upload_src.openCamersa();
            }
        }else{
            if(this.shoot_upload_src){
                this.shoot_upload_src?.closeCamersa(true);
                this.shoot_upload_src?.node?.removeFromParent();
                this.shoot_upload_src = null;
            }
        }
    }
    //拍照完成
    shootFinish(base64Str:string){
        if(base64Str != ''){
            this.allEditItem[2].setImage(base64Str);
            let self = this;
            setTimeout(() => {
                self.responseContinueColor()
            }, 100);
        }
    }
    protected onDestroy(): void {
        cc.director.off("Single_Toggle_Filter_Result",this.filterTime,this);
    }
}
