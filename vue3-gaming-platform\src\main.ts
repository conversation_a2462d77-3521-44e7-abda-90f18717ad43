import { createApp } from 'vue'
import { createPinia } from 'pinia'
import router from './router'
import App from './App.vue'

// 样式导入
import './styles/main.scss'
import 'animate.css'

// 全局组件导入
import GlobalLoading from './components/Common/GlobalLoading.vue'
import GlobalToast from './components/Common/GlobalToast.vue'
import GlobalModal from './components/Common/GlobalModal.vue'
import NetworkStatus from './components/Common/NetworkStatus.vue'

// 工具函数导入
import { setupGlobalProperties } from './utils/globalProperties'
import { setupErrorHandler } from './utils/errorHandler'
import { setupPerformanceMonitor } from './utils/performance'

// 创建应用实例
const app = createApp(App)

// 创建Pinia实例
const pinia = createPinia()

// 注册全局组件
app.component('GlobalLoading', GlobalLoading)
app.component('GlobalToast', GlobalToast)
app.component('GlobalModal', GlobalModal)
app.component('NetworkStatus', NetworkStatus)

// 使用插件
app.use(pinia)
app.use(router)

// 设置全局属性
setupGlobalProperties(app)

// 设置错误处理
setupErrorHandler(app)

// 设置性能监控
if (import.meta.env.PROD) {
  setupPerformanceMonitor()
}

// 全局错误处理
app.config.errorHandler = (err, vm, info) => {
  console.error('Global error:', err, info)
  
  // 发送错误到监控系统
  if (import.meta.env.PROD) {
    // TODO: 集成错误监控服务
    // errorReporting.captureException(err, { extra: { info, vm } })
  }
}

// 全局警告处理
app.config.warnHandler = (msg, vm, trace) => {
  if (import.meta.env.DEV) {
    console.warn('Global warning:', msg, trace)
  }
}

// 性能配置
app.config.performance = import.meta.env.DEV

// 挂载应用
app.mount('#app')

// 开发环境下的调试工具
if (import.meta.env.DEV) {
  // 暴露应用实例到全局，方便调试
  window.__VUE_APP__ = app
  
  // 添加开发工具
  if (window.__VUE_DEVTOOLS_GLOBAL_HOOK__) {
    window.__VUE_DEVTOOLS_GLOBAL_HOOK__.Vue = app
  }
}

// 注册Service Worker（生产环境）
if (import.meta.env.PROD && 'serviceWorker' in navigator) {
  window.addEventListener('load', () => {
    navigator.serviceWorker.register('/sw.js')
      .then(registration => {
        console.log('SW registered: ', registration)
      })
      .catch(registrationError => {
        console.log('SW registration failed: ', registrationError)
      })
  })
}

// 应用初始化完成事件
app.config.globalProperties.$appReady = true
window.dispatchEvent(new CustomEvent('app:ready', { detail: { app } }))
