declare interface Date {
	/**
	 * @description 格式当前时间 
	 * @example 
	 * y : 年
	 * M ：月
	 * d : 日
	 * h : 时 
	 * m : 分
	 * s : 秒
	 * q : 季度
	 * S ：毫秒
	 * let now = new Date();
	 * let str = now.format("yyyy:MM:dd hh:mm:ss"); //2019:11:07 10:19:51
	 * str = now.format("yyyy/MM/dd");//2019/11/07
	 * str = now.format("hh:mm:ss");//10:19:51
	 * str = now.format("yyyy/MM/dd hh:mm:ss.SS 第qq季度");//2022/07/21 23:32:23.75 第03季度
	 * */
	format(format: string): string;
}

declare interface DateConstructor {
	/**
	 * @description 返回当前时间的秒数
	 * @example 
	 * Date.timeNow()
	 *  */
	timeNow(): number;
	/**
	 * @description 返回当前时间的毫秒数 
	 * @example 
	 * Date.timeNowMillisecons()
	 * */
	timeNowMillisecons(): number;
	/**
	 * @description 返回格式化后的时间
	 * @param format 
	 * @param date 如果不传入，则为当前时间
	 */
	format(format: string, date?: Date): string;
}

/**
 * @description 单例接口类
 */
declare interface ISingleton {
	/**@description 初始化 */
	init?(...args: any[]): any;
	/**@description 销毁(单例销毁时调用) */
	destroy?(...args: any[]): any;
	/**@description 清理数据 */
	clear?(...args: any[]): any;
	/**@description 是否常驻，即创建后不会删除 */
	isResident?: boolean;
	/**@description 不用自己设置，由单例管理器赋值 */
	module: string;
	/**输出调试信息 */
	debug?(...args: any[]): void;
}

declare function dispatch(name: string, ...args: any[]): void;