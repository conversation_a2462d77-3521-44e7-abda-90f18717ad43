{"__type__": "cc.AnimationClip", "_name": "roomlist", "_objFlags": 0, "_native": "", "_duration": 0.5, "sample": 60, "speed": 1, "wrapMode": 1, "curveData": {"paths": {"roombg": {"props": {"y": [{"frame": 0, "value": 1300}, {"frame": 0.3333333333333333, "value": 0}]}}, "lay/downNd": {"props": {"y": [{"frame": 0, "value": -790}, {"frame": 0.5, "value": -455}]}}, "lay/btnScroll": {"props": {"x": [{"frame": 0, "value": 2500}, {"frame": 0.16666666666666666, "value": 2500, "curve": [0.18, 0.89, 0.31, 1.21]}, {"frame": 0.5, "value": 0}]}}, "lay/GameListTab": {"props": {"opacity": [{"frame": 0, "value": 0}, {"frame": 0.05, "value": 255}]}}, "lay/lanbg": {"props": {"opacity": [{"frame": 0, "value": 0}, {"frame": 0.3333333333333333, "value": 255}]}}, "lay/title": {"props": {"opacity": [{"frame": 0, "value": 0}, {"frame": 0.3333333333333333, "value": 255}]}}}}, "events": []}