import UICommon from "../component/UICommon";
import { DEEP_INDEXZ, E_PAGE_TYPE, UI_PATH_DIC } from "../GlobalConstant";
import Global from "../GlobalScript";
import { AutoPopMgr } from "../mgr/AutoPopMgr";
import { uiManager } from "../mgr/UIManager";

const { ccclass, property } = cc._decorator;

export function showVIPTip() {
    let parent = Global.getInstance().popNode
    if (!parent) return
    let gameNd = parent.getChildByName("VipTip")
    if (gameNd) {
        return
    }
    uiManager.instance.loadPrefabByLoading(UI_PATH_DIC.VipTip,cc.Prefab, () => {
    }, (err, prefab: cc.Prefab) => {
        if (!prefab) return
        let gameNd = parent.getChildByName("VipTip")
        if (gameNd) {
            return
        }
        if (err) return
        if(Global.getInstance().getPageId() != E_PAGE_TYPE.HALL){
            AutoPopMgr.set_isshowing();//没弹出来后卡住问题
            return
        }//控制必须在hall 页面弹出
        let ndPop = cc.instantiate(prefab)
        ndPop.name = "VipTip"
        ndPop.zIndex = DEEP_INDEXZ.VIPTIPS;
        ndPop.parent = parent
    })
}

@ccclass
export default class VipTip extends UICommon {
    @property(cc.Label)
    toastText: cc.Label = null;

    @property(cc.Node)
    yesbtn: cc.Node = null;

    confirmfun = null;
    cancelfun = null;
    target = null;
    is_pop_goon = true;//是否继续弹出其它框


    onLoad(): void {
        cc.director.on('closeVipTip', this.closeDialog, this);
        if(Global.getInstance().userdata) {
            const currentDate = new Date();
            const dayOfMonth = currentDate.getDate();
            const isWithinFirstHalf = dayOfMonth >= 1 && dayOfMonth <= 15;
            const isWithinSecondHalf = dayOfMonth >= 16 && dayOfMonth <= 31;
            let isVip = parseInt(Global.getInstance().userdata?.is_vip) || 0;
            if(isVip && isWithinFirstHalf) {
                Global.getInstance().setStoreageData("HAS_SHOWN_UPGRADE_TIP"+Global.getInstance().userdata.user_id, true);//本地存储 是否弹出过vip升级提示
            } else if (isVip && isWithinSecondHalf) {
                Global.getInstance().setStoreageData("HAS_SHOWN_UPGRADE_TIP2"+Global.getInstance().userdata.user_id, true);//本地存储 是否弹出过vip升级提示
            }
        }
    }

    onDestroy(): void {
        cc.director.off('closeVipTip', this.closeDialog, this);
        if(this.is_pop_goon){
            cc.director.emit("DestroyQueuePopup");
        }
    }

    closeDialog() {
        
    }

    closeVipTip() {
        this.node.removeFromParent(true);
        this.node.destroy()
    }
    setInfo(word, confirmfun, cancelfun, target) {
        this.toastText.string = word;
        this.confirmfun = confirmfun;
        this.cancelfun = cancelfun;
        this.target = target;
    }

    setSys() {
        this.node.zIndex = 1000;
    }

    confirmClick() {
        if (this.confirmfun !== null && this.confirmfun !== undefined) {
            this.confirmfun.call(this.target, this.node.name);
        }

        if (!!this.node && this.node.isValid) {
            this.node.removeFromParent(true);
            this.node.destroy();
        }
    }

    cancelClick() {
        if (this.cancelfun !== null && this.cancelfun !== undefined) {
            this.cancelfun.call(this.target);
        }
        this.node.removeFromParent(true);
        this.node.destroy();
    }

    setOneBtn(word, target, confirmfun) {
        this.toastText.string = word;
        this.target = target;
        // this.yesbtn.setPosition(cc.v2(0,-130));
        // if(n_w != null)
        //     this.nolabel.string = n_w;
        if (confirmfun !== undefined) {
            this.confirmfun = confirmfun;
        }
    }

    onClickVipDetails() {
        this.is_pop_goon = false;//不再继续弹出 .
        this.node.destroy();
        Global.instance.hide_spin_btn();
        Global.instance.hideEnvelopeBtn();
        uiManager.instance.showDialog(UI_PATH_DIC.VipDetail,null,null,DEEP_INDEXZ.ACTIVITY_CONTENT);//VIP详情页
    }
}
