<template>
  <div class="game-list">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner"></div>
      <p class="loading-text">加载游戏中...</p>
    </div>

    <!-- 游戏网格 -->
    <div v-else-if="games.length > 0" class="games-grid">
      <div
        v-for="game in games"
        :key="game.id"
        class="game-item"
        :class="{ 
          maintenance: game.status === 2,
          hot: hasTag(game, 1),
          new: hasTag(game, 2)
        }"
        @click="handleGameClick(game)"
      >
        <!-- 游戏图标 -->
        <div class="game-icon-container">
          <img
            :src="game.icon_url"
            :alt="game.game_name"
            class="game-icon"
            @error="handleImageError"
          />
          
          <!-- 维护遮罩 -->
          <div v-if="game.status === 2" class="maintenance-overlay">
            <i class="icon-maintenance"></i>
            <span>维护中</span>
          </div>
          
          <!-- 游戏标签 -->
          <div class="game-tags">
            <span v-if="hasTag(game, 1)" class="tag hot-tag">HOT</span>
            <span v-if="hasTag(game, 2)" class="tag new-tag">NEW</span>
          </div>
          
          <!-- 收藏按钮 -->
          <button
            class="like-btn"
            :class="{ liked: isLiked(game.id) }"
            @click.stop="handleLikeClick(game.id)"
          >
            <i class="icon-heart"></i>
          </button>
        </div>

        <!-- 游戏信息 -->
        <div class="game-info">
          <h3 class="game-name">{{ game.game_name }}</h3>
          <p class="game-company">{{ getCompanyName(game.company_id) }}</p>
        </div>

        <!-- 动画效果 -->
        <div v-if="game.is_animation" class="animation-indicator">
          <i class="icon-play"></i>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <div class="empty-icon">
        <i class="icon-game-controller"></i>
      </div>
      <h3 class="empty-title">暂无游戏</h3>
      <p class="empty-description">当前分类下没有可用的游戏</p>
    </div>

    <!-- 加载更多 -->
    <div v-if="hasMore && !loading" class="load-more-container">
      <button class="load-more-btn" @click="loadMore">
        加载更多
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { HallGameIcon } from '@/types/hall'
import { GAME_TAGS } from '@/types/hall'

interface Props {
  games: HallGameIcon[]
  loading?: boolean
  hasMore?: boolean
}

interface Emits {
  (e: 'game-click', game: HallGameIcon): void
  (e: 'game-like', gameId: number): void
  (e: 'load-more'): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  hasMore: false
})

const emit = defineEmits<Emits>()

// 计算属性
const likedGames = computed(() => {
  const liked = localStorage.getItem('likedGames')
  return liked ? JSON.parse(liked) : []
})

// 方法
function handleGameClick(game: HallGameIcon) {
  emit('game-click', game)
}

function handleLikeClick(gameId: number) {
  emit('game-like', gameId)
}

function handleImageError(event: Event) {
  const img = event.target as HTMLImageElement
  img.src = '/images/game-placeholder.png' // 默认占位图
}

function hasTag(game: HallGameIcon, tagType: number): boolean {
  // 这里需要根据实际的标签数据结构来判断
  // 假设游戏对象有tags字段
  return (game as any).tags?.includes(tagType) || false
}

function isLiked(gameId: number): boolean {
  return likedGames.value.includes(gameId)
}

function getCompanyName(companyId: number): string {
  // 这里应该从公司数据映射中获取名称
  const companyMap: Record<number, string> = {
    1: 'Evolution',
    2: 'Pragmatic Play',
    3: 'NetEnt',
    4: 'Microgaming',
    5: 'Play\'n GO',
    // 添加更多公司映射
  }
  return companyMap[companyId] || '未知厂商'
}

function loadMore() {
  emit('load-more')
}
</script>

<style scoped>
.game-list {
  width: 100%;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: rgba(255, 255, 255, 0.8);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid #4CAF50;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin: 0;
  font-size: 16px;
}

.games-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
  gap: 16px;
  padding: 0;
}

.game-item {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  backdrop-filter: blur(10px);
  border: 2px solid transparent;
}

.game-item:hover {
  transform: translateY(-4px);
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.game-item.hot {
  border-color: #FF6B6B;
  box-shadow: 0 0 20px rgba(255, 107, 107, 0.3);
}

.game-item.new {
  border-color: #4CAF50;
  box-shadow: 0 0 20px rgba(76, 175, 80, 0.3);
}

.game-item.maintenance {
  opacity: 0.6;
  cursor: not-allowed;
}

.game-item.maintenance:hover {
  transform: none;
}

.game-icon-container {
  position: relative;
  width: 100%;
  aspect-ratio: 1;
  margin-bottom: 12px;
  border-radius: 8px;
  overflow: hidden;
}

.game-icon {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.game-item:hover .game-icon {
  transform: scale(1.05);
}

.maintenance-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  gap: 4px;
}

.game-tags {
  position: absolute;
  top: 8px;
  left: 8px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.tag {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: bold;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.hot-tag {
  background: linear-gradient(135deg, #FF6B6B, #FF8E53);
}

.new-tag {
  background: linear-gradient(135deg, #4CAF50, #45A049);
}

.like-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 28px;
  height: 28px;
  border: none;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.5);
  color: white;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.like-btn:hover {
  background: rgba(0, 0, 0, 0.7);
  transform: scale(1.1);
}

.like-btn.liked {
  background: #FF6B6B;
  color: white;
}

.game-info {
  text-align: center;
  color: white;
}

.game-name {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: bold;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.game-company {
  margin: 0;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.animation-indicator {
  position: absolute;
  bottom: 8px;
  right: 8px;
  width: 20px;
  height: 20px;
  background: rgba(76, 175, 80, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 10px;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-title {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: bold;
}

.empty-description {
  margin: 0;
  font-size: 14px;
  opacity: 0.7;
}

.load-more-container {
  display: flex;
  justify-content: center;
  padding: 20px;
}

.load-more-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.load-more-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .games-grid {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 12px;
  }
  
  .game-item {
    padding: 8px;
  }
  
  .game-name {
    font-size: 12px;
  }
  
  .game-company {
    font-size: 10px;
  }
}

@media (max-width: 480px) {
  .games-grid {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 8px;
  }
}
</style>
