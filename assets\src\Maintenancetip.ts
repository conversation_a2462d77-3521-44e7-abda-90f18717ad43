import { ALL_APP_SOURCE_CONFIG } from "./Config";
import { DEEP_INDEXZ, E_CHANEL_TYPE, MAINTENANCE_GAME, MAINTENANCETIPCODE, UI_PATH_DIC } from "./GlobalConstant";
import Global from "./GlobalScript";
import UICommon from "./component/UICommon";
import { uiManager } from "./mgr/UIManager";
import { SERVICE_TYPE, serviceMgr } from "./mgr/serviceMgr";
import utils from "./utils/utils";

const { ccclass, property } = cc._decorator;

//添加默认全局维护
export function showMaintenancetip(word: string,err_code = MAINTENANCETIPCODE) {
    console.log('-----------err_code:',err_code)
    // let errcode = err_code
    // if(!err_code)errcode = MAINTENANCETIPCODE
    uiManager.instance.showDialog(UI_PATH_DIC.Maintenancetip, [{ word: word ,err_code:err_code}], null, DEEP_INDEXZ.MAX)
}

const HEIGHTOFFSET = 230
const HIEGTEST = 980
@ccclass
export default class Maintenancetip extends UICommon {
    @property(cc.Label)
    word: cc.Label = null;

    @property(cc.Node)
    yesbtn: cc.Node = null;

    @property(cc.Node)
    ndBg: cc.Node = null

    //全部维护 node
    @property(cc.Node)
    mainten_all: cc.Node = null

    beginHeight = 0

    onLoad(): void {
        this.setSys()
        this.beginHeight = this.ndBg.height
    }

    onDestroy(): void {

    }

    closeDialog() {
        this.closeMaintenancetip()
    }

    closeMaintenancetip() {
        this.node.removeFromParent(true);
        this.node.destroy()
    }

    init(data) {
        if (!data) return
        this.mainten_all.active = false;//先隐藏维护全部node
        this.setInfo(data.word,data.err_code)
        if (this.word.node.height > HEIGHTOFFSET) {
            let result = this.beginHeight + (this.word.node.height - HEIGHTOFFSET)
            if (result > HIEGTEST) {
                result = HIEGTEST
            }
            this.ndBg.height = result
        }
    }

    setInfo(word: string,err_code:number) {
        //如果是全局维护 执行这里
        if(err_code == MAINTENANCETIPCODE){
            this.mainten_all.active = true;
            let conent = this.mainten_all.getChildByName('content').getComponent(cc.Label)
            conent.string = word;
            return;
        }
        this.word.string = word;
        let title = this.node.getComponentsInChildren(cc.Label)
        if(err_code == MAINTENANCE_GAME){
            title[1].string = 'Maintenance'
        }else{
            title[1].string = 'Tips'
        }
        this.word._forceUpdateRenderData(true)
    }

    setSys() {
        this.node.zIndex = 1299;
    }

    confirmClick() {
        if (Global.getInstance().token && Global.getInstance().userdata) {
            Global.getInstance().logout(true)
        } else {
            cc.director.emit("AutoLoginEvent")
        }
        if (cc.isValid(this.node)) {
            this.node.removeFromParent(true);
            this.node.destroy();
        }
        if (ALL_APP_SOURCE_CONFIG.channel == E_CHANEL_TYPE.G_CASH) {
            if(this.word.string.indexOf('please log in again') == -1){
                //直接关闭这个页面
                window.close();
            }else{
                //跳转gcash 小程序
                // window.open(Global.getInstance().gcashShopUrl, "_blank");
                utils.openUrl(Global.getInstance().gcashShopUrl);
                setTimeout(() => {
                    window.close();
                }, 1000);
            }
            return;
        }
    }

    cancelClick() {
        this.node.removeFromParent(true);
        this.node.destroy();
    }

    //弹出客服页面
    show_service(){
        serviceMgr.instance.show_achat(SERVICE_TYPE.coustom);
    }
}
