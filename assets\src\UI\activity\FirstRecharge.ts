import { UI_PATH_DIC } from "../../GlobalConstant";
import Global from "../../GlobalScript";
import UICommon from "../../component/UICommon";
import { IActivityJump } from "../../hall/ActivityMgr";
import { uiManager } from "../../mgr/UIManager";
import { util } from "../../net/proto/protobuf";
import utils from "../../utils/utils";

const { ccclass, property } = cc._decorator;

export function showFirstRecharge(firstRechargeActId?: number) {
    if (!firstRechargeActId) firstRechargeActId = Global.getInstance().firstRechargeActId
    uiManager.instance.showDialog(UI_PATH_DIC.ActivityFirstRecharge, [{ related_activity_id: firstRechargeActId }])//未用到
}

function saveKeyStr(times: number) {
    let timestampStr = Global.getInstance().now() + "|" + times
    let userId = Global.getInstance().userdata.user_id
    Global.getInstance().setStoreageData(Global.GLOBAL_STORAGE_KEY.FIRSTRECHARGE + userId, timestampStr)
}

export function AutoShowFirstRecharge() {
    if (!Global.getInstance().userdata) return
    if (!Global.getInstance().is_first_charge) return
    let n = Global.getInstance().popNode;
    if (n.childrenCount > 0) {
        for (let index = 0; index < n.children.length; index++) {
            const node = n.children[index];
            if (node.name == "MoreGameList" || node.name == "CasinoGameList") {
                continue
            }
            if (node.active) return
        }
    }
    let userId = Global.getInstance().userdata.user_id
    let v = Global.getInstance().getStoreageData(Global.GLOBAL_STORAGE_KEY.FIRSTRECHARGE + userId, "")
    if (v.length > 0) {
        let arrData = v.split("|")
        let timestamp = Number(arrData[0])
        let times = Number(arrData[1])
        if (utils.isToday(timestamp)) {
            if (times < 3) {
                showFirstRecharge()
                times = times + 1
                saveKeyStr(times)
            }
        } else {
            times = 1
            saveKeyStr(times)
            showFirstRecharge()
        }
    } else {
        let times = 1
        saveKeyStr(times)
        showFirstRecharge()
    }
}


@ccclass
export default class FirstRecharge extends UICommon implements IActivityJump {

    @property(cc.Label)
    lbMoney: cc.Label = null

    lbCount: cc.Label = null;
    activityId: number;

    init(actData?) {
        this.node.zIndex = 1001
        this.activityId = actData.related_activity_id
        this.lbMoney.string = Global.getInstance().getCountryCurrency() + Global.getInstance().config.first_recharge_bonus

    }

    jumpToActivityDetail() {
        this.hide(true)
        cc.director.emit("activityJump", { actId: this.activityId || 1 });
    }

    onClickDetail() {
        cc.director.emit("closeMoreGameListDialog")
        this.jumpToActivityDetail()
    }

    closeAction() {
        this.hide(true);
    }
}
