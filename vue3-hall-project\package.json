{"name": "vue3-hall-project", "version": "1.0.0", "description": "Vue3 Hall Module Project - Converted from Cocos Creator TypeScript", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "type-check": "vue-tsc --noEmit", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.5", "pinia": "^2.1.7", "axios": "^1.6.0", "@vueuse/core": "^10.5.0", "swiper": "^11.0.0", "animate.css": "^4.1.1"}, "devDependencies": {"@types/node": "^20.8.10", "@vitejs/plugin-vue": "^4.4.0", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.4.0", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "prettier": "^3.0.3", "typescript": "~5.2.0", "vite": "^4.4.11", "vue-tsc": "^1.8.19"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["vue3", "typescript", "hall", "gaming", "casino", "pinia", "vite"], "author": "Development Team", "license": "MIT"}