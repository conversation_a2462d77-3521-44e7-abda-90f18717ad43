import { ALL_APP_SOURCE_CONFIG } from "../Config";
import { E_GAME_TYPE, E_THIRD_PROVIDER, GOLD_RATIO } from "../GlobalConstant";
import Global from "../GlobalScript";
import { showMaintenancetip } from "../Maintenancetip";
import { showMoreGame } from "../MoreGame";
import MoreGameManager from "../MoreGameManager";
import Resource = require("../Resource");
import RoundRectMask from "../RoundRectMask";
import UICommon from "../component/UICommon";
import HttpUtils from "../net/HttpUtils";
import utils from "../utils/utils";

const {ccclass, property} = cc._decorator;

@ccclass
export default class BetDetail extends UICommon {

    @property(cc.Sprite)
    gameImg: cc.Sprite = null;

    @property(cc.Node)
    orderSettledType: cc.Node = null;

    @property(cc.Node)
    orderUnSettledType: cc.Node = null;

    @property(cc.Node)
    orderCancelledType: cc.Node = null;

    @property(cc.Node)
    orderPromosType: cc.Node = null;

    @property(cc.Label)
    labGameName: cc.Label = null;

    @property(cc.Label)
    labBetId: cc.Label = null;

    @property(cc.Label)
    labProvider: cc.Label = null;

    @property(cc.Label)
    labGameType: cc.Label = null;

    @property(cc.Label)
    labBetNum: cc.Label = null;

    @property(cc.Label)
    labValidBetNum: cc.Label = null;

    @property(cc.Label)
    labBetTime: cc.Label = null;

    @property(cc.Label)
    labSettleTime: cc.Label = null;

    @property(cc.Label)
    labPayOutTitle: cc.Label = null;

    @property(cc.Label)
    labPayOut: cc.Label = null;

    @property(cc.Sprite)
    spIcon: cc.Sprite = null;

    @property(cc.ScrollView)
    detailsScroll:cc.ScrollView = null;

    @property(cc.Node)
    mainNode: cc.Node = null;

    @property(cc.Node)
    betIDNode: cc.Node = null;

    @property(cc.Node)
    providerNode: cc.Node = null;

    @property(cc.Node)
    gameTypeNode: cc.Node = null;

    @property(cc.Node)
    betNode: cc.Node = null;

    @property(cc.Node)
    validBetNode: cc.Node = null;

    @property(cc.Node)
    betTimeNode: cc.Node = null;

    @property(cc.Node)
    settlementTimeNode: cc.Node = null;

    @property(cc.Node)
    payoutNode: cc.Node = null;

    @property(cc.Node)
    leagueNameNode: cc.Node = null;

    @property(cc.Node)
    eventNameNode: cc.Node = null;

    @property(cc.Node)
    playAgainBtnNode: cc.Node = null;

    @property([cc.SpriteFrame])
    providerFreams: cc.SpriteFrame[] = [];

    private filterTime = null;
    private orderData = null;
    private gameInfo = null;

    ICON_STATUS = {
        NORMAL :1,
        MAINTENANCE:2
    }

    onLoad () {
       
    }

    initData(info, filtertime) {
        this.orderData = info;
        this.filterTime = filtertime;
        this.labGameName.string = info.game_name;
        if(info.status == "promos"){
            this.labGameName.string = info.provider_name + " Promos";
            this.showProviderIcon(info);
        }

        let layout = utils.getChildByPath(this.detailsScroll.node, "view.content.layout");
        let leaguename = utils.getChildByPath(this.detailsScroll.node, "view.content.layout.masknode.bg.league_name");
        let lab_leaguename = utils.getChildByPath(this.detailsScroll.node, "view.content.layout.masknode.bg.league_name.lab_leaguename");
        let eventname = utils.getChildByPath(this.detailsScroll.node, "view.content.layout.masknode.bg.event_name");
        let lab_eventname = utils.getChildByPath(this.detailsScroll.node, "view.content.layout.masknode.bg.event_name.lab_eventname");
        //Fiee - SPORTS类型做下区分
        if(info.game_type == "SPORTS" && info.event_name != ""){
            this.labGameName.string = info.event_name;
            if(leaguename) leaguename.active = true;
            if(eventname) eventname.active = true;
            if(lab_leaguename && info.league_name) lab_leaguename.getComponent(cc.Label).string = info.league_name;
            if(lab_eventname && info.event_name) lab_eventname.getComponent(cc.Label).string = info.event_name; 
        } else {
            if(leaguename) leaguename.active = false;
            if(eventname) eventname.active = false;
            layout.getChildByName("masknode").getComponent(RoundRectMask).radius = 58;
        }
        let jackpot_contribution = utils.getChildByPath(this.detailsScroll.content, "layout.masknode.bg.jackpot_contribution");
        let lab_jackpot_contribution = utils.getChildByPath(this.detailsScroll.content, "layout.masknode.bg.jackpot_contribution.label");
        let jackpot_payout = utils.getChildByPath(this.detailsScroll.content, "layout.masknode.bg.jackpot_payout");
        let lab_jackpot_payout = utils.getChildByPath(this.detailsScroll.content, "layout.masknode.bg.jackpot_payout.label");
        //如果 jackpot_contribution存在 并且不为空
        if(info?.jackpot_contribution && (info?.jackpot_contribution > 0 || info?.jackpot_payout > 0)){
            lab_jackpot_contribution.getComponent(cc.Label).string = utils.formatNumberWithCommas(info.jackpot_contribution / GOLD_RATIO);
            lab_jackpot_payout.getComponent(cc.Label).string = utils.formatNumberWithCommas(info.jackpot_payout / GOLD_RATIO);
        }else{
            if(jackpot_contribution) jackpot_contribution.active = false;
            if(jackpot_payout) jackpot_payout.active = false;
        }

        this.labBetId.string = info.id;
        this.labProvider.string = info.provider_name;
        this.labGameType.string = info.game_type;
        this.labBetNum.string = utils.formatNumberWithCommas(info.bet / GOLD_RATIO);
        this.labValidBetNum.string = utils.formatNumberWithCommas(parseFloat(info.effective_bet) / GOLD_RATIO);
        this.labBetTime.string = info.third_create_time;
        this.labSettleTime.string = info.third_updated_time;
        if (info.status == 1) {
            this.orderType(false, true, false, false);
            this.labPayOutTitle.string = "Payout";
            this.labPayOut.string = "--";
            this.labSettleTime.string = "--";
        } else if (info.status == 2) {
            this.orderType(false, false, true, false);
            this.labPayOutTitle.string = "Refund";
            this.labPayOut.string = utils.formatNumberWithCommas(info.refund / GOLD_RATIO);
        } else if (info.status == 3) {
            this.orderType(true, false, false, false);
            this.labPayOutTitle.string = "Payout";
            this.labPayOut.string = utils.formatNumberWithCommas(info.payouts / GOLD_RATIO);
        } else if (info.status == "promos"){
            this.orderType(false, false, false, true);
            this.labPayOutTitle.string = "WIN";
            this.labPayOut.string = utils.formatNumberWithCommas(info.win_lose / GOLD_RATIO);
            this.providerNode.active = false;
            this.gameTypeNode.active = false;
            this.betNode.active = false;
            this.validBetNode.active = false;
            this.betTimeNode.active = false;
        }
        
        this.getGameInfo(info);
    }

    loadGameIcon(element) {
        if (!cc.isValid(this.node)) return
        // let localFilePath = "GameID_" + element.third_party_game_id + "_EN_" + element.company_id
        // Resource.loadSpriteFrame("MoreGameIcons/more", localFilePath, (spriteFrame: cc.SpriteFrame) => {
        //     if (!cc.isValid(this.spIcon)) return
        //     let localFilePathTemp = "GameID_" + element.third_party_game_id + "_EN_" + element.company_id
        //     if (localFilePath != localFilePathTemp) {
        //         return
        //     }
        //     if (spriteFrame && cc.isValid(this.spIcon)) {
        //         this.spIcon.spriteFrame = spriteFrame
        //     } else {
        //         if (cc.isValid(this.node) && element) {
        //             Global.getInstance().loadImgFromUrl(element.images, this.spIcon)
        //         }
        //     }
        // })
        //直接下载
        if (cc.isValid(this.node) && element) {
            Global.getInstance().download_img(element.images, this.spIcon)
        }
    }
    //从本地动态读取 游戏厂商icon
    load_provider_icon(sprite_cs,csid = 'no',icon = ''){
        let src_pre = 'MoreGameIcons/providers'
        let localFilePath = 'provider_'+csid
        //优先加载本地的,本地没有从网络下载
        Resource.loadSpriteFrame(src_pre, localFilePath, (spriteFrame: cc.SpriteFrame) => {
            if (!cc.isValid(sprite_cs)) return
            if (spriteFrame) {
                sprite_cs.spriteFrame = spriteFrame
            }
            let image = icon;
            if (image != "") {
                if (!/^http/.test(image)) {
                    image = ALL_APP_SOURCE_CONFIG.remote_assets_prod[Global.DEBUG]+image;
                }
                Global.getInstance().download_img(image,sprite_cs,(isture)=>{
                    // this.reset_roundrecd(sprite_cs.node)
                })
            }
        })            
    }
    showProviderIcon(info){
        //promos页全部去掉play again
        this.playAgainBtnNode.active = false;
        Global.getInstance().getGameProvider((provider:any)=>{
            let providerItemsList = provider;
            let hasit = false;
            for (let index = 0; index < providerItemsList.length; index++) {
                let element = providerItemsList[index];
                // {"id": 3,"provider": "Evo","short_name": "","sort": 0,"icon_home": "images\/daef73b18c6e52061a2c614ee0a3f9fb.png",
                //     "icon_other": "images\/63d11ff4929c045a20228906748c0caa.png","game_type": ["10001","10004","10006"],"status": 1,"content": ""
                if(element.id == info.provider_id){
                    hasit = true;
                    this.load_provider_icon(this.spIcon,element.id,element.icon_other);
                }
            }
            if(!hasit){
                this.load_provider_icon(this.spIcon,info.provider_id,'');
            }
        });
    }

    orderType(s:boolean, u:boolean, c:boolean, p:boolean) {
        this.orderSettledType.active = s;
        this.orderUnSettledType.active = u;
        this.orderCancelledType.active = c;
        this.orderPromosType.active = p;
    }

    onClickCopyID() {
        Global.getInstance().setPasteboard(this.orderData.id.toString());
        Global.getInstance().showSimpleTip("Copied!");
    }

    onClickPlayAgain() {
        if (!cc.isValid(this.node)) return
        if (this.gameInfo.id != "") {
            showMoreGame(this.gameInfo.third_party_game_id, this.gameInfo.company_id, this.gameInfo.is_jump, this.gameInfo.id);
        } else {
            showMoreGame(this.gameInfo.third_party_game_id, this.gameInfo.company_id, this.gameInfo.is_jump);
        }
    }

    start () {
        let page = this.node.getChildByName("page").getComponent(cc.PageView);
        page.node.on('page-turning', ()=>{
            let curIndex = page.getCurrentPageIndex();
            if (curIndex != 1) {
                this.node.destroy();
            }
        }, this);
        let tempnode = this.node.getChildByName("temp");
        page.insertPage(cc.instantiate(tempnode),0);
        page.scrollToPage(1,0.01);
    }

    getGameInfo(info) {
        let params = {
            token: Global.getInstance().token,
            id: info.game_id
        };
        let btnNode = this.mainNode.getChildByName("layout").getChildByName("btn_playagain");
        let btn = btnNode.getComponent(cc.Button)
        btnNode.active = false;
        this.playAgainBtnNode.active = false;
        HttpUtils.getInstance().post(1, 3, this, "/vdr/api/get/third/party/game/list", params,
            (response) => {
                if (!this.node || !this.node.isValid) {
                    return;
                }
                //只有在正常的情况下 才显示
                if(info.status != "promos"){
                    if(Global.getInstance().game_is_normal(info.game_id)){
                        btnNode.active = true;
                        this.playAgainBtnNode.active = true;
                    }else{
                        //适配一下detail scorll
                        this.detailsScroll.getComponent(cc.Widget).bottom = 0;
                        this.detailsScroll.getComponent(cc.Widget).updateAlignment();
                        this.detailsScroll.node.getChildByName('view').height = this.detailsScroll.node.getChildByName('view').height + 225;
                        this.detailsScroll.content.height = this.detailsScroll.content.height + 225;
                    }
                }
                if (response.data && response.data[0]) {
                    this.gameInfo = response.data[0];
                    if(info.status != "promos"){
                        this.loadGameIcon(response.data[0]);
                    }
                }
                if (!response.data || !response.data[0] || response.data[0].length <= 0) {
                    btnNode.opacity = 120;
                    btn.enabled = false;
                }
            }, (res) => {
                if (res && res.msg) {
                    Global.getInstance().showSimpleTip(res.msg);
                }
            });
    }

    // update (dt) {}
}
