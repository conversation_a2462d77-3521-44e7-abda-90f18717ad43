import AudioManager from "./AudioManager";
import { CLIENTLOGKEY, ClientLogManager } from "./ClientLogConfis";
import { ALL_APP_SOURCE_CONFIG } from "./Config";
import GameControl from "./GameControl";
import { DEEP_INDEXZ, E_CHANEL_TYPE, MAINTENANCETIPCODE, METHODS, UI_PATH_DIC } from "./GlobalConstant";
import Global from "./GlobalScript";
import { showMaintenancetip } from "./Maintenancetip";
import MoreGameManager from "./MoreGameManager";
import RoundRectMask from "./RoundRectMask";
import UICommon from "./component/UICommon";
import { uiManager } from "./mgr/UIManager";
import HttpUtils from "./net/HttpUtils";
import utils from "./utils/utils";

const { ccclass, property } = cc._decorator;

enum AMOUNTBG_TYPE {
    MIN = 210,
    MIDDLE = 385,
    MAX = 580
}

enum PAYOUT_STATUS {
    PENDING = 1,
    SUCCESS = 2,
    FAIL = 3
}

const PAY_METHOD = {
    MAYA_WEB: "9",
    GCASH_WEB: "10"
}

@ccclass
export default class Deposit extends UICommon {

    //是否连续充值的按钮
    @property(cc.Node)
    check_icon: cc.Node = null;

    @property(cc.Node)
    paymentNode: cc.Node = null;

    @property(cc.Node)
    pay_web: cc.Node = null;

    @property(cc.Node)
    item_paymethod: cc.Node = null;

    @property(cc.Node)
    amountNode: cc.Node = null;

    @property(cc.Node)
    btn_pull: cc.Node = null;


    @property(cc.EditBox)
    amount_edit: cc.EditBox = null;

    @property(cc.Node)
    payAmount: cc.Node[] = [];

    //上面最少充值提示node
    @property(cc.Node)
    mini_tips: cc.Node = null;

    @property(cc.Label)
    minimumTip: cc.Label = null;

    @property(cc.Label)
    maximumTip: cc.Label = null;

    @property(cc.Label)
    insufficientTip: cc.Label = null;

    @property(cc.Button)
    continueBtn: cc.Button = null;

    //lemoon 修复不能上下滑动的问题 
    @property(cc.Node)
    amountBgNode_new: cc.Node = null;


    //payitem
    @property(cc.Node)
    payitem_prefab: cc.Node = null;

    @property(cc.EditBox)
    inputTextNode: cc.EditBox = null;

    @property(cc.Label)
    totalDepositNum: cc.Label = null;

    @property(cc.Label)
    bonusNum: cc.Label = null;

    @property([cc.SpriteFrame])
    btnSpriteFrame: cc.SpriteFrame[] = []

    @property(cc.Button)
    btnAmount: cc.Button = null;

    @property(cc.Node)
    firstdeposit_img:  cc.Node = null;
    //用于上下滑动 判断高度
    @property(cc.Node)
    mainNode: cc.Node = null

    //首次 充值的 view
    @property(cc.Node)
    total_view: cc.Node = null;

    //普通充值的 钱包余额
    @property(cc.Node)
    wallet_view: cc.Node = null;

    //钱包余额显示
    @property(cc.Label)
    wallet_label: cc.Label = null;

    @property([cc.SpriteFrame])
    itempay_select: cc.SpriteFrame[] = [];

    @property([cc.SpriteFrame])
    itempay_unselect: cc.SpriteFrame[] = [];

    private uiBox = null;
    private labNum = null;
    private _curMethod: string = '';
    private _methodType: string = '';
    firstDepositAwradList = null;
    isFirstDeposit = false;

    _amount: string = "";

    rechargeID = 0;
    awardNum = 0;

    rechargeData = null;
    //payment method
    paynameData = null;
    payiconData = null;

    //amountConfig
    amountConfig = null;

    //itemPayList
    itemPayList = [];
    //payMthod Index
    payMethodIndex = 0;
    //lastSelectIndex
    lastSelectIndex = null;
    curProductId: any = "";
    //payM
    payM = null;
    lastReqTime = 0;
    isReqDepositing = false;

    //标记打开界面的类型(为了区分从大厅/account入口打开Deposit)
    openType = null;
    request_times = 0;//获取充值 配置信息次数
    depositTimeout:any = null;
    onLoad() {
        this.minimumTip.node.active = false;
        this.maximumTip.node.active = false;
        this.insufficientTip.node.active = false;
        this.continueBtn.enabled = false;
        this.continueBtn.node.opacity = 128;
        if (ALL_APP_SOURCE_CONFIG.channel == E_CHANEL_TYPE.WEB) {
            this.loadAmountConfig();
        } else if (ALL_APP_SOURCE_CONFIG.channel == E_CHANEL_TYPE.MAYA) {
            this.initFistDeposit(()=>{
                this.loadAmountConfig();
            });
        } else if (ALL_APP_SOURCE_CONFIG.channel == E_CHANEL_TYPE.G_CASH) {
            this.initFistDeposit(()=>{
                this.loadAmountConfig();
            });
        } 
        
        // let firstdeposit_img = this.node.getChildByName("firstdeposit_img");
        // let payment = this.node.getChildByName("payment");
        // let AmountBg = this.node.getChildByName("AmountBg");
        // let total_view = this.node.getChildByName("total_view");
        this.firstdeposit_img.active = false;
        this.total_view.active = false;
        //this.paymentNode.y = cc.winSize.height/2-200-15;
        //this.amountNode.y = cc.winSize.height/2-500-30;

        cc.game.on("screenResize", () => {
            this.resize()
        })
        //WEB端区分
        this.pay_web.active = false;
        if (ALL_APP_SOURCE_CONFIG.channel == E_CHANEL_TYPE.WEB) {
            this.pay_web.active = true;
        }
        this.paymentNode.active = false;
        //先禁用 有拉到数据再启用
        this.amount_edit.enabled = false;
        this.btnAmount.node.active = true;
        this.amountBgNode_new.active = false;
    }
    protected onEnable(): void {
        cc.director.on("purchase_suc", this.purchaseSuc, this);
    }
    protected onDisable(): void {
        cc.director.off("purchase_suc", this.purchaseSuc, this);
    }
    //点击连续充值按钮
    continuous_buy(){
        this.check_icon.active = !this.check_icon.active
    }
    //购买成功 通知
    purchaseSuc(code: number, token: string) {
        let self = this;
        this.scheduleOnce(() => {
            if (code != 0 || !this.curProductId || !token) {
                return Global.getInstance().showSimpleTip(Global.getInstance().getLabel("tipword91"));
            }
            let url = "/api/pay-service/verify-pay"
            // if (self.payM == PAY_METHOD.GCASH_WEB) {
            //     url = '/api/go/gcash/notify/web/payment'
            // }
            HttpUtils.getInstance().post(3, 3, this, url, {
                token: Global.getInstance().token,
                purchaseToken: token,
                purchaseState: code,
                productId: this.curProductId,
                packageName: Global.getInstance().getAppBundleId()
            }, (response) => {
                if (response) {
                    Global.getInstance().showSimpleTip(Global.getInstance().getLabel("tipword90"));

                    HttpUtils.getInstance().post(3, 3, this, "/api/global-config/recharge", { token: Global.getInstance().token }, (response) => {
                        Global.getInstance().rechargeConfig = response.data;
                        this.init({ type: 0 })
                    });
                    if (Global.getInstance().is_first_charge) Global.getInstance().logNewEvent("first_buy_coins", { CUID: Global.getInstance().userdata.user_id }, 1)
                    Global.getInstance().is_first_charge = false;
                }
            });
        }, 0.3)
    }
    init(args){
        if(args) this.openType = args;
        if(args.type){
            this.openType = args.type;
        }
        if(args.num){
            this.amount_edit.string = args.num;
            if(parseFloat(args.num) > 0){
                this.continueBtn.enabled = true;
                this.continueBtn.node.opacity = 255;
                this._amount = args.num;//修复webpay回来 参数带不上第一次
            }
        }
    }

    resize() {
        if (!this.node || !cc.isValid(this.node)) return
        // let firstdeposit_img = this.node.getChildByName("firstdeposit_img");
        // let payment = this.node.getChildByName("payment");
        // let AmountBg = this.node.getChildByName("AmountBg");
        // let total_view = this.node.getChildByName("total_view");
        if (!this.isFirstDeposit) {
            this.firstdeposit_img.active = false;
            this.total_view.active = false;
            // this.paymentNode.y = cc.winSize.height/2-200-10;
            // this.amountNode.y = cc.winSize.height/2-500-20;
        } else {
            this.firstdeposit_img.active = true;
            this.total_view.active = true;
            // this.firstdeposit_img.y = cc.winSize.height/2-200-10;
            // this.paymentNode.y = cc.winSize.height/2-450-20;
            // this.amountNode.y = cc.winSize.height/2-750-30;
        }
    }

    start() {
        let phoneNum = "0"+Global.getInstance().userdata.phone;
        //this.mayaAccount.string = this.formatPhoneNumber(phoneNum);
        //this.gcashAccount.string = this.formatPhoneNumber(phoneNum);
        // this.mainNode.height = 300;
        //改成上下滑动 删除页面
        let page = this.node.getChildByName("page").getComponent(cc.PageView);
        page.node.on(cc.Node.EventType.TOUCH_END,this.slideClose,this);
        //隐藏首存红包按钮
        Global.getInstance().hideEnvelopeBtn();

        //钱包显示
        this.wallet_label.string = utils.formatNumberWithCommas(Global.getInstance().balanceTotal);
    }
    clickCloseBtn() {
        cc.tween(this.mainNode).call(()=>{
            this.mask.active = false;
        }).
        to(0.2,{position:new cc.Vec3(0,-2880)}).call(()=>{
            this.node.destroy();
        }).start();
    }
    slideClose(event) {
        if (event.touch._startPoint.y > this.mainNode.height) {
            this.clickCloseBtn();
        } else {
            let deltaY = event.touch._startPoint.y-event.touch._point.y;
            if (deltaY > this.mainNode.height/3) {
                this.clickCloseBtn();
            }
        }
    }
    initFistDeposit(cb) {
        HttpUtils.getInstance().post(1, 3, this, "/common/api/global-config/first/recharge/rule", {
            token: Global.getInstance().token,
        }, (response) => {
            if (!this.node || !this.node.isValid) {
                return;
            }
            if (response.data) {
                this.isFirstDeposit = response.data.is_first_charge;
                if (response.data.recharge && response.data.recharge.length > 0) {
                    let rechargeList = response.data.recharge;
                    rechargeList.sort((a,b)=>{return a.id - b.id});
                    this.firstDepositAwradList = rechargeList;
                }
                if (!this.firstDepositAwradList || this.firstDepositAwradList.length == 0) {
                    this.isFirstDeposit = false;
                }
                // let firstdeposit_img = this.node.getChildByName("firstdeposit_img");
                // let payment = this.node.getChildByName("payment");
                // let AmountBg = this.node.getChildByName("AmountBg");
                // let total_view = this.node.getChildByName("total_view");
                if (!this.isFirstDeposit) {
                    this.firstdeposit_img.active = false;
                    this.total_view.active = false;
                    this.amountBgNode_new.active = false;
                    // this.paymentNode.y = cc.winSize.height/2-200-10;
                    // this.amountNode.y = cc.winSize.height/2-500-20;
                } else {
                    if (ALL_APP_SOURCE_CONFIG.channel !== E_CHANEL_TYPE.WEB) {
                        this.firstdeposit_img.active = true;
                        this.total_view.active = true;
                        // this.firstdeposit_img.y = cc.winSize.height/2-200-10;
                        // this.paymentNode.y = cc.winSize.height/2-454-20;
                        // this.amountNode.y = cc.winSize.height/2-754-30;
                        // this.total_view.y = cc.winSize.height/2-1488-40;
                        this.firstdeposit_img.y = 0;
                        this.paymentNode.y = this.firstdeposit_img.y - this.firstdeposit_img.height - 20;
                        // this.amountNode.y = this.paymentNode.y - this.paymentNode.height - 20;
                    }
                    this.amountBgNode_new.active = false;
                }
                
                if (cb) {
                    cb();
                }
            }
        });
    }

    adaptAmountBg(index) {
        let tips_num = this.rechargeData[index].first_restriction;
        if(tips_num == 0){
            this.mini_tips.active = false;
        }else{
            this.mini_tips.active = true;
            this.mini_tips.getChildByName('content').getComponent(cc.Label).string = `Minimum top-out of Php ${tips_num} for first-time users.`
        }
    }

    setLabAmount(index) {
        if(this.rechargeData.length > 0) {
            if (!this.isFirstDeposit) {
                this.amountBgNode_new.active = true;
            }
            let tags = this.rechargeData[index].tags;
            let minAmount = parseFloat(this.rechargeData[index].min);
            let maxAmount = parseFloat(this.rechargeData[index].max);
            let first_restriction = this.rechargeData[this.payMethodIndex].first_restriction || 0;
            if (this.isFirstDeposit && parseFloat(first_restriction) > 0) {
                minAmount = parseFloat(first_restriction);
            }
            this.inputTextNode.placeholder = "Enter Amount: " + utils.formatNumberWithCommas(minAmount,0) + " - " + utils.formatNumberWithCommas(maxAmount,0) + "₱";
            if(this.insufficientTip.node.active)this.insufficientTip.string = "Enter Amount: " + utils.formatNumberWithCommas(minAmount,0) + " - " + utils.formatNumberWithCommas(maxAmount,0) + "₱";
            //添加 所有的buttons
            if(tags.length > 0){
                this.amountBgNode_new.active = true;
                this.amountBgNode_new.removeAllChildren();
                for (let index = 0; index < tags.length; index++) {
                    let abutns = cc.instantiate(this.payitem_prefab)
                    abutns.parent = this.amountBgNode_new;
                    //let ii = index % 3
                    //let jj = Math.floor(index / 3.0)
                    //abutns.setPosition(-330 + 330 * ii,-86 - 172 * jj)
                    abutns.on(cc.Node.EventType.TOUCH_END,()=> this.onClickConfigBtn(null,index+''), this);
                    this.payAmount[index] = abutns
                }
            }else{
                this.amountBgNode_new.active = false;
            }
            
            //根据后台配置显示
            this.payAmount.forEach((nd, ndIdx) => {
                if (ndIdx > tags.length - 1) {
                    nd.active = false;
                } else {
                    nd.active = true;
                    let labAmount = nd.getChildByName("labnum").getComponent(cc.Label);
                    let first_tag = nd.getChildByName("first_tag");
                    let lab_num = first_tag.getChildByName("lab_num").getComponent(cc.Label);
                    this.labNum = tags[ndIdx].toString();
                    labAmount.string = utils.formatNumberWithCommas(this.labNum, 0);
                    // if (Global.instance.is_mini_game()) {
                    //     if (this.firstDepositAwradList && this.firstDepositAwradList.length > 0 && this.isFirstDeposit && this.getFirstBonusNum(tags[ndIdx]) > 0) {
                    //         first_tag.active = true;
                    //         lab_num.string = "+"+this.getFirstBonusNum(tags[ndIdx]);
                    //     } else {
                    //         first_tag.active = false;
                    //     }
                    // }
                    if (this.firstDepositAwradList && this.firstDepositAwradList.length > 0 && this.isFirstDeposit && this.getFirstBonusNum(tags[ndIdx]) > 0) {
                        first_tag.active = true;
                        lab_num.string = "+"+this.getFirstBonusNum(tags[ndIdx]);
                    } else {
                        first_tag.active = false;
                    }
                }
            })

            //更改一下 下面数值 选择框
            let amount = this.amount_edit.string;
            if(amount.length > 0){
                this.changeTotalView(parseFloat(amount));
            }
        }
    }

    loadAmountConfig() {
        this.request_times += 1;
        HttpUtils.getInstance().post(1, 3, this, "/common/api/global-config/recharge-withdraw", {
            token: Global.getInstance().token,
            appChannel: ALL_APP_SOURCE_CONFIG.channel
        }, (response) => {
            if (!this.node || !this.node.isValid) {
                return;
            }
            // console.log('----recharge-withdraw---',JSON.stringify(response))
            if (response.data && response.data.recharge && response.data.recharge.length > 0) {
                this.btnAmount.node.active = false;
                this.amount_edit.enabled = true;
                this.rechargeData = response.data.recharge;
                this.rechargeData.sort((a, b) => {
                    if (a.sort != b.sort){ //按sort值从大到小
                        return b.sort - a.sort
                    } else { //sort值相同按照首字母从大到小排序
                        if (a.name && b.name) {
                            return b.name.localeCompare(a.name);
                        }
                    } 
                });
                for (let index = 0; index < this.rechargeData.length; index++) {
                    //payment method
                    let payname = this.rechargeData[index].name;
                    let payicon = this.rechargeData[index].icon;
                    this.paynameData = payname;
                    this.payiconData = payicon;
                }
                
                if (Global.instance.is_mini_game()) {
                    this.adaptAmountBg(0);
                    this.setLabAmount(0);
                }
                if(ALL_APP_SOURCE_CONFIG.channel == E_CHANEL_TYPE.WEB) {
                    this.initPayMethod();
                } 
            } else {
                this.amountBgNode_new.active = false;
                this.amount_edit.enabled = false;
                this.btnAmount.node.active = true;
            }
            
        }, (response)=>{
            this.amountBgNode_new.active = false;
            this.amount_edit.enabled = false;
            this.btnAmount.node.active = true;
            let self = this;
            if(this.request_times < 3){
                this.loadAmountConfig();
            }
        });
    }
    protected update(dt: number): void {
        this.resetDepositBG();
    }
    //重置充值框大小并且 画圆角
    resetDepositBG(time = 100){
        if(this.mainNode.getChildByName('content_all').height != this.mainNode.height){
            this.mainNode.height = this.mainNode.getChildByName('content_all').height;
            let roundsrc = this.mainNode.getComponent(RoundRectMask)
            roundsrc.radius = 35;
        }
    }
    initPayMethod() {
        const rechargeLength = this.rechargeData.length;
        if (rechargeLength > 0) {
            this.paymentNode.active = true;
            //设置paymentNode高度和btn_pull状态
            const masknode = this.paymentNode.getChildByName("masknode");
            if (rechargeLength <= 2) {
                this.btn_pull.active = false;
                // this.paymentNode.height = 230;
                if (masknode) masknode.height = 230;
            } else if (rechargeLength <= 4) {
                this.btn_pull.active = true;
                // this.paymentNode.height = 460;
                if (masknode) masknode.height = 230;
            } else {
                this.btn_pull.active = true;
                // this.paymentNode.height = 690;
                if (masknode) masknode.height = 230;
            }

            for (let index = 0; index < rechargeLength; index++) {
                let itemPay = cc.instantiate(this.item_paymethod);
                let pay_icon = itemPay.getChildByName("pay_icon");
                let pay_name = itemPay.getChildByName("pay_name");

                this.loadPaymentIcon(index, pay_icon);

                if (pay_name) {
                    pay_name.getComponent(cc.Label).string = this.rechargeData[index].name;
                }
                itemPay.parent = this.pay_web;
                this.itemPayList.push(itemPay);

                //如果没有选择payment method 默认选第一个 或 选中最后一次选择的itemPay
                let storedIndex = Global.getInstance().getStoreageData("SELECT_PAYMENT",0);//设置默认值0 
                //修改 本地存储index > 目前列表长度bug 
                if(storedIndex >= rechargeLength)storedIndex = 0;//默认0
                this.payMethodIndex = storedIndex;
                if (this.lastSelectIndex === null && index === 0) {
                    if(storedIndex >= 0){
                    }else{
                        itemPay.getComponent(cc.Toggle).isChecked = true;
                        this.adaptAmountBg(index);
                        this.setLabAmount(index);
                        if (pay_name) pay_name.color = cc.color(255,255,255);
                        this.payM = this.rechargeData[index].account_type;
                        this.lastSelectIndex = index; // 更新 lastSelectIndex 以确保选中状态
                    }
                }

                if (storedIndex >= 0) {
                    itemPay.getComponent(cc.Toggle).isChecked = true;
                    this.adaptAmountBg(storedIndex);
                    this.setLabAmount(storedIndex);
                    if (pay_name) pay_name.color = cc.color(255,255,255);
                    this.payM = this.rechargeData[storedIndex].account_type;
                    if (storedIndex > 0 && index != storedIndex) {
                        itemPay.getComponent(cc.Toggle).isChecked = false;
                        if (pay_name) pay_name.color = cc.color(255,255,255);
                    }
                    if (storedIndex == 0 && index !== 0) {
                        itemPay.getComponent(cc.Toggle).isChecked = false;
                        if (pay_name) pay_name.color = cc.color(255,255,255);
                    }
                }

                let item_bg = itemPay.getChildByName("masknode").getChildByName("sp_bg");
                let shadow = itemPay.getChildByName("sp_shadow");
                let checkmark = itemPay.getChildByName("checkmark");
                let unselect = itemPay.getChildByName("unselect").getComponent(cc.Sprite);
                let lname = this.rechargeData[index].name.toLocaleLowerCase();
                if(lname.indexOf('maya') != -1){
                    if(item_bg) item_bg.color = cc.color(1,212,106);
                    if(shadow) shadow.color = cc.color(1,212,106);
                    unselect.spriteFrame = this.itempay_unselect[0];
                    if(checkmark) checkmark.getComponent(cc.Sprite).spriteFrame = this.itempay_select[0];
                } 
                if(lname.indexOf('gcash') != -1){
                    if(item_bg) item_bg.color = cc.color(72,129,237);
                    if(shadow) shadow.color = cc.color(72,129,237);
                    unselect.spriteFrame = this.itempay_unselect[1];
                    if(checkmark) checkmark.getComponent(cc.Sprite).spriteFrame = this.itempay_select[1];
                }

                

                //通用点击事件
                itemPay.on(cc.Node.EventType.TOUCH_END, (event) => {
                    this.itemPayList.forEach((item) => {
                        let toogle = item.getComponent(cc.Toggle);
                        if (toogle) toogle.isChecked = false;
                        let p_name = item.getChildByName("pay_name");
                        if (p_name) p_name.color = cc.color(255,255,255);
                        let lname = this.rechargeData[index].name.toLocaleLowerCase();
                        if(lname.indexOf('maya') != -1){
                            unselect.spriteFrame = this.itempay_unselect[0];
                        }else if(lname.indexOf('gcash') != -1){
                            unselect.spriteFrame = this.itempay_unselect[1];
                        }
                    })
                    let toggle = itemPay.getComponent(cc.Toggle);
                    if (toggle) toggle.isChecked = true;
                    if (pay_name) pay_name.color = cc.color(255,255,255);
                    this.payMethodIndex = index;
                    this.lastSelectIndex = index;
                    Global.getInstance().setStoreageData("SELECT_PAYMENT", index);//保存最后一次选择的Index
                    this.adaptAmountBg(index);
                    this.setLabAmount(index);
                    if (this.inputTextNode.string.trim() != "") {
                        this.inputErrTip(this.inputTextNode.string);
                    }
                    this.payM = this.rechargeData[index].account_type;
                    //如果paymethod>6个，调整顺序
                    if (rechargeLength > 6) {
                        const selectPayItem = this.rechargeData[index];
                        this.rechargeData.splice(index, 1);
                        this.rechargeData.unshift(selectPayItem);
                    }
                }, this);
            }
        }
    }

    /**点击btn_pull*/
    clickPullBtn() {
        let masknode = this.paymentNode.getChildByName("masknode");
        if (this.btn_pull.scaleY == 1) {
            // let add_height = Math.ceil((this.rechargeData.length - 6) / 2)*230;
            let toheight = Math.ceil(this.rechargeData.length/2.0) * 230
            cc.tween(this.paymentNode)
            //   .to(0.5, {height: this.paymentNode.height + add_height})
                .to(0.5,{height:toheight})
              .call(()=>{
                    if(masknode) masknode.height = this.paymentNode.height;
                    if(this.btn_pull) this.btn_pull.scaleY = -1;
              })
              .start()
        } else if (this.btn_pull.scaleY == -1) {
            cc.tween(this.paymentNode)
              .to(0.5, {height: 500})
              .call(()=>{
                    if(masknode) masknode.height = 230;
                    // if(this.amountNode) this.amountNode.y = this.paymentNode.y - this.paymentNode.height - 20;
                    if(this.btn_pull) this.btn_pull.scaleY = 1;
              })
              .start()
        }
    }

    /**加载payment method icon */
    loadPaymentIcon(index, payicon) {
        let url = this.rechargeData[index].icon;
        if(url.length <= 4)return;//不是图片 或者为空
        if (!/^http/.test(url)) {
            url = ALL_APP_SOURCE_CONFIG.remote_assets_prod[Global.DEBUG] + url;
        }
        Global.getInstance().loadImgFromUrl_V2(url, (loaded, image)=>{
            if (payicon) payicon.getComponent(cc.Sprite).spriteFrame = image;
        },payicon);
    }

    getFirstBonusNum(chargeNum) {
        if (!this.firstDepositAwradList || !chargeNum) {
            return 0;
        }
        let totallength = this.firstDepositAwradList.length;
        if (chargeNum >= this.firstDepositAwradList[totallength-1].amount) {
            return this.firstDepositAwradList[totallength-1].award;
        }
        for (let index = 0; index < totallength; index++) {
            let element = this.firstDepositAwradList[index];
            if (chargeNum < element.amount) {
                if (index == 0) {
                    return 0;
                } else {
                    return this.firstDepositAwradList[index-1].award;
                }
            }
        }
    }

    getChargeId(chargeNum) {
        if (!this.firstDepositAwradList || !chargeNum) {
            return 0;
        }
        let totalLen = this.firstDepositAwradList.length;
        if (chargeNum >= this.firstDepositAwradList[totalLen - 1].amount) {
            return this.firstDepositAwradList[totalLen - 1].id;
        }
        for (let idx = 0; idx < totalLen; idx++) {
            let element = this.firstDepositAwradList[idx];
            if (chargeNum < element.amount) {
                if (idx == 0) {
                    return 0;
                } else {
                    return this.firstDepositAwradList[idx - 1].id;
                }
            }
        }
    }

    changeTotalView(depositNum) {
        if (!depositNum) depositNum = 0;
        let bonus;
        bonus = this.getFirstBonusNum(depositNum);
        if (ALL_APP_SOURCE_CONFIG.channel == E_CHANEL_TYPE.WEB) {
            bonus = 0;
        }
        
        let maxAmount = this.rechargeData[this.payMethodIndex].max;
        if (Global.instance.is_mini_game()) {
            maxAmount = this.rechargeData[0].max; 
        }
        //if (depositNum <= maxAmount) {
        this.totalDepositNum.string = utils.formatNumberWithCommas(depositNum+bonus);
        //}
        this.bonusNum.string = utils.formatNumberWithCommas(bonus);
        let deposit_list = this.rechargeData[this.payMethodIndex].tags;
        for (let index = 0; index < deposit_list.length; index++) {
            let element = deposit_list[index];
            if (depositNum == element) {
                this.payAmount[index].getChildByName('Background').getComponent(cc.Sprite).spriteFrame = this.btnSpriteFrame[1];
            } else {
                this.payAmount[index].getChildByName('Background').getComponent(cc.Sprite).spriteFrame = this.btnSpriteFrame[0];
            }
        }
    }

    formatPhoneNumber(phoneNumber: string) {
        if (phoneNumber.length !== 11) {
            throw new Error('Invalid phone number length. It should be 11 digits.');
        }
        //保留前三位和后四位，中间用****替换
        return `${phoneNumber.slice(0, 4)}****${phoneNumber.slice(8)}`;
    }

    inputErrTip(text) {
        let minAmount = parseFloat(this.rechargeData[this.payMethodIndex].min);
        let maxAmount = parseFloat(this.rechargeData[this.payMethodIndex].max);
        let first_restriction = this.rechargeData[this.payMethodIndex].first_restriction || 0;
        if (this.isFirstDeposit && parseFloat(first_restriction) > 0) {
            minAmount = parseFloat(first_restriction);
        }
        this.continueBtn.enabled = true;
        this.continueBtn.node.opacity = 255;
        let show_err = false;
        if(!isNaN(parseFloat(text)) && !isNaN(Number(text))) {
            if (Global.instance.is_mini_game()) {
                minAmount = this.rechargeData[0].min; 
                maxAmount = this.rechargeData[0].max;
            }
            if (this.isFirstDeposit && parseFloat(first_restriction) > 0) {
                minAmount = parseFloat(first_restriction);
            }
            let tex = parseFloat(text)
            if(tex < minAmount) {
                this.minimumTip.node.active = true;
                this.maximumTip.node.active = false;
                this.insufficientTip.node.active = false;
                this.continueBtn.enabled = false;
                this.continueBtn.node.opacity = 128;
                let amountNum = utils.formatNumberWithCommas(minAmount); 
                this.minimumTip.string = `The minimum amount is ${amountNum}₱`
                show_err = true
            } else if (tex >= minAmount && tex < maxAmount) {
                this.minimumTip.node.active = false;
                this.maximumTip.node.active = false;
                this.insufficientTip.node.active = false;
            }
            else if (tex == maxAmount) {
                this.maximumTip.node.active = true;
                this.minimumTip.node.active = false;
                this.insufficientTip.node.active = false;
                let amountNum = utils.formatNumberWithCommas(maxAmount);
                this.maximumTip.string = `The maximum allowable input is ${amountNum}₱`
            }
            else if (tex > maxAmount) {
                this.maximumTip.node.active = true;
                this.minimumTip.node.active = false;
                this.insufficientTip.node.active = false;
                let amountNum = utils.formatNumberWithCommas(maxAmount);
                this.maximumTip.string = `The maximum amount is ${amountNum}₱`
                if(tex > maxAmount){
                    this.continueBtn.enabled = false;
                    this.continueBtn.node.opacity = 128;
                }
                show_err = true
            }

            this.maximumTip.node.color = text == maxAmount ? cc.color(192, 192, 192) : cc.color(172, 17, 64);
        } else {
            this.minimumTip.node.active = false;
            this.maximumTip.node.active = false;
            this.insufficientTip.node.active = true;
            this.continueBtn.enabled = false;
            this.continueBtn.node.opacity = 128;
            show_err = true
            this.insufficientTip.string = "Enter Amount: " + utils.formatNumberWithCommas(minAmount,0) + " - " + utils.formatNumberWithCommas(maxAmount,0) + "₱";
        }
    }

    /**拉取不到数据 点击此按钮给出提示信息*/
    onclickAmountBnt() {
        Global.getInstance().showSimpleTip(Global.getInstance().getLabel("tipword100"));
    }

    onEdittingDidBegin(editbox: cc.EditBox) {
        Global.instance.scrollTo(0,0,100);
        Global.getInstance().editParentMove(editbox, this.mainNode.parent,800);
        editbox.placeholderLabel.node.name = editbox.placeholder
        editbox.placeholder = ""
    }

    onPhoneText(text, editbox, customEventData) {
        let changetx = text.replace(/[^0-9]/g, '');
        this._amount = changetx;
        if (parseInt(this._amount)) {
            editbox.string = parseInt(this._amount);
            let maxAmount = parseFloat(this.rechargeData[this.payMethodIndex].max);
            if(parseInt(this._amount) >= maxAmount){
                this._amount = maxAmount+'';
                editbox.string = maxAmount;
            }
        }
        let impl = editbox['_impl'];
        impl._updateStyleSheet();
        this.inputErrTip(this._amount);
        this.setStateContinue();
        this.changeTotalView(parseFloat(this._amount));
    }

    onEdittingDidEnd(editbox: cc.EditBox) {
        if (Global.getInstance().needScreenUp()) {
            this.mainNode.parent.y = 0
        }
        editbox.placeholder = editbox.placeholderLabel.node.name
        this.inputErrTip(editbox.string);
    }

    onClickConfigBtn(event, data) {
        let idx = parseInt(data);
        if (!this.payMethodIndex) this.payMethodIndex = 0;
        let amount = this.rechargeData[this.payMethodIndex].tags[idx];
        this.amount_edit.string = amount.toString()
        this.inputErrTip(amount);
        this._amount = amount.toString()
        this.setStateContinue();
        this.changeTotalView(parseFloat(amount));
    }

    onClickContinue() {
        // let gap = (Global.getInstance().now() - this.lastReqTime)/1000;
        if (this.isReqDepositing) {
            return;
        }
        const rechargeLength = this.rechargeData.length;
        // this.lastReqTime = Global.getInstance().now();
        let minAmount = parseFloat(this.rechargeData[this.payMethodIndex].min);
        let maxAmount = parseFloat(this.rechargeData[this.payMethodIndex].max);
        let first_restriction = this.rechargeData[this.payMethodIndex].first_restriction || 0;
        if (this.isFirstDeposit && parseFloat(first_restriction) > 0) {
            minAmount = parseFloat(first_restriction);
        }
        let text = this.inputTextNode.string;
        if(!isNaN(parseFloat(text)) && !isNaN(Number(text))) {
            let tex = parseFloat(text)
            if(tex >= minAmount || tex <= maxAmount) {
                this.onBuy();
            }
        }
    }

    //ContinueBtn状态
    setStateContinue() {
        // if (this.minimumTip.node.active || this.maximumTip.node.active || this.insufficientTip.node.active) {
        //    this.continueBtn.node.opacity = 128;
        // } else {
        //     this.continueBtn.node.opacity = 255;
        // }
    }
    //跳转账单页面
    toBillPage(){
        this.hide();
        uiManager.instance.showDialog(UI_PATH_DIC.Transations, ["deposit"],null,DEEP_INDEXZ.TRANSATIONS);
    }
    //弹窗提示 跳回小程序
    back_mini_buy_tips(){
        let str = "Please click 'Go Deposit' to return to the GCash mini-program for top-up."
        Global.getInstance().showCommonTip2({ word: str, confirm: "Go Deposit" }, this, false, () => {this.gcash_buy()}, null, null,"Quick Reminder"); 
    }
    //gcash内购 跳回小程序
    gcash_buy(){
        uiManager.instance.loadPrefabLoading_noback()
        let url = "/common/api/gcash/set/jumpType";
        let params;
        params = {
            token: Global.getInstance().token,
            type: parseInt(this._amount)
        }
        HttpUtils.getInstance().post(3, 3, this,url , params, (response) => {
            uiManager.instance.hidePrefabByLoading()
            this.isReqDepositing = false;
            // window.open(Global.getInstance().gcashShopUrl, "_blank")
            utils.openUrl(Global.getInstance().gcashShopUrl);
        }, (err) => {
            uiManager.instance.hidePrefabByLoading()
            this.isReqDepositing = false;
            if (err?.code == MAINTENANCETIPCODE) { //服务器维护
                showMaintenancetip(err.msg)
                return
            }
        });
    }
    onBuy() {        
        if (Global.instance.is_mini_game()) {
            if(ALL_APP_SOURCE_CONFIG.channel == E_CHANEL_TYPE.G_CASH){
                this.back_mini_buy_tips();
                return;
            }
            //判定是否是首充
            if (this.isFirstDeposit) {
                this.rechargeID = this.getChargeId(this._amount);
                this.awardNum = this.getFirstBonusNum(this._amount);
            } else {
                this.rechargeID = 0;
                this.awardNum = 0;
            }
        } else if (ALL_APP_SOURCE_CONFIG.channel == E_CHANEL_TYPE.WEB) {
            this.rechargeID = 0;
            this.awardNum = 0;
        }
        //这里判断是否自动勾选 连续充值 maximum_single_recharge  //set/get方法里面有返回
        if(Global.getInstance().config?.maximum_single_recharge && Global.getInstance().config?.maximum_single_recharge <= parseInt(this._amount)){
            this.check_icon.active = true;
        }
        let params;
        params = {
            token: Global.getInstance().token,
            app_package_name: Global.getInstance().getAppBundleId(),
            app_version: Global.getInstance().getAppVersion(),
            amount: parseInt(this._amount),
            award: this.awardNum,
            email: Global.getInstance().payAccount.email,
            phone: Global.getInstance().userdata.phone,
            name: Global.getInstance().userdata.nickname,
            config_id: this.rechargeID,
            identifier: METHODS.MAYA_PAY,
            platform: utils.myOS() + "_" + (cc.sys.isBrowser ? 1 : 0)
        }
        let url = "/common/api/payment/balance-add";
        //支付渠道
        if (this.payM == PAY_METHOD.MAYA_WEB) {
            url = "/open/api/maya/payment";
            params["identifier"] = METHODS.MAYA_WEB;
        } else if (this.payM == PAY_METHOD.GCASH_WEB) {
            url = "/open/api/gcash/payment";
            params["identifier"] = METHODS.GCASH_WEB;
        }
        this.isReqDepositing = true;
        let callBackBool = false;
        setTimeout(() => {
            this.isReqDepositing = false;
        }, 20000);
        
        HttpUtils.getInstance().post(3, 3, this,url , params, (response) => {
            this.isReqDepositing = false;
            //WEB渠道
            if (ALL_APP_SOURCE_CONFIG.channel == E_CHANEL_TYPE.WEB) {
                if (response.data.paymentUrl) {
                    callBackBool = true;
                    //防止 iOS15以上 谈提示框
                    if(cc.sys.OS_IOS === cc.sys.os){
                        utils.assignUrl(response.data.paymentUrl)
                    }else{
                        // window.open(response.data.paymentUrl, "_blank")
                        utils.openUrl(response.data.paymentUrl);
                    }    
                }
            }
            if (Global.instance.is_mini_game()) {
                if (ALL_APP_SOURCE_CONFIG.channel == E_CHANEL_TYPE.G_CASH) {
                    if (response.data.paymentUrl) {
                        callBackBool = true;
                        utils.openUrl(response.data.paymentUrl);      
                    }
                }else{
                    callBackBool = true;
                    //充值成功
                    Global.getInstance().updateBalanceAfterGame(() => {
                        this.stopDepTimeoutSchedule();//清掉充值超时定时器
                        let str = "Deposit Successful! Enjoy Your Nustar Online Gaming Experience!"
                        if (this.isFirstDeposit) {
                            str = "You have successfully claimed your bonus. Best of luck on your journey ahead!"
                        }
                        //如果连续充值 
                        if(this.check_icon.active){
                            // Global.getInstance().showCommonTip2({ word: str, confirm: "Done" }, this, true, () => {}, null, null,"Congratulations");
                            Global.getInstance().showSimpleTip(str);
                        }else{
                            Global.getInstance().showCommonTip2({ word: str, confirm: "Bet Now" }, this, true, () => {this.clickBetNow()}, null, null,"Congratulations");
                        }
                        this.wallet_label.string = utils.formatNumberWithCommas(Global.getInstance().balanceTotal);
                    })
                }
            }
        }, (err) => {
            this.isReqDepositing = false;
            if (err?.code == MAINTENANCETIPCODE) { //服务器维护
                callBackBool = true;
                showMaintenancetip(err.msg)
                return
            }
            // console.log('err.code----------:',JSON.stringify(err));
            //提现 超时 返回错误码1 int类型
            if ("1" == err?.code+'') {
                callBackBool = true;
                // Global.getInstance().showSimpleTip(err.msg)
                let errstr = err.msg
                Global.getInstance().showCommonTip2({ word: errstr, confirm: "Done" }, this, true, () => {this.toBillPage()}, null, null,"Oops");
                
                ClientLogManager.upLoadSingleLogs(CLIENTLOGKEY.payError, params)
            } else if (err?.code == "103035" || err?.code == "103037") {
                // let str = ''
                // if (Global.getInstance().mayaMode) {
                //     showWalletLimitPop(PRODUCT_TYPE.MAYA_MINI, E_TRANSACTION_TYPE.recharge, parseInt(this._amount))
                //     return;
                // } else {
                //     let word = ''
                //     str = Global.getInstance().getLabel("php_code_" + err.code).replace("%d", word);
                //     Global.getInstance().showSimpleTip(str);
                // }
                callBackBool = true;
                let errstr;
                if (err.code == "103035") {//充值限额
                    errstr = "Transaction limit exceeded.";
                }
                if (err.code == "103037") {//钱包余额不足
                    let payType;
                    if (params["identifier"] == METHODS.MAYA_PAY || params["identifier"] == METHODS.MAYA_WEB) {
                        payType = "Maya";
                    } else if (params["identifier"] == METHODS.GCASH_WEB) {
                        payType = "GCash";
                    }
                    errstr = `Your ${payType} account has insufficient balance.`;
                }
                let conf = "Done";
                Global.getInstance().showCommonTip2({ word: errstr, confirm: conf }, this, true, () => {});
            } else if (err?.code == "103099") {
                callBackBool = true;
                //充值失败(其他错误)
                let errstr = "The payment was unsuccessful due to an abnormality.Please try again later.";//err.msg
                let conf = "Done"
                Global.getInstance().showCommonTip2({ word: errstr, confirm: conf }, this, true, () => {});
            } else {//除103035、103037、200的code外，其它应该都提示The payment was unsuccessful due to an abnormality. Please try again later.
                let errstr = "The payment was unsuccessful due to an abnormality.Please try again later.";//其他错误
                let conf = "Done"
                Global.getInstance().showCommonTip2({ word: errstr, confirm: conf }, this, true);
                ClientLogManager.upLoadSingleLogs(CLIENTLOGKEY.payError, params)
                callBackBool = true;
            }
        });
        //10秒超时处理 关闭响应
        let timeoutFun = () => {
            if(callBackBool)return;
            Global.getInstance().hideShowLoading(url);
            let str = "Your deposit request is currently being processed. Please kindly wait."
            Global.getInstance().showCommonTip2({ word: str, confirm: "Done" }, this, true, () => {this.toBillPage()}, null, null,"Tips");
        };
        this.stopDepTimeoutSchedule();
        if (!this.depositTimeout) {
            this.depositTimeout = setTimeout(timeoutFun,10000);
        }

        // setTimeout(() => {
        //     if(callBackBool)return;
        //     Global.getInstance().hideShowLoading(url);
        //     let str = "Your deposit request is currently being\n\nprocessed. Please kindly wait."
        //     Global.getInstance().showCommonTip2({ word: str, confirm: "Done" }, this, true, () => {this.toBillPage()}, null, null,"Tips");      
        // }, 10000);
    }

    /**
     * 清掉充值超时定时器
     */
    stopDepTimeoutSchedule(){
        if(this.depositTimeout){
            clearTimeout(this.depositTimeout);
            this.depositTimeout = null;
        }
    }

    audioPlay(url,loop) {
        AudioManager.getInstance().playEffect(url, loop);
    }

    closeDialog() {
        this.hide();
        cc.director.emit("ReturnToHome2");
    }

    onClickRecordBtn() {
        uiManager.instance.showDialog(UI_PATH_DIC.Transations, [{type:"deposit", openMark:"deposit"}],null,DEEP_INDEXZ.TRANSATIONS);
    }

    clickBetNow(){
        //点击Bet Now 跳转至游戏 History 页面
        let gamescene =  cc.director.getScene().getComponentInChildren(GameControl);
        gamescene.mTabbar.clickGoBet();
        this.hide();
    }
}
