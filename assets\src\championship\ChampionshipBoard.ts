import { UIComponent } from "../customComponent/UIComponent";
import { CHAMPIONSHIP_STATUS, E_CHANEL_TYPE, UI_PATH_DIC, EVENT, JUMP_TYPE, DEEP_INDEXZ } from "../GlobalConstant";
import Global from "../GlobalScript";
import List from "../listView/List";
import { uiManager } from "../mgr/UIManager";
import utils from "../utils/utils";
import { TournamentDetailInfo, TournamentInfo, TournamentRankItemData, TournamentRankPageSize } from "./ChampionshipConst";

const { ccclass, property } = cc._decorator;

const TAB_PAGR = ['preRound', 'curRound'];

@ccclass
export default class ChampionshipBoard extends UIComponent {
    private _acitvityId: number;
    private _status: number;
    private _tabInfo: TournamentInfo;
    private _detailInfo: TournamentDetailInfo;
    private _rankData: TournamentRankItemData[];

    private _isSliding: boolean = false;
    private _currentPage: string = null;

    private _curPage: number = 1;
    private _prePage: number = 1;
    private _curTotalCount: number = 0;
    private _preTotalCount: number = 0;
    private _isQuerying: boolean = false;

    onLoad() {
        super.onLoad();

        this.initDefaultUI();

        const curRankList = utils.getChildByPath(this.nodeAtNode("curRound"), "listview.ScrollView").getComponent(cc.ScrollView);
        const preRankList = utils.getChildByPath(this.nodeAtNode("preRound"), "listview.ScrollView").getComponent(cc.ScrollView);
        curRankList.node.on(cc.Node.EventType.TOUCH_MOVE, this.onRankScroll, this);
        preRankList.node.on(cc.Node.EventType.TOUCH_MOVE, this.onRankScroll, this);
    }

    start() {
        let page = this.node.getChildByName("page").getComponent(cc.PageView);
        page.node.on('page-turning', () => {
            let curIndex = page.getCurrentPageIndex();
            if (curIndex != 1) {
                this.node.destroy();
            }
        }, self);
        let tempnode = this.node.getChildByName("temp");
        page.insertPage(cc.instantiate(tempnode), 0);
        page.scrollToPage(1, 0.01);
    }

    show(args) {
        if (!args) return;

        this._status = args.status;
        this._tabInfo = args.info;
        this._acitvityId = args.info.id;

        this.nodeAtNode("infoNode").active = this._status == CHAMPIONSHIP_STATUS.WAITING;
        this.nodeAtNode("rankNode").active = this._status != CHAMPIONSHIP_STATUS.WAITING;

        this.changeTab(1);
    }

    addEvents(): void {
        this.addEvent(EVENT.CHAMPIONSHIP_DETAIL, this.initUI);
        this.addEvent(EVENT.CHAMPIONSHIP_RANK, this.initRank);
    }

    onClick(name: string, btn: cc.Node): void {
        if (name == "btn_close") {
            this.node.destroy();
        }
        else if (name == "btn_help") {
            uiManager.instance.showDialog(UI_PATH_DIC.ChampionshipBoardRule, [this._detailInfo],null,DEEP_INDEXZ.ACTIVITYS);
        }
        else if (name.startsWith("btn_tab")) {
            const index = Number(name[name.length - 1]);
            this.changeTab(index);
        }
        else if (name == "btn_histroy") {
            this.nodeAtNode("infoNode").active = false;
            this.nodeAtNode("rankNode").active = true;
            this.changeTab(0);
        }
        else if (name == "btn_require") {
            if (this._tabInfo.registration_rule == 1) {
                utils.linkJump({ jumpType: JUMP_TYPE.DEPOSIT });
            }
            else if (this._tabInfo.registration_rule == 2) {
                const gameIDs = this._detailInfo.game_ids;
                const vendors = this._detailInfo.vendors;
                const brands = this._detailInfo.brands;

                utils.linkJump({ jumpType: JUMP_TYPE.GAME, gameType: gameIDs, providerList: vendors });
            }
        }
    }

    initDefaultUI() {
        this.nodeAtNode("infoNode").active = false;
        this.nodeAtNode("rankNode").active = false;

        const defalutStr = "--";
        this.setLabel("txt_total_balance", "");
        this.setLabel("txt_balance_top1", defalutStr);
        this.setLabel("txt_balance_top2", defalutStr);
        this.setLabel("txt_balance_top3", defalutStr);
        this.setLabel("txt_hour", defalutStr);
        this.setLabel("txt_minute", defalutStr);
        this.setLabel("txt_second", defalutStr);
        this.setLabel("txt_progress", defalutStr);
    }

    reqDetailsInfo() {
        const params = {
            activity_id: this._acitvityId,
            is_history: this.bHistroy()
        };

        this.httpProxy.getChampionshipDetails(params);
    }

    changeTab(index: number) {
        if (this._currentPage == TAB_PAGR[index]) return;

        if (this._isSliding) {
            return;
        }
        this._isSliding = true;

        this._curPage = this._prePage = 1;

        this.nodeAtSpriteSwitcher("tab").selectedIndex = index == 0 ? 0 : 1;

        const preNode = this.nodeAtNode("preRound");
        const curNode = this.nodeAtNode("curRound");
        if (index == 0) {
            cc.tween(preNode).to(0.2, { x: 0 }).call(() => {
                preNode.active = true;
            }).start();
            cc.tween(curNode).to(0.2, { x: 1080 }).call(() => {
                curNode.active = false;

                this._currentPage = TAB_PAGR[0];
                this._isSliding = false;

                this.reqDetailsInfo();
            }).start();
        }
        else {
            cc.tween(preNode).to(0.2, { x: -1080 }).call(() => {
                preNode.active = false;
            }).start();
            cc.tween(curNode).to(0.2, { x: 0 }).call(() => {
                curNode.active = true;

                this._currentPage = TAB_PAGR[1];
                this._isSliding = false;

                this.reqDetailsInfo();
            }).start();
        }
    }

    initUI(data) {
        this._detailInfo = data;

        if (this.nodeAtNode("infoNode").active) {
            this.initPrepareUI(data);
        }
        else {
            const node = this.bHistroy() ? this.nodeAtNode("preRound") : this.nodeAtNode("curRound");
            this.showSelfInfo(node);

            const params = {
                activity_id: this._acitvityId,
                is_history: this.bHistroy(),
                page: this.bHistroy() ? this._prePage : this._curPage,
                page_size: TournamentRankPageSize
            };

            this.httpProxy.getChampionshipRankList(params);
        }
    }

    initPrepareUI(data) {
        this.setLabel("txt_total_balance", "₱" + utils.formatNumberWithCommas(data.total_price, 0));

        const topRankings = data.top_rankings;
        for (let i = 0; i < topRankings.length; i++) {
            const info = topRankings[i];
            this.setLabel("txt_balance_top" + info.rank, utils.formatNumberWithCommas(info.bonus, 0))
        }

        const startTime = data.start_time;
        const now = Global.getInstance().now() / 1000;
        let diffTime = startTime - now;

        const updateTime = () => {
            if (diffTime < 0) {
                this.unschedule(updateTime);
                return;
            }
            const hour = Math.floor((diffTime / 3600) % 24).toString().padStart(2, '0');
            const minute = Math.floor((diffTime / 60) % 60).toString().padStart(2, '0');
            const second = Math.floor(diffTime % 60).toString().padStart(2, '0');
            this.setLabel("txt_hour", hour);
            this.setLabel("txt_minute", minute);
            this.setLabel("txt_second", second);

            diffTime--;
        }
        updateTime();
        this.schedule(updateTime, 1);

        const rule = data.registration_rule;
        const value = Number(data.my_registration_value);
        const requirement = Number(data.registration_requirement);

        this.nodeAtNode("bg_progress").active = rule > 0;
        this.nodeAtNode("btn_require").active = rule > 0 && value < requirement;

        if (rule > 0) {
            const str = ["", "Deposit ", "Bet "];
            this.setLabel("txt_progress", str[rule] + value + "/" + requirement);
            this.setProgress("progressBar", value / requirement);
        }

        this.nodeAtSpriteSwitcher("txt_time_tip").selectedIndex = rule == 0 ? 0 : 1;
        this.nodeAtNode("bg_time").y = rule == 0 ? 580 : 780;
    }

    initRank(data) {
        this._isQuerying = false;

        const bAdd = data.bAdd;
        this._rankData = data.list;
        this.bHistroy() ? this._preTotalCount = data.total : this._curTotalCount = data.total;

        const node = this.bHistroy() ? this.nodeAtNode("preRound") : this.nodeAtNode("curRound");

        if (!bAdd) this.showTop3(node);
        this.showScrollList(node, bAdd);
    }

    showSelfInfo(parentNode) {
        const data = this._detailInfo.my_rank;

        const itemNode = parentNode.getChildByName("self_info");
        this.initRankItem(itemNode, data);

        const bShowBetBtn = this._status == CHAMPIONSHIP_STATUS.ONGOING && this._currentPage == TAB_PAGR[1];
        const btnBet = utils.seekFromRootByName(itemNode, "btn_bet");
        btnBet.active = bShowBetBtn;
        this.addButtonClick(btnBet, () => {
            const gameIDs = this._detailInfo.game_ids;
            const vendors = this._detailInfo.vendors;
            const brands = this._detailInfo.brands;

            utils.linkJump({ jumpType: JUMP_TYPE.GAME, gameType: gameIDs, providerList: vendors });
        });

        itemNode.height = bShowBetBtn ? 323 : 126;
        parentNode.getChildByName("listview").getComponent(cc.Widget).bottom = bShowBetBtn ? 323 : 126;
        itemNode.getComponent(cc.Widget).updateAlignment();
        itemNode.getChildByName("self_node").getComponent(cc.Widget).updateAlignment();
        this.updateAlignment(parentNode.getChildByName("listview"));
    }

    showTop3(parentNode) {
        if (!this._rankData) return;

        for (let index = 0; index < 3; index++) {
            let element = this._rankData[index];
            if (!element) continue;

            let node = utils.getChildByPath(parentNode, "top.number" + (index + 1));
            let lab_userid = node.getChildByName("lab_userid").getComponent(cc.Label);
            let lab_betamout = utils.seekFromRootByName(node, "lab_betamout").getComponent(cc.Label);
            let lab_award = utils.seekFromRootByName(node, "lab_award").getComponent(cc.Label);

            lab_userid.string = utils.formatPlayerId(element.user_id);
            lab_betamout.string = utils.formatNumberWithCommas(parseFloat(element.score), 0);
            lab_award.string = utils.formatNumberWithCommas(Number(element.bonus), 0);

            utils.loadAvatar(this.seekFromRootByName(node, "ic_avatar").getComponent(cc.Sprite), element.avatar);

            const playerId = element.user_id;
            this.addButtonClick(node, () => {
                uiManager.instance.showDialog(UI_PATH_DIC.ChampionshipDetail, [{ activity_id: this._acitvityId, player_id: playerId, is_history: this.bHistroy() }],null,DEEP_INDEXZ.ACTIVITYS);
            })
        }
    }

    showScrollList(parentNode, bAdd: boolean = false) {
        if (!this._rankData) return;

        if (bAdd) {
            utils.getChildByPath(parentNode, "listview.ScrollView").getComponent(List).addItems(this._rankData);
        }
        else {
            let data = this._rankData.slice(3);
            data.map(item => {
                item["params"] = { activity_id: this._acitvityId, player_id: item.user_id, is_history: this.bHistroy() };

                const myRank = this._detailInfo.my_rank;
                item["bSelf"] = myRank && myRank.rank && myRank.rank == item.rank;
            });

            utils.getChildByPath(parentNode, "listview.ScrollView").getComponent(List).setData(data);
        }
    }

    onRankScroll() {
        if (this._isQuerying) {
            return;
        }

        const page = this.bHistroy() ? this._prePage : this._curPage;
        const totlaCount = this.bHistroy() ? this._preTotalCount : this._curTotalCount;
        if (page * TournamentRankPageSize >= totlaCount) return;

        const list = this.bHistroy() ? utils.getChildByPath(this.nodeAtNode("preRound"), "listview.ScrollView").getComponent(cc.ScrollView) : utils.getChildByPath(this.nodeAtNode("curRound"), "listview.ScrollView").getComponent(cc.ScrollView);
        let offset = list.getScrollOffset().y;
        let maxoffset = list.getMaxScrollOffset().y;

        if ((maxoffset - offset) < 20) {
            const params = {
                activity_id: this._acitvityId,
                is_history: this.bHistroy(),
                page: this.bHistroy() ? ++this._prePage : ++this._curPage,
                page_size: TournamentRankPageSize
            };

            this._isQuerying = true;
            this.httpProxy.getChampionshipRankList(params);
        }
    }

    initRankItem(node, info) {
        const rank = info && info.rank > 0 ? info.rank : "--";
        const userId = Global.instance.userdata.user_id;
        const avatar = Global.instance.getAvatar();
        const score = info ? info.score : "--";
        const bonus = info ? info.bonus : "--";

        this.setLabel("txt_rank", rank, node);
        this.setLabel("txt_userid", utils.formatPlayerId(userId), node);
        utils.loadAvatar(this.seekFromRootByName(node, "ic_avatar").getComponent(cc.Sprite), avatar);
        this.setLabel("txt_betamout", utils.formatNumberWithCommas(score, 0), node);
        this.setLabel("txt_award", utils.formatNumberWithCommas(bonus, 0), node);

        this.addButtonClick(utils.seekFromRootByName(node, "btn_go"), () => {
            uiManager.instance.showDialog(UI_PATH_DIC.ChampionshipDetail, [{ activity_id: this._acitvityId, player_id: userId, is_history: this.bHistroy() }],null,DEEP_INDEXZ.ACTIVITYS);
        })
    }

    bHistroy() {
        return this._currentPage == TAB_PAGR[0];
    }
}