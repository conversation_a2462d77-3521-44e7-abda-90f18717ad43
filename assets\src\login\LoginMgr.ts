// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

import { EMIT_PARAMS } from "../GlobalConstant";
import Global from "../GlobalScript";

const {ccclass, property} = cc._decorator;

@ccclass("LoginMgr")
export class LoginMgr{
    static _instance: LoginMgr;
    static get instance() {
        if (this._instance) {
            return this._instance;
        }
        this._instance = new LoginMgr();
        return this._instance;
    }
    //获取重定向 链接根据环境
    private get_redirect_uri(){
        return 'urn:ietf:wg:oauth:2.0:oob:auto';
        switch (Global.DEBUG) {
            case Global.DEBUG_MODE.DEV:
                return 'https://dev-h5.nustaronline.vip/';
            case Global.DEBUG_MODE.TEST:
                return 'https://test-h5.nustaronline.vip/';
            case Global.DEBUG_MODE.PRE:
                return 'https://web.nustaronline.vip/';
            case Global.DEBUG_MODE.RELEASE:
                return 'https://h5.nustargame.com/';
            default:
                return 'https://h5.nustargame.com/';
        }
    }
    //google 登录初始化
    public google_init(){
        var script = document.createElement('script');
        script.src = "https://accounts.google.com/gsi/client"
        // script.src = 'https://apis.google.com/js/api:client.js'
        script.async = true;
        document.head.appendChild(script);
        script.onload = () => {
            console.log('-----googleskd 加载完成');
        }
    }


    //Facebook 登录初始化
    public facebook_init(){
        let d = document, s = 'script', id = 'facebook-jssdk'
        var js, fjs = d.getElementsByTagName(s)[0];
        if (d.getElementById(id)) {
            console.log('-----Facebookskd 已经加载过');
            return;
        }
        js = d.createElement(s); js.id = id;
        js.async = true;
        js.src = "https://connect.facebook.net/en_US/sdk.js";
        fjs.parentNode.insertBefore(js, fjs);
        js.onload = () => {
            console.log('-----Facebookskd 加载完成');
            //FB
            window['fbAsyncInit'] = function () {
                window['FB'].init({
                    // appId: '****************',//pt id 无法使用
                    appId: '741410988858835',//nustar lemoon id
                    // 542364020202343 lemoon ID
                    // appId: '4177464032500897',//nustar 
                    // 741410988858835 正式
                    xfbml: true,
                    version: 'v23.0'
                });
            };
        }
    }
    public google_login2(){
        const gapi = window['gapi'];
        if(!gapi){
            Global.getInstance().showSimpleTip('The service is not available in the current region');
            return;
        }
        gapi.load('auth2', function() {
            gapi.auth2.init({
              client_id: '************-pkhqbtg7m0vd9sap3snpcko78iaomvgj.apps.googleusercontent.com',
              scope: 'profile email openid',
              ux_mode: 'popup',
            }).then(function(auth2) {
              // 监听登录状态变化
              auth2.isSignedIn.listen(this.signinChanged);
              
              // 获取当前用户
              if (auth2.isSignedIn.get()) {
                const user = auth2.currentUser.get();
                this.handleUser(user);
              }
            });
          });
    }
    signinChanged(isSignedIn) {
        if (isSignedIn) {
            const gapi = window['gapi'];
          const user = gapi.auth2.getAuthInstance().currentUser.get();
          this.handleUser(user);
        }
      }
      
    handleUser(user) {
        // 获取 ID Token
        const idToken = user.getAuthResponse().id_token;
        console.log("ID Token:", idToken);
        
        // 验证 Token
        // verifyTokenOnBackend(idToken);
    }
      
    signIn() {
        // gapi.auth2.getAuthInstance().signIn();
     }
      
    signOut() {
        // gapi.auth2.getAuthInstance().signOut();
    }
      
    //Google登录
    public google_login(){
        const googleSDK = window['google'];
        if(!googleSDK){
            Global.getInstance().showSimpleTip('The service is not available in the current region');
            return;
        }
        Global.getInstance().showLoading('google_login_loading');
        const client = googleSDK.accounts.oauth2.initCodeClient({
            client_id: "************-pkhqbtg7m0vd9sap3snpcko78iaomvgj.apps.googleusercontent.com",
            scope: 'https://www.googleapis.com/auth/userinfo.profile https://www.googleapis.com/auth/userinfo.email',
            ux_mode: 'popup',
            // ux_mode: 'redirect',
            redirect_uri:this.get_redirect_uri(),
            // client_id: "************-465eeuiqnjv4t40fabmkv0c35poct4dj.apps.googleusercontent.com",
            // scope: 'https://www.googleapis.com/auth/userinfo.profile https://www.googleapis.com/auth/userinfo.email',
            // ux_mode: 'popup',
            // redirect_uri: "https://www.atm27.com",
            callback: this.handleOuthResponse,
            error_callback: this.logBeforeClose
        })
        client.requestCode();
    }
    //google logout
    public google_logout(){
        const googleSDK = window['google'];
        if(!googleSDK)return;
        googleSDK.accounts.id.disableAutoSelect();
    }
    logBeforeClose() {
        console.log('------这里登录页面关闭 未成功');
        Global.getInstance().hideShowLoading('google_login_loading');
        cc.director.emit(EMIT_PARAMS.GOOGLE_SIGN_IN_ERROR)
    }
    handleOuthResponse(response) {
        // decodeJwtResponse() is a custom function defined by you
        // response.credential
        Global.getInstance().hideShowLoading('google_login_loading');
        console.log("handleCredentialResponse", JSON.stringify(response))
        if (response.error) {
            cc.director.emit(EMIT_PARAMS.GOOGLE_SIGN_IN_ERROR)
        } else {
            cc.director.emit(EMIT_PARAMS.GOOGLE_SIGN_IN_SUCCESS, response.code)
        }
    }


     //Facebook 登录
     facebook_login(){
        const FB = window['FB']
        if (typeof FB === 'undefined') {
            Global.getInstance().showSimpleTip('The service is not available in the current region');
            return;
        }
        Global.getInstance().showLoading('facebook_login_loading');
        FB.getLoginStatus(function (response) {
            if (response.status == "connected") {
                let authResponse = response.authResponse
                cc.director.emit(EMIT_PARAMS.FACEBOOK_SIGN_IN_SUCCESS,authResponse.accessToken, authResponse.userID);
                Global.getInstance().hideShowLoading('facebook_login_loading');
            } else {
                if (response.status == "unknown") {
                    FB.logout();
                    Global.getInstance().hideShowLoading('facebook_login_loading');
                }
                FB.login(function (response) {
                    if (response.authResponse) {
                        console.log("FaceBookLogin ==>", response)
                        let authResponse = response.authResponse
                        cc.director.emit(EMIT_PARAMS.FACEBOOK_SIGN_IN_SUCCESS,authResponse.accessToken, authResponse.userID);
                    } else {
                        console.log('User cancelled login or did not fully authorize.');
                        Global.getInstance().showSimpleTip('User cancelled login or did not fully authorize.');
                        cc.director.emit(EMIT_PARAMS.FACEBOOK_SIGN_IN_ERROR)
                    }
                    Global.getInstance().hideShowLoading('facebook_login_loading');
                });
            }
        });
     }

     //Facebook 登出
     facebook_logout(){
        if (typeof window['FB'] === 'undefined') { return }
        window['FB'].logout()
     }
}