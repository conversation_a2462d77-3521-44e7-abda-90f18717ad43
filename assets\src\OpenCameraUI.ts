
import { UI_PATH_DIC } from "./GlobalConstant";
import Global from "./GlobalScript";
import MoreGameManager from "./MoreGameManager";
import { showRecommendGame } from "./RecommendGame";
import BackgroundAdapter, { FIT_POLICY } from "./component/BackgroundAdapter";
import UICommon from "./component/UICommon";
import { uiManager } from "./mgr/UIManager";
import HttpUtils from "./net/HttpUtils";
import utils from "./utils/utils";

const { ccclass, property } = cc._decorator;

export function showOpenCameraUI() {
    uiManager.instance.showDialog(UI_PATH_DIC.OpenCameraUI)//未用到
}

@ccclass
export default class OpenCameraUI extends UICommon {


    onLoad() {
        cc.director.emit("showOpenCamera")
    }



    onDestroy() {

    }

    closeAction() {
        cc.director.emit("hideOpenCamera")
        this.hide();
    }

}
