<template>
  <div class="transaction-list">
    <!-- 筛选器 -->
    <div class="filters">
      <div class="filter-tabs">
        <button
          v-for="tab in tabs"
          :key="tab.key"
          class="filter-tab"
          :class="{ active: activeTab === tab.key }"
          @click="setActiveTab(tab.key)"
        >
          {{ tab.label }}
        </button>
      </div>
      
      <div class="time-filter">
        <select v-model="timeFilter" @change="handleTimeFilterChange" class="time-select">
          <option value="0">今天</option>
          <option value="1">昨天</option>
          <option value="2">最近3天</option>
          <option value="3">最近7天</option>
        </select>
      </div>
    </div>

    <!-- 交易记录列表 -->
    <div class="transaction-items">
      <div v-if="loading" class="loading-container">
        <div class="loading-spinner"></div>
        <p>加载中...</p>
      </div>

      <div v-else-if="filteredTransactions.length === 0" class="empty-state">
        <div class="empty-icon">
          <i class="icon-receipt"></i>
        </div>
        <h3>暂无记录</h3>
        <p>当前时间段内没有{{ getTabLabel(activeTab) }}记录</p>
      </div>

      <div v-else class="records-container">
        <div
          v-for="(group, date) in groupedTransactions"
          :key="date"
          class="date-group"
        >
          <div class="date-header">
            <span class="date-text">{{ formatDate(date) }}</span>
            <span class="count-text">{{ group.length }}条记录</span>
          </div>
          
          <div class="records-list">
            <TransactionItem
              v-for="record in group"
              :key="record.id"
              :record="record"
              :type="activeTab"
              @click="showDetails(record)"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 详情弹窗 -->
    <TransactionDetails
      v-if="showDetailsModal"
      :record="selectedRecord"
      :type="activeTab"
      @close="closeDetails"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useHallStore } from '@/stores/hallStore'
import TransactionItem from './TransactionItem.vue'
import TransactionDetails from './TransactionDetails.vue'
import type { TransactionRecord } from '@/types/hall'
import { FilterData, RecordTag } from '@/types/hall'

const hallStore = useHallStore()

// 响应式数据
const activeTab = ref<string>('deposit')
const timeFilter = ref<number>(0)
const loading = ref(false)
const showDetailsModal = ref(false)
const selectedRecord = ref<TransactionRecord | null>(null)

// 标签页配置
const tabs = [
  { key: 'deposit', label: '充值记录' },
  { key: 'withdraw', label: '提现记录' },
  { key: 'award', label: '奖励记录' }
]

// 计算属性
const filteredTransactions = computed(() => {
  const allRecords = hallStore.getFilteredTransactions()
  
  return allRecords.filter(record => {
    switch (activeTab.value) {
      case 'deposit':
        return record.type === 'Deposit' || 
               record.type === 'mayapay' || 
               record.type === 'mayawebpay' || 
               record.type === 'gcashwebpay' ||
               record.type === 'Adjustment'
      case 'withdraw':
        return record.type === 'Withdrawal' || 
               record.type === 'Batch Withdrawal'
      case 'award':
        return record.type === 'Reward'
      default:
        return true
    }
  })
})

const groupedTransactions = computed(() => {
  const groups: Record<string, TransactionRecord[]> = {}
  
  filteredTransactions.value.forEach(record => {
    const date = new Date(record.created_at).toDateString()
    if (!groups[date]) {
      groups[date] = []
    }
    groups[date].push(record)
  })
  
  // 按日期排序（最新的在前）
  const sortedGroups: Record<string, TransactionRecord[]> = {}
  Object.keys(groups)
    .sort((a, b) => new Date(b).getTime() - new Date(a).getTime())
    .forEach(date => {
      sortedGroups[date] = groups[date].sort((a, b) => 
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      )
    })
  
  return sortedGroups
})

// 方法
function setActiveTab(tab: string) {
  activeTab.value = tab
}

function handleTimeFilterChange() {
  hallStore.setFilterTimeType(timeFilter.value)
  loadTransactions()
}

function getTabLabel(tab: string): string {
  const tabMap: Record<string, string> = {
    'deposit': '充值',
    'withdraw': '提现',
    'award': '奖励'
  }
  return tabMap[tab] || ''
}

function formatDate(dateString: string): string {
  const date = new Date(dateString)
  const today = new Date()
  const yesterday = new Date(today)
  yesterday.setDate(yesterday.getDate() - 1)
  
  if (date.toDateString() === today.toDateString()) {
    return '今天'
  } else if (date.toDateString() === yesterday.toDateString()) {
    return '昨天'
  } else {
    return date.toLocaleDateString('zh-CN', {
      month: 'long',
      day: 'numeric',
      weekday: 'long'
    })
  }
}

function showDetails(record: TransactionRecord) {
  selectedRecord.value = record
  showDetailsModal.value = true
}

function closeDetails() {
  showDetailsModal.value = false
  selectedRecord.value = null
}

async function loadTransactions() {
  loading.value = true
  try {
    // TODO: 实现API调用
    // const records = await api.getTransactions({
    //   type: activeTab.value,
    //   timeFilter: timeFilter.value
    // })
    // hallStore.setTransactionRecords(records)
  } catch (error) {
    console.error('Failed to load transactions:', error)
  } finally {
    loading.value = false
  }
}

// 监听标签页变化
watch(activeTab, () => {
  loadTransactions()
})

// 生命周期
onMounted(() => {
  loadTransactions()
})
</script>

<style scoped>
.transaction-list {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  backdrop-filter: blur(10px);
}

.filters {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  gap: 16px;
}

.filter-tabs {
  display: flex;
  gap: 8px;
}

.filter-tab {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
}

.filter-tab:hover {
  background: rgba(255, 255, 255, 0.15);
  color: rgba(255, 255, 255, 0.9);
}

.filter-tab.active {
  background: #4CAF50;
  color: white;
  font-weight: bold;
}

.time-filter {
  flex-shrink: 0;
}

.time-select {
  padding: 8px 12px;
  border: none;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 14px;
  cursor: pointer;
  backdrop-filter: blur(10px);
}

.time-select option {
  background: #333;
  color: white;
}

.transaction-items {
  min-height: 300px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: rgba(255, 255, 255, 0.8);
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid #4CAF50;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-state h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: bold;
}

.empty-state p {
  margin: 0;
  font-size: 14px;
  opacity: 0.7;
}

.records-container {
  space-y: 16px;
}

.date-group {
  margin-bottom: 20px;
}

.date-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  margin-bottom: 8px;
}

.date-text {
  font-weight: bold;
  color: white;
  font-size: 14px;
}

.count-text {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

.records-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .transaction-list {
    padding: 16px;
  }
  
  .filters {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .filter-tabs {
    justify-content: center;
  }
  
  .filter-tab {
    flex: 1;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .filter-tabs {
    flex-direction: column;
  }
  
  .date-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
</style>
