import UICommon from "../component/UICommon";
import { DEEP_INDEXZ, UI_PATH_DIC } from "../GlobalConstant";
import Global from "../GlobalScript";
import PromoDetial from "../hall/PromoDetial";
import { SERVICE_TYPE, serviceMgr } from "../mgr/serviceMgr";
import { uiManager } from "../mgr/UIManager";
import HttpUtils from "../net/HttpUtils";
import utils from "../utils/utils";

const {ccclass, property} = cc._decorator;
@ccclass
export default class Vip extends UICommon {
    @property(cc.Node)
    vipIcon:cc.Node = null;
 
    @property(cc.Label)
    labNextRefreshTime:cc.Label = null;
    @property(cc.Label)
    labExceed:cc.Label = null;
    @property(cc.Label)
    labCycleTime:cc.Label = null;
    @property(cc.Node)
    betBar:cc.Node = null;
    @property(cc.Label)
    labBetRange:cc.Label = null;
    @property(cc.Sprite)
    hCashbackIcon:cc.Sprite = null;
    @property(cc.Label)
    labCashback:cc.Label = null;
    @property(cc.Sprite)
    dedicatedIcon:cc.Sprite = null;
    @property(cc.Sprite)
    priorityIcon:cc.Sprite = null;
    @property(cc.Node)
    btnDetailNode:cc.Node = null;
    @property(cc.Node)
    btnCustomerNode:cc.Node = null;
    @property(cc.Node)
    btnWithDrawalNode:cc.Node = null;
    @property(cc.Node)
    progressBg:cc.Node = null;

    //红点 客服
    @property(cc.Node)
    custom_red:cc.Node = null;

    @property([cc.SpriteFrame])
    vipIconFrames:cc.SpriteFrame[] = [];
   


    activityDePrefab: cc.Prefab = null;

    curBet:number = 0;//用户当前投注额
    totalBet:number = 0;//当前最低晋级投注额

    start_time = null;//vip cashback活动开始时间
    end_time = null;//vip cashback活动结束时间
    onLoad () {
        this.initBetData();
        this.initVIPCashBackData();
    }

    init(args) {
        if (args.type && args.type == "unvip_user") {//非VIP用户
            this.vipIcon.getComponent(cc.Sprite).spriteFrame = this.vipIconFrames[0];
            this.labNextRefreshTime.string = "(Keep your VIP rank) Next refresh time " + utils.getVIPRefreshTime();
            //获取当前月份的天数
            const currentDate = new Date();
            const year = currentDate.getFullYear();
            const month = currentDate.getMonth() + 1;//getMonth() 从0开始计数,因此要+1

            const dayOfMonth = currentDate.getDate();
            const isWithinFirstHalf = dayOfMonth >= 1 && dayOfMonth <= 15;
            const isWithinSecondHalf = dayOfMonth >= 16 && dayOfMonth <= 31;

            const daysInCurMonth = utils.getDaysInMonth(year, month);
            //当前时间周期(1-15号)
            if (isWithinFirstHalf) {
                let startDay = 1;
                let endDay = 15;
                this.labCycleTime.string = `${year}/${month}/${startDay} ~ ${year}/${month}/${endDay}`;
            } else if (isWithinSecondHalf) {//16号-daysInCurMonth(当月天数)
                let startDay = 16;
                let endDay = daysInCurMonth;
                this.labCycleTime.string = `${year}/${month}/${startDay} ~ ${year}/${month}/${endDay}`;
            }
            // this.labCycleTime.node.color = cc.color(255,255,255);

          
            
            //labExceed
            this.labExceed.node.color = cc.color(255,255,255);
            //labBetRange
            // this.labBetRange.node.color = cc.color(125,125,125);
            
            //vip benefits
            //禁用Detail、Customer Service、Withdrawal按钮
            this.btnDetailNode.getComponent(cc.Button).enabled = false;
            this.btnCustomerNode.getComponent(cc.Button).enabled = false;
            this.btnWithDrawalNode.getComponent(cc.Button).enabled = false;
            this.btnDetailNode.parent.opacity = 128;
            this.btnCustomerNode.parent.opacity = 128;
            this.btnWithDrawalNode.parent.opacity = 128;
        } else if (args.type && args.type == "vip_user") {//VIP用户
            this.vipIcon.getComponent(cc.Sprite).spriteFrame = this.vipIconFrames[1];
            this.labNextRefreshTime.string = "(Level Up) Next refresh time " + utils.getVIPRefreshTime();

            let vip_starttime = Global.getInstance().userdata.vip_start;
            let vip_endtime = Global.getInstance ().userdata.vip_end;
            let startTime = utils.timestampToTime2(vip_starttime);
            let endTime = utils.timestampToTime2(vip_endtime);
            //VIP时间周期
            this.labCycleTime.string = `${startTime} ~ ${endTime}`;
            // this.labCycleTime.node.color = cc.color(255,255,255);

            
            //labExceed
            this.labExceed.node.color = cc.color(94,47,21);
            //labBetRange
            // this.labBetRange.node.color = cc.color(255,255,255);
            
            //vip benefits
            //显示Detail、Customer Service、Withdrawal按钮
            this.btnDetailNode.active = true;
            this.btnCustomerNode.active = true;
            this.btnWithDrawalNode.active = true;
            this.btnDetailNode.getComponent(cc.Button).enabled = true;
            this.btnCustomerNode.getComponent(cc.Button).enabled = true;
            this.btnWithDrawalNode.getComponent(cc.Button).enabled = true;
            this.btnDetailNode.opacity = 255;
            this.btnCustomerNode.opacity = 255;
            this.btnWithDrawalNode.opacity = 255;
        }
    }

    start () {
        //注册一下 客服红点
        cc.director.on("update_home_widget",this.update_red,this);
        this.update_red();
        let page = this.node.getChildByName("page").getComponent(cc.PageView);
        page.node.on('page-turning', ()=>{
            let curIndex = page.getCurrentPageIndex();
            if (curIndex != 1) {
                this.node.destroy();
            }
        }, this);
        let tempnode = this.node.getChildByName("temp");
        page.insertPage(cc.instantiate(tempnode),0);
        page.scrollToPage(1,0.01);
    }
    update_red(){
        if(Global.getInstance().custom_msg > 0){
            this.custom_red.active = true
        }else{
            this.custom_red.active = false
        }
    }

    initBetData() {
        HttpUtils.getInstance().post(1, 3, this, "/avt/api/activity/rebate-progress", {
            token: Global.getInstance().token,
        }, (response) => {
            if (!this.node || !this.node.isValid) {
                return;
            }
            if (response.data) {
                this.curBet = parseFloat(response.data.score);//当前投注额
                this.totalBet = parseFloat(response.data.bet_line);//当前最低晋级投注额
                if (this.curBet > this.totalBet) this.curBet = this.totalBet;
                let curValidBet = utils.formatNumberWithCommas(this.curBet, 0);
                let totalValidBet = utils.formatNumberWithCommas(this.totalBet, 0);
                this.labBetRange.string = `${curValidBet} / ${totalValidBet}`;

                let total_bet = utils.formatNumberWithCommas(this.totalBet, 0);
                this.labExceed.string = `Betting amount exceeding ₱${total_bet} within`;

                let rangeNum = this.curBet / this.totalBet;
                this.betBar.getComponent(cc.Sprite).fillRange = rangeNum;
            }
        });
    }

    /**Highest cashback */
    initVIPCashBackData() {
        HttpUtils.getInstance().post(1, 3, this, "/avt/api/activity/rebate-conf", {
            token: Global.getInstance().token,
            config_type: 4,//1:普通返水 4:vip返水
        }, (response) => {
            if (!this.node || !this.node.isValid) {
                return;
            }
            if (response.data && response.data.config && response.data.config.length > 0) {
                let config = response.data.config[0][1];
                this.labCashback.string = "Highest cashback "+config.rate+"%";
            }
        });
    }

    /**点击进入VIP详情页*/
    onClickVIPDetail() {
        uiManager.instance.showDialog(UI_PATH_DIC.VipDetail,null,null,DEEP_INDEXZ.ACTIVITY_CONTENT);
    }

    /**跳转至VIP专属反水活动页面*/
    onClickCashBackDetail() {
        this.vipCashbackPeriod();
    }

    /**vip cashback活动周期时间 */
    vipCashbackPeriod() {
        let params = {
            token:Global.getInstance().token
        };
        HttpUtils.getInstance().post(1, 3, this, "/avt/api/banner/activity/list", params, (response) => {
            if (!this.node || !this.node.isValid) {
                return;
            }
            if (response.data && response.data.banner) {
                let bannerList = response.data.banner;
                let isshow = true
                for (let i = 0; i < bannerList.length; i++) {
                    let activityList = bannerList[i].activity_list;
                    if (activityList == 4) {
                        this.start_time = utils.timeToTimestamp(bannerList[i].start_at);
                        this.end_time = utils.timeToTimestamp(bannerList[i].end_at);
                        //点击前往VIP专属返水活动页面
                        this.showActivityView();
                        isshow = false;
                    }
                }
                if(isshow){
                    Global.getInstance().showSimpleTip('Activity not activated!')
                }
            }
        });
    }

    showActivityView() {
        uiManager.instance.loadPrefabByLoading("prefab/hall/cashback_vipview",cc.Prefab,(finish, total, item) => {
        }, (err, prefab: any) => {
                if (err) {
                    return;
                }
                let pop = cc.instantiate(prefab);
                pop.parent = Global.getInstance().popNode;
                this.node.destroy();
        });
    }

    /**打开三方客服页面 */
    onClickCustomer() {
        serviceMgr.instance.show_achat(SERVICE_TYPE.vip);
    }

    /**跳转至提现页面 */
    onClickWithDrawal() {
        Global.getInstance().clickToWithdrawal();
    }

    // update (dt) {}
    protected onDestroy(): void {
        // console.log("Fiee:==============>Vip:onDestroy");
    }
}
