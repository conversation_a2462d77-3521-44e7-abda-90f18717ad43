// 通用类型定义

// Banner数据接口
export interface BannerData {
  id: number
  title: string
  image_url: string
  link_url?: string
  sort: number
  updated_at: string
  activity_type?: number
  start_time?: string
  end_time?: string
  target?: '_blank' | '_self'
  is_active: boolean
}

// 活动数据接口
export interface ActivityData {
  id: number
  name: string
  type: number
  start_at: string
  end_at: string
  channel: string
  page: string
  content: string
  image_url?: string
  banner_url?: string
  rules?: string
  rewards?: string
  status: number
  created_at: string
  updated_at: string
}

// 活动类型枚举
export enum ActivityType {
  SIGNBIND = 1,              // 签到绑定活动
  FIRSTDEPOSIT = 2,          // 首次充值活动
  CASHBACK = 3,              // 返水活动
  VIPCASHBACK = 4,           // VIP返水活动
  RANK = 5,                  // 排行榜活动
  SPIN = 6,                  // 转盘活动
  RANKJILI = 7,              // JILI排行榜
  VALENTINE = 8,             // 情人节活动
  WEEKLY = 9,                // 周薪活动
  PP_DAILY_WINS = 10,        // PP每日获胜
  LATE_NIGHT_CASHBACK = 11,  // 深夜返水
  FC_FREE_SPIN = 12,         // FC免费旋转
  GET_EASTER_BONUS = 13,     // 复活节奖励
  YELLOW_BAT = 14,           // 黄蝙蝠活动
  JILI_FREE_SPIN = 15,       // JILI免费旋转
  FC_FREE_SPIN2 = 16,        // FC免费旋转2
  CASHBACK_DETIALS = 100,    // 返水详情
  VIP_CASHBACK_DETIALS = 101 // VIP返水详情
}

// 用户余额接口
export interface UserBalance {
  total: number
  available: number
  frozen: number
  currency: string
  last_updated: string
}

// 游戏厂商信息
export interface GameProvider {
  id: number
  name: string
  code: string
  logo_url: string
  is_active: boolean
  sort_order: number
}

// 游戏类型枚举
export enum GameType {
  CASINO = 'casino',
  SLOTS = 'slots',
  POKER = 'poker',
  BINGO = 'bingo',
  ARCADE = 'arcade',
  SPORTS = 'sports',
  LIVE = 'live',
  FISHING = 'fishing',
  LOTTERY = 'lottery'
}

// 游戏状态枚举
export enum GameStatus {
  ACTIVE = 1,
  MAINTENANCE = 2,
  DISABLED = 3
}

// 游戏标签枚举
export enum GameTag {
  REGULAR = 0,
  HOT = 1,
  NEW = 2,
  RECOMMENDED = 3
}

// 跳转类型枚举
export enum JumpType {
  GAME = 'game',
  ACTIVITY = 'activity',
  PROMO = 'promo',
  EXTERNAL = 'external',
  INTERNAL = 'internal'
}

// 跳转参数接口
export interface JumpParams {
  jumpType: JumpType
  gameId?: string
  gameType?: string[]
  providerList?: string[]
  activityId?: string
  url?: string
  target?: '_blank' | '_self'
}

// 通知类型枚举
export enum NotificationType {
  SYSTEM = 'system',
  ACTIVITY = 'activity',
  REWARD = 'reward',
  TRANSACTION = 'transaction',
  GAME = 'game',
  SECURITY = 'security'
}

// 通知状态枚举
export enum NotificationStatus {
  UNREAD = 0,
  READ = 1,
  ARCHIVED = 2
}

// 通知信息接口
export interface NotificationInfo {
  id: string
  type: NotificationType
  title: string
  content: string
  image_url?: string
  action_url?: string
  status: NotificationStatus
  created_at: string
  read_at?: string
  expires_at?: string
}

// 邮件信息接口
export interface MailInfo {
  id: string
  from_user_id: number
  to_user_id: number
  title: string
  content: string
  attachment?: {
    type: 'reward' | 'item'
    amount?: number
    item_id?: string
    item_name?: string
  }
  is_read: boolean
  is_claimed: boolean
  created_at: string
  read_at?: string
  claimed_at?: string
  expires_at?: string
}

// 设置项接口
export interface SettingItem {
  key: string
  label: string
  type: 'boolean' | 'select' | 'range' | 'text'
  value: any
  options?: Array<{ label: string; value: any }>
  min?: number
  max?: number
  step?: number
  description?: string
  category: string
}

// 用户设置接口
export interface UserSettings {
  sound_enabled: boolean
  music_enabled: boolean
  vibration_enabled: boolean
  language: string
  currency: string
  timezone: string
  notifications: {
    email: boolean
    sms: boolean
    push: boolean
    activity: boolean
    transaction: boolean
    security: boolean
  }
  privacy: {
    show_online_status: boolean
    show_game_history: boolean
    allow_friend_requests: boolean
  }
  display: {
    theme: 'light' | 'dark' | 'auto'
    font_size: 'small' | 'medium' | 'large'
    animation_enabled: boolean
  }
}

// 排行榜类型枚举
export enum LeaderboardType {
  DAILY = 'daily',
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
  ALL_TIME = 'all_time'
}

// 排行榜项接口
export interface LeaderboardItem {
  rank: number
  user_id: string
  username: string
  avatar?: string
  score: number
  reward?: number
  change?: number // 排名变化
}

// 排行榜数据接口
export interface LeaderboardData {
  type: LeaderboardType
  title: string
  description?: string
  start_time: string
  end_time: string
  total_participants: number
  user_rank?: number
  user_score?: number
  items: LeaderboardItem[]
  rewards: Array<{
    rank_start: number
    rank_end: number
    reward_amount: number
    reward_type: string
  }>
}

// 转盘配置接口
export interface SpinWheelConfig {
  id: string
  is_start: number
  left_times: number
  real_left_times: number
  cost_per_spin: number
  rewards: Array<{
    id: string
    name: string
    type: 'coins' | 'bonus' | 'free_spins' | 'item'
    amount: number
    probability: number
    icon?: string
  }>
  rules?: string
}

// 转盘结果接口
export interface SpinWheelResult {
  reward_id: string
  reward_name: string
  reward_type: string
  reward_amount: number
  remaining_times: number
  new_balance: number
}

// 错误码枚举
export enum ErrorCode {
  SUCCESS = 0,
  UNKNOWN_ERROR = 1000,
  NETWORK_ERROR = 1001,
  TIMEOUT_ERROR = 1002,
  AUTH_ERROR = 2000,
  TOKEN_EXPIRED = 2001,
  PERMISSION_DENIED = 2002,
  VALIDATION_ERROR = 3000,
  INSUFFICIENT_BALANCE = 3001,
  TASK_NOT_FOUND = 3002,
  TASK_EXPIRED = 3003,
  TASK_COMPLETED = 3004,
  GAME_MAINTENANCE = 4000,
  GAME_NOT_FOUND = 4001,
  PROVIDER_ERROR = 4002
}

// 错误信息映射
export const ERROR_MESSAGES = {
  [ErrorCode.SUCCESS]: '操作成功',
  [ErrorCode.UNKNOWN_ERROR]: '未知错误',
  [ErrorCode.NETWORK_ERROR]: '网络连接失败',
  [ErrorCode.TIMEOUT_ERROR]: '请求超时',
  [ErrorCode.AUTH_ERROR]: '认证失败',
  [ErrorCode.TOKEN_EXPIRED]: '登录已过期，请重新登录',
  [ErrorCode.PERMISSION_DENIED]: '权限不足',
  [ErrorCode.VALIDATION_ERROR]: '数据验证失败',
  [ErrorCode.INSUFFICIENT_BALANCE]: '余额不足',
  [ErrorCode.TASK_NOT_FOUND]: '任务不存在',
  [ErrorCode.TASK_EXPIRED]: '任务已过期',
  [ErrorCode.TASK_COMPLETED]: '任务已完成',
  [ErrorCode.GAME_MAINTENANCE]: '游戏维护中',
  [ErrorCode.GAME_NOT_FOUND]: '游戏不存在',
  [ErrorCode.PROVIDER_ERROR]: '游戏厂商错误'
} as const

// 页面状态枚举
export enum PageStatus {
  LOADING = 'loading',
  SUCCESS = 'success',
  ERROR = 'error',
  EMPTY = 'empty'
}

// 操作状态枚举
export enum OperationStatus {
  IDLE = 'idle',
  PENDING = 'pending',
  SUCCESS = 'success',
  ERROR = 'error'
}

// 动画类型枚举
export enum AnimationType {
  FADE = 'fade',
  SLIDE_LEFT = 'slide-left',
  SLIDE_RIGHT = 'slide-right',
  SLIDE_UP = 'slide-up',
  SLIDE_DOWN = 'slide-down',
  SCALE = 'scale',
  BOUNCE = 'bounce'
}

// 存储键常量
export const STORAGE_KEYS = {
  AUTH_TOKEN: 'auth_token',
  USER_ID: 'user_id',
  USER_INFO: 'user_info',
  USER_SETTINGS: 'user_settings',
  LANGUAGE: 'language',
  THEME: 'theme',
  GAME_HISTORY: 'game_history',
  LIKED_GAMES: 'liked_games',
  LAST_LOGIN: 'last_login',
  DEVICE_ID: 'device_id'
} as const

// 事件名称常量
export const EVENT_NAMES = {
  USER_LOGIN: 'user:login',
  USER_LOGOUT: 'user:logout',
  BALANCE_UPDATE: 'balance:update',
  GAME_START: 'game:start',
  GAME_END: 'game:end',
  TASK_COMPLETE: 'task:complete',
  REWARD_RECEIVED: 'reward:received',
  NOTIFICATION_RECEIVED: 'notification:received',
  NETWORK_STATUS_CHANGE: 'network:status_change'
} as const

// 默认配置常量
export const DEFAULT_CONFIG = {
  API_TIMEOUT: 10000,
  REFRESH_INTERVAL: 30000,
  MAX_RETRY_ATTEMPTS: 3,
  PAGINATION_SIZE: 20,
  ANIMATION_DURATION: 300,
  DEBOUNCE_DELAY: 300,
  THROTTLE_DELAY: 1000
} as const
