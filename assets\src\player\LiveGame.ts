import { ALL_APP_SOURCE_CONFIG } from "../Config";
import { E_PAGE_TYPE, EMIT_PARAMS, UI_PATH_DIC } from "../GlobalConstant";
import Global from "../GlobalScript";
import { uiManager } from "../mgr/UIManager";
import { LOGIN_WAY } from "../hall/Hall";
import GameControl from "../GameControl";
import { AutoPopMgr } from "../mgr/AutoPopMgr";
import utils from "../utils/utils";

const {ccclass, property} = cc._decorator;
@ccclass
export default class LiveGame extends cc.Component {
    //拳皇 head 
    @property(cc.Node)
    live_head_pic:cc.Node = null;
    //拳皇 直播按钮 
    @property(cc.Node)
    live_btn_node:cc.Node = null;
    //manny_label
    @property(cc.Label)
    manny_mlv: cc.Label = null;
    //mario_label
    @property(cc.Label)
    mario_mlv: cc.Label = null;

    //直播 开始时间_label
    @property(cc.Label)
    live_start_time: cc.Label = null;

    //缺省 直播 图片
    @property(cc.Node)
    default_vedio_pic:cc.Node = null;
    @property(cc.Node)
    livePageView: cc.Node = null;
    @property([cc.Button])
    liveTagBtns: cc.Button[] = [];
    @property([cc.Node])
    liveGaps: cc.Node[] = [];
    @property(cc.Button)
    liveClickPlay: cc.Button = null;
    @property(cc.Node)
    gameLivetagBg: cc.Node = null;
    @property([cc.Label])
    gameLivetaglabs: cc.Label[] = [];
    @property(cc.Label)
    labGameLiveTitle: cc.Label = null;
    @property(cc.Sprite)
    spIcon: cc.Sprite = null;

    //live 的标识
    @property(cc.Node)
    live_sprite_tips: cc.Node = null;

    //banner 的滑动标识
    @property(cc.Node)
    bannerSliderModel: cc.Node = null;
    //保存banner分页滑块
    sliderItems = [];
    //3张视频 预览图 缓存使用 最多3张
    vedio3SpriteFrames: cc.SpriteFrame[] = [null,null,null];

    flvVideo0: HTMLVideoElement = null;
    flvVideo1: HTMLVideoElement = null;
    flvVideo2: HTMLVideoElement = null;
    hlsVideo0: HTMLVideoElement = null;
    hlsVideo1: HTMLVideoElement = null;
    hlsVideo2: HTMLVideoElement = null;
    flvPlayer = null;
    hlsPlayer = null;
    topPercent = null;
    leftPercent = null;
    curLiveVideoIndex = 0;
    nextLiveVideoIndex = 0;
    livegameData:any = null;
    isLoadComplete = [false,false,false];

    isScrolling = false;//标记变量，表示是否在处理滑动
    startPos = null;

    videoType = '.mp4';

    open_pre1 = true;//打开预先下载一张图片 优化内容
    is_load_other_pics = false;//默认剩余的图片还没下载等待通知

    video_hided = false;//用于视频隐藏标识 防止 自动更换的时候 第二个视频 播放

    default_video_width = 1010;
    default_video_height = 556;

    update_num = 0;//这个刷新次数 防止视频穿透 0.5秒检查一次

    onLoad() {
        this.liveClickPlay.node.on(cc.Node.EventType.TOUCH_START, this.onTouchLiveStart, this);
        this.liveClickPlay.node.on(cc.Node.EventType.TOUCH_MOVE, this.onTouchLiveMove, this);
        this.liveClickPlay.node.on(cc.Node.EventType.TOUCH_END, this.onTouchLiveEnd, this);
        this.leftPercent = (((cc.winSize.width - this.default_video_width) / 2) / cc.winSize.width)*100+"%";
        cc.game.on("screenResize", () => {
            this.resize()
        })
        // this.resize()
        cc.director.on("LiveGame_TopPos", this.updateLiveTopPos, this);
        cc.director.on("Show_LiveVideo", this.showLiveVideo, this);
        cc.director.on("Hide_LiveVideo", this.hideAllLiveVideo, this);
        cc.director.on("Remove_Video", this.removeVideo, this);
        cc.director.on(EMIT_PARAMS.APSPORTS_DATA_CHANGE,this.change_apsports_hdp,this);
        cc.director.on(EMIT_PARAMS.APSPORTS_GOPLAY,this.onClickLivePlay_apsports,this)
        this.node.on('hall_load_others', this.load_video_other, this);
    }

    start() {
        
    }

    initData() {
        let self = this;
        setTimeout(() => {
            self.getLiveGame(()=>{
                self.initLiveTagBtns();
                self.initLiveGameTitle();
                if(!self.open_pre1){
                    self.init_video()
                }else{
                    self.check_can_load_newres()
                }
                self.clickLiveTagBtn(null, 0)
                //轮播Live
                self.initCarouselLiveGame();
            })
        }, 50);
    }
    //改变直播实时赔率
    change_apsports_hdp(){
        const lv_data = Global.getInstance().ap_sports_data;
        if(!!lv_data && lv_data.length > 0){
            const leagues = lv_data[0].leagues;
            if(leagues && leagues.length){
                const event = leagues[0].events;
                if(event && event.length > 0){  
                    const event1 = event[0]
                    this.manny_mlv.string = event1?.moneyline?.home || '--';
                    this.mario_mlv.string = event1?.moneyline?.away || '--';
                    const start_time = event1?.starts;
                    let c_show = Global.getInstance().get_apsports_starttime(start_time);
                    this.live_start_time.string = c_show;
                }
            }
        }
    }
    //循环查看是否 可以加载视频
    check_can_load_newres(){
        let self = this;
        setTimeout(() => {
            if(!AutoPopMgr.has_pops()){
                self.load_video_other()
            }else{
                self.check_can_load_newres()
            }
        }, 6000);
    }
    //后加载视频 等通知
    load_video_other(){
        if(this.is_load_other_pics)return;
        this.is_load_other_pics = true;//标记一下 已经加载
        this.init_video()
    }
    updateLiveTopPos(per){
        if(this.livegameData && this.livegameData.length > 0){
            for(let index = 0; index < this.livegameData.length; index++){
                const videoElement = document.getElementById('video'+index);
                const hlsVideoElement = document.getElementById('hlsvideo'+index);
                if(videoElement){
                    videoElement.style.top = per*100+"%";
                }
                if(hlsVideoElement){
                    hlsVideoElement.style.top = per*100+"%";
                }
            }
        }
    }

    resize() {
        if (!this.node || !cc.isValid(this.node)) return
        let curIndex = this.curLiveVideoIndex;
        const videoElement = document.getElementById('video'+curIndex);
        const hlsVideoElement = document.getElementById('hlsvideo'+curIndex);
        if (cc.view.getDesignResolutionSize().width < cc.winSize.width) {
            if(videoElement) {
                videoElement.style.width = cc.view.getDesignResolutionSize().width/cc.winSize.width*100 + "%";
                videoElement.style.left = (cc.winSize.width/2-cc.view.getDesignResolutionSize().width/2)/cc.winSize.width*100+"%";
            }
            if(hlsVideoElement) {
                hlsVideoElement.style.width = cc.view.getDesignResolutionSize().width/cc.winSize.width*100 + "%";
                hlsVideoElement.style.left = (cc.winSize.width/2-cc.view.getDesignResolutionSize().width/2)/cc.winSize.width*100+"%";
            }
        } else {
            if(videoElement){
                videoElement.style.width = cc.winSize.width/cc.view.getDesignResolutionSize().width*100 + "%";
                videoElement.style.left = (cc.view.getDesignResolutionSize().width/2-cc.winSize.width/2)/cc.view.getDesignResolutionSize().width*100+"%";
            }
            if(hlsVideoElement){
                hlsVideoElement.style.width = cc.winSize.width/cc.view.getDesignResolutionSize().width*100 + "%";
                hlsVideoElement.style.left = (cc.view.getDesignResolutionSize().width/2-cc.winSize.width/2)/cc.view.getDesignResolutionSize().width*100+"%";
            }
        }
        this.leftPercent = (((cc.winSize.width - cc.view.getDesignResolutionSize().width) / 2) / cc.winSize.width)*100+"%"; 
    }

    /**获取直播游戏 */
    getLiveGame(cb?) {
        let livedata = Global.getInstance().getLiveGame();
        // console.log('-------直播数据流格式查看:',JSON.stringify(livedata))
        //[{"id":3,"url":"https://cph-p2p-msl.akamaized.net/hls/live/2000341/test/master.m3u8","sort":"6","cover_picture":"images/67122185f7e6171f1ec0d9ce85737a92.jpeg","game_description":"Peraphy Time","duration":"6","game_id":"2","game_name":"Peraphy Time"},
        // {"id":4,"url":"http://zhibo.hkstv.tv/livestream/mutfysrq.flv","sort":"2","cover_picture":"images/5abed9f84dc815acc4eeadd404302676.jpeg","game_description":"Flirting Scholar","duration":"4","game_id":"100","game_name":"Flirting Scholar"},
        // {"id":5,"url":"http://playertest.longtailvideo.com/adaptive/bipbop/gear4/prog_index.m3u8","sort":"4","cover_picture":"images/38066e3ec5d76701e7badd32af135d40.jpeg","game_description":"Pinata Wins","duration":"5","game_id":"101","game_name":"Pinata Wins"}]
        if (livedata.length > 0) {
            this.livePageView.active = true;
            livedata.sort((a, b) => {
                if (a.sort != b.sort){ //按sort值从大到小
                    return b.sort - a.sort
                } else { //sort值相同按照首字母从小到大排序
                    if (a.game_name && b.game_name) {
                        return a.game_name.localeCompare(b.game_name);
                    }
                } 
            });
            this.livegameData = livedata.length > 3 ? livedata.slice(0, 3) : livedata;
            //缓存所有的 视频 默认pic
            this.getAllCoverLiveGameImg();
            if (cb) cb();   
        } else {
            this.livePageView.active = false;
            let gamescene = cc.director.getScene().getComponentInChildren(GameControl);
            gamescene.mHall.livePageView.active = false;
        }
    }

    /**初始化直播tag按钮 */
    initLiveTagBtns(){
        const gameDataLength = this.livegameData.length;
        // 先隐藏所有gap和tag按钮
        this.liveGaps[0].active = this.liveGaps[1].active = false;
        this.liveTagBtns.forEach(btn => btn.node.active = false);
        this.gameLivetaglabs.forEach(label => label.node.active = false);

        // 根据 livegameData 的长度处理不同的显示逻辑
        if (gameDataLength >= 3) {
            this.liveGaps[0].active = this.liveGaps[1].active = true;
            this.liveTagBtns.forEach((btn, index) => {
                const gamename = this.trimLiveGameName(this.livegameData[index].game_name.split("|")[0]);
                this.liveTagBtns[index].node.active = true;
                this.gameLivetaglabs[index].node.active = true;
                this.gameLivetaglabs[index].string = gamename;
            });
        } else if (gameDataLength === 2) {
            this.liveGaps[0].x = 0;
            this.liveGaps[0].active = true;
            this.liveTagBtns[0].node.active = this.liveTagBtns[1].node.active = true;
            this.gameLivetaglabs[0].node.active = this.gameLivetaglabs[1].node.active = true;

            // 设置按钮宽度和位置
            this.setupTagBtnLayout(2);
            // 更新标签文本
            [0, 1].forEach(index => {
                const gamename = this.trimLiveGameName(this.livegameData[index].game_name);
                this.gameLivetaglabs[index].string = gamename;
            });

            // 隐藏第三个按钮
            this.liveTagBtns[2].node.active = false;
            this.gameLivetaglabs[2].node.active = false;
        } else if (gameDataLength === 1) {
            this.liveTagBtns[0].node.active = true;
            this.gameLivetaglabs[0].node.active = true;

            // 设置第一个按钮的宽度和位置
            this.liveTagBtns[0].node.width = this.gameLivetagBg.width;
            this.gameLivetaglabs[0].node.x = 0;
            this.liveTagBtns[0].node.x = this.gameLivetagBg.x;

            // 更新标签文本
            const gamename = this.trimLiveGameName(this.livegameData[0].game_name);
            this.gameLivetaglabs[0].string = gamename;
        }
    }

    // 提取处理视频游戏名称的公共函数
    trimLiveGameName(livegamename: string): string {
        if (livegamename.length > 13) {
            return livegamename.slice(0, 13) + "...";
        }
        return livegamename;
    }

    // 用于设置标签按钮的布局
    setupTagBtnLayout(totalBtns: number) {
        const widthPerBtn = this.gameLivetagBg.width / totalBtns;
        this.liveTagBtns.forEach((btn, index) => {
            if (index < totalBtns) {
                btn.node.width = widthPerBtn;
                btn.node.x = (index - 0.5) * widthPerBtn;
                this.gameLivetaglabs[index].node.x = btn.node.x;
            }
        });
    }

    /**初始化Live Title*/
    initLiveGameTitle() {
        if (this.livegameData.length != 0) {
            if (!this.livegameData[0].game_description) this.labGameLiveTitle.string = ""; 
            else this.labGameLiveTitle.string = this.livegameData[0].game_description;
        }
    }
    /**水平居中分布分页滑块 */
    layoutSliderItems(slideritems) {
        const spacing = 40;
        const count = slideritems.length;
        if (count === 0) return;
        // 计算起始点，使物体在父节点内水平居中
        const totalWidth = (count - 1) * spacing;
        let startX = -totalWidth / 2;
        for (let i = 0; i < count; i++) {
            const child = slideritems[i];
            child.x = startX + i * spacing;
        }
    }
    /**设置当前分页标志 */
    setCurIndicator(curIndex) {
        for (let idx = 0; idx < this.sliderItems.length; idx++) {
            let sliderModel = this.sliderItems[idx];
            let slider = utils.getChildByPath(sliderModel, "slider_dot.slider");
            let dot = utils.getChildByPath(sliderModel, "slider_dot.dot");
            if(dot && slider && idx == curIndex) {
                dot.active = false;
                slider.active = true;
            } else {
                dot.active = true;
                slider.active = false;
            } 
        }
    }
    /**Live视频未加载时显示cover_picture */
    getAllCoverLiveGameImg() {
        if(!this.livegameData || this.livegameData.length === 0) return;
        this.livegameData.forEach((data, index)=>{
            if(!data.cover_picture){
                this.showDefaultCover();
            }else{
                const url = this.getCoverImageUrl(data.cover_picture);
                Global.getInstance().loadImgFromUrl_V2(url, (loaded, image) => {
                    this.onImageLoadCallback(loaded, image, index)
                },this.spIcon);
            }
            //banner分页滑块标志
            let sliderItemModel = cc.instantiate(this.bannerSliderModel);
            sliderItemModel.parent = this.node;
            this.sliderItems.push(sliderItemModel);
            sliderItemModel.position = new cc.Vec3(index*50,-255,0);
            this.layoutSliderItems(this.sliderItems);
            //默认第一页
            let slider = utils.getChildByPath(this.sliderItems[0], "slider_dot.slider");
            let dot = utils.getChildByPath(this.sliderItems[0], "slider_dot.dot"); 
            if(slider && dot){
                slider.active = true;
                dot.active = false;
            }
        });
    }

    //显示默认封面 
    showDefaultCover(){
        this.spIcon.node.active = false;
        this.default_vedio_pic.active = true;
    }

    //获取封面图片的完整url
    getCoverImageUrl(coverPicture:string){
        if(/^http/.test(coverPicture)){
            return coverPicture;
        }
        return ALL_APP_SOURCE_CONFIG.remote_assets_prod[Global.DEBUG] + coverPicture;
    }

    //图片加载回调
    onImageLoadCallback(loaded:boolean, image: cc.SpriteFrame, index: number){
        if(loaded){
            this.vedio3SpriteFrames[index] = image;
            //如果没加载成功 则默认显示默认图片
            if(this.curLiveVideoIndex == index){
                //如果没加载成功 则默认显示默认图片
                if(!this.isLoadComplete[index]){
                    this.default_vedio_pic.active = false;
                    this.spIcon.node.active = true;
                    this.spIcon.spriteFrame = image;
                }
            }
        }
    }

    /**初始化video */
    init_video(){
        if(this.livegameData && this.livegameData.length > 0){
            this.livegameData.forEach((data, index)=>{
                if(data.url){
                    if(data.url.includes('.flv') || data.url.includes('.mp4')){
                        this.initFLVPlayer(data.url, this[`flvVideo${index}`], index);
                    }else if(data.url.includes('.m3u8')){
                        this.initHLSPlayer(data.url, this[`hlsVideo${index}`], index);
                    }
                }
            })
        }
    }
    /**初始化flv格式直播流组件*/
    initFLVPlayer(live_url, flvVideo, videoId){
        let element = document.getElementById("video"+videoId);
        if (element && element.parentNode) {
            //防止重复添加
            element.parentNode.removeChild(element); // 经典删除方式（兼容旧浏览器）
            element = null;
        }
       //创建并配置flv视频元素
       const Flv = require('flv.js');
       flvVideo = document.createElement('video');
       flvVideo.setAttribute('id', 'video'+videoId);
        this.hideVideoControls(flvVideo);//适配隐藏控制栏
       flvVideo.setAttribute('playsinline', 'true'); //For iOS Safari
       flvVideo.setAttribute('webkit-playsinline','true');//ios 内联播放器
        flvVideo.setAttribute('x5-playsinline', 'true');
        flvVideo.setAttribute('x5-video-player-type', 'h5');
       flvVideo.style.position = 'absolute';
       let videoWidth = this.default_video_width;
       let videoHeight = this.default_video_height;
       let heightPercent = Math.abs(videoHeight / cc.winSize.height)*100+"%"
       let widthPercent = Math.abs(videoWidth / cc.winSize.width)*100+"%";
       flvVideo.style.top = "0px";
       flvVideo.style.left = this.leftPercent;
       flvVideo.style.width = widthPercent;
       flvVideo.style.height = heightPercent;
       flvVideo.style.zIndex = '-1';
       flvVideo.style.visibility = 'visible';
       //flvVideo.setAttribute('muted', '');  
       flvVideo.style.objectFit = 'cover';//设置填充模式
       flvVideo.style.transform = 'rotate(0deg)';// 禁用方向自动调整 

       flvVideo.playsInline = true;
       flvVideo.muted = true; // 确保静音
       
       let muDive = document.getElementById("Cocos2dGameContainer");
       if (muDive) muDive.appendChild(flvVideo);

       if (live_url && live_url.indexOf('.mp4') != -1) {
           this.videoType = '.mp4';
       } else if(live_url && live_url.indexOf('.flv') != -1) {
           this.videoType = '.flv';
       }
       // 检查 flv.js 支持情况
       if (Flv.isSupported() || live_url.indexOf('.mp4') != -1) {
           flvVideo.controls = false;//隐藏默认的控制栏
           this.flvPlayer = Flv.createPlayer({
               type: this.videoType,
               url: live_url
           }, {
               enableStashBuffer: false,//减少缓冲
               stashInitialSize: 128//设置初始缓冲大小
           });
    
           // 连接 flv.js 与 video 元素
           this.flvPlayer.attachMediaElement(flvVideo);
           this.flvPlayer.load();
           this.flvPlayer.muted = true;// 确保视频初始是静音的
           this.flvPlayer.zIndex = -1;
           this.flvPlayer.controls = false;
           //在设置 autoplay 和 loop 属性之前,需要确保视频元素已经准备就绪
           flvVideo.addEventListener('loadedmetadata', () => {
               this.isLoadComplete[videoId] = true;
               if (this.spIcon.node) this.spIcon.node.active = false;
               this.default_vedio_pic.active = false;
               flvVideo.autoplay = true;
               flvVideo.loop = true;
               flvVideo.play();
               if(this.video_hided){
                    this.hideAllLiveVideo()
               }
                //如果没有轮播到自己则隐藏
                if(this.curLiveVideoIndex != videoId){
                    this.hideLiveVideo(videoId);
                }
           });

           // 监听错误事件
           this.flvPlayer.on(Flv.Events.ERROR, (err, errdet) => {
               // 参数 err 是一级异常，errdet 是二级异常
               if (err == Flv.ErrorTypes.MEDIA_ERROR) {
                   console.log('media error')
                   if (errdet == Flv.ErrorDetails.MEDIA_FORMAT_UNSUPPORTED) console.log('Media format not supported')
               }
               if (err == Flv.ErrorTypes.NETWORK_ERROR) {
                   console.log('network error')
                   if (errdet == Flv.ErrorDetails.NETWORK_STATUS_CODE_INVALID) console.log('HTTP status code exception')
               }
               if (err == Flv.ErrorTypes.OTHER_ERROR) console.log('Other anomalies', errdet)
           })

           // 加载完成
           this.flvPlayer.on(Flv.Events.METADATA_ARRIVED, () => {
               console.log('Video loading completed');
               if (this.spIcon.node.active) this.spIcon.node.active = false;
               this.default_vedio_pic.active = false;
               flvVideo.play();
                //如果没有轮播到自己则隐藏
                if(this.curLiveVideoIndex != videoId){
                    this.hideLiveVideo(videoId);
                }
           });
       } else {
           console.log('FLV.js is not supported in this browser.');
       }
    }

    /**初始化m3u8格式直播流组件*/
    initHLSPlayer(hlsUrl, hlsVideo, videoId) {
        //创建hls视频
        const Hls = require("hls.js");
        hlsVideo = document.createElement('video');
        hlsVideo.setAttribute('id', 'hlsvideo'+videoId);
        // hlsVideo.setAttribute('controls', '');
        this.hideVideoControls(hlsVideo);//适配隐藏控制栏
        // hlsVideo.setAttribute('playsinline', 'true'); //For iOS Safari
        // hlsVideo.setAttribute('webkit-playsinline','true');//ios 内联播放器
        hlsVideo.style.position = 'absolute';
        let videoWidth = this.default_video_width;
        let videoHeight = this.default_video_height;
        let heightPercent = Math.abs(videoHeight / cc.winSize.height)*100+"%"
        let widthPercent = Math.abs(videoWidth / cc.winSize.width)*100+"%";
        hlsVideo.style.top = this.topPercent;
        hlsVideo.style.left = this.leftPercent;
        hlsVideo.style.width = widthPercent;
        hlsVideo.style.height = heightPercent;
        hlsVideo.style.zIndex = '-1';
        hlsVideo.style.visibility = 'visible';
        hlsVideo.style.objectFit = 'cover';//设置填充模式
        hlsVideo.style.transform = 'rotate(0deg)';// 禁用方向自动调整 

        hlsVideo.playsInline = true;
        hlsVideo.muted = true; // 确保静音
        
        let muDive = document.getElementById("Cocos2dGameContainer");
        if (muDive) muDive.appendChild(hlsVideo);
        hlsVideo.controls = false; //隐藏默认的控制栏
        if (Hls.isSupported()) {
            this.hlsPlayer = new Hls();
            this.hlsPlayer.loadSource(hlsUrl);
            this.hlsPlayer.attachMedia(hlsVideo);
            //在设置 autoplay 和 loop 属性之前,需要确保视频元素已经准备就绪
            hlsVideo.addEventListener('loadedmetadata', () => {
                this.isLoadComplete[Number(videoId)] = true;
                if (this.spIcon.node) this.spIcon.node.active = false;
                this.default_vedio_pic.active = false;
                hlsVideo.autoplay = true;
                hlsVideo.loop = true;
                hlsVideo.muted = true;
                hlsVideo.play();
               if(this.video_hided){
                    this.hideAllLiveVideo()
               }
               //如果没有轮播到自己则隐藏
               if(this.curLiveVideoIndex != videoId){
                    this.hideLiveVideo(videoId);
               }
            });
            //当播放器成功解析 HLS 播放清单（manifest）文件时触发
            this.hlsPlayer.on(Hls.Events.MANIFEST_PARSED, ()=>{
                if (this.spIcon.node.active) this.spIcon.node.active = false;
                this.default_vedio_pic.active = false;
                this.hlsPlayer.loadPlaylist(hlsUrl);
                hlsVideo.play();
            })
        } else if (hlsVideo.canPlayType('application/vnd.apple.mpegurl')) {
            // 如果浏览器原生支持HLS
            hlsVideo.src = hlsUrl;
            hlsVideo.addEventListener('loadedmetadata', () => {
                hlsVideo.play();
                if(this.video_hided){
                    this.hideAllLiveVideo()
                }
                //如果没有轮播到自己则隐藏
                if(this.curLiveVideoIndex != videoId){
                    this.hideLiveVideo(videoId);
                }
            });
        } else {
            console.log('This browser does not support HLS playback.');
        }
    }
    // 检测浏览器类型并应用相应样式
    hideVideoControls(video:HTMLVideoElement) {
        // const video = document.querySelector('video');
        const isWebkit = 'WebkitAppearance' in document.documentElement.style;
        const isFirefox = navigator.userAgent.toLowerCase().indexOf('firefox') > -1;
        
        if (isWebkit) {
            video.style.cssText += '::-webkit-media-controls { display:none !important; }';
        }
        if (isFirefox) {
            video.style.cssText += '::-moz-media-controls { display:none; }';
        }
        video.controls = false;
        video.disablePictureInPicture = true;//是否禁用画动画 有些浏览器会自动开启播放小视频
        video.setAttribute('poster','data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==');//用于指定在视频下载时或用户点击播放前显示的替代图像（封面图）。
        video.setAttribute('controlsList', 'nodownload nofullscreen noremoteplayback');
        video.style.cssText += `
            ::-webkit-media-controls { display:none !important; }
            ::-webkit-media-controls-enclosure { display:none !important; }
            ::-webkit-media-controls-panel { display:none !important; }
            ::-webkit-media-controls-play-button{display: none;}
        `;
    }
    /**轮播LiveGame */
    initCarouselLiveGame(){
        this.livePageView.stopAllActions();
        let liveDuration = [];
        if (this.livegameData && this.livegameData.length > 1) {//LiveGame>1 进行轮播
            for (let index = 0; index < this.livegameData.length; index++) {
                let duration = parseInt(this.livegameData[index].duration);
                liveDuration.push(duration);
            }    
        } else {
            return;
        }
    
        let sequenceArr = [];
        for (let idx = 0; idx < liveDuration.length; idx++) {
            sequenceArr.push(cc.delayTime(liveDuration[idx]));
            sequenceArr.push(cc.callFunc(() => {
                let gamescene =  cc.director.getScene().getComponentInChildren(GameControl);
                if(gamescene.mTabbar.isHall_show()){   
                    this.nextLiveVideoIndex = (this.curLiveVideoIndex + 1) % this.livegameData.length;
                    this.clickLiveTagBtn(null, this.nextLiveVideoIndex); // event 参数移除或传 null
                } else {
                    this.hideLiveVideo(idx);//隐藏Live
                }
            }));
        }
        // 使用 apply 方法来传递 sequenceArr
        let sequence = cc.sequence.apply(cc, sequenceArr);
        let repeat = cc.repeatForever(sequence);
        this.livePageView.runAction(repeat);
    }

    /**隐藏指定视频 */
    hideLiveVideo(idx){
        //隐藏video
        const videoElement = document.getElementById('video'+idx);
        const hlsVideoElement = document.getElementById('hlsvideo'+idx);
        if(videoElement){//隐藏视频元素
            videoElement.style.display = 'none';
            videoElement.style.visibility = 'hidden';
        }
        if(hlsVideoElement){//隐藏视频元素
            hlsVideoElement.style.display = 'none';
            hlsVideoElement.style.visibility = 'hidden';
        }
    }

    /**隐藏所有视频 */
    hideAllLiveVideo(){
        if(this.livegameData && this.livegameData.length > 0){
            this.livegameData.forEach((_, index)=>{
                //隐藏video
                const videoElement = document.getElementById('video'+index);
                const hlsVideoElement = document.getElementById('hlsvideo'+index);
                if(videoElement){//隐藏视频元素
                    videoElement.style.display = 'none';
                    videoElement.style.visibility = 'hidden';
                }
                if(hlsVideoElement){//隐藏视频元素
                    hlsVideoElement.style.display = 'none';
                    hlsVideoElement.style.visibility = 'hidden';
                }
            })
            this.spIcon.node.active = true;
            this.video_hided = true;
        }
    }

    /**显示视频 */
    showLiveVideo(currentTime = 0){
        this.video_hided = false;
        if(!this.isLoadComplete[this.curLiveVideoIndex]){
            //视频还没加载好的话 默认显示 图片
            if(this.vedio3SpriteFrames[this.curLiveVideoIndex] != null){
                //显示视频图片
                this.spIcon.node.active = true;
                this.spIcon.spriteFrame = this.vedio3SpriteFrames[this.curLiveVideoIndex];
                this.default_vedio_pic.active = false;
            }else{
                //显示默认图片
                this.default_vedio_pic.active = true;
            }
            return;
        }
        //显示video
        const videoElement = document.getElementById('video'+this.curLiveVideoIndex);
        const hlsVideoElement = document.getElementById('hlsvideo'+this.curLiveVideoIndex);
        let nomovie = true
        if(videoElement){
            videoElement.style.display = 'block';
            videoElement.style.visibility = 'visible';
            if(currentTime == 0){
                videoElement['currentTime'] = 0;
            }
            nomovie = false;
        }
        if(hlsVideoElement){
            hlsVideoElement.style.display = 'block';
            hlsVideoElement.style.visibility = 'visible';
            if(currentTime == 0){
                hlsVideoElement['currentTime'] = 0;
            }
            nomovie = false;
        }
        if(!nomovie){
            if (this.spIcon.node.active) this.spIcon.node.active = false;
        }else{
            this.spIcon.node.active = true;
        }
    }
    /**点击直播区域的全屏按钮 */
    onClickLiveAllScreen() {
        if (!Global.getInstance().token) {
            //未登录 跳转登录页
            uiManager.instance.showDialog(UI_PATH_DIC.phonePasswordLogin, [{ way: LOGIN_WAY.PhoneCode }])
            return;
        }
        const str = 'Please turn off the auto-rotate function of the mobile phone.';
        Global.getInstance().showCommonTip2({ word: str, confirm: "OK" }, this, false, () => {
            //这里处理 直播 放大逻辑 针对直播的视频
            let gamescene =  cc.director.getScene().getComponentInChildren(GameControl);
            gamescene.mTabbar.showVideoPlayer();
        }, null, null,"Tips");
    }
    /**点击Live区域的Play按钮 */
    onClickLivePlay_apsports() {
        if (!Global.getInstance().token) {
            //未登录 跳转登录页
            uiManager.instance.showDialog(UI_PATH_DIC.phonePasswordLogin, [{ way: LOGIN_WAY.PhoneCode }])
            return;
        }
        let livedata = null;
        this.livegameData.forEach(element => {
            if(element.game_id == 3765){
                livedata = element;
            }
        });
        //根据ID 判断是否是 拳皇直播游戏
        if(livedata){
            //获取需要的数据
            let loginid = 0;
            let eventid = 0;
            const lv_data = Global.getInstance().ap_sports_data;
            if(!!lv_data && lv_data.length > 0){
                const leagues = lv_data[0]?.leagues[0];
                // ?.events[0];
                loginid = leagues?.id;
                const events = leagues?.events[0];
                eventid = events?.id;
                Global.getInstance().go_apsports_boxing(loginid,eventid);
            }else{
                //数据没加载出来的时候 正常进入apsports
                Global.getInstance().getDesignGamePlay(this.node, livedata);
            }
        }else{
            Global.getInstance().getDesignGamePlay(this.node, this.livegameData[this.curLiveVideoIndex]);
        }
    }
    /**点击Live区域的Play按钮 */
    onClickLivePlay() {
        if (!Global.getInstance().token) {
            //未登录 跳转登录页
            uiManager.instance.showDialog(UI_PATH_DIC.phonePasswordLogin, [{ way: LOGIN_WAY.PhoneCode }])
            return;
        }
        //根据ID 判断是否是 拳皇直播游戏
        if(this.livegameData[this.curLiveVideoIndex].game_id == 3765){
            //获取需要的数据
            let loginid = 0;
            let eventid = 0;
            const lv_data = Global.getInstance().ap_sports_data;
            if(!!lv_data && lv_data.length > 0){
                const leagues = lv_data[0]?.leagues[0];
                // ?.events[0];
                loginid = leagues?.id;
                const events = leagues?.events[0];
                eventid = events?.id;
                Global.getInstance().go_apsports_boxing(loginid,eventid);
            }else{
                //数据没加载出来的时候 正常进入apsports
                Global.getInstance().getDesignGamePlay(this.node, this.livegameData[this.curLiveVideoIndex]);
            }
        }else{
            Global.getInstance().getDesignGamePlay(this.node, this.livegameData[this.curLiveVideoIndex]);
        }
    }

    onTouchLiveStart(event) {
        if (!this.isScrolling) {
            this.startPos = event.getLocation();//记录初始位置
        }
    }

    onTouchLiveMove(event) {
        if (this.startPos && !this.isScrolling) {
            let currentPos = event.getLocation();
            let delta = currentPos.sub(this.startPos);//计算偏移量
            if (Math.abs(delta.x) >= 150) {
                this.isScrolling = true;//标记正在处理滑动
                let direction = delta.x < 0 ? 1 : -1;//确定滑动方向
                this.curLiveVideoIndex += direction;
                let maxIndex = this.livegameData.length - 1;
                if (this.curLiveVideoIndex > maxIndex) {
                    this.curLiveVideoIndex = 0;
                } else if (this.curLiveVideoIndex < 0) {
                    this.curLiveVideoIndex = maxIndex;
                }
                this.clickLiveTagBtn(event, this.curLiveVideoIndex);
                //延迟后解除标志
                this.scheduleOnce(()=>{
                    this.isScrolling = false;
                },0.5)
            }
        }
    }
    protected update(dt: number): void {
        this.update_num++;
        if(this.update_num > 30){
            this.update_num = 0;
            let gamescene =  cc.director.getScene().getComponentInChildren(GameControl);
            //只有在home 页面
            if(gamescene.mTabbar.currentMainTab == 'home'){
                //这里判断 是否显示 防止穿透
                const allchildren = Global.getInstance().popNode.children;
                let has_up = false;
                allchildren.forEach(element => {
                    if(element.zIndex > 49 && element.active){
                        // console.log('--------element.zindex:',element.name,element.zIndex);
                        this.hideAllLiveVideo();
                        has_up = true; 
                    }
                });
                if(!has_up){
                    this.showLiveVideo(1);//不从0开始 防止一致播放第一帧
                }
            }
        }
    }
    onTouchLiveEnd(event) {
        this.startPos = null;//重置初始位置
    }

    clickLiveTagBtn(event, userdata) {
        const curIndex = parseInt(userdata);
        this.updateLiveTagBtns(curIndex);
        this.setCurIndicator(curIndex)
        //没有直播视频数据 直接返回
        if(!this.livegameData || this.livegameData.length === 0){
            return;
        }
        //更新视频区内容
        this.updateLiveContent(curIndex);
    }

    /**
     * 更新视频标签按钮的状态
     * @param curIndex 
     */
    updateLiveTagBtns(curIndex){
        this.liveTagBtns.forEach((btn, index)=>{
            btn.target.opacity = index === curIndex ? 255 : 0;
            this.gameLivetaglabs[index].node.color = index === curIndex ? cc.color(0,0,0) : cc.color(137,137,137);
        });
    }

    /**
     * 更新直播视频区内容
     * @param curIndex 
     */
    updateLiveContent(curIndex){
        this.curLiveVideoIndex = curIndex;

        const curData = this.livegameData[curIndex];
        const isLoaded = this.isLoadComplete[curIndex];
        const curSpriteFrame = this.vedio3SpriteFrames[curIndex];

        //更新视频封面和加载状态
        if(isLoaded && !this.video_hided){
            this.spIcon.node.active = false;
            this.default_vedio_pic.active = false;
        }else if(!curData.cover_picture){
            this.spIcon.node.active = false;
            this.default_vedio_pic.active = true;
        }else if(curSpriteFrame){
            this.spIcon.node.active = true;
            this.spIcon.spriteFrame = curSpriteFrame;
            this.default_vedio_pic.active = false;
        }else{
            this.default_vedio_pic.active = true;
        }
        this.live_head_pic.active = false;
        this.live_btn_node.active = false;
        // 更新直播标题
        this.labGameLiveTitle.string = curData.game_description || "";
        // 显示/隐藏视频
        this.livegameData.forEach((_, index) => {
            if (index === curIndex) {
                if(isLoaded){
                    if(!this.video_hided){
                        this.showLiveVideo(); 
                    }else{
                        this.hideLiveVideo(index);
                    }
                }
                if(this.livegameData[curIndex].game_id == 3765){
                    this.live_head_pic.active = true;
                    this.live_btn_node.active = true;
                    this.change_apsports_hdp();
                }
                if(this.livegameData[curIndex].url.indexOf('.m3u8') != -1){
                    this.live_sprite_tips.active = true;
                }else{
                    this.live_sprite_tips.active = false;
                }
            } else {
                this.hideLiveVideo(index);
            }
        });
    }

    removeVideo(){
        this.onDestroy();
    }

    onDestroy() {
        for(let index = 0; index < this.livegameData.length; index++){
            let videoElement = document.getElementById('video'+index);
            let hlsVideoElement = document.getElementById('hlsvideo'+index);
            if(this.flvPlayer){
                this.flvPlayer.pause();
                this.flvPlayer.unload();
                this.flvPlayer.detachMediaElement();
                this.flvPlayer.destroy();
                this.flvPlayer = null; 
            }
            if (videoElement && videoElement.parentNode) {
                //防止重复添加
                videoElement.parentNode.removeChild(videoElement); // 经典删除方式（兼容旧浏览器）
                this[`flvVideo${index}`] = null;
            }
            if(this.hlsPlayer){
                this.hlsPlayer.destroy();
                this.hlsPlayer = null;
                
            }
            if (hlsVideoElement && hlsVideoElement.parentNode) {
                //防止重复添加
                hlsVideoElement.parentNode.removeChild(hlsVideoElement); // 经典删除方式（兼容旧浏览器）
                this[`hlsVideo${index}`] = null;
            }
        }
        cc.director.off("LiveGame_TopPos", this.updateLiveTopPos, this);
        cc.director.off("Show_LiveVideo", this.showLiveVideo, this);
        cc.director.off("Hide_LiveVideo", this.hideAllLiveVideo, this);
        cc.director.off("Remove_Video", this.removeVideo, this);
        cc.director.off(EMIT_PARAMS.APSPORTS_DATA_CHANGE,this.change_apsports_hdp,this);
        cc.director.off(EMIT_PARAMS.APSPORTS_GOPLAY,this.onClickLivePlay_apsports,this)
        this.node.off('hall_load_others', this.load_video_other, this);
    }
}