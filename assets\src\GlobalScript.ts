// import UserInfo from "./UserInfo";
import HttpUtils from "./net/HttpUtils";
import { i18nMgr } from "./i18n/i18nMgr";
import CalcEval = require("./libs/CalcEval");
import { SocketUtils } from "./net/SocketUtils";
import { Md5 } from "./libs/Md5";
import GameControl from "./GameControl";
import { ALL_APP_SOURCE_CONFIG } from "./Config";
// import NewShop, { ShowShop } from "./NewShop";
import Hall, { HallGame_Icon } from "./hall/Hall";
import SimpleTip from "./SimpleTip";
import CommonTip from "./CommonTip";
import utils from "./utils/utils";
import { BU_GAME, CLIENTLOG, DOWNLOAD_CHANNEL, E_BALANCE_UPDATE, GOLD_RATIO, MAN_MADE_LOGIN, POPBANNERS, SOURCE_ACTIVITY_SPORT, TYPE_BALANCE_CHANGE, TYPE_ACTIVITY_BALANCE_CHANGE, UI_PATH_DIC, TYPE_BALANCE_RECHARGE, E_PAGE_TYPE, E_CHANEL_TYPE, MAINTENANCETIPCODE, CHANEL_PARAM, EMIT_PARAMS, DEEP_INDEXZ } from "./GlobalConstant";
import { showExchangeSucceed } from "./ExchangeSucceed";
import CommonTipS from "./CommonTipS";
import { uiManager } from "./mgr/UIManager";
import { ELOGIN_TYPE } from "./login/Login";
import MoreGameManager from "./MoreGameManager";
import { AutoShowFirstRecharge, showFirstRecharge } from "./UI/activity/FirstRecharge";
import Toast from "./component/Toast";
import { showMoreGame } from "./MoreGame";
import { KycMgr } from "./KYC/KycMgr";
import { serviceMgr } from "./mgr/serviceMgr";
import CCGIF from "./GIF/CCGIF";
import { showMaintenancetip } from "./Maintenancetip";
import { showActivityBonus } from "./hall/ActivityBonus";
import { AWARD_NAME, AWARD_UPDATE_TYPE } from "./hall/Transations";
import { LOGIN_WAY } from "./login/phonePasswordLogin";
import { ACTIVITY_TYPE } from "./hall/Promo";
import RoundRectMask from "./RoundRectMask";
import PromoDetialSignup from "./hall/PromoDetialSignup";
import PromoDetialFirstCharge from "./hall/PromoDetialFirstCharge";
import PromoDetialCashback from "./hall/PromoDetialCashback";
import PromoDetialvipCashback from "./hall/PromoDetialvipCashback";
import SpinWheel from "./hall/SpinWheel";
import { GameData } from "./data/GameData";
// import { ALL_APP_SOURCE_CONFIG } from "./Config";

(<any>window).onSDKRsp = function (token, userId, code) {
    if (code <= 0) {
        cc.director.emit("fb_callback", code, token, userId);
    } else {
        console.log("something is not deal");
    }
};
(<any>window).onCaptchaRsp = function (token, code) {
    if (code == 0 && token != null) {
        Global.getInstance().asCaptchaToken = token;
        cc.director.emit("captcha_succeed", code);
    } else {
        console.log("captcha failed");
    }
};
// (<any>window).onSDKRsp = function(token, errCode) {
//     cc.director.emit("voiceRelease");
// };

(<any>window).firebaseTokenRsp = function (token) {

};

(<any>window).closeWebViewNative = function () {
    console.log("closeWebViewNative ======>")
    cc.director.emit("CloseMoreGameWebView");
};


(<any>window).closePrivacyWebViewNative = function () {
    console.log("closeWebViewNative ======>")
    cc.director.emit("ClosePrivacyWebView");
};

(<any>window).onPurchaseSuc = function (token, userId, code) {
    if (code <= 0) {
        cc.director.emit("purchase_suc", code, token);
    } else {
        cc.director.emit("purchase_fail")
    }
};
interface bundles_type {
    name: string,
    bundle: cc.AssetManager.Bundle
}
export function createClientLogInfo(key: string, data: any) {
    if (data.url && data.url == "/common/api/sys/log"
        || (data.url == "/vdr/api/jili/login" && data.res && data.res.game_url)) {
        return
    }
    let logInfo: CLIENTLOG = {} as CLIENTLOG
    logInfo.key = key
    logInfo.deviceId = Global.getInstance().getDeviceId()
    logInfo.deviceName = Global.getInstance().getDeviceName()
    let nowTime = Global.getInstance().now()
    logInfo.uploadTime = nowTime.toString()
    logInfo.formatUpTime = utils.timestampToTime(nowTime / 1000)
    logInfo.extraData = data
    if (data && data["title"]) {
        data["title"] = key + " " + data["title"]
    }
    if (Global.getInstance().userdata) {
        logInfo.userId = Global.getInstance().userdata.user_id
    }
    return logInfo
}

export function getDuration(beginTime: number) {
    let now = Global.getInstance().now()
    return Math.floor(now - beginTime) / 1000
}

export class MsgToDeal {
    msgType: number;
    update_at: number;
    msgContent?: { update_type: number, amount: number };
    expire: number
}
export interface DownImageDic {
    img_url: string,
    sprite: cc.Sprite,
    callback: any,
    ext: string//后缀 iOS用到
}
export default class Global extends cc.Component {

    //所有游戏 小图list
    gameListAtlas: cc.SpriteAtlas[] = [];

    token: string = null;
    asCaptchaToken: string = null;
    is_first_charge: boolean = false;
    is_first_request: number = 1;
    is_recommend: boolean = true
    sceneid: number = 0;
    pageid: number = 0;
    ismusicon: number = 1;
    issoundon: number = 1;
    isvibrate: number = 1;
    audiobgid: number = -1;
    m_curr_clip: cc.AudioClip = null;
    m_btn_clip: cc.AudioClip = null;
    connecturl: any = null;
    scrollmessage: any = {};
    // isRemoveLanch:boolean = false;
    config: any = null;
    userdata: any = null;
    payAccount: any = null;
    // bank: any = null;
    activityData: any = null;
    activityTypeData: any = null;
    newActivityData: any = null;
    // priority:any = null;
    mgm: any = null;
    // mgm_activity = null;
    money_to_coin_ratio = 1000;
    turntable: any = null;
    giftConfig: any = null;
    rechargeConfig: any = null;
    storeConfig: any = null;
    isGiftBuy: any = false;
    isRegister: boolean = false;
    coupon: any = null;
    deviceInfo: any = null;
    balanceTotal: any = 0;
    bonusTotal: any = 0;
    withdrawBalance: any = 0;
    loadingName: any = [];
    playerType: number = 0;
    serverTime: number = 0;
    localTime: number = 0;
    gameVersion: any = {};
    headData: any = null;
    gameList: HallGame_Icon[] = null;
    is_guest: boolean = false
    country: any = null;
    m_country_atlas: any = null;
    vip: any = null;
    avatarSpriteAtlas: cc.SpriteAtlas = null;
    m_simpletip_prefab: cc.Prefab = null;
    m_commontip_prefab: cc.Prefab = null;
    m_commontipS_prefab: cc.Prefab = null;
    m_getcoin_prefab: cc.Prefab = null;
    m_loading_prefab: cc.Prefab = null;
    m_shop_prefab: cc.Prefab = null;
    m_pay_prefab: cc.Prefab = null;
    m_setInHall_prefab: cc.Prefab = null;
    m_setInGame_prefab: cc.Prefab = null;
    service_prefab: cc.Prefab = null;
    popNode: cc.Node = null
    prefabNode: cc.Node = null
    sceneRoot: cc.Node = null;
    gcashMode: boolean = false;
    mayaMode: boolean = false;
    hadLogined = false //已经登陆过
    //luckyshot刷新时间
    nextluckyShotTime: number = 0;
    luckyshotRemainTimes: number = 0;
    registeraward: any = null;
    //guide
    public guideSwitch: any = {};

    private static downloadMap = {};
    hasGamePlayed: boolean = false;
    hasSportAchieved: boolean = false;
    static instance: Global = null;
    curLoginType: number = -1
    popBanners: any[] = [];
    popActivityBonus: any[] = [];//这里保存所有的 返奖活动 列表
    msgsToDeal: MsgToDeal[] = []
    rechargeMsgToDeal: MsgToDeal[] = [];
    downloadChannel: string = DOWNLOAD_CHANNEL.CHUANYIN
    firstRechargeActId = 0;

    lastReqTime_bonus = 0;//间隔请求 反奖弹窗
    //下载图片 队列
    down_image_queue: DownImageDic[] = [];
    down_img_num: number = 0;//同时下载图片数量
    //是否弹出过popad 只有第一次弹
    showPops: boolean = false;
    popAdTapId: number = -1;//默认 没有点击过 popid 正常弹出 这里在 没有登录的时候 点击某一个popad 登录成功之后 继续这个id 逻辑
    lastReqBanlanceTime = 0;

    t_channel = 'web';//默认是web 如果是ios 或者android  google就是原生了
    isNative = false;//这个参数区别是否 打包成原生模式
    //第三方的三个参数
    third_game_id = '';
    company_id = 0;
    third_id = '';
    //所有维护的游戏ID
    all_maintain_game_list: string[] = []
    //所以隐藏游戏ID
    all_hide_game_list: string[] = []
    //客服信息数量 如果大于0 显示红点
    custom_msg = 0;
    //标记是否是vip用户
    //isVipUser:boolean = true;
    unreadMarks: any = null;//未读红点
    nextReqTime: number = null;

    m_toast_prefab: cc.Prefab = null;
    mcommontip: cc.Prefab = null;
    static DEBUG_MODE = {
        DEV: "DEBUG_MODE_DEV_PZV",
        TEST: "DEBUG_MODE_TEST_PZV",
        RELEASE: "DEBUG_MODE_RELEASE_PZV",
        PRE: "DEBUG_MODE_PRE_PZV",
    };
    static SOURCENAME = "mania";
    static DEVICE_OS = "WEB";
    static DEVICE_NAME = "WEB";
    static DEBUG = Global.DEBUG_MODE.TEST;

    static EXIT_REASON = {
        EXIT_NONE: 0,	//无
        EXIT_PLAYER: 1,	//玩家操作
        EXIT_SWITCH: 2,	//换桌
        EXIT_BALANCE: 3,	//余额不足
        EXIT_ERROR: 4,	//服务器异常
        EXIT_OFFLINE: 5,	//掉线
        EXIT_TIMEOUT: 6,	//超时踢
        EXIT_CLUB_FORBID: 7,   //禁玩/俱乐部被ban
        EXIT_CHANGE_GAME: 999,
    };

    static BALANCE_TYPE = {
        BT_NONE: 0,	//无
        BT_RECHARGE: 1,	//充值账户
        BT_WITHDRAW: 2,	//可提现
        BT_GIFT: 3,	//赠送
    }

    static ATTACH_STATUS = {
        DEFAULT: 0,
        CLAIMED: 1,
    };
    static MAIL_READ_STATUS = {
        UNREAD: 0,
        READ: 1,
    };

    static USER_INFO_BIND = {
        INVALID: 0,
        MOBILE: 1, // 手机号登录
        FACEBOOK: 2, // facebook登录
        GOOGLE: 3 // google登录
    };

    static PLAYER_TAG_TYPE = {
        TT_NORMAL: 0,	//正常玩家
        TT_SUPPER_FAKE: 1,    //高级陪打
        TT_CHEAT: 2,	//作弊玩家
        TT_GENERAL_FAKE: 3,	//普通陪打
        TT_ROBOT: 99,	//机器人
    };

    static GLOBAL_STORAGE_KEY = {
        SESSION_ID: "kSessionId",
        LANGUAGE: "kLanguage",
        FIREBASE_TOKEN: "kFirebaseToken",
        MAIL_LIST: "kMailList",
        MAIL_READ_TO: "kMailReadTo",
        GAMEMUSIC: "kGameyinyue",
        GAMEMSOUND: "kGameyinxiao",
        GAMEMVIBRATE: "kGgamevibrate",
        LAST_ROOM: "kLastRoom",
        PHONE: "kPhone",
        PASSWORD: "kPassword",
        REMEMBER: "KRemember",
        LOGIN_TYPE: "kLoginType",
        USE_LOGIN_WAY: "kLoginWay", //1 手机验证码登录  2、手机密码登录  //默认手机验证码登录
        FB_USERID: "kFbUserId",
        GAMEVERSION: "kGameVersion",
        COUNTRYCODE: "kCountryCode",
        KAPPLERECEIPT: "KappleReceipt",
        GAME_GUIDE: "kgameGuide_",
        UPLEVEL_DATA: "upLevelAnimationData",
        EDITEPROFILEUI: "EditProfileUI",
        KCREATETABLEID: "KcreatetableId",
        kGAMERULEMETHOD: "kGameRuleMethod",
        kGAMERULEPERSON: "kGameRulePerson",
        kDefaultGCashAccount: "kDefaultGCashAccount",
        kLaunchUpload: "kLaunchUpload",
        kDeviceId: "kDeviceId",
        kMsgsBox: "kMsgsBox",
        FIRSTRECHARGE: "FirstRechargeShowTimes",     //首充每日弹出次数
        MOREGAMELISTVERSION: "MoreGameListVersion",
        MOREGAMELISTDATA: "MoreGameListData",
        MAYAVIPTIP: "mayaviptip",
        LASTLOGINUSERID: "LASTLOGINUSERID",  //上次登录的UserID
        KYC: "KYC",//KYC 所有的数据 缓存 by lemoon
        SELECT_ACCOUNT: "SELECT_ACCOUNT",//缓存 选择的withdraw account
        COUNTDOWN_ENDTIME: "KENDTIME",//保存结束时间到本地存储
        HAVE_SUBMIT_KYC: "HAVE_SUBMIT_KYC",//是否已经提交过KYC
        WALLET_TASK: "WALLET_TASK",
        WALLET_TASK_INFO: "WALLET_TASK_INFO",
        WALLET_TASK_DAY: "WALLET_TASK_DAY",
        DOWNLOAD_TIP: "DOWNLOAD_TIP",
    };
    gcashAppId: string = "****************"
    gcashShopUrl: string = ""
    historyTime = {
        startYear: 2023,
        startMonth: 6,
        startday: 1,
        endYear: 2023,
        endMonth: 6,
        endday: 2,
    }

    defaultGameType = [
        {
            "id": 10006,
            "game_type": "CASINO",
            "sort": "0"
        },
        {
            "id": 10000,
            "game_type": "SLOTS",
            "sort": "0"
        },
        {
            "id": 10001,
            "game_type": "POKER",
            "sort": "0"
        },
        {
            "id": 10002,
            "game_type": "ARCADE",
            "sort": "0"
        },
        {
            "id": 10004,
            "game_type": "BINGO",
            "sort": "0"
        },
        {
            "id": 10005,
            "game_type": "SPORTS",
            "sort": "0"
        }
    ]

    provider: any = [];
    avatarList: any = [];
    livegameData: any = null;
    gameType: any = [];
    login_conf: any = null;

    private _iosPopUpWindow: number = null;
    set iosPopUpWindow(value: number) {
        this._iosPopUpWindow = value;
    }
    get iosPopUpWindow() { return this._iosPopUpWindow; }

    ap_sports_data: any = null;//拳皇直播数据 15秒更新一下数据

    envelopeData: any = null;//保存首存红包数据
    hideGameList = null;//隐藏的游戏列表
    envelopeInfo = {
        first_deposit_bonus_guide_is_start: "0",
        is_first_recharge: true,
    }

    bannerData: any = [];
    popBannerData: any = [];
    //缓存所有的bundle
    all_game_bundles: bundles_type[] = []
    //broadcastData
    broadcastData: any = null;//保存待播放的跑马灯数据
    yesterdayRankInfo = null;
    yesterdayRankInfoJili = null;
    spinInfo = {
        is_start: 0,
        left_times: 5,
        real_left_times: 0,
    }
    //所有预加载的图片
    all_preload_dirs = [];
    isVipActivityOn = false;//vip活动是否开启
    adjustment_data = null;//活动调账类型数据

    public static getRandomInt(min, max) {
        return Math.floor(Math.random() * (max - min)) + min; //[min,max)
    }

    syncLoadRes(completeCb: any) {
        !!completeCb && completeCb();
        // if (this.avatarSpriteAtlas) {
        //     if (completeCb) completeCb();
        // } else {
        //     cc.resources.load<cc.SpriteAtlas>('atlas/Avatar', cc.SpriteAtlas, (err, assets) => {
        //         if (err) {
        //             console.log("###load atlas/Avatar error!");
        //             // resolve();
        //             return;
        //         }
        //         this.avatarSpriteAtlas = assets;
        //         !!completeCb && completeCb();
        //     });
        // }
    }

    static getInstance() {
        if (Global.instance == null) {
            Global.instance = new Global();
            Global.instance.init();

            let language = cc.sys.localStorage.getItem(Global.GLOBAL_STORAGE_KEY.LANGUAGE);
            if (!language || language == "en") {
                i18nMgr.setLanguage("en");
            } else {
                i18nMgr.setLanguage("puyu");
            }
        }

        return Global.instance;
    }

    get data() { return GameData.instance; }

    preLoadBundle() {
        //加载bundle 1 2 3 5 10 11 more 预加载文件夹 效果不好 暂时不用
        let allbundles = ['1', '2', '3', '5', '7', '10', '11', '12', 'more', 'provider_home', 'providers']
        for (let index = 0; index < allbundles.length; index++) {
            const element = 'MoreGameIcons/' + allbundles[index];
            this.preloadImageFolder(element);
        }
    }
    // 异步预加载某个文件夹内的所有图片（SpriteFrame）
    async preloadImageFolder(url: string) {
        try {
            // 指定文件夹路径（例如 "textures/icons"）
            await new Promise<void>((resolve, reject) => {
                cc.resources.preloadDir(url, cc.SpriteFrame, (err) => {
                    if (err) {
                        cc.error("预加载失败:", err);
                        reject(err);
                    } else {
                        cc.log("预加载完成:", url);
                        this.all_preload_dirs[this.all_preload_dirs.length] = url;//加载完成一个 记录一个
                        resolve();
                    }
                });
            });
        } catch (error) {
            // 错误处理
        }
    }
    init() {
        // this.userinfo = new UserInfo();
        this.scrollmessage.data = [];
        this.scrollmessage.msg = {};
        this.headData = {};
        this.gameVersion["local"] = {};
        // this.isRemoveLanch = false;
        this.readYinCinfig();
        // this.preLoadBundle();
        // cc.resources.load("audio/dian", cc.AudioClip, (err, clip: any) => {
        //     console.log("load btn clip" + clip.toString());
        //     this.m_btn_clip = clip;
        // });

        cc.game.on(cc.game.EVENT_SHOW, () => {
            // this.backUpdateRoom();
            setTimeout(() => {
                Global.getInstance().updateBalanceAfterGame()
                if (MoreGameManager.instance().isNeedReload) {
                    MoreGameManager.instance().isNeedReload = false
                    this.setStoreageData(MAN_MADE_LOGIN, "0")
                    if (Global.getInstance().mayaMode) {
                        window.location.reload()
                    } else {
                        window.location.assign(window.location.href)
                    }
                } else {
                    AutoShowFirstRecharge()
                }
                if (ALL_APP_SOURCE_CONFIG.channel == E_CHANEL_TYPE.MAYA) {
                    this.refrshMayaToken();
                }
            }, 200);
        }, this);

        this.refrshMayaTokenSchedule();

        if (cc.sys.isBrowser) {
            // cc.view.resizeWithBrowserSize(true);
            cc.view.setResizeCallback(() => {
                // Global.getInstance().showSimpleTip("screen size resize")
                cc.game.emit("screenResize")
            })
        }

        if (Global.DEBUG != Global.DEBUG_MODE.RELEASE) {
            this.gcashShopUrl = "https://gcashdev.page.link/?link=https://gcash.splashscreen/?redirect=gcash%3A%2F%2Fcom.mynt.gcash%2Fapp%2F006300121300%3FappId%3D2170020216334562%2526page%253Dpages%252Fgame%252Flist%2526apn%253Dcom.globe.gcash.android.uat%2526isi%253D1358216762%2526ibi%253Dxyz.mynt.gcashdev&apn=com.globe.gcash.android.uat&ibi=xyz.mynt.gcashdev"

        } else {
            this.gcashShopUrl = "https://gcashapp.page.link?link=https://gcash.splashscreen/?redirect%3Dgcash%3A%2F%2Fcom.mynt.gcash%2Fapp%2F006300121300%3FappId%3D2170020216334562%2526page%253Dpages%252Fgame%252Flist%2526apn%253Dcom.globe.gcash.android%2526isi%253D520020791%2526ibi%253Dcom.globetel.gcash&apn=com.globe.gcash.android&isi=520020791&ibi=com.globetel.gcash"

        }

        //加载出来笑脸之后 重新刷新页面
        if (cc.sys.isBrowser) {
            let canvas = document.getElementById('GameCanvas');
            canvas.addEventListener("webglcontextlost", function (event) {
                event.preventDefault();
                console.log("WebGL context lost?");
                setTimeout(() => {
                    location.reload(); // 刷新页面
                }, 100);
            });
        }
    }
    setToNative(channel?) {
        this.isNative = true;
        this.t_channel = channel;
        console.log('-----------1.0--我这里标记一下是 渠道:', channel, '-----', cc.sys.os);
    }
    //只针对原生
    request_focus_toself() {
        //适配原生
        if (Global.getInstance().isNative) {
            document.location = 'nustaronlinekey://action=focus';
        }
    }
    refrshMayaTokenSchedule() {
        this.unschedule(this.refrshMayaToken);
        this.nextReqTime = this.now() + 6 * 60 * 1000;
        if (ALL_APP_SOURCE_CONFIG.channel == E_CHANEL_TYPE.MAYA) {
            this.schedule(this.refrshMayaToken, 2 * 60, cc.macro.REPEAT_FOREVER, 2 * 60)
        }
    }

    refrshMayaToken() {
        if (this.nextReqTime < this.now() && Global.getInstance().token) {
            HttpUtils.getInstance().post(1, 3, this, "/common/api/maya/refresh/token", {
                token: Global.getInstance().token
            }, (response) => {
                if (response && response.data && response.data.expire) {
                    let expire = response.data.expire;
                    if (typeof expire == "number") {
                        this.nextReqTime = expire * 1000 + this.now();
                    } else if (typeof expire == "string") {
                        this.nextReqTime = Number(expire) * 1000 + this.now();
                    }
                } else {
                    this.nextReqTime = this.nextReqTime + 7 * 60 * 1000;
                }
            }, (response) => {
                // window.location.reload()
            });
        }
    }

    sync(serverTime: number) {
        this.serverTime = serverTime;
        this.localTime = Date.now();
    }

    now(): number {
        if (this.serverTime == 0 || this.localTime == 0) {
            return Date.now();
        }
        return Date.now() - this.localTime + this.serverTime;
    }

    adjust(duration: number, serverTime: number): number {
        if (this.serverTime == 0 || this.localTime == 0) {
            console.log("Global adjust: supplement sync");
            this.sync(serverTime);
        }
        console.log("Global adjust: this.serverTime = " + this.serverTime + " this.localTime = " + this.localTime);
        let ret: number = duration * 1000 - (this.now() - serverTime);
        if (ret > duration * 1000) {
            let revise: number = ret - (duration * 1000);
            console.log("Global adjust: revise this.serverTime " + revise);
            this.serverTime += revise;
            ret = duration * 1000;
        }
        return ret;
    }

    getLabel(opt: string, params: string[] = []): string {
        return i18nMgr._getLabel(opt, params)
    }

    typeBind(bind: number) {
        let _bind: number[] = this.userdata["bind"];
        return _bind.indexOf(bind) >= 0;
    }
    //弹窗提示 跳回小程序 是否从hall过来
    back_mini_buy_tips(hall = false, deposit_num?) {
        if (hall) {
            //应藏 视频 防止透视
            cc.director.emit("Hide_LiveVideo");
        }
        let str = "Please click 'Go Deposit' to return to the GCash mini-program for top-up."
        Global.getInstance().showCommonTip2({ word: str, confirm: "Go Deposit" }, this, false, () => {
            if (hall) {
                //打开 防止回来的时候看不到bug
                cc.director.emit("Show_LiveVideo");//显示当前video
            }
            Global.getInstance().gcash_buy(deposit_num);
        }, () => {
            //取消按钮
            if (hall) {
                //打开 防止回来的时候看不到bug
                cc.director.emit("Show_LiveVideo");//显示当前video
            }
        }, null, "Quick Reminder");
    }
    //gcash内购 跳回小程序
    gcash_buy(deposit_num?) {
        uiManager.instance.loadPrefabLoading_noback()
        let url = "/common/api/gcash/set/jumpType";
        let params;
        params = {
            token: Global.getInstance().token,
            type: deposit_num || 10
        }
        HttpUtils.getInstance().post(3, 3, this, url, params, (response) => {
            uiManager.instance.hidePrefabByLoading()
            if (cc.sys.OS_IOS === cc.sys.os) {
                utils.assignUrl(Global.getInstance().gcashShopUrl)
            } else {
                // window.open(Global.getInstance().gcashShopUrl, "_blank")
                utils.openUrl(Global.getInstance().gcashShopUrl);
            }
        }, (err) => {
            uiManager.instance.hidePrefabByLoading()
            if (err?.code == MAINTENANCETIPCODE) { //服务器维护
                showMaintenancetip(err.msg)
                return
            }
        });
    }
    //设置显示 page id
    setPageId(id: number) {
        this.pageid = id;
    }
    //获取id 
    getPageId() {
        return this.pageid;
    }
    setSceneId(id: number) {
        this.sceneid = id;
    }

    getSceneId() {
        return this.sceneid;
    }

    readYinCinfig() {
        this.ismusicon = parseFloat(this.getStoreageData(Global.GLOBAL_STORAGE_KEY.GAMEMUSIC, 0.5));
        // this.ismusicon = 0.5
        this.issoundon = parseFloat(this.getStoreageData(Global.GLOBAL_STORAGE_KEY.GAMEMSOUND, 1));
        // this.issoundon = 1
        this.isvibrate = parseFloat(this.getStoreageData(Global.GLOBAL_STORAGE_KEY.GAMEMVIBRATE, 1));
        // this.isvibrate = 1
    }

    clearToken() {
        cc.sys.localStorage.removeItem(Global.GLOBAL_STORAGE_KEY.SESSION_ID);
    }

    onErrorCallback(data: any = null) {
        console.log("GameNet onErrorCallback");
        this.hideShowLoading();
        SocketUtils.getInstance().close();
        if (!cc.director.getScene().getChildByName("networkerror")) {
            this.showCommonTip(this.getLabel("php_code_102022"), this, true, () => {//cancel
                this.closeBgAudio();
                //token过期 直接返回大厅 当前未登录状态
                //cc.director.loadScene('loginScene');
            }, null, "networkerror");
        }

        !!data && this.logNewEvent("no_internet", data);
    }

    onLoggedErrorCallback(data: any = null) {
        console.log("GameNet onErrorCallback");
        this.hideShowLoading();
        SocketUtils.getInstance().close();
        if (!cc.director.getScene().getChildByName("networkerror")) {
            this.showCommonTip(this.getLabel("tipword51"), this, true, () => {//cancel
                this.closeBgAudio();
                console.log("GameNet onErrorCallback----2");
                // cc.director.loadScene('loginScene');
                Global.instance.logout(false);
            }, null, "networkerror");
        }

        !!data && this.logNewEvent("no_internet", data);
    }

    // onCloseCallback(e){
    //     console.log("GameNet onCloseCallback");
    //     this.onErrorCallback(e);
    // }   

    setStoreageData(key, value) {
        // console.log("store key = " + key);
        cc.sys.localStorage.setItem(key, value);
    }

    getStoreageData(key, defaultValue: any = null) {
        let value = cc.sys.localStorage.getItem(key);

        if (value == null) {
            return defaultValue;
        }
        if (value == 'false') {
            return false;
        }
        if (value == 'true') {
            return true;
        }
        if (value == 'null') {
            return defaultValue;
        }

        return value;
    }

    /**清除特定key的本地数据 */
    removeStoreageData(key) {
        cc.sys.localStorage.removeItem(key);
    }

    audioCallback() {
        if (this.ismusicon > 0 && this.m_curr_clip != null) {
            // cc.audioEngine.setFinishCallback(this.audiobgid, this.audioCallback);
            console.log("ismusicon1111 = " + this.ismusicon)
            this.audiobgid = cc.audioEngine.play(this.m_curr_clip, false, this.ismusicon);
            cc.audioEngine.setFinishCallback(this.audiobgid, this.audioCallback);
        }
    }

    playBgAudio(clip: cc.AudioClip) {
        if (this.audiobgid == -1) {
            this.m_curr_clip = clip;
            console.log("ismusicon = " + this.ismusicon)
            // cc.audioEngine.setFinishCallback(this.audiobgid, this.audioCallback);
            if (this.ismusicon != 0) {
                this.audiobgid = cc.audioEngine.play(clip, true, this.ismusicon);
                cc.audioEngine.setFinishCallback(this.audiobgid, this.audioCallback);
            }

        } else {
            if (this.m_curr_clip != null && this.m_curr_clip == clip) {
                this.resumeBgAudio();
            }
            else {
                this.closeBgAudio();
                this.m_curr_clip = clip;
                if (this.ismusicon != 0) {
                    this.audiobgid = cc.audioEngine.play(clip, true, this.ismusicon);
                    cc.audioEngine.setFinishCallback(this.audiobgid, this.audioCallback);
                }
            }
        }
    }

    closeBgAudio() {
        if (this.audiobgid != -1) {
            cc.audioEngine.stop(this.audiobgid);
            this.audiobgid = -1;
            this.m_curr_clip = null;
        }
    }

    setBgVolume(value) {
        if (cc.sys.isNative && cc.sys.OS_ANDROID === cc.sys.os) {
            if (this.audiobgid != -1)
                cc.audioEngine.setVolume(this.audiobgid, value);
        }
    }

    pauseBgAudio() {
        if (this.audiobgid != -1) {
            let s = cc.audioEngine.getState(this.audiobgid);
            if (s == cc.audioEngine.AudioState.PLAYING) {
                cc.audioEngine.pause(this.audiobgid);
            }
        }
    }

    resumeBgAudio() {
        console.log("resumeBgAudio audiobgid = " + this.audiobgid + " ismusicon = " + this.ismusicon)
        if (this.audiobgid == -1) {
            if (this.m_curr_clip != null) {
                if (this.ismusicon != 0) {
                    this.audiobgid = cc.audioEngine.play(this.m_curr_clip, false, this.ismusicon);
                    cc.audioEngine.setFinishCallback(this.audiobgid, this.audioCallback);
                }
            }
        }
        else {
            if (this.ismusicon != 0) {
                let s = cc.audioEngine.getState(this.audiobgid);
                cc.audioEngine.setVolume(this.audiobgid, this.ismusicon);
                if (s == cc.audioEngine.AudioState.PAUSED) {
                    cc.audioEngine.resume(this.audiobgid);
                }
            }
        }
    }

    loadImgFromUrl(imgUrl: string, sprite, cb?: Function, priority?, ignoreScale?) {
        let tturl = imgUrl + '';
        if (!imgUrl || imgUrl == "") return;
        if (imgUrl.indexOf('.gif') != -1) {
            // return this.loadImgFromUrl_gif(imgUrl,cb,sprite)
        };
        let height_img = sprite.node.height
        let width_img = sprite.node.width
        // if(height_img == 258 && width_img == 258){
        //     height_img = 60;
        //     width_img = 60;
        // }
        if (!ignoreScale && (Global.DEBUG == Global.DEBUG_MODE.PRE || Global.DEBUG == Global.DEBUG_MODE.RELEASE)) {
            imgUrl = imgUrl + "?x-image-process=image/resize,m_lfit,h_" + height_img + ",w_" + width_img;
        }
        if (imgUrl.substring(0, 6) == "avatar") {
            let frame = this.avatarSpriteAtlas.getSpriteFrame(imgUrl);
            sprite.spriteFrame = frame;
        } else {
            if (cc.sys.isMobile && cc.sys.isNative) {
                let fullPath: string = this.GetFullPath(imgUrl);
                if (jsb.fileUtils.isFileExist(fullPath)) {
                    this.loadImage(fullPath, sprite);
                    if (cb) cb()
                }
                else {
                    this.downloadImage(imgUrl, fullPath, () => {
                        this.loadImage(fullPath, sprite);
                        if (cb) cb()
                    });
                }
            } else {
                // const imageExt = ".png"; // 假设图片后缀名为 .png
                // 检查 imageUrl 是否已带图片后缀名
                // if (!imgUrl.endsWith(imageExt) && !imgUrl.endsWith("jpg")) {
                //     // 不带图片后缀名，拼接后缀名
                //     imgUrl += imageExt;
                // }

                let down_img: DownImageDic = {
                    img_url: imgUrl,
                    sprite: sprite,
                    callback: cb,
                    ext: utils.getPicsextWithUrl(tturl)
                }
                this.addToQueue(down_img)
            }
        }
    }

    loadImgFromUrl_V2(imgUrl: string, onLoaded?, sprite?) {
        let tturl = imgUrl + '';
        if (!imgUrl || imgUrl == "") return;
        // console.log('-----urlidimg:',imgUrl)
        // if(imgUrl.indexOf('.gif') != -1){
        //    return this.loadImgFromUrl_gif(imgUrl,onLoaded,sprite)
        // };
        if (sprite && sprite.node && (Global.DEBUG == Global.DEBUG_MODE.PRE || Global.DEBUG == Global.DEBUG_MODE.RELEASE)) {
            imgUrl = imgUrl + "?x-image-process=image/resize,m_lfit,h_" + sprite.node.height + ",w_" + sprite.node.width;
        }
        // if (cc.sys.isMobile && cc.sys.isNative) {
        //     let fullPath: string = this.GetFullPath(imgUrl);
        //     if (jsb.fileUtils.isFileExist(fullPath)) {
        //         onLoaded && onLoaded(true, fullPath);
        //     }else {
        //         this.downloadImage(imgUrl, fullPath, () => {
        //             onLoaded && onLoaded(true, fullPath);
        //         });
        //     }
        // } else {
        //     let down_img:DownImageDic = {
        //         img_url:imgUrl,
        //         sprite:null,
        //         callback:onLoaded,
        //         ext:utils.getPicsextWithUrl(tturl)
        //     }
        //     this.addToQueue(down_img)
        // }
        let down_img: DownImageDic = {
            img_url: imgUrl,
            sprite: null,
            callback: onLoaded,
            ext: utils.getPicsextWithUrl(tturl)
        }
        this.addToQueue(down_img)
    }
    //从队列里面删除
    removeFromQueue(downurl: string) {
        // console.log('--------需要删除的图片名称:',downurl)
        this.down_image_queue = this.down_image_queue.filter((item: DownImageDic) => {
            return item.img_url.indexOf(downurl) == -1
        })
        // console.log('-------打印一下还有多少队列需要下载:',this.down_image_queue.length)
    }
    //增加到队列
    addToQueue(down_img: DownImageDic) {
        //先判断一下 是否有这个图片 下载 如果有 删除上一个
        const index = this.down_image_queue.findIndex(item => item.img_url == down_img.img_url);
        if (index >= 0) { this.down_image_queue.splice(index, 1) }
        let random = Math.random() * 100
        //防止 每次都从 第一个 开始下载 阻塞 后续没法下载
        if (random < 50) {
            this.down_image_queue.push(down_img)
        } else {
            this.down_image_queue.unshift(down_img)
        }
        this.startDownImage();
    }
    //开始下载图片队列
    startDownImage() {
        //测试 都不下载
        // this.down_img_num = 20000
        //同时下载5个
        if (this.down_img_num >= 5) return;
        if (this.down_image_queue.length == 0) return;
        this.down_img_num++;
        let first = this.down_image_queue.shift();
        // let parms = {priority: 2, maxRetryCount: 0};
        let self = this;
        cc.assetManager.loadRemote(first.img_url, {
            ext: first.ext
        }, (err, texture: any) => {
            if (err == null) {
                var frame = new cc.SpriteFrame(texture);
                if (first.sprite != null) {
                    first.sprite.spriteFrame = frame;
                }
                first.callback && (first.callback)(true, frame, first.img_url)
            } else {
                console.log('下载错误-----', JSON.stringify(err))
                // this.addToQueue(first)//重新加载到下载队列
            }
            this.down_img_num--;
            self.startDownImage();
        });
    }

    //单独下载图片不加入队列 betorder使用
    download_img(imgUrl: string, sprite, cb?: Function, priority?, ignoreScale?) {
        let tturl = imgUrl + '';
        if (!imgUrl || imgUrl == "") return;
        if (imgUrl.indexOf('.gif') != -1) {
            // return this.loadImgFromUrl_gif(imgUrl,cb,sprite)
        };
        let height_img = sprite.node.height
        let width_img = sprite.node.width

        if (!ignoreScale && (Global.DEBUG == Global.DEBUG_MODE.PRE || Global.DEBUG == Global.DEBUG_MODE.RELEASE)) {
            imgUrl = imgUrl + "?x-image-process=image/resize,m_lfit,h_" + height_img + ",w_" + width_img;
        }
        if (cc.sys.isMobile && cc.sys.isNative) {
            let fullPath: string = this.GetFullPath(imgUrl);
            if (jsb.fileUtils.isFileExist(fullPath)) {
                this.loadImage(fullPath, sprite);
                if (cb) cb()
            }
            else {
                this.downloadImage(imgUrl, fullPath, () => {
                    this.loadImage(fullPath, sprite);
                    if (cb) cb()
                });
            }
        } else {
            let ext = utils.getPicsextWithUrl(tturl)
            cc.assetManager.loadRemote(imgUrl, {
                ext: ext
            }, (err, texture: any) => {
                if (err == null) {
                    var frame = new cc.SpriteFrame(texture);
                    if (sprite != null) {
                        sprite.spriteFrame = frame;
                    }
                    cb && cb(true)
                } else {
                    console.log('下载错误-----', JSON.stringify(err))
                    cb && cb(false)
                }
            });
        }
    }
    loadImgFromUrl_gif(imgUrl: string, onLoaded?, sprite?) {
        sprite.node.addComponent(CCGIF);
        setTimeout(() => {
            let ccgif = sprite.node.getComponent(CCGIF);
            ccgif.loadseturl(imgUrl, onLoaded, sprite);
        }, 200);

    }
    // loadAvatarAtlas(cb:any = null){
    //     if(this.avatarSpriteAtlas){
    //         !!cb && cb();
    //         return;
    //     }
    //     cc.resources.load<cc.SpriteAtlas>('atlas/Avatar', cc.SpriteAtlas, (err, assets)=>{
    //         if(err) return;
    //         this.avatarSpriteAtlas = assets;
    //         !!cb && cb();
    //     }); 
    // }

    public downloadImage(resource: string, fullPath: string, successCallback?: () => void) {
        if (Global.downloadMap[resource]) {
            Global.downloadMap[resource]["successCallback"].push(successCallback);
            return;
        }
        console.log("开始下载：" + resource);
        Global.downloadMap[resource] = {};
        Global.downloadMap[resource]["successCallback"] = [];
        Global.downloadMap[resource]["successCallback"].push(successCallback);
        HttpUtils.getInstance().download(resource, (response) => {
            console.log("下载成功：" + resource);
            let data = new Int8Array(response);
            // @ts-ignore
            jsb.fileUtils.writeDataToFile(data, fullPath);
            Global.downloadMap[resource]["successCallback"].forEach(element => {
                console.log("成功回调：" + resource);
                element();
            });
            Global.downloadMap[resource] = null;
        }, (error) => {
            console.log("下载失败：" + resource);
            Global.downloadMap[resource] = null;
        });
    }

    public loadImage(fullPath: string, sprite: cc.Sprite) {
        cc.assetManager.loadRemote(fullPath, (error, texture: cc.Texture2D) => {
            if (!error && !!texture) {
                try {
                    if (!!sprite && !!sprite.node && sprite.node.isValid) {
                        sprite.spriteFrame = new cc.SpriteFrame(texture);
                    }
                } catch (error) {
                    console.log("head Get Error" + fullPath);
                }
            } else {
                if (jsb.fileUtils.isFileExist(fullPath)) {
                    jsb.fileUtils.removeFile(fullPath);
                }
                console.log(JSON.stringify(error));
            }
        });
    }

    public GetFullPath(resource: string): string {
        if (cc.sys.isNative) {
            return jsb.fileUtils.getWritablePath() + Md5.hashStr(resource) + ".jpeg";
        }
        return null;
    }

    getCountryAtlas(target: any, callback: any = null, file: any = null) {
        if (this.m_country_atlas == null) {
            var self = this;
            file = !!file ? file : this.country as string;

            cc.resources.load<cc.SpriteAtlas>('atlas/' + file, cc.SpriteAtlas, (err, assets) => {
                if (err) return;
                self.m_country_atlas = assets;
                if (!!callback) {
                    callback.call(target);
                }
            });
        } else {
            if (!!callback) {
                callback.call(target);
            }
        }
    }

    getCountryCurrency(type: any = 0) {
        let file = this.country as string;
        return "₱";
        if (file == "BD") {
            if (type == 0) {
                return "৳ ";
            } else {
                return "Tk.";
            }
        } else {
            if (type == 0) {
                return "R$ ";
            } else {
                return "Rs.";
            }
        }
    }

    showSimpleTip(word: string) {
        let cb = () => {
            let node = cc.instantiate(this.m_simpletip_prefab);
            node.parent = cc.director.getScene();
            node.zIndex = 999;
            let s = node.getComponent(SimpleTip);
            s.show(word, null, null);
        }
        if (this.m_simpletip_prefab == null) {
            uiManager.instance.loadPrefabByLoading("prefab/simpletip", cc.Prefab, () => {
            }, (err, prefab: any) => {
                if (err) {
                    return;
                }
                this.m_simpletip_prefab = prefab;
                cb()
            });
        }
        else {
            cb()
        }
    }

    showCommonTip(word: string, target: any, isOneBtn: boolean = false, confirmfun: any = null, cancelfun: any = null, myname: any = null) {
        let cb = () => {
            let node = cc.instantiate(this.m_commontip_prefab);
            node.parent = Global.getInstance().popNode;
            node.zIndex = 999;
            if (myname != null && myname != "") {
                node.name = myname;
            }
            let s = node.getComponent(CommonTip);
            if (isOneBtn) {
                s.setOneBtn(word, target, confirmfun);
            } else {
                s.setInfo(word, confirmfun, cancelfun, target);
            }
        }
        if (this.m_commontip_prefab == null) {
            uiManager.instance.loadPrefabByLoading("prefab/commontip4", cc.Prefab, () => {
            }, (err, prefab: any) => {
                if (err) {
                    return;
                }
                this.m_commontip_prefab = prefab;
                cb()
            });
        }
        else {
            cb()
        }
    }
    showCommonTip2(word: { word: string, confirm?: string }, target: any, isOneBtn: boolean = false, confirmfun: any = null, cancelfun: any = null, myname: any = null, title?, openType?) {
        let cb = () => {
            let node = cc.instantiate(this.m_commontipS_prefab);
            node.parent = this.popNode;
            node.zIndex = 99999;//保持最高层 
            if (myname != null && myname != "") {
                node.name = myname;
            }
            let s = node.getComponent(CommonTipS);
            s.setOpenType(openType);
            if (isOneBtn) {
                s.setOneBtn(word, target, confirmfun, title);
            } else {
                s.setInfo(word, confirmfun, cancelfun, target, title);
            }
        }
        if (this.m_commontipS_prefab == null) {
            uiManager.instance.loadPrefabByLoading("prefab/commontip2", cc.Prefab, () => {
            }, (err, prefab: any) => {
                if (err) {
                    return;
                }
                this.m_commontipS_prefab = prefab;
                cb()
            });
        }
        else {
            cb()
        }
    }
    showCommonTip3(params: { word: string, target: any, confirmfun: any, cancelfun: any, myname: any, title?: any, ok?: any, cancel?: any }) {
        uiManager.instance.showDialog(UI_PATH_DIC.Commontip3, [{ params: params }], null, DEEP_INDEXZ.LOADING)
    }
    showGetCoin(num: any, bonus: any, cb?: Function) {
        let itemdata = { spf: null, count: num, bonus: bonus, info: "You have successfully bought coins! Wish you good luck!" };
        showExchangeSucceed(itemdata, cb)
        // let cb = () => {
        //     let node = cc.instantiate(this.m_getcoin_prefab);
        //     node.parent = cc.director.getScene();
        //     node.name = "GetCoin";
        //     let s = node.getComponent(GetCoin);
        //     s.init(num, needbind);
        // }
        // if (this.m_getcoin_prefab == null) {
        //     cc.resources.load("prefab/getcoin", cc.Prefab, () => {
        //     }, (err, prefab: any) => {
        //         if (err) {
        //             return;
        //         }
        //         this.m_getcoin_prefab = prefab;
        //         cb()
        //     });
        // }
        // else {
        //     cb()
        // }
    }

    showLoading(myname: string, isWeak?: boolean) {
        let contain = false;
        for (let i = 0; i < this.loadingName.length; i++) {
            if (this.loadingName[i] == myname) {
                return;
            }
        }
        if (!contain) {
            this.loadingName.push(myname);
        }

        // if (cc.director.getScene().getChildByName("waitloading") != null) {
        if (Global.getInstance().popNode.getChildByName("waitloading") != null) {
            return;
        }
        let cb = () => {
            let node = cc.instantiate(this.m_loading_prefab);
            // node.parent = cc.director.getScene();
            node.parent = Global.getInstance().popNode;
            node.name = "waitloading";
            node.zIndex = 999;
            this.scheduleOnce(() => {
                this.hideShowLoading(myname);
            }, 20)
        }
        if (this.m_loading_prefab == null) {
            cc.resources.load("prefab/loading", cc.Prefab, () => {
            }, (err, prefab: any) => {
                if (err) {
                    return;
                }
                this.m_loading_prefab = prefab;
                cb()
            });
        }
        else {
            cb()
        }
    }

    hideShowLoading(myname: string = null) {
        if (!!myname) {
            for (let i = 0; i < this.loadingName.length; i++) {
                if (this.loadingName[i] == myname) {
                    this.loadingName.splice(i, 1);
                }
            }
        } else {
            this.loadingName = [];
        }

        if (this.loadingName.length == 0) {
            // let loading = cc.director.getScene().getChildByName("waitloading");
            let loading = Global.getInstance().popNode.getChildByName("waitloading");

            if (loading != null) {
                loading.removeFromParent();
                loading.destroy();
            }
        }
    }

    showShop(type: number = 0, extraData?) {
        // this.getShopConfig(3, "/api/global-config/recharge", () => {
        //     this.getStoreConfig(3, "/api/product/list", () => {
        //         uiManager.instance.showDialog(UI_PATH_DIC.Shop, [{ type: type, extraData: extraData }])
        //     });
        // });
        uiManager.instance.showDialog(UI_PATH_DIC.Shop, [{ type: type, extraData: extraData }]) //未用到
    }

    showSetInHall() {
        uiManager.instance.showDialog(UI_PATH_DIC.Setting);//未用到
    }

    showSetInGame() {
        let cb = () => {
            let node = cc.instantiate(this.m_setInGame_prefab);
            node.parent = cc.director.getScene();
        }
        if (this.m_setInGame_prefab == null) {
            Global.getInstance().showLoading("setInGameLoading");
            uiManager.instance.loadPrefabByLoading("prefab/gameSetting/settingUI", cc.Prefab, () => {
            }, (err, prefab: any) => {
                Global.getInstance().hideShowLoading("setInGameLoading");
                if (err) {
                    return;
                }
                this.m_setInGame_prefab = prefab;
                cb()
            });
        } else {
            cb()
        }
    }

    backUpdateRoom() {
        if (this.sceneid == 2 || this.sceneid == 3) {
            let n = cc.director.getScene().getChildByName("Shop");
            if (n != null) {
                if (this.isReview() && cc.sys.os == cc.sys.OS_IOS) {
                    let s = n.getComponent("IOSShop");
                    if (s != null) {
                        return;
                    }
                } else {
                    let s = n.getComponent("Shop");
                    if (s != null) {
                        if (s.webviewnode.active == true) {
                            return;
                        } else if (!!s.payData) {
                            if (s.payData["openType"] == 2) {
                                this.confirmRequest(s.payData["paySerialNo"])
                                s.payData = null;
                            }
                        }
                    }
                }
            }

            //SocketUtils.getInstance().connect();
            //SocketUtils.getInstance().checkConnection()
        }
    }

    confirmRequest(paySerialNo: any) {
        HttpUtils.getInstance().post(1, 3, this, "/api/payment/history", {
            token: this.token,
            paySerialNo: paySerialNo,
        }, (response) => {
            if (response.data) {
                if (response.data.status == 1) {
                    this.paySuccess(response.data);
                } else {
                    this.showSimpleTip("Payment Processing...");
                }
            }
        });
    };

    verificationOrder(paySerialNo: string) {
        if (!paySerialNo) return;
        HttpUtils.getInstance().post(1, 0, this, "/api/user/recharge-dot", {
            token: this.token,
            paySerialNo: paySerialNo,
        }, () => {

        });
    }

    paySuccess(param: any) {
        let amount = parseFloat(param["amount"]);
        let bonus = param["bonus"]

        // this.showShop()
        if (this.is_first_charge) this.logNewEvent("first_buy_coins", { CUID: Global.getInstance().userdata.user_id }, 1)
        this.is_first_charge = false;

        // if (!this.userdata["recharge_amount"]) {
        //     this.userdata["recharge_amount"] = 0;
        // }
        // this.userdata["recharge_amount"] = new CalcEval().eval(this.userdata["recharge_amount"] + "+" + amount);
        this.showGetCoin(amount, bonus, () => {
            cc.director.emit("pay_event");
        });
        this.rechargeConfig = null;
        this.storeConfig = null;
        this.giftConfig = null;
        this.coupon = null;
        // Global.getInstance().is_first_charge = false
        // cc.director.emit("pay_event", this.isGiftBuy);

    }
    quickJoinRoom(gameId: number, roomId?: number) {
        let gameControl = cc.director.getScene().getComponentInChildren(GameControl)
        if (gameId == base.GAME_ID.GID_TONGITS) {
            let data = {
                gameId: base.GAME_ID.GID_TONGITS,
                roomId: roomId || 700,
                quickPlay: 1
            }
            gameControl.goToGame(Global.getInstance().gameIdToString(gameId), function () {
                let src = this.getComponent("TongitsRoom")
                src.joinTongits(data);
            });
        } else if (gameId == base.GAME_ID.GID_PUSOY) {
            let data = {
                gameId: base.GAME_ID.GID_PUSOY,
                roomId: roomId || 600,
                quickPlay: 1
            }
            gameControl.goToGame(Global.getInstance().gameIdToString(gameId), function () {
                let src = this.getComponent("PusoyGame")
                src.joinPusoy(data);
            });
        }
    }

    pokerGamelistContainGameID(gameID: number | string) {
        for (let index = 0; index < this.gameList.length; index++) {
            const data = this.gameList[index];
            if (data.game_id == gameID) {
                return true
            }
        }
        return false
    }

    static parseNickName(nickname = '', length = 10) {
        let getstr = (str) => {
            let len = 0;
            for (let i = 0; i < str.length; i++) {
                let c = str.charCodeAt(i);
                let step = 2;
                if ((c >= 0x0001 && c <= 0x007e) || (0xff60 <= c && c <= 0xff9f)) {
                    step = 1;
                }
                len += step;
            }
            if (len <= length) {
                return str;
            }

            let end = 0;
            let newlen = 0;
            for (let i = 0; i < str.length; i++) {
                let c = str.charCodeAt(i);
                let step = 2;
                if ((c >= 0x0001 && c <= 0x007e) || (0xff60 <= c && c <= 0xff9f)) {
                    step = 1;
                }
                if (newlen + step + 3 > length) {
                    break;
                }
                newlen += step;
                end = i;
            }

            return str.substring(0, end + 1) + "...";
        }

        return getstr(nickname);
    }

    static checkDayTakeView(key) {
        let date = new Date();
        let year = date.getFullYear();
        let month = (date.getMonth() + 1);
        let day = date.getDate();

        let value = cc.sys.localStorage.getItem(key);
        if ((value == "" || value == undefined || value == null || value == "undefined")) {
            // pg.storage.save(key, year + ":" + month + ":" + day);
            cc.sys.localStorage.setItem(key, year + ":" + month + ":" + day);
            return true;
        }
        // let nowDay = Sunyoo.LocalStorage.getItem(key, year + ":" + month + ":" + day);
        let nowDay = cc.sys.localStorage.getItem(key);//year + ":" + month + ":" + day;

        let s = nowDay.split(":");
        let y = Number(s[0]);
        let m = Number(s[1]);
        let d = Number(s[2]);

        if (y < year) {
            cc.sys.localStorage.setItem(key, year + ":" + month + ":" + day);
            return true;
        } else if (m < month) {
            cc.sys.localStorage.setItem(key, year + ":" + month + ":" + day);
            return true;
        } else if (d < day) {
            cc.sys.localStorage.setItem(key, year + ":" + month + ":" + day);
            return true;
        }
        return false;
    }

    static playerTagColor(player: Object): cc.Color {
        let colors = {
            0: cc.color(255, 255, 255),
            1: cc.color(255, 255, 0),
            2: cc.color(255, 0, 0),
            3: cc.color(0, 0, 255),
            99: cc.color(0, 255, 0),
        }
        if (Global.getInstance().playerType == Global.PLAYER_TAG_TYPE.TT_SUPPER_FAKE
            || Global.getInstance().playerType == Global.PLAYER_TAG_TYPE.TT_GENERAL_FAKE) {
            if (!!colors[player["baseInfo"]["tag"]]) {
                return colors[player["baseInfo"]["tag"]];
            }
        }
        return colors[Global.PLAYER_TAG_TYPE.TT_NORMAL];
    }
    is_mini_game() {
        if (ALL_APP_SOURCE_CONFIG.channel == E_CHANEL_TYPE.G_CASH) return true;
        if (ALL_APP_SOURCE_CONFIG.channel == E_CHANEL_TYPE.MAYA) return true;
        return false;
    }
    getConfig(type: Number, callback: any = null, failcallback: any = null) {
        if (!this.config) {
            HttpUtils.getInstance().post(type, 3, this, "/common/api/set/get", {
                "appChannel": ALL_APP_SOURCE_CONFIG.channel,
                "appSource": Global.SOURCENAME,
                "appVersion": this.getAppVersion(),
                "appPackageName": this.getAppBundleId(),
            }, (response) => {
                if (response.code == MAINTENANCETIPCODE) { //服务器维护
                    showMaintenancetip(response.msg)
                    return;
                }
                this.config = response["data"];
                this.setGameProvider(this.config.third_company);
                this.setLiveGame(this.config.live_game);
                this.setGameType(this.config.game_type);
                this.setLoginWay(this.config.login_conf);
                this.iosPopUpWindow = Number(this.config.ios_pop_up_window);
                if (callback) callback();

            }, (response) => {
                console.log('-----获取config错误-----错误信息:', JSON.stringify(response))
                if (failcallback) failcallback(response);
            });
        } else {
            if (callback) callback();
        }
    }

    /**获取待播放的跑马灯数据 */
    getBroadCastsFromServer(cb?) {
        HttpUtils.getInstance().get(1, 3, this, "/common/api/marquee/list", {}, (response) => {
            if (response.data && response.data.length > 0) {
                let nowTime = Date.now();
                let validData = response.data.filter(item => {
                    let startTime = item.start_at * 1000;// 将开始时间转换为毫秒
                    let endTime = item.end_at * 1000;//将结束时间转换为毫秒
                    let channel = item.channel;//渠道
                    // return utils.getBitValue(isJump, OS_JumpOpen.ios_h5) == 1
                    if (channel && utils.getBitValue(parseInt(channel), 1) == 1 && ALL_APP_SOURCE_CONFIG.channel == E_CHANEL_TYPE.MAYA) {
                        return nowTime >= startTime && nowTime <= endTime; // 过滤掉非Maya渠道msg
                    } else if (channel && utils.getBitValue(parseInt(channel), 2) == 1 && ALL_APP_SOURCE_CONFIG.channel == E_CHANEL_TYPE.WEB) {
                        return nowTime >= startTime && nowTime <= endTime; // 过滤掉非Web渠道msg
                    } else if (channel && utils.getBitValue(parseInt(channel), 3) == 1 && ALL_APP_SOURCE_CONFIG.channel == E_CHANEL_TYPE.G_CASH) {
                        return nowTime >= startTime && nowTime <= endTime; // 过滤掉非gcash渠道msg
                    }
                    return false
                });
                if (validData.length > 0) {
                    if (validData.length <= 100) {
                        this.broadcastData = validData;
                    } else {//最多展示100条数据
                        this.broadcastData = validData.slice(0, 100);
                    }
                    if (cb) cb(validData);
                } else {
                    this.broadcastData = []; // 清空数据
                    console.log("No valid broadcast data found.");
                }
            } else {
                this.broadcastData = []; // 清空数据
                console.log("No broaddata received.");
                if (cb) cb();
            }
        }, (response) => {
            Global.getInstance().showSimpleTip("Failed to get marquee list.")
            return;
        });
    }

    reqBannerData(cb?) {
        HttpUtils.getInstance().post(1, 3, this, "/avt/api/banner/activity/list", { token: Global.getInstance().token }, (response) => {
            if (response.data && response.data.banner && response.data.banner.length > 0) {
                this.setBannerData(response.data.banner);
                if (cb) cb();
            }
            if (response.data && response.data.pop_banner && response.data.pop_banner.length > 0) {
                this.setPopBannerData(response.data.pop_banner);
            }

        }, function (response) {
        });
    }

    /**首存红包弹窗数据 */
    reqEnvelopeData(cb?) {
        if (!this.token) return;
        let params = {
            token: Global.getInstance().token
        }
        HttpUtils.getInstance().post(1, 3, this, "/common/api/payment/first-deposit-bonus-guide", params, (response) => {
            if (response.data) {
                this.setEnvelopeData(response.data);
                Global.getInstance().envelopeInfo = response.data;
                if (cb) cb();
            }
        }, function (response) {
        });
    }

    setEnvelopeData(msg) {
        this.envelopeData = msg;
    }

    getEnvelopeData() {
        return this.envelopeData;
    }

    getBroadCastData() {
        return this.broadcastData;
    }


    getStoreConfig(type: Number, url: any, callback: any = null) {
        // if (!this.storeConfig) {
        let param = {
            token: this.token,
        };
        if (this.mayaMode)
            param["product_type"] = 7;
        HttpUtils.getInstance().post(type, 3, this, url, param, (response) => {
            this.storeConfig = response.data;
            if (callback) {
                callback();
            }
        });
        // } else {
        //     if (callback) {
        //         callback();
        //     }
        // }
    }

    getShopConfig(type: Number, url: any, callback: any = null) {
        HttpUtils.getInstance().post(type, 3, this, url, {
            token: this.token
        }, (response) => {
            if (url == "/api/global-config/recharge") {
                this.rechargeConfig = response.data;
            }
            if (callback) {
                callback();
            }
        });
    }

    gameIdToString(gameId: number) {
        if (gameId == base.GAME_ID.GID_TONGITS) {
            return "tongits";
        } else if (gameId == 2) {
            return "rummy";
        } else if (gameId == 8) {
            return "andar";
        } else if (gameId == 10) {
            return "dragon";
        } else if (gameId == 12) {
            return "forest";
        } else if (gameId == 11) {
            return "wingo";
        } else if (gameId == 9) {
            return "updown";
        } else if (gameId == 13 || gameId == 14 || gameId == 15) {
            return "carrom";
        } else if (gameId == 18 || gameId == 19 || gameId == 20) {
            return "guess";
        } else if (gameId == 1 || gameId == 4 || gameId == 5) {
            return "teen";
        } else if (gameId == 25 || gameId == 26 || gameId == 40 || gameId == 41) {
            return "truco";
        } else if (gameId == 3) {
            return "texas";
        } else if (gameId == 27) {
            return "cacheta";
        } else if (gameId == 30) {
            return "crash";
        } else if (gameId == 31) {
            return "slots";
        } else if (gameId == 32) {
            return "slots2";
        } else if (gameId == base.GAME_ID.GID_PUSOY) {
            return "pusoy";
        } else if (gameId == base.GAME_ID.GID_COLOR) {
            return "colorgame";
        }
        return null;
    }

    gameStringToId(gameString: any) {
        if (gameString == "andar") {
            return 8;
        } else if (gameString == "dragon") {
            return 10;
        } else if (gameString == "forest") {
            return 12;
        } else if (gameString == "wingo") {
            return 11;
        } else if (gameString == "updown") {
            return 9;
        } else if (gameString == "truco") {
            return 25;
        } else if (gameString == "tongits") {
            return base.GAME_ID.GID_TONGITS;
        } else if (gameString == "rummy") {
            return 2;
        } else if (gameString == "texas") {
            return 3;
        } else if (gameString == "cacheta") {
            return 27;
        } else if (gameString == "crash") {
            return 30;
        } else if (gameString == "slots") {
            return 31;
        } else if (gameString == "slots2") {
            return 32;
        } else if (gameString == "pusoy") {
            return base.GAME_ID.GID_PUSOY;
        } else if (gameString == "colorgame") {
            return base.GAME_ID.GID_COLOR;
        }
        return null;
    }

    isGameLv2(gameId: any) {
        if (gameId >= 30 && gameId <= 32) return true;
        if (gameId >= 8 && gameId <= 12) {
            return true;
        }
        return false;
    }

    public get pokerGameList(): HallGame_Icon[] {
        return this.gameList.filter(gameData => {
            return !this.isGameLv2(gameData.game_id) && gameData.game_id != 100;
        });
    }
    scrollTo(topx, lefty, time) {
        if (cc.sys.isBrowser) {
            if (!Global.getInstance().isNative) {
                setTimeout(() => {
                    window.scrollTo({ top: topx, left: lefty, behavior: 'smooth' });
                }, 200);
            } else {
                Global.getInstance().request_focus_toself();
            }
        }
    }
    isReview() {
        // return true;
        // if (!!this.userdata && this.userdata["withdraw_model"] == 1) {
        //     return true;
        // } else
        //审核中
        // if (!!this.config) {
        //     return this.config["control_config"]["is_review"] == 1;
        // }

        return false;
    }

    isLevelOpen() {
        if (!!this.config) {
            return this.config["isOpen"] == 1;
        }

        return false;
    }

    setBalance(data: base.PlayerBalanceEvent) {
        // balance
        if (!!data.balances && data.balances.length > 0) {
            this.balanceTotal = 0;
            data.balances.forEach(balance => {
                if (balance.type == Global.BALANCE_TYPE.BT_WITHDRAW) {
                    this.withdrawBalance = !(balance.amount / GOLD_RATIO) ? 0 : (balance.amount / GOLD_RATIO);
                }
                this.balanceTotal += !(balance.amount / GOLD_RATIO) ? 0 : (balance.amount / GOLD_RATIO);
            });
        }
        // bonus
        if (!!data.bonuses && data.bonuses.length > 0) {
            this.bonusTotal = 0;
            data.bonuses.forEach(bonuse => {
                if (bonuse.expireTime * 1000 > this.now()) {
                    this.bonusTotal += !(bonuse.amount / GOLD_RATIO) ? 0 : (bonuse.amount / GOLD_RATIO);
                }
            });
        }

        if (data.updateType == BU_GAME) {
            if (!this.hasGamePlayed) { this.logNewEvent("first_play_games", { CUID: Global.getInstance().userdata.user_id }, 1) }
            this.hasGamePlayed = true;
        } else if (data.updateType == SOURCE_ACTIVITY_SPORT) {
            this.hasSportAchieved = true;
        }
        cc.director.emit("update_gold", data);
    }

    noBalanceTip() {
        if (!cc.director.getScene().getChildByName("nobalance")) {
            this.showCommonTip("Not Enough Gold!", this, true, () => {
                this.showShop();
            }, null, "nobalance");
        }
    }

    eval(s: string) {
        return new CalcEval().eval(s);
    }

    fix(n: number, len: number = 2): string {
        let _n: number = new CalcEval().eval(n + "*100");
        _n = Math.floor(_n);
        _n = new CalcEval().eval(_n + "/100");
        return _n.toFixed(len);
    }

    fixCash(n: number, len: number = 2): string {
        return this.fix(new CalcEval().eval(n + "/100"), len);
    }

    fixCashToL(n: number, len: number = 2): string {
        let ret = this.fixCash(n, len);
        let _n = parseFloat(ret);
        if (_n >= 100000) {
            _n = new CalcEval().eval(_n + "/100000");
            ret = _n.toFixed(len) + "L";
        }
        return ret;
    }

    formatTime(time: number, timezone: number) {
        let date = new Date(time + (timezone * 60 * 60 * 1000));
        return date.getUTCFullYear() + "-" + (date.getUTCMonth() + 1) + "-" + date.getUTCDate();
    }

    formatString(strFormat: string, ...str: string[]): string {
        var s = arguments[0];
        for (var i = 0; i < arguments.length - 1; i++) {
            var reg = new RegExp("\\{" + i + "\\}", "gm");
            s = s.replace(reg, arguments[i + 1]);
        }
        return s;
    }

    ThousandformatNum(str: string) {
        let intPartStr: string = '';
        let decPartStr: string = '';

        let dotIndex = str.indexOf('.');
        intPartStr = str.substring(0, dotIndex == -1 ? str.length : dotIndex);
        decPartStr = dotIndex == -1 ? '' : str.substring(dotIndex);
        if (intPartStr.length <= 3) return str;

        let subStr: string[] = [];
        let c = intPartStr.length % 3;
        let l = Math.ceil(intPartStr.length / 3);
        let index = 0;
        for (let i = 0; i < l; i++) {
            let ll = 0;
            if (i == 0) ll = c == 0 ? 3 : c;
            else ll = 3;
            let temp = intPartStr.substr(index, ll);
            subStr.push(temp);
            index += ll;
        }

        let formatStr = '';
        for (let i = 0; i < subStr.length; i++) {
            if (i == 0) formatStr = `${subStr[i]}`;
            else formatStr = `${formatStr},${subStr[i]}`;
        }

        return `${formatStr}${decPartStr}`;
    }
    setCustomerID(id: string) {
        if (cc.sys.OS_ANDROID === cc.sys.os && cc.sys.isNative) {
            jsb.reflection.callStaticMethod("org/cocos2dx/javascript/AppActivity", "setCustomerID", "(Ljava/lang/String;)V", id);
        }
    }
    //type 0：两种打点方式，1：af， 2：firebase
    logNewEvent(eventName: any, jsonData: any, type: number = 0) {
        console.log("logEvent = " + eventName);
        if (type == 0 || type == 1) {
            if (cc.sys.OS_ANDROID === cc.sys.os && cc.sys.isNative) {
                jsb.reflection.callStaticMethod("org/cocos2dx/javascript/AppActivity", "logEvent", "(Ljava/lang/String;Ljava/lang/String;)V", eventName, JSON.stringify(jsonData));
            } else if (cc.sys.OS_IOS === cc.sys.os && cc.sys.isNative) {
                jsb.reflection.callStaticMethod("AFUtils", "trackEvent:params:", eventName, JSON.stringify(jsonData));
            }
        }

        if (type == 0 || type == 2) {
            if (cc.sys.OS_ANDROID === cc.sys.os && cc.sys.isNative) {
                jsb.reflection.callStaticMethod("org/cocos2dx/javascript/FirebaseUtils", "logEvent", "(Ljava/lang/String;Ljava/lang/String;)V", eventName, JSON.stringify(jsonData));
            } else if (cc.sys.OS_IOS === cc.sys.os && cc.sys.isNative) {
                jsb.reflection.callStaticMethod("FirebaseUtils", "logEvent:params:", eventName, JSON.stringify(jsonData));
            }
        }
    }

    setPasteboard(str: any) {
        if (cc.sys.OS_IOS === cc.sys.os && cc.sys.isNative) {
            jsb.reflection.callStaticMethod("DeviceUtils", "copyText:", str);
        } else if (cc.sys.OS_ANDROID === cc.sys.os && cc.sys.isNative) {
            jsb.reflection.callStaticMethod("org/cocos2dx/javascript/AppActivity", "setPasteboard", "(Ljava/lang/String;)V", str);
        } else {
            utils.copyFromiOSWeb(str)
        }
    }

    getPasteboard() {
        let str = null;
        if (cc.sys.OS_IOS === cc.sys.os && cc.sys.isNative) {
            //@ts-ignore
            str = jsb.reflection.callStaticMethod("DeviceUtils", "getCopyText");
        } else if (cc.sys.OS_ANDROID === cc.sys.os && cc.sys.isNative) {
            str = jsb.reflection.callStaticMethod("org/cocos2dx/javascript/AppActivity", "getPasteboard", "()Ljava/lang/String;");
        }

        return str;
        // return '#00000081#'
    }

    removeLanch() {
        // if(this.isRemoveLanch == false){
        //     if (cc.sys.OS_IOS===cc.sys.os) {
        //         console.log("removeLanch");
        //         jsb.reflection.callStaticMethod("AppController", "removeLanch");
        //     }
        //     else if (cc.sys.OS_ANDROID===cc.sys.os) { 
        //         console.log("removeLanch");
        //         jsb.reflection.callStaticMethod("org/cocos2dx/javascript/AppActivity", "removeLanch", "()I");
        //     }
        //     this.isRemoveLanch = true;
        // }
    }

    getDeviceInfo() {
        if (cc.sys.os == cc.sys.OS_IOS && cc.sys.isNative) {
            //@ts-ignore
            this.deviceInfo = jsb.reflection.callStaticMethod("DeviceUtils", "getPhoneInfo");
        }
        else if (cc.sys.os == cc.sys.OS_ANDROID && cc.sys.isNative) {
            this.deviceInfo = jsb.reflection.callStaticMethod("org/cocos2dx/javascript/AppActivity", "getPhoneInfo", "()Ljava/lang/String;");
            // } else if (cc.sys.isBrowser) {
            //     this.deviceInfo = navigator.mediaDevices.getUserMedia()
            console.log("deviceInfo ", this.deviceInfo)
        }
        return null;
    }

    shake() {
        if (this.isvibrate == 0) return;
        if (cc.sys.OS_ANDROID === cc.sys.os && cc.sys.isNative) {
            jsb.reflection.callStaticMethod("org/cocos2dx/javascript/AppActivity", "playSystemSound", "()V");
        } else if (cc.sys.os == cc.sys.OS_IOS && cc.sys.isNative) {
            //@ts-ignore
            jsb.reflection.callStaticMethod("DeviceUtils", "shake");
        }
    }

    getBatteryLevel() {
        if (cc.sys.os == cc.sys.OS_IOS && cc.sys.isNative) {
            //@ts-ignore
            return jsb.reflection.callStaticMethod("DeviceUtils", "getBatteryLevel") > 1 ? 1 : jsb.reflection.callStaticMethod("DeviceUtils", "getBatteryLevel");
        }
        else if (cc.sys.os == cc.sys.OS_ANDROID && cc.sys.isNative) {
            return cc.sys.getBatteryLevel() > 1 ? 1 : cc.sys.getBatteryLevel();
        }
    }

    // 返回设备号的MD5 IOS使用IDFA作为设备号 Android使用android id + serial 作为设备号
    getDeviceId(): string {
        if (this.deviceInfo == null) {
            this.getDeviceInfo();
        }

        if (!!this.deviceInfo) {
            let strarray = this.deviceInfo.split(",");
            if (strarray.length > 0) {
                return Md5.hashStr(strarray[0]).toString();
            }
        }

        // h5端上传唯一标识deviceId给服务器
        let deviceId = cc.sys.localStorage.getItem(Global.GLOBAL_STORAGE_KEY.kDeviceId) || '';
        if (deviceId.length > 0) {
            return deviceId
        } else {
            deviceId = utils.generateUuid()
            cc.sys.localStorage.setItem(Global.GLOBAL_STORAGE_KEY.kDeviceId, deviceId)
            return deviceId
        }

        // return Md5.hashStr("").toString();
    }
    getRedirectUri(): string {
        let returl = ''
        switch (Global.DEBUG) {
            case Global.DEBUG_MODE.TEST:
                returl = 'https://test-h5.nustaronline.vip/'
                break;
            case Global.DEBUG_MODE.DEV:
                returl = 'https://dev-h5.nustaronline.vip/'
                break;
            case Global.DEBUG_MODE.PRE:
                returl = 'https://web.nustaronline.vip/'
                break;
            case Global.DEBUG_MODE.RELEASE:
                returl = 'https://h5.nustargame.com/'
                break;
            default:
                break;
        }
        return returl;
    }
    getDeviceName(): string {
        if (this.deviceInfo == null) {
            this.getDeviceInfo();
        }

        if (!!this.deviceInfo) {
            let strarray = this.deviceInfo.split(",");
            if (strarray.length > 1) {
                return strarray[1];
            }
        }

        return Global.DEVICE_NAME;
    }

    getSystemVersion(): string {
        if (this.deviceInfo == null) {
            this.getDeviceInfo();
        }

        if (!!this.deviceInfo) {
            let strarray = this.deviceInfo.split(",");
            if (strarray.length > 2) {
                return strarray[2];
            }
        }

        return Global.DEVICE_OS;
    }

    getAppBundleId(): string {
        if (this.deviceInfo == null) {
            this.getDeviceInfo();
        }

        if (!!this.deviceInfo) {
            let strarray = this.deviceInfo.split(",");
            if (strarray.length > 3) {
                return strarray[3];
            }
        }

        return ALL_APP_SOURCE_CONFIG.app_bundle_id;
    }

    getAppVersion(): string {
        if (this.deviceInfo == null) {
            this.getDeviceInfo();
        }

        if (!!this.deviceInfo) {
            let strarray = this.deviceInfo.split(",");
            if (strarray.length > 4) {
                return strarray[4];
            }
        }

        return ALL_APP_SOURCE_CONFIG.app_version;
    }

    getCurrentLanguage() {
        if (this.deviceInfo == null) {
            this.getDeviceInfo();
        }

        if (!!this.deviceInfo) {
            let strarray = this.deviceInfo.split(",");
            if (strarray.length > 5) {
                return strarray[5];
            }
        }

        return null;
    }

    getCurrentTimeZone() {
        if (this.deviceInfo == null) {
            this.getDeviceInfo();
        }

        if (!!this.deviceInfo) {
            let strarray = this.deviceInfo.split(",");
            if (strarray.length > 6) {
                return strarray[6];
            }
        }

        return null;
    }

    getCountryCode() {
        // if(this.deviceInfo == null){
        //     this.getDeviceInfo();
        // }

        // if(!!this.deviceInfo){
        //     let strarray = this.deviceInfo.split(",");
        //     if(strarray.length > 7){
        //         return strarray[7];
        //     }
        // }

        //return "BD";
        return "BR";
    }
    getCurrentChannel() {
        if (cc.sys.isNative) {
            let os = utils.myOS()
            if (os == "ios") {
                return DOWNLOAD_CHANNEL.IOS
            } else if (os == "android") {
                return this.downloadChannel;
            }
            return "PC"
        } else {
            if (this.mayaMode) return DOWNLOAD_CHANNEL.MAYA
            else if (this.gcashMode) return DOWNLOAD_CHANNEL.GCASH
            return DOWNLOAD_CHANNEL.WEB_H5
        }
    }

    getSourceCode() {
        let os = utils.myOS()
        if (os == "windows" || os == "macOS") {
            if (cc.sys.isBrowser) return 4
        }
        if (os == "ios" || os == "ipad") {
            if (cc.sys.isBrowser) return 1
            else return 3
        }
        if (os == "android") {
            if (cc.sys.isBrowser) return 0
            else return 2
        }
        return null
    }
    available() {
        if (cc.sys.os == cc.sys.OS_IOS && cc.sys.isNative) {
            //@ts-ignore
            return jsb.reflection.callStaticMethod("AppleLoginUtils", "available");
        }
        return false;
    }

    AppleInit() {
        cc.game.on("on_apple_pay_success", (params) => {
            this.hideShowLoading("applepay");
            let receipt = encodeURIComponent(params["encodedReceipt"]);
            let checkReceipt = this.getStoreageData(Global.GLOBAL_STORAGE_KEY.KAPPLERECEIPT, "");

            if (checkReceipt != receipt) {
                this.setStoreageData(Global.GLOBAL_STORAGE_KEY.KAPPLERECEIPT, receipt);
                HttpUtils.getInstance().post(3, 0, this, "/api/payment/balance/recheck", {
                    token: this.token,
                    appleReceipt: receipt,
                }, () => {
                    this.showSimpleTip("Payment Successful!");
                }, () => {
                    this.showSimpleTip("Payment Error, Please Try Again.");
                });
            }
        });

        cc.game.on("on_apple_pay_failed", (params) => {
            this.hideShowLoading("applepay");
            this.showSimpleTip("Payment Error, Please Try Again.");
        });
    }

    getCullingMask(list: string[]) {
        let groups: any[] = cc.game.config.groupList;
        let groupIndexs: number[] = [];
        groups.forEach((val, index) => {
            if (-1 != list.indexOf(val)) {
                groupIndexs.push(index);
            }
        });

        let cullingMask = 0;
        groupIndexs.forEach(e => {
            cullingMask += Math.pow(2, e);
        });

        return cullingMask;
    }

    downloadUrl() {
        if (this.isReview()) {
            if (cc.sys.os === cc.sys.OS_ANDROID) {
                // return "https://play.google.com/store/apps/details?id=" + this.getAppBundleId();
                return "https://h5cdn.playzone.vip/"
            }
        }
        // if (APP_SOURCE.NAME == APP_SOURCE_CONFIG.CLUB.NAME) {
        //     return "http://www.grandclubpro.com"
        // }
        // return this.config["url_download"]; //可配置
        return "https://h5cdn.playzone.vip/"
    }

    inviteCommand(): string {
        let url = this.downloadUrl();
        let inviteCode = this.userdata["invite_code"]
        let share_text = "[Wow,come and play a fun game.]{0} click the link and open in browser to download; or copy this message #{1}# and open → Play Zone ←"
        return this.formatString(share_text, url, inviteCode);
    }


    isInviteBind() {
        let invite_code = this.userdata["invite_user_id"]
        return invite_code.length > 0
    }


    isRedAndBlackNotice() {
        return //(!!this.redAndBlackRltData || !!this.redAndBlackRemainTimes);
    }

    // updateWithdraw() {
    // if (this.vip && this.vip["vip_config"]) {
    //     let info = this.vip["vip_config"][this.userdata["vip_level"]]
    //     this.config["withdraw_config"]["count_user_day"] = info["withdraw_count"]
    //     this.config["withdraw_config"]["always_amount"] = info["withdraw_amount_per"]
    //     this.config["withdraw_fee"] = info["withdraw_fee"]
    // }
    // }

    getHallTabs() {
        if (!this.config) {
            return;
        }
        return this.config["tab"];
    }

    static formatDeltaTime(seconds: number): string {
        let _timeLeft: number = seconds;
        let h: number = Math.floor(_timeLeft / 60 / 60);
        _timeLeft -= h * 60 * 60;
        let m: number = Math.floor(_timeLeft / 60);
        _timeLeft -= m * 60;
        let s: number = Math.floor(_timeLeft);
        return (h < 10 ? "0" + h : h)
            + ":" + (m < 10 ? "0" + m : m)
            + ":" + (s < 10 ? "0" + s : s);
    }

    // isLuckyShotOpen() {
    //     return this.config && this.config["luckyshot"] && this.config["luckyshot"] == 1
    // }

    // isLuckyShotAutoOpen() {
    //     let pop = this.config && this.config["luckyshot_pop"] && this.config["luckyshot_pop"] == 1;
    //     return this.isLuckyShotOpen() && pop;
    // }

    share(content: string) {
        if (cc.sys.OS_ANDROID == cc.sys.os && cc.sys.isNative) {
            jsb.reflection.callStaticMethod("org/cocos2dx/javascript/AppActivity", "share", "(Ljava/lang/String;)V", content);
        }
        else {

        }
    }

    shareFb(content: string) {
        if (cc.sys.OS_ANDROID == cc.sys.os && cc.sys.isNative) {
            let ret = jsb.reflection.callStaticMethod("org/cocos2dx/javascript/AppActivity", "shareFaceBook", "(Ljava/lang/String;)Z", content);
        }
        else {

        }
    }

    shareWhatsApp(content: string) {
        if (cc.sys.OS_ANDROID == cc.sys.os && cc.sys.isNative) {
            let ret = jsb.reflection.callStaticMethod("org/cocos2dx/javascript/AppActivity", "shareWhatsApp", "(Ljava/lang/String;)Z", content);
        }
        else {

        }
    }

    shareSms(content: string) {
        if (cc.sys.OS_ANDROID == cc.sys.os && cc.sys.isNative) {
            jsb.reflection.callStaticMethod("org/cocos2dx/javascript/AppActivity", "shareSmS", "(Ljava/lang/String;)V", content);
        }
        else {

        }
    }

    needScreenUp() {
        if (Global.getInstance().isNative) return true;
        //修复 小米原生浏览器 输入框问题
        if(cc.sys.browserType == cc.sys.BROWSER_TYPE_MIUI){
            return true;
        }
        return cc.sys.OS_ANDROID == cc.sys.os && cc.sys.isNative
    }

    getRate(amount: any): any {
        let config = Global.getInstance().rechargeConfig["config"]
        let levels: Object[] = config.filter((element) => {
            return amount >= element["amount_min"] && amount < element["amount_max"]
                && (element["rate"] > 0 || element["bonus_rate"] > 0);
        });
        return levels.length > 0 ? levels[0] : 0;
    }

    //得到商场挽留的数据
    getRetentionData() {
        let retention_option = this.rechargeConfig.options.retention_option
        let options = this.rechargeConfig.options.recharge_options;
        let index = options.indexOf(retention_option)

        let itemdata
        if (cc.sys.os === cc.sys.OS_ANDROID && cc.sys.isNative && Global.getInstance().isReview()) {
            let configs = Global.getInstance().config.shopping;
            let needConfig
            for (let index = 0; index < configs.length; index++) {
                const element = configs[index];
                if (Number(element.amount) == Number(retention_option)) {
                    needConfig = element
                    break
                }
            }
            if (needConfig) {
                console.log("configs[index].bonus_ratio ==>", needConfig.bonus_ratio)
                itemdata = { sku: needConfig.sku, type: 1, name: needConfig.name, price: needConfig.amount, level: index, rate: needConfig.bonus_ratio };
            } else {
                itemdata = { type: 1, name: retention_option, price: retention_option, level: index, rate: 0.15 };
            }
        } else {
            let config = Global.getInstance().getRate(retention_option)
            itemdata = { type: 1, name: retention_option, price: retention_option, level: index, rate: config ? config.rate : 0, configId: config ? config.id : 0 };
        }
        return itemdata
    }

    getActivityDataWithType(type: number) {
        let data = Global.getInstance().activityTypeData
        if (!data) return false
        for (let i = 0; i < data.length; i++) {
            let element = data[i]
            if (element.key == type) {
                return element.status
            }
        }
    }
    //用于原生
    editParentMove(editBox: cc.EditBox, node: cc.Node, up_height = 500) {
        setTimeout(() => {
            if (Global.getInstance().needScreenUp()) {
                cc.tween(node)
                    .by(0.1, { x: 0, y: up_height })
                    .start();
            }
        }, 200)
        this.request_focus_toself();
        this.editBoxzIndex();
    }
    editBoxMove(editBox: cc.EditBox, node: cc.Node) {
        setTimeout(() => {
            if (Global.getInstance().needScreenUp()) {
                let offsetY = utils.getEditBoxOffset(editBox)
                let action = cc.moveTo(0.1, cc.v2(node.x, node.y + offsetY))
                node.runAction(action);
            }
        }, 200)
        this.request_focus_toself();
        this.editBoxzIndex();
    }

    editBoxzIndex() {
        if (!cc.sys.isNative) {
            let muDive = document.getElementsByClassName("cocosEditBox");
            if (muDive) {
                muDive[0]['style'].zIndex = cc.macro.MAX_ZINDEX;
                // muDive[0]['style']
                setTimeout(() => {
                    muDive[0]['focus']();
                }, 100);
            }
        }
    }
    //退出 登录
    logout(isNoClearToekn?: boolean) {
        SocketUtils.getInstance().close();//关闭socket
        KycMgr.instance.clearData();//退出登录 把kyc状态信息也清理一下
        serviceMgr.instance.logout();
        if (!isNoClearToekn) {
            Global.getInstance().clearToken();
            //清除本地缓存信息
            Global.getInstance().setStoreageData(Global.GLOBAL_STORAGE_KEY.KYC, '');
        }
        //目前maya 和web 端没有用到
        // if (Global.getInstance().getStoreageData(Global.GLOBAL_STORAGE_KEY.LOGIN_TYPE + Global.getInstance().userdata.user_id, 0) == 0) {
        //     if (cc.sys.isBrowser) {
        //         if (Global.getInstance().curLoginType == ELOGIN_TYPE.LOGIN_GOOGLE) {
        //             cc.director.emit("GoogleLogout")
        //         } else if (Global.getInstance().curLoginType == ELOGIN_TYPE.LOGIN_FACEBOOK) {
        //             cc.director.emit("FaceBookLogout")
        //         }
        //     } else {
        //         //to do 
        //         if (cc.sys.OS_ANDROID === cc.sys.os && cc.sys.isNative) {
        //             jsb.reflection.callStaticMethod("org/cocos2dx/javascript/AppActivity", "WXLogoutReq", "()V");
        //         } else if (cc.sys.os == cc.sys.OS_IOS && cc.sys.isNative) {
        //             jsb.reflection.callStaticMethod("FacebookUtils", "logout");
        //         }
        //     }
        // }
        Global.getInstance().mgm = null;
        Global.getInstance().token = null;
        Global.getInstance().userdata = null;
        Global.getInstance().activityData = null;
        Global.getInstance().rechargeConfig = null;
        Global.getInstance().storeConfig = null;
        Global.getInstance().giftConfig = null;
        Global.getInstance().withdrawBalance = 0;
        Global.getInstance().balanceTotal = 0;
        Global.getInstance().bonusTotal = 0;
        Global.getInstance().nextluckyShotTime = 0;
        Global.getInstance().luckyshotRemainTimes = 0;
        Global.getInstance().curLoginType = -1
        // Global.getInstance().redAndBlackRemainTimes = 0;
        // Global.getInstance().redAndBlackRltData = null;
        cc.director.loadScene('loginScene');
    }

    updateBalanceAfterGame(cb?: Function, bUpdateGold: boolean = true) {
        if (!Global.getInstance().token) return
        // let timegap = (this.now() - this.lastReqBanlanceTime)/1000;
        // if (timegap < 2) {
        //     cc.warn("updateBalanceAfterGame too frequently")
        //     return;
        // }
        // this.lastReqBanlanceTime = this.now();
        HttpUtils.getInstance().post(1, 3, this, "/common/api/jili/backhall", {
            token: Global.getInstance().token,
        }, (response) => {
            if (response && response.balance != null) {
                this.balanceTotal = response.balance / GOLD_RATIO
                if (bUpdateGold) cc.director.emit("update_gold");
                if (cb) cb()
            }
        }, (err) => {
        });
    }
    /**是否需要弹出VIP升级提示 */
    isNeedShowVipTip(): boolean {
        let is_vip = 0;
        if (Global.getInstance().userdata) {
            is_vip = parseInt(Global.getInstance().userdata.is_vip);
        }
        if (!is_vip) return false;
        /**检查用户升级为VIP */
        const currentDate = new Date();
        const dayOfMonth = currentDate.getDate();
        const isWithinFirstHalf = dayOfMonth >= 1 && dayOfMonth <= 15;
        const isWithinSecondHalf = dayOfMonth >= 16 && dayOfMonth <= 31;

        let has_shown_upgrade_tip = Global.getInstance().getStoreageData("HAS_SHOWN_UPGRADE_TIP" + Global.getInstance().userdata.user_id, false);
        let has_shown_upgrade_tip2 = Global.getInstance().getStoreageData("HAS_SHOWN_UPGRADE_TIP2" + Global.getInstance().userdata.user_id, false);
        if (is_vip && !has_shown_upgrade_tip && isWithinFirstHalf) {//判断用户是否在前半周期(1-15号)升级
            //用户升级为vip
            //has_shown_upgrade_tip = false;//重置状态
            //Global.getInstance().setStoreageData("HAS_SHOW_UPGRADE_TIP"+Global.getInstance().userdata.user_id, true);//本地存储 是否弹出过vip升级提示
            return true;
        }
        if (is_vip && !has_shown_upgrade_tip2 && isWithinSecondHalf) {//判断用户是否在后半周期(>=16号)升级
            //用户升级为vip
            //has_shown_upgrade_tip2 = false;//重置状态
            //Global.getInstance().setStoreageData("HAS_SHOW_UPGRADE_TIP2"+Global.getInstance().userdata.user_id, true);//本地存储 是否弹出过vip升级提示
            return true;
        }
        return false;
    }
    //是否需要显示 返奖弹窗
    isNeedShowActivityBonus(): boolean {
        if (!this.token) return false;
        if (!this.popActivityBonus && !this.popActivityBonus.length) return false;
        return this.popActivityBonus.length > 0;
    }
    /**是否需要弹出PopupBanner弹窗 */
    isNeedShowPopupBanner(): boolean {
        if (!this.popBanners && !this.popBanners.length) return false;
        let pb = [];
        let result = false;
        for (let i = 0; i < this.popBanners.length; i++) {
            let element = this.popBanners[i];
            let idUniq = "_" + POPBANNERS + element.id;
            if (Global.getInstance().userdata && Global.getInstance().userdata.user_id) {
                idUniq = Global.getInstance().userdata.user_id + "_" + POPBANNERS + element.id;
            }
            let p = Global.getInstance().getStoreageData(idUniq, "");
            let p_pop = p.split('_');
            if (p_pop[1] !== utils.getToday()) {
                Global.getInstance().removeStoreageData(idUniq);
                p = Global.getInstance().getStoreageData(idUniq, "");
                p_pop = p.split('_');
            }
            pb.push(p_pop[0]);
            if (pb[i] == "" || parseInt(pb[i]) <= element.show_count) {
                result = true;
                break;
            }
        }
        return result;
    }
    //加载提现配置 是否是充值
    loadWithdrawConfig(cb?, deposit = false) {
        if (!Global.getInstance().token) {
            //未登录 跳转登录页
            uiManager.instance.showDialog(UI_PATH_DIC.phonePasswordLogin, [{ way: LOGIN_WAY.PhoneCode }], null, DEEP_INDEXZ.LOGIN)
            return;
        }
        if (deposit) {
            cb && cb();
        } else {
            //1倍流水风控 验证
            HttpUtils.getInstance().post(4, 3, this, "/common/api/withdraw/risk", {
                token: Global.getInstance().token,
            }, (response) => {
                if (response.data && response.data.status == 0) {
                    uiManager.instance.showDialog(UI_PATH_DIC.BeforeWithdrawal, [response.data], null, DEEP_INDEXZ.BEFORE_WITHDRAW);
                } else {
                    cb && cb();
                }
            }, (response) => {
                cb && cb();
            });
        }
    }
    /**是否需要弹出注册奖励弹窗 */
    isNeedShowRegisterBonus(): boolean {
        if (Global.getInstance().registeraward && Global.getInstance().registeraward.type) {
            let type = Global.getInstance().registeraward.type;
            let amount = Global.getInstance().registeraward.amount;
            if ((type == 1 || type == 3 || type == 2) && amount > 0) {
                return true;
            }
        } else {
            return false;
        }
    }

    /**是否需要展示首存红包弹窗 */
    isNeedShowEnvelopePop(): boolean {
        if (!this.token) {
            return false;
        }
        if (parseInt(Global.getInstance().envelopeInfo.first_deposit_bonus_guide_is_start) == 1 && !Global.getInstance().envelopeInfo.is_first_recharge) {//用户未完成首充且活动开启
            let idEnv = "_ENVELOPE";
            if (Global.getInstance().userdata && Global.getInstance().userdata.user_id) {
                idEnv = Global.getInstance().userdata.user_id + "_ENVELOPE";
            }
            let en = Global.getInstance().getStoreageData(idEnv, "");
            let en_pop = en.split('_');
            if (en_pop[1] !== utils.getToday()) {
                Global.getInstance().removeStoreageData(idEnv);
                en = Global.getInstance().getStoreageData(idEnv, "");
                en_pop = en.split('_');
            }
            if (en == "" || parseInt(en_pop[0]) <= 3) {
                return true;
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    /**是否需要弹出任务弹窗 */
    isNeedShowTaskPop(): boolean {
        return false;
        //获取当前日期
        const currentDate = new Date();
        const currentDateStr = `${currentDate.getFullYear()}-${currentDate.getMonth() + 1}-${currentDate.getDate()}`;
        if (!!Global.getInstance().token) {
            //从本地存储中读取最后一次弹出时间
            const lastEnterDateStr = cc.sys.localStorage.getItem("TASK_LAST_ENTER_TIME" + Global.getInstance().userdata.user_id, "");
            //判断是否为今天首次进入
            if (lastEnterDateStr !== currentDateStr) {
                return true; //今天首次进入
            }
        }
        return false; //不是今天首次进入
    }

    /**是否需要弹出转盘弹窗 */
    isNeedShowSpin(): boolean {
        let currentDate = new Date();
        let currentDateStr = `${currentDate.getFullYear()}-${currentDate.getMonth() + 1}-${currentDate.getDate()}`;
        if (!!Global.getInstance().token) {
            let lastEnterDateStr = Global.getInstance().getStoreageData("SPIN_LAST_ENTER_TIME" + Global.getInstance().userdata.user_id, "");
            if (lastEnterDateStr !== currentDateStr && this.spinInfo.is_start == 1) {
                //修改逻辑 不自动弹出
                // return true; 
            }
            if (this.spinInfo.real_left_times > 0 && this.spinInfo.is_start == 1) {
                return true;
            }
        }
        return false;
    }

    isNeedShowRankPop() {
        let currentDate = new Date();
        let currentDateStr = `${currentDate.getFullYear()}-${currentDate.getMonth() + 1}-${currentDate.getDate()}`;
        if (!!Global.getInstance().token) {
            let lastEnterDateStr = Global.getInstance().getStoreageData("RANK_LAST_ENTER_TIME", "");
            if (lastEnterDateStr == "" && this.yesterdayRankInfo) {
                return true;
            }
            let split = lastEnterDateStr.split("_");
            if (split && split[0] && split[1] && this.yesterdayRankInfo) {
                if (split[0] != currentDateStr || (split[0] == currentDateStr && parseInt(split[1]) < 2)) {
                    return true;
                }
            }
        }
        return false;
    }

    isNeedShowRankPopJili() {
        let currentDate = new Date();
        let currentDateStr = `${currentDate.getFullYear()}-${currentDate.getMonth() + 1}-${currentDate.getDate()}`;
        if (!!Global.getInstance().token) {
            let lastEnterDateStr = Global.getInstance().getStoreageData("RANK_LAST_ENTER_TIME_JILI", "");
            if (lastEnterDateStr == "" && this.yesterdayRankInfoJili) {
                return true;
            }
            let split = lastEnterDateStr.split("_");
            if (split && split[0] && split[1] && this.yesterdayRankInfoJili) {
                if (split[0] != currentDateStr || (split[0] == currentDateStr && parseInt(split[1]) < 2)) {
                    return true;
                }
            }
        }
        return false;
    }

    isNeedShowWalletTask() {
        if (!Global.getInstance().token) return false;

        if (!this.data.walletTaskData) return false;

        if (this.data.enterHomeType) {
            if (this.data.existWalletTask()) return true;

            if (this.data.existNewWalletTask()) return true;

            return false;
        }
        else {
            return this.data.getWalletTaskRewardAmount() > 0;
        }
    }

    //显示奖励 弹框
    showGetBonusPop() {
        if (Global.getInstance().registeraward && Global.getInstance().registeraward.type) {
            if (Global.getInstance().getPageId() != E_PAGE_TYPE.HALL) { return }//控制必须在hall 页面弹出
            let type = Global.getInstance().registeraward.type;
            let amount = Global.getInstance().registeraward.amount;
            if (type == 1 || type == 3) {
                // let string1 = "<color=#707070>Welcome to NUSTAR Online!\n\nYou've got </c>"+"<color=#F26225>"+"<b>₱"+amount+"</b></c>"+"<color=#707070> bonus!</c>"
                // Global.getInstance().showCommonTip2({ word: string1, confirm: "Done" }, this, true, () => {},null,null,"Congratulations", "register");
                Global.getInstance().updateBalanceAfterGame();//刷新左上角 金币余额
                showActivityBonus(AWARD_UPDATE_TYPE.REGISTER_USER)
            } else if (type == 2) {
                // let string1 = "<color=#707070>Congratulations on successfully binding \nyour mobile phone number\n\nYou've got</c>"+"<color=#F26225>"+"<b>₱"+amount+"</b></c>"+"<color=#707070> bonus!</c>";
                // Global.getInstance().showCommonTip2({ word: string1, confirm: "Done" }, this, true, () => {},null,null,"Congratulations", "bind");
                Global.getInstance().updateBalanceAfterGame();//刷新左上角 金币余额
                showActivityBonus(AWARD_UPDATE_TYPE.REGISTER_USER)
            }
            Global.getInstance().registeraward.type = 0;//防止重复弹框
        }
    }

    addToMsgBox(msgs) {
        if (msgs.length == 0) return;
        msgs.forEach(msg => {
            if (msg.msgType == TYPE_ACTIVITY_BALANCE_CHANGE) {
                if ((msg.msgContent.update_type == E_BALANCE_UPDATE.SOURCE_ADMIN_ACTIVITY_DAILY_REBATE || msg.msgContent.update_type == E_BALANCE_UPDATE.SOURCE_ACTIVITY_DAILY_REBATE) ||
                    (msg.msgContent.update_type == E_BALANCE_UPDATE.SOURCE_ADMIN_ACTIVITY_WEEKLY_SIGNIN || msg.msgContent.update_type == E_BALANCE_UPDATE.SOURCE_ACTIVITY_WEEKLY_SIGNIN) ||
                    (msg.msgContent.update_type == E_BALANCE_UPDATE.SOURCE_ADMIN_ACTIVITY_PAYDAY || msg.msgContent.update_type == E_BALANCE_UPDATE.SOURCE_ACTIVITY_PAYDAY)) {
                    let m = new MsgToDeal()
                    m.msgType = msg.msgType;
                    m.msgContent = {
                        update_type: msg.msgContent.update_type,
                        amount: msg.msgContent.amount,
                    }
                    m.expire = msg.expire;
                    m.update_at = msg.update_at
                    this.msgsToDeal.push(msg)
                }
            } else if (msg.msgType == TYPE_BALANCE_RECHARGE) {
                let m = new MsgToDeal()
                m.msgType = msg.msgType;
                m.msgContent = {
                    update_type: msg.msgContent.update_type,
                    amount: msg.msgContent.amount,
                }
                this.rechargeMsgToDeal.push(msg)
            }
        });
        this.dealBalanceRecharge()
        this.setStoreageData(Global.GLOBAL_STORAGE_KEY.kMsgsBox, JSON.stringify(this.msgsToDeal))
    }
    dealBalanceRecharge() {
        if (this.rechargeMsgToDeal.length == 0) return;
        let p = {
            amount: 0,
            bonus: 0
        }
        for (let i = 0; i < this.rechargeMsgToDeal.length; i++) {
            const element = this.rechargeMsgToDeal[i];
            if (element.msgContent.update_type == E_BALANCE_UPDATE.SOURCE_RECHARGE) {
                p.amount += element.msgContent.amount;
            } else if (element.msgContent.update_type == E_BALANCE_UPDATE.SOURCE_RECHARGE_CASHBACK) {
                p.bonus += element.msgContent.amount;
            }
        }
        this.rechargeMsgToDeal = []
        Global.getInstance().paySuccess(p);
    }
    delMsgBox(updateTime, msgType?, updateType?) {
        if (this.msgsToDeal.length == 0) return;

        for (let index = 0; index < this.msgsToDeal.length; index++) {
            const element = this.msgsToDeal[index];
            if (element.update_at == updateTime) {
                if (msgType != null && updateType != null) {
                    if (msgType == element.msgType && updateType == element.msgContent.update_type) {
                        this.msgsToDeal.splice(index, 1)
                        break;
                    }
                } else if (msgType != null) {
                    if (msgType == element.msgType) {
                        this.msgsToDeal.splice(index, 1)
                        break;
                    }
                } else if (updateType != null) {
                    if (updateType == element.msgContent.update_type) {
                        this.msgsToDeal.splice(index, 1)
                        break;
                    }
                }
            }
        }
        this.setStoreageData(Global.GLOBAL_STORAGE_KEY.kMsgsBox, JSON.stringify(this.msgsToDeal))
    }
    getAllMsgBox() {
        let str = this.getStoreageData(Global.GLOBAL_STORAGE_KEY.kMsgsBox)
        if (!str) return null;
        this.msgsToDeal = JSON.parse(str)
        return this.msgsToDeal
    }
    getMsgBox(timeStamp, msgType?: number, updateType?: number) {
        if (this.msgsToDeal.length == 0) {
            let str = this.getStoreageData(Global.GLOBAL_STORAGE_KEY.kMsgsBox)
            if (!str) return null;
            this.msgsToDeal = JSON.parse(str)
        }
        for (let index = 0; index < this.msgsToDeal.length; index++) {
            const element = this.msgsToDeal[index];
            if (element.update_at == timeStamp) {
                if (msgType && updateType) {
                    if (element.msgType == msgType && element.msgContent.update_type == updateType) return element
                } else {
                    return element;
                }
            }
        }
        return null;
    }

    setLiveGame(livegameData) {
        if (!livegameData) return;
        this.livegameData = livegameData;
    }
    start_get_livedata() {
        // //拳皇直播 使用到下面代码
        for (let index = 0; index < this.livegameData.length; index++) {
            const element = this.livegameData[index];
            //根据ID 判断是否是 拳皇直播游戏
            if (element.game_id == 3765) {
                Global.getInstance().get_apsports_data();
            }
        }
    }
    //转换拳皇直播的开始时间 如果开始了则 显示started
    get_apsports_starttime(times: string) {
        //根据times获取时间戳
        let retstr = '--';
        // const date = new Date(times);
        let timestamp_start = new Date(times).getTime();
        timestamp_start += 4 * 60 * 60 * 1000;
        const ended_stamp = timestamp_start + 2 * 3600 * 1000;
        const timestamp_now = Date.now();
        if (timestamp_start > timestamp_now) {
            const date = new Date(timestamp_start);
            retstr = date.toLocaleString("en-US", {
                timeZone: "Asia/Manila",
                month: "2-digit",
                day: "2-digit",
                hour: "2-digit",
                minute: "2-digit",
                hour12: false
            }).replace(/,/g, ''); // 移除逗号（某些地区格式可能不同）
        } else if (timestamp_now <= ended_stamp) {
            retstr = 'Ongoing'
        } else {
            retstr = 'Ended'
        }
        return retstr;
    }
    //获取拳皇直播数据
    get_apsports_data() {
        const token_ = Global.getInstance().token
        if (!!token_) {
            HttpUtils.getInstance().post(1, 3, this, "/vdr/api/sports/boxing", {
                token: token_
            }, (response) => {
                // console.log('------apstooo:',JSON.stringify(response))
                if (response.data && response.data.list && response.data.list.length > 0) {
                    let list_data = response.data.list;
                    // let is_same = utils.compare2arrayEqual(list_data, Global.getInstance().ap_sports_data);
                    // if(!is_same){
                    Global.getInstance().ap_sports_data = response.data.list;
                    // [{"id":6,"name":"Boxing","leagues":[{"id":197047,"name":"Boxing Matches","events":[{"id":1610115649,"home":"Manny Pacquiao","away":"Mario Barrios","starts":"2025-07-19T22:00:00Z","spreads":[],"totals":[{"points":10.5,"over":"0.877","under":"0.980"}],"moneyline":{"home":"3.670","away":"1.313","draw":null}}]}]}]
                    // console.log('---222---apstooo:',JSON.stringify(response.data.list))
                    cc.director.emit(EMIT_PARAMS.APSPORTS_DATA_CHANGE);
                    // }
                }
            }, (err) => {

            });
        }
        //每15秒 请求一次数据
        setTimeout(() => {
            Global.getInstance().get_apsports_data();
        }, 15000);
    }

    setLoginWay(login_conf) {
        // "login_conf": {
        //     "login_password": 0,1关 0开
        //     "login_google": 1,
        //     "login_facebook": 1
        // }
        this.login_conf = login_conf;
    }

    /**密码登录是否开启 */
    getLoginPasswordStatus() {
        return this.login_conf && this.login_conf?.login_password == '0';
    }
    /**facebook是否开启 */
    getLoginFBStatus() {
        return this.login_conf && this.login_conf.login_facebook == '0';
    }
    /**google是否开启 */
    getLoginGoogleStatus() {
        return this.login_conf && this.login_conf.login_google == '0';
    }

    /**获取Live直播数据 */
    getLiveGame() {
        return this.livegameData;
    }

    setGameType(gameType) {
        if (!gameType) {
            gameType = this.defaultGameType;
        }
        gameType.sort((a, b) => b.sort - a.sort);
        this.gameType = gameType;
    }

    getGameType() {
        return this.gameType;
    }

    isAllGameType(gameTypeList: string[]) {
        const gameTypeAll = this.gameType.map((element) => { return element.id.toString(); });
        return gameTypeAll.every(val => gameTypeList.includes(val));
    }

    isAllProvider(providerList: string[]) {
        const providerAllList = this.provider.map((element) => { return element.id.toString(); });
        return providerAllList.every(val => providerList.includes(val));
    }

    setGameProvider(provider) {
        if (!provider) {
            return;
        }
        let tempList = []
        for (let index = 0; index < provider.length; index++) {
            let element = provider[index];
            //1是正常 2是维护 3是隐藏
            if (element.status != 3) {
                tempList.push(element);
            }
        }
        tempList.sort((a, b) => b.sort - a.sort);
        this.provider = tempList;
    }

    formatAvatar(avatar: string) {
        if (avatar == "" || avatar == "1") return "images/38441c19a785edac53ace52c45f662e7.png"; // 默认头像兼容处理
        return avatar;
    }

    getAvatar() {
        if (this.userdata.avatar == "" || this.userdata.avatar == "1") this.userdata.avatar = "images/38441c19a785edac53ace52c45f662e7.png"; // 默认头像兼容处理

        return this.userdata.avatar;
    }

    getAvatarList(cb?) {
        if (this.avatarList.length == 0) {
            HttpUtils.getInstance().get(3, 3, this, "/open/api/user/avatar/list", {
                token: Global.getInstance().token,
                terminal: CHANEL_PARAM[ALL_APP_SOURCE_CONFIG.channel]
            }, (response) => {
                if (response.data && response.data.List && response.data.List.length > 0) {
                    this.avatarList = response.data.List;
                    cb && cb(response.data.List);
                    return this.avatarList;
                }
            }, (err) => {
                cb && cb([]);
            });
        } else {
            cb && cb(this.avatarList);
            return this.avatarList;
        }
    }

    getGameProvider(cb?) {
        if (this.provider.length == 0) {
            HttpUtils.getInstance().post(3, 3, this, "/common/api/get/game/provider", {
                token: Global.getInstance().token,
            }, (response) => {
                if (response.data && response.data.length > 0) {
                    this.provider = response.data;
                    cb && cb(response.data);
                    return this.provider;
                }
            }, (err) => {
                cb && cb([]);
            });
        } else {
            cb && cb(this.provider);
            return this.provider;
        }
    }

    getGameProviderNameById(companyId) {
        if (this.provider.length <= 0) {
            return "";
        }
        for (let index = 0; index < this.provider.length; index++) {
            let element = this.provider[index];
            if (element.id == companyId) {
                return element.provider;
            }

        }
        return "";
    }

    getGameProviderShortNameById(companyId) {
        if (this.provider.length <= 0) {
            return "";
        }
        for (let index = 0; index < this.provider.length; index++) {
            let element = this.provider[index];
            if (element.id == companyId) {
                if (element.short_name && element.short_name != "") {
                    return element.short_name;
                } else {
                    return element.provider;
                }
            }

        }
        return "";
    }

    getGameProviderIdByName(companyName) {
        if (this.provider.length <= 0) {
            return "";
        }
        for (let index = 0; index < this.provider.length; index++) {
            let element = this.provider[index];
            if (element.provider == companyName) {
                return element.id;
            }
        }
        return "";
    }

    getInterfaceByCompany(company_id) {
        let name = this.getGameProviderNameById(company_id);
        let interfaceId = "";
        switch (name) {
            case "JILI":
                interfaceId = "/vdr/api/jili/login";
                break;

            default:
                interfaceId = "/vdr/api/jili/login";
                break;
        }
        return interfaceId;
    }
    /**跳转直播游戏 拳皇赛使用到 */
    go_apsports_boxing(leagueid, eventid) {
        let params_1 = {
            token: Global.getInstance().token,
            id: 3765
        };
        HttpUtils.getInstance().post(3, 3, this, "/vdr/api/get/third/party/game/list", params_1,
            (response) => {
                if (response.data && response.data[0]) {
                    let gameInfo = response.data[0];
                    // if()
                    let params = {
                        token: Global.getInstance().token,
                        loginId: leagueid,
                        eventId: eventid
                    };
                    HttpUtils.getInstance().post(3, 3, this, "/vdr/api/loginSports/boxing", params,
                        (response) => {
                            console.log('-------直播的数据是什么:', JSON.stringify(response));
                            if (gameInfo.id != "") {
                                showMoreGame(gameInfo.third_party_game_id, gameInfo.company_id, gameInfo.is_jump, gameInfo.id, response);
                            } else {
                                showMoreGame(gameInfo.third_party_game_id, gameInfo.company_id, gameInfo.is_jump, '3765', response);
                            }
                        }, (res) => {
                            if (res && res.msg) {
                                Global.getInstance().showSimpleTip(res.msg);
                            }
                        });
                }
            }, (res) => {
                if (res && res.msg) {
                    Global.getInstance().showSimpleTip(res.msg);
                }
            });

    }
    /**Play指定游戏 */
    getDesignGamePlay(node, gameData) {
        if (!cc.isValid(node)) return;
        let params = {
            token: Global.getInstance().token,
            id: gameData.game_id
        };
        HttpUtils.getInstance().post(1, 3, this, "/vdr/api/get/third/party/game/list", params,
            (response) => {
                if (response.data && response.data[0]) {
                    let gameInfo = response.data[0];
                    if (gameInfo.id != "") {
                        showMoreGame(gameInfo.third_party_game_id, gameInfo.company_id, gameInfo.is_jump, gameInfo.id);
                    } else {
                        showMoreGame(gameInfo.third_party_game_id, gameInfo.company_id, gameInfo.is_jump);
                    }
                }
            }, (res) => {
                if (res && res.msg) {
                    Global.getInstance().showSimpleTip(res.msg);
                }
            });
    }
    /**根据gid跳转指定游戏 */
    getGameByGID(gid) {
        let params = {
            token: Global.getInstance().token,
            id: gid
        };
        HttpUtils.getInstance().post(1, 3, this, "/vdr/api/get/third/party/game/list", params,
            (response) => {
                if (response.data && response.data[0]) {
                    let gameInfo = response.data[0];
                    if (gameInfo.id != "") {
                        showMoreGame(gameInfo.third_party_game_id, gameInfo.company_id, gameInfo.is_jump, gameInfo.id);
                    } else {
                        showMoreGame(gameInfo.third_party_game_id, gameInfo.company_id, gameInfo.is_jump);
                    }
                }
            }, (res) => {
                if (res && res.msg) {
                    Global.getInstance().showSimpleTip(res.msg);
                }
            });
    }
    //这里真实的 去其他游戏
    goThirdGameScene(third_game_id: string, company_id: number, id?: string) {
        this.third_game_id = third_game_id;
        this.company_id = company_id;
        this.third_id = id;
        cc.director.loadScene('WebScene')
    }
    goInternalUrl(iUrl: string) {
        this.third_game_id = iUrl;
        this.company_id = -1;
        this.third_id = 'none';
        cc.director.loadScene('WebScene')
    }
    // popWindow() {
    //     cc.director.emit("closeUIEvent")
    // }

    setBannerData(msg) {
        this.bannerData = msg;
    }

    setPopBannerData(msg) {
        this.popBannerData = msg;
    }

    getBannerData() {
        return this.bannerData;
    }

    getPopBannerData() {
        return this.popBannerData;
    }
    /**显示VIP升级提示弹窗 */
    showVipTip() {
        uiManager.instance.showDialog(UI_PATH_DIC.VipTip, null, null, DEEP_INDEXZ.VIPTIPS);
    }
    /**转换为 Cocos Creator 支持的 <color> 标签 */
    convertHtmlToRichText(htmlString: string): string {
        // 使用正则表达式匹配带有color样式的span标签
        const colorRegex = /<span style="color:\s*rgb\((\d+),\s*(\d+),\s*(\d+)\);?">/gi;
        // 将匹配到的rgb颜色值转换为16进制颜色值
        htmlString = htmlString.replace(colorRegex, (match, r, g, b) => {
            const hexColor = `#${parseInt(r).toString(16).padStart(2, '0')}${parseInt(g).toString(16).padStart(2, '0')}${parseInt(b).toString(16).padStart(2, '0')}`;
            return `<color=${hexColor}>`;
        });
        // 替换掉span结束标签为<color>结束标签
        htmlString = htmlString.replace(/<\/span>/gi, '</color>');
        return htmlString;
    }
    clickGoBet(node, type?) {
        //点击Bet Now 跳转至游戏 History 页面
        MoreGameManager.instance().queryHistoryGameList(() => {
            let historyList = MoreGameManager.instance().getHistoruGameData();
            //如果 History 页面无数据，跳转至 Like 页，若 Like 页也无数据。跳转至首页 Top Game 的位置
            if (historyList.length > 0) {
                if (type) {
                    cc.director.emit("showAllGameSearchView", "History", type);
                } else {
                    cc.director.emit("showAllGameSearchView", "History");
                }
            } else {
                MoreGameManager.instance().queryLikeGameList(() => {
                    let likeList = MoreGameManager.instance().getLikeGameData();
                    if (likeList.length > 0) {
                        if (type) {
                            cc.director.emit("showAllGameSearchView", "Like", type);
                        } else {
                            cc.director.emit("showAllGameSearchView", "Like");
                        }
                    } else {
                        node.destroy();
                        if (type && type == "before_withdrawal") {
                            cc.director.emit('close_withdrawal');
                        }
                        cc.director.emit("ReturnToHome", "", "home");
                    }
                })
            }
        });
    }
    // 隐藏首存红包按钮
    hideEnvelopeBtn(zIndex_m: number = -1) {
        let btn_envelope = Global.getInstance().popNode.getChildByName("btn_envelope");
        if (btn_envelope) btn_envelope.zIndex = zIndex_m;
    }

    // 隐藏转盘按钮
    hide_spin_btn() {
        let btn_spin = Global.getInstance().popNode.getChildByName("btn_spin");
        if (btn_spin) btn_spin.zIndex = -1;
    }

    /**点击游戏Icon按钮 */
    clickGameIconBtn(gamename) {
        let gid = gamename;
        this.hideGameList = Global.getInstance().all_hide_game_list;
        let hide_game = this.hideGameList.filter(ele => ele == gid);
        if (hide_game.length > 0) {
            Global.getInstance().showSimpleTip("Currently under game maintenance");
        } else {
            Global.getInstance().getGameByGID(gid);
        }
    }

    getSpinInfo(type?) {
        let token_ = Global.getInstance().token;
        let parms = {};
        if (token_) {
            parms = { token: token_ };
        }

        HttpUtils.getInstance().get(1, 0, this, "/open/api/activity/spin/config", parms, (response) => {
            let spindata = response?.data;
            Global.getInstance().spinInfo = spindata;
            if (spindata && spindata.is_start && spindata.is_start == 1) {
                Global.getInstance().showSpinView(type);
            } else {
                Global.getInstance().showSimpleTip("The activity has ended!");
            }
        }, function (response) {
        });
    }

    showSpinView(type?) {
        if (!Global.getInstance().token) {
            uiManager.instance.showDialog(UI_PATH_DIC.phonePasswordLogin, [{ way: LOGIN_WAY.PhoneCode }], null, DEEP_INDEXZ.LOGIN)
            return
        }
        let spin = Global.getInstance().popNode.getChildByName("SpinView");
        if (spin && spin.isValid) {
            spin.active = true;
            return;
        }

        uiManager.instance.loadPrefabByLoading(UI_PATH_DIC.SpinView, cc.Prefab, () => {
        }, (err, prefab: any) => {
            if (err) {
                return;
            }
            let spin = Global.getInstance().popNode.getChildByName("SpinView");
            if (spin && spin.isValid) {
                return;
            }
            let node = cc.instantiate(prefab);
            node.name = "SpinView"
            node.zIndex = DEEP_INDEXZ.SPINWHEEL;
            node.position = new cc.Vec3(0, 0);
            node.parent = Global.getInstance().popNode;
            let script = node.getComponent(SpinWheel);
            script.initType(type);
        });
    }

    cashbackDetials(cb?) {
        HttpUtils.getInstance().post(1, 3, this, "/avt/api/activity/rebate-conf", {
            token: Global.getInstance().token,
            config_type: 4,//1:普通返水 4:vip返水
        }, (response) => {
            if (response.data) {
                this.isVipActivityOn = response.data.status;
            }
            if (cb) cb();
        }, (response) => {
            if (cb) cb();
        });
    }

    getVipActivityStatus() {
        return this.isVipActivityOn;
    }

    showActivityView(activeType, type?, start_time?, end_time?) {
        let prefabname = "";
        if (activeType == ACTIVITY_TYPE.SIGNBIND) {//signup
            prefabname = "prefab/hall/signbind_bonus";
        } else if (activeType == ACTIVITY_TYPE.FIRSTDEPOSIT) {//firstrecharge
            prefabname = "prefab/hall/firsttime_bonus";
        } else if (activeType == ACTIVITY_TYPE.CASHBACK) {//cashback
            prefabname = "prefab/hall/cashback_view";
        } else if (activeType == ACTIVITY_TYPE.CASHBACK_DETIALS) {//cashback detials(普通返水详情)
            prefabname = "prefab/hall/cashback_detials";
        } else if (activeType == ACTIVITY_TYPE.VIP_CASHBACK_DETIALS) {//vip cashback detials(vip专属返水详情)
            prefabname = "prefab/hall/cashback_vipdetials";
        } else if (activeType == ACTIVITY_TYPE.VIPCASHBACK) {//vip cashback
            prefabname = "prefab/hall/cashback_vipview";
        }

        Global.getInstance().showLoading("setActivityLoading");
        uiManager.instance.loadPrefabByLoading(prefabname, cc.Prefab, (finish, total, item) => {
        }, (err, prefab: any) => {
            Global.getInstance().hideShowLoading("setActivityLoading");
            if (err) {
                return;
            }
            let pop = cc.instantiate(prefab);
            pop.parent = Global.getInstance().popNode;
            let script;
            if (activeType == ACTIVITY_TYPE.SIGNBIND) {
                script = pop.getComponent(PromoDetialSignup);
            } else if (activeType == ACTIVITY_TYPE.FIRSTDEPOSIT) {
                script = pop.getComponent(PromoDetialFirstCharge);
            } else if (activeType == ACTIVITY_TYPE.CASHBACK) {
                script = pop.getComponent(PromoDetialCashback);
            } else if (activeType == ACTIVITY_TYPE.VIPCASHBACK) {
                script = pop.getComponent(PromoDetialvipCashback);
            }
            script.initType(type);
        });
    }
    popBanner_jump(element, type?) {
        let starttime = utils.timeToTimestamp(element.start_at);
        let endtime = utils.timeToTimestamp(element.end_at);
        const jumptype = parseInt(element.activity_list)
        //是否是vip用户
        let isVip;
        if (Global.getInstance().userdata) {
            isVip = parseInt(Global.getInstance().userdata.is_vip);
        } else {
            isVip = 0;
        }
        switch (jumptype) {
            case ACTIVITY_TYPE.CASHBACK:
                Global.getInstance().hide_spin_btn();
                Global.getInstance().hideEnvelopeBtn();
                Global.getInstance().cashbackDetials(() => {
                    if (isVip && Global.getInstance().getVipActivityStatus()) {
                        Global.getInstance().showActivityView(element.activity_list, type, starttime, endtime);
                        setTimeout(() => {
                            let str = "You are a VIP member,and can participate in higher Cashback activities.";//点击Click To Go前往VIP专属返水活动页面
                            Global.getInstance().showCommonTip2({ word: str, confirm: "Click To Go" }, this, false, () => { Global.getInstance().showActivityView(4, "vip_cashback", starttime, endtime); }, null, null, "Tips");
                        }, 10)
                    } else {
                        Global.getInstance().showActivityView(element.activity_list, type, starttime, endtime);
                    }
                })
                break;
            case ACTIVITY_TYPE.VIPCASHBACK:
            case ACTIVITY_TYPE.FIRSTDEPOSIT:
                Global.getInstance().hide_spin_btn();
                Global.getInstance().hideEnvelopeBtn();
                Global.getInstance().showActivityView(element.activity_list, type, starttime, endtime);
                break;
            case ACTIVITY_TYPE.SPIN:
                if (!!Global.getInstance().token) {
                    Global.getInstance().getSpinInfo(type);
                } else {
                    uiManager.instance.showDialog(UI_PATH_DIC.phonePasswordLogin, null, null, DEEP_INDEXZ.LOGIN);
                }
                break;
            case ACTIVITY_TYPE.RANK:
                if (!!Global.getInstance().token) {
                    Global.getInstance().hide_spin_btn();
                    Global.getInstance().hideEnvelopeBtn();
                    uiManager.instance.showDialog(UI_PATH_DIC.Rank, [{ tpye: type }], null, DEEP_INDEXZ.ACTIVITYS);
                } else {
                    uiManager.instance.showDialog(UI_PATH_DIC.phonePasswordLogin);
                }
                break;
            case ACTIVITY_TYPE.RANKJILI:
                if (!!Global.getInstance().token) {
                    Global.getInstance().hide_spin_btn();
                    Global.getInstance().hideEnvelopeBtn();
                    uiManager.instance.showDialog(UI_PATH_DIC.RankJili, [{ tpye: type }], null, DEEP_INDEXZ.ACTIVITYS);
                } else {
                    uiManager.instance.showDialog(UI_PATH_DIC.phonePasswordLogin);
                }
                break;
            case ACTIVITY_TYPE.VALENTINE:
                if (!!Global.getInstance().token) {
                    Global.getInstance().hide_spin_btn();
                    Global.getInstance().hideEnvelopeBtn();
                    //弹出情人节活动
                    uiManager.instance.showDialog(UI_PATH_DIC.Valentine, [{ tpye: type }], null, DEEP_INDEXZ.ACTIVITYS);
                } else {
                    uiManager.instance.showDialog(UI_PATH_DIC.phonePasswordLogin);
                }
                break;
            case ACTIVITY_TYPE.WEEKLY:
                if (!!Global.getInstance().token) {
                    Global.getInstance().hide_spin_btn();
                    Global.getInstance().hideEnvelopeBtn();
                    //weekly payday活动
                    uiManager.instance.showDialog(UI_PATH_DIC.WeeklyPayday, [{ tpye: type }], null, DEEP_INDEXZ.ACTIVITYS);
                } else {
                    uiManager.instance.showDialog(UI_PATH_DIC.phonePasswordLogin);
                }
                break;
            case ACTIVITY_TYPE.PP_DAILY_WINS:
                if (!!Global.getInstance().token) {
                    Global.getInstance().hide_spin_btn();
                    Global.getInstance().hideEnvelopeBtn();
                    //弹出PP Daily Wins
                    uiManager.instance.showDialog(UI_PATH_DIC.PPDailyWins, [{ tpye: type }], null, DEEP_INDEXZ.ACTIVITYS);
                } else {
                    uiManager.instance.showDialog(UI_PATH_DIC.phonePasswordLogin);
                }
                break;
            case ACTIVITY_TYPE.LATE_NIGHT_CASHBACK:
                if (!!Global.getInstance().token) {
                    Global.getInstance().hide_spin_btn();
                    Global.getInstance().hideEnvelopeBtn();
                    //弹出Late Night Cashback
                    uiManager.instance.showDialog(UI_PATH_DIC.LateNightCashback, [{ tpye: type }], null, DEEP_INDEXZ.ACTIVITYS);
                } else {
                    uiManager.instance.showDialog(UI_PATH_DIC.phonePasswordLogin);
                }
                break;
            case ACTIVITY_TYPE.FC_FREE_SPIN:
                if (!!Global.getInstance().token) {
                    Global.getInstance().hide_spin_btn();
                    Global.getInstance().hideEnvelopeBtn();
                    //弹出FC Free Spin活动
                    uiManager.instance.showDialog(UI_PATH_DIC.FCFreeSpinDetail, [{ tpye: type }], null, DEEP_INDEXZ.ACTIVITYS);
                } else {
                    uiManager.instance.showDialog(UI_PATH_DIC.phonePasswordLogin);
                }
                break;
            case ACTIVITY_TYPE.YELLOW_BAT:
                if (!!Global.getInstance().token) {
                    Global.getInstance().hide_spin_btn();
                    Global.getInstance().hideEnvelopeBtn();
                    //弹出Yellow Bat活动
                    uiManager.instance.showDialog(UI_PATH_DIC.YellowBatDetail, [{ tpye: type }], null, DEEP_INDEXZ.ACTIVITYS);
                } else {
                    uiManager.instance.showDialog(UI_PATH_DIC.phonePasswordLogin);
                }
                break;
            case ACTIVITY_TYPE.JILI_FREE_SPIN:
                if (!!Global.getInstance().token) {
                    Global.getInstance().hide_spin_btn();
                    Global.getInstance().hideEnvelopeBtn();
                    //弹出JILI Free Spin活动
                    uiManager.instance.showDialog(UI_PATH_DIC.JILIFreeSpinDetail, [{ tpye: type }], null, DEEP_INDEXZ.ACTIVITYS);
                } else {
                    uiManager.instance.showDialog(UI_PATH_DIC.phonePasswordLogin);
                }
                break;
            case ACTIVITY_TYPE.FC_FREE_SPIN2:
                if (!!Global.getInstance().token) {
                    Global.getInstance().hide_spin_btn();
                    Global.getInstance().hideEnvelopeBtn();
                    //弹出FC FREE SPIN2活动
                    uiManager.instance.showDialog(UI_PATH_DIC.FCFreeSpinDetail2, [{ tpye: type }], null, DEEP_INDEXZ.ACTIVITYS);
                } else {
                    uiManager.instance.showDialog(UI_PATH_DIC.phonePasswordLogin);
                }
                break;
            default:
                Global.getInstance().hide_spin_btn();
                Global.getInstance().hideEnvelopeBtn();
                Global.getInstance().showActivityView(element.activity_list, type);
                break;
        }
    }
    /**点击banner的页面跳转 */
    bannerJumpView(element, type?) {
        let starttime = utils.timeToTimestamp(element.start_at);
        let endtime = utils.timeToTimestamp(element.end_at);
        //是否是vip用户
        let isVip;
        if (Global.getInstance().userdata) {
            isVip = parseInt(Global.getInstance().userdata.is_vip);
        } else {
            isVip = 0;
        }

        if (element.jump_type == 0) {//None
            //不进行跳转
        } else if (element.jump_type == 1) {//Internal Url
            if (cc.sys.os == cc.sys.OS_IOS) {
                utils.assignUrl(element.url)
            } else {
                utils.openUrl(element.url)
            }
        } else if (element.jump_type == 2) {//Outside URL
            if (cc.sys.os == cc.sys.OS_IOS) {
                utils.assignUrl(element.url)
            } else {
                utils.openUrl(element.url)
            }
        } else if (element.jump_type == 4) {//Activity detail
            switch (parseInt(element.activity_list)) {
                case ACTIVITY_TYPE.CASHBACK:
                    Global.getInstance().hide_spin_btn();
                    Global.getInstance().hideEnvelopeBtn();
                    Global.getInstance().cashbackDetials(() => {
                        if (isVip && Global.getInstance().getVipActivityStatus()) {
                            Global.getInstance().showActivityView(element.activity_list, type, starttime, endtime);
                            setTimeout(() => {
                                let str = "You are a VIP member,and can participate in higher Cashback activities.";//点击Click To Go前往VIP专属返水活动页面
                                Global.getInstance().showCommonTip2({ word: str, confirm: "Click To Go" }, this, false, () => { Global.getInstance().showActivityView(4, "vip_cashback", starttime, endtime); }, null, null, "Tips");
                            }, 10)
                        } else {
                            Global.getInstance().showActivityView(element.activity_list, type, starttime, endtime);
                        }
                    })
                    break;
                case ACTIVITY_TYPE.VIPCASHBACK:
                case ACTIVITY_TYPE.FIRSTDEPOSIT:
                    Global.getInstance().hide_spin_btn();
                    Global.getInstance().hideEnvelopeBtn();
                    Global.getInstance().showActivityView(element.activity_list, type, starttime, endtime);
                    break;
                case ACTIVITY_TYPE.SPIN:
                    if (!!Global.getInstance().token) {
                        Global.getInstance().getSpinInfo(type);
                    } else {
                        uiManager.instance.showDialog(UI_PATH_DIC.phonePasswordLogin);
                    }
                    break;
                case ACTIVITY_TYPE.RANK:
                    if (!!Global.getInstance().token) {
                        Global.getInstance().hide_spin_btn();
                        Global.getInstance().hideEnvelopeBtn();
                        uiManager.instance.showDialog(UI_PATH_DIC.Rank, [{ tpye: type }], null, DEEP_INDEXZ.ACTIVITYS);
                    } else {
                        uiManager.instance.showDialog(UI_PATH_DIC.phonePasswordLogin);
                    }
                    break;
                case ACTIVITY_TYPE.RANKJILI:
                    if (!!Global.getInstance().token) {
                        Global.getInstance().hide_spin_btn();
                        Global.getInstance().hideEnvelopeBtn();
                        uiManager.instance.showDialog(UI_PATH_DIC.RankJili, [{ tpye: type }], null, DEEP_INDEXZ.ACTIVITYS);
                    } else {
                        uiManager.instance.showDialog(UI_PATH_DIC.phonePasswordLogin);
                    }
                    break;
                case ACTIVITY_TYPE.VALENTINE:
                    if (!!Global.getInstance().token) {
                        Global.getInstance().hide_spin_btn();
                        Global.getInstance().hideEnvelopeBtn();
                        //弹出情人节活动
                        uiManager.instance.showDialog(UI_PATH_DIC.Valentine, [{ tpye: type }], null, DEEP_INDEXZ.ACTIVITYS);
                    } else {
                        uiManager.instance.showDialog(UI_PATH_DIC.phonePasswordLogin);
                    }
                    break;
                case ACTIVITY_TYPE.WEEKLY:
                    if (!!Global.getInstance().token) {
                        Global.getInstance().hide_spin_btn();
                        Global.getInstance().hideEnvelopeBtn();
                        //weekly payday活动
                        uiManager.instance.showDialog(UI_PATH_DIC.WeeklyPayday, [{ tpye: type }], null, DEEP_INDEXZ.ACTIVITYS);
                    } else {
                        uiManager.instance.showDialog(UI_PATH_DIC.phonePasswordLogin);
                    }
                    break;
                case ACTIVITY_TYPE.PP_DAILY_WINS:
                    if (!!Global.getInstance().token) {
                        Global.getInstance().hide_spin_btn();
                        Global.getInstance().hideEnvelopeBtn();
                        //弹出PP Daily Wins
                        uiManager.instance.showDialog(UI_PATH_DIC.PPDailyWins, [{ tpye: type }], null, DEEP_INDEXZ.ACTIVITYS);
                    } else {
                        uiManager.instance.showDialog(UI_PATH_DIC.phonePasswordLogin);
                    }
                    break;
                case ACTIVITY_TYPE.LATE_NIGHT_CASHBACK:
                    if (!!Global.getInstance().token) {
                        Global.getInstance().hide_spin_btn();
                        Global.getInstance().hideEnvelopeBtn();
                        //弹出Late Night Cashback
                        uiManager.instance.showDialog(UI_PATH_DIC.LateNightCashback, [{ tpye: type }], null, DEEP_INDEXZ.ACTIVITYS);
                    } else {
                        uiManager.instance.showDialog(UI_PATH_DIC.phonePasswordLogin);
                    }
                    break;
                case ACTIVITY_TYPE.FC_FREE_SPIN:
                    if (!!Global.getInstance().token) {
                        Global.getInstance().hide_spin_btn();
                        Global.getInstance().hideEnvelopeBtn();
                        //弹出FC Free Spin活动
                        uiManager.instance.showDialog(UI_PATH_DIC.FCFreeSpinDetail, [{ tpye: type }], null, DEEP_INDEXZ.ACTIVITYS);
                    } else {
                        uiManager.instance.showDialog(UI_PATH_DIC.phonePasswordLogin);
                    }
                    break;
                case ACTIVITY_TYPE.YELLOW_BAT:
                    if (!!Global.getInstance().token) {
                        Global.getInstance().hide_spin_btn();
                        Global.getInstance().hideEnvelopeBtn();
                        //弹出Yellow Bat活动
                        uiManager.instance.showDialog(UI_PATH_DIC.YellowBatDetail, [{ tpye: type }], null, DEEP_INDEXZ.ACTIVITYS);
                    } else {
                        uiManager.instance.showDialog(UI_PATH_DIC.phonePasswordLogin);
                    }
                    break;
                case ACTIVITY_TYPE.JILI_FREE_SPIN:
                    if (!!Global.getInstance().token) {
                        Global.getInstance().hide_spin_btn();
                        Global.getInstance().hideEnvelopeBtn();
                        //弹出JILI Free Spin活动
                        uiManager.instance.showDialog(UI_PATH_DIC.JILIFreeSpinDetail, [{ tpye: type }], null, DEEP_INDEXZ.ACTIVITYS);
                    } else {
                        uiManager.instance.showDialog(UI_PATH_DIC.phonePasswordLogin);
                    }
                    break;
                case ACTIVITY_TYPE.FC_FREE_SPIN2:
                    if (!!Global.getInstance().token) {
                        Global.getInstance().hide_spin_btn();
                        Global.getInstance().hideEnvelopeBtn();
                        //弹出FC FREE SPIN2活动
                        uiManager.instance.showDialog(UI_PATH_DIC.FCFreeSpinDetail2, [{ tpye: type }], null, DEEP_INDEXZ.ACTIVITYS);
                    } else {
                        uiManager.instance.showDialog(UI_PATH_DIC.phonePasswordLogin);
                    }
                    break;
                default:
                    Global.getInstance().hide_spin_btn();
                    Global.getInstance().hideEnvelopeBtn();
                    Global.getInstance().showActivityView(element.activity_list, type);
                    break;
            }
        } else if (element.jump_type == 7) {//Activity Pictures
            if (!Global.getInstance().token) {
                //未登录 跳转登录页
                uiManager.instance.showDialog(UI_PATH_DIC.phonePasswordLogin, [{ way: LOGIN_WAY.PhoneCode }])
                return;
            }
            if (parseInt(element.picture_id) != 0) {
                Global.getInstance().hide_spin_btn();
                Global.getInstance().hideEnvelopeBtn();
                uiManager.instance.showDialog(UI_PATH_DIC.PromoCommonDetail, [{ data: element, tpye: type }], null, DEEP_INDEXZ.ACTIVITYS)
            } else {
                Global.getInstance().showSimpleTip("Sorry, the current event does not exist");
            }
        }
    }
    //是否隐藏或者维护状态
    game_is_normal(game_id: any) {
        let alllist = Global.getInstance().all_maintain_game_list;
        console.log('--------gamid:', game_id)
        if (alllist.length > 0) {
            if (alllist.indexOf(game_id) != -1) {
                return false;
            }
        }
        let all_hidelist = Global.getInstance().all_hide_game_list;
        if (all_hidelist.length > 0) {
            if (all_hidelist.indexOf(game_id) != -1) {
                return false;
            }
        }
        return true;
    }

    //下一帧重置圆角
    reset_roundrecd(sp) {
        if (sp && cc.isValid(sp)) {
            let parent_sp = sp.parent;
            setTimeout(() => {
                let prsrc = parent_sp.getComponent(RoundRectMask)
                prsrc.radius = 55;
            }, 10);
        }
    }
    //根据game_id显示不同的游戏icon
    showGameIcon(sp: cc.Sprite, game_id: string | number) {
        let gameid = game_id;
        let c_gamedata = MoreGameManager.instance().getThirdGameDataWithGameid(gameid);
        //先从本地数据 找icon
        if (c_gamedata) {
            let image = c_gamedata.images;
            if (image != "") {
                if (!/^http/.test(image)) {
                    image = ALL_APP_SOURCE_CONFIG.remote_assets_prod[Global.DEBUG] + image;
                }
                Global.getInstance().download_img(image, sp, () => {
                    this.reset_roundrecd(sp.node)
                })
            }
        } else {
            //如果没有图片 重新请求图片
            let params = {
                token: Global.getInstance().token,
                id: gameid
            };
            HttpUtils.getInstance().post(1, 3, this, "/vdr/api/get/third/party/game/list", params,
                (response) => {
                    if (!sp || !sp.isValid) {
                        return;
                    }
                    if (response.data && response.data[0]) {
                        //这里image 自动带了 前缀
                        Global.getInstance().download_img(response.data[0].images, sp, () => {
                            this.reset_roundrecd(sp.node)
                        })
                    }
                }, (res) => {
                    if (res && res.msg) {
                        Global.getInstance().showSimpleTip(res.msg);
                    }
                });
        }
    }

    /**活动调账枚举数据*/
    actAdjustmentData(cb?) {
        if (!!Global.getInstance().token) {
            HttpUtils.getInstance().get(3, 3, this, "/avt/api/activity/adjustment/enum", {
                token: Global.getInstance().token
            }, (response) => {
                if (response.data && response.data.length > 0) {
                    Global.getInstance().adjustment_data = response.data;
                    if (cb) cb();
                }
            }, (response) => {
                console.log("load adjustment enum data failed.");
                if (cb) cb();
            })
        }
    }

    init_enum_typestr(update_type, type_str, register_str, lab_str?) {
        //产品要求保险起见 客户端先保留枚举
        switch (update_type) {
            case AWARD_UPDATE_TYPE.FIRST_RECHARGE://12-首充奖励
                if (lab_str) lab_str.string = AWARD_NAME.FIRST_DESPOSIT;
                else if (!type_str) type_str = AWARD_NAME.FIRST_DESPOSIT;
                break;
            case AWARD_UPDATE_TYPE.FREE_REGISTRATION://65-注册
                if (lab_str) lab_str.string = register_str;
                else if (!type_str) type_str = register_str;
                break;
            case AWARD_UPDATE_TYPE.CASHBACK://111-返水活动
                if (lab_str) lab_str.string = AWARD_NAME.CASHBACK;
                else if (!type_str) type_str = AWARD_NAME.CASHBACK;
                break;
            case AWARD_UPDATE_TYPE.VIP_CASHBACK://241 119-VIP专属返水活动
            case AWARD_UPDATE_TYPE.VIP_CASHBACK_119:
                if (lab_str) lab_str.string = AWARD_NAME.VIP_CASHBACK;
                else if (!type_str) type_str = AWARD_NAME.VIP_CASHBACK;
                break;
            case AWARD_UPDATE_TYPE.BING_PHONE:
                if (lab_str) lab_str.string = AWARD_NAME.BING_PHONE;
                else if (!type_str) type_str = AWARD_NAME.BING_PHONE;
                break;
            case AWARD_UPDATE_TYPE.DAILY_BETTING:
                if (lab_str) lab_str.string = AWARD_NAME.DAILY_BETTING;
                else if (!type_str) type_str = AWARD_NAME.DAILY_BETTING;
                break;
            case AWARD_UPDATE_TYPE.FIRST_DESPOSIT:
                if (lab_str) lab_str.string = AWARD_NAME.FIRST_DESPOSIT;//也是首充 兼容以前版本
                else if (!type_str) type_str = AWARD_NAME.FIRST_DESPOSIT;//也是首充 兼容以前版本
                break;
            case AWARD_UPDATE_TYPE.INVITEE_GIFT:
                if (lab_str) lab_str.string = AWARD_NAME.INVITEE_GIFT;
                else if (!type_str) type_str = AWARD_NAME.INVITEE_GIFT;
                break;
            case AWARD_UPDATE_TYPE.INVITE_5GIFT:
                if (lab_str) lab_str.string = AWARD_NAME.INVITE_5GIFT;
                else if (!type_str) type_str = AWARD_NAME.INVITE_5GIFT;
                break;
            case AWARD_UPDATE_TYPE.INVITE_FRIENDS:
                if (lab_str) lab_str.string = AWARD_NAME.INVITE_FRIENDS;
                else if (!type_str) type_str = AWARD_NAME.INVITE_FRIENDS;
                break;
            case AWARD_UPDATE_TYPE.PAYDAY_BONUS:
                if (lab_str) lab_str.string = AWARD_NAME.PAYDAY_BONUS;
                else if (!type_str) type_str = AWARD_NAME.PAYDAY_BONUS;
                break;
            case AWARD_UPDATE_TYPE.SIGN_UP_BONUS:
                if (lab_str) lab_str.string = AWARD_NAME.SIGN_UP_BONUS;
                else if (!type_str) type_str = AWARD_NAME.SIGN_UP_BONUS;
                break;
            case AWARD_UPDATE_TYPE.SPORT_FIRST_TIME:
                if (lab_str) lab_str.string = AWARD_NAME.SPORT_FIRST_TIME;
                else if (!type_str) type_str = AWARD_NAME.SPORT_FIRST_TIME;
                break;
            case AWARD_UPDATE_TYPE.SUPERACE_CASH:
                if (lab_str) lab_str.string = AWARD_NAME.SUPERACE_CASH;
                else if (!type_str) type_str = AWARD_NAME.SUPERACE_CASH;
                break;
            case AWARD_UPDATE_TYPE.JILI_GAMES_CASHBACK:
                if (lab_str) lab_str.string = AWARD_NAME.JILI_GAMES_CASH;
                else if (!type_str) type_str = AWARD_NAME.JILI_GAMES_CASH;
                break;
            case AWARD_UPDATE_TYPE.WEEKLY_SIGNIN:
                if (lab_str) lab_str.string = AWARD_NAME.WEEKLY_SIGNIN;
                else if (!type_str) type_str = AWARD_NAME.WEEKLY_SIGNIN;
                break;
            case AWARD_UPDATE_TYPE.CASINO_LEADERBOARD:
                if (lab_str) lab_str.string = AWARD_NAME.CASINO_LEADERBOARD;
                else if (!type_str) type_str = AWARD_NAME.CASINO_LEADERBOARD;
                break;
            case AWARD_UPDATE_TYPE.GET_EASTER_BONUS:
                if (lab_str) lab_str.string = AWARD_NAME.GET_EASTER_BONUS;
                else if (!type_str) type_str = AWARD_NAME.GET_EASTER_BONUS;
                break;
            case AWARD_UPDATE_TYPE.YB_SLOT_CASHBACK:
                if (lab_str) lab_str.string = AWARD_NAME.YB_SLOT_CASHBACK;
                else if (!type_str) type_str = AWARD_NAME.YB_SLOT_CASHBACK;
                break;
            case AWARD_UPDATE_TYPE.SPORTS_LOSS_CASHBACK:
                if (lab_str) lab_str.string = AWARD_NAME.SPORTS_LOSS_CASHBACK;
                else if (!type_str) type_str = AWARD_NAME.SPORTS_LOSS_CASHBACK;
                break;
            case AWARD_UPDATE_TYPE.SPORTS_DAILY_BONUS:
                if (lab_str) lab_str.string = AWARD_NAME.SPORTS_DAILY_BONUS;
                else if (!type_str) type_str = AWARD_NAME.SPORTS_DAILY_BONUS;
                break;
            case AWARD_UPDATE_TYPE.NBA_CHAMPION_PREDICTION:
                if (lab_str) lab_str.string = AWARD_NAME.NBA_CHAMPION_PREDICTION;
                else if (!type_str) type_str = AWARD_NAME.NBA_CHAMPION_PREDICTION;
                break;
            case AWARD_UPDATE_TYPE.SPIN_ACTIVITY:
                if (lab_str) lab_str.string = AWARD_NAME.SPIN_ACTIVITY;
                else if (!type_str) type_str = AWARD_NAME.SPIN_ACTIVITY;
                break;
            case AWARD_UPDATE_TYPE.JILI_LEADERBOARD:
                if (lab_str) lab_str.string = AWARD_NAME.JILI_LEADERBOARD;
                else if (!type_str) type_str = AWARD_NAME.JILI_LEADERBOARD;
                break;
            case AWARD_UPDATE_TYPE.Valentine:
                if (lab_str) lab_str.string = AWARD_NAME.Valentine;
                else if (!type_str) type_str = AWARD_NAME.Valentine;
                break;
            case AWARD_UPDATE_TYPE.WEEKLY_PAYDAY:
                if (lab_str) lab_str.string = AWARD_NAME.WEEKLY_PAYDAY;
                else if (!type_str) type_str = AWARD_NAME.WEEKLY_PAYDAY;
                break;
            case AWARD_UPDATE_TYPE.LATE_NIGHT_CASHBACK:
                if (lab_str) lab_str.string = AWARD_NAME.LATE_NIGHT_CASHBACK;
                else if (!type_str) type_str = AWARD_NAME.LATE_NIGHT_CASHBACK;
                break;
            default:
                break;
        }
    }
    /**点击提现统一方法*/
    clickToWithdrawal() {
        HttpUtils.getInstance().post(3, 3, this, "/common/api/get/withdraw/list", {
            token: Global.getInstance().token,
        }, (response) => {
            if (ALL_APP_SOURCE_CONFIG.channel == E_CHANEL_TYPE.WEB) {
                //有提现账户跳转提现页面
                if (response.data.list && response.data.list.length > 0) {
                    Global.getInstance().loadWithdrawConfig(() => {
                        uiManager.instance.showDialog(UI_PATH_DIC.WithDraw, null, null, DEEP_INDEXZ.WITHDRAW);
                    });
                } else {//无提现账户 直接跳转添加提现账户页面
                    //看是否关闭了所有提现接口 针对web
                    Global.getInstance().loadWithdrawConfig(() => {
                        uiManager.instance.showDialog(UI_PATH_DIC.WithdrawAccount, [{ type: "no_account" }], null, DEEP_INDEXZ.ADD_WITHDRAW)
                    });
                }
            } else if (ALL_APP_SOURCE_CONFIG.channel == E_CHANEL_TYPE.MAYA) {
                Global.getInstance().loadWithdrawConfig(() => {
                    uiManager.instance.showDialog(UI_PATH_DIC.WithDraw, null, null, DEEP_INDEXZ.WITHDRAW)
                });
            } else if (ALL_APP_SOURCE_CONFIG.channel == E_CHANEL_TYPE.G_CASH) {
                Global.getInstance().loadWithdrawConfig(() => {
                    uiManager.instance.showDialog(UI_PATH_DIC.WithDraw, null, null, DEEP_INDEXZ.WITHDRAW)
                });
            }
        });
    }
}

(<any>window).Global = Global;
