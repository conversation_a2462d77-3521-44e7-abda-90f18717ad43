<template>
  <div class="banner-carousel" v-if="banners.length > 0">
    <div class="carousel-container">
      <div 
        class="carousel-track"
        :style="{ transform: `translateX(-${currentIndex * 100}%)` }"
      >
        <div 
          v-for="(banner, index) in banners" 
          :key="banner.id"
          class="banner-item"
          @click="handleBannerClick(banner)"
        >
          <img 
            :src="banner.image_url" 
            :alt="banner.title"
            class="banner-image"
            @load="onImageLoad(index)"
            @error="onImageError(index)"
          />
          <div class="banner-overlay">
            <h3 class="banner-title">{{ banner.title }}</h3>
          </div>
        </div>
      </div>
    </div>

    <!-- 分页指示器 -->
    <div class="pagination-dots" v-if="banners.length > 1">
      <button
        v-for="(banner, index) in banners"
        :key="`dot-${banner.id}`"
        class="dot"
        :class="{ active: index === currentIndex }"
        @click="goToSlide(index)"
      ></button>
    </div>

    <!-- 导航按钮 -->
    <button 
      v-if="banners.length > 1"
      class="nav-btn prev-btn"
      @click="prevSlide"
      :disabled="currentIndex === 0"
    >
      <i class="icon-chevron-left"></i>
    </button>
    <button 
      v-if="banners.length > 1"
      class="nav-btn next-btn"
      @click="nextSlide"
      :disabled="currentIndex === banners.length - 1"
    >
      <i class="icon-chevron-right"></i>
    </button>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import type { BannerData } from '@/types/hall'

interface Props {
  banners: BannerData[]
  autoPlay?: boolean
  autoPlayInterval?: number
}

interface Emits {
  (e: 'banner-click', banner: BannerData): void
}

const props = withDefaults(defineProps<Props>(), {
  autoPlay: true,
  autoPlayInterval: 5000
})

const emit = defineEmits<Emits>()

// 响应式数据
const currentIndex = ref(0)
const isAutoPlaying = ref(false)
const loadedImages = ref<Set<number>>(new Set())
const autoPlayTimer = ref<NodeJS.Timeout>()

// 方法
function handleBannerClick(banner: BannerData) {
  emit('banner-click', banner)
}

function goToSlide(index: number) {
  if (index >= 0 && index < props.banners.length) {
    currentIndex.value = index
    resetAutoPlay()
  }
}

function nextSlide() {
  if (currentIndex.value < props.banners.length - 1) {
    currentIndex.value++
  } else if (props.autoPlay) {
    currentIndex.value = 0 // 循环播放
  }
  resetAutoPlay()
}

function prevSlide() {
  if (currentIndex.value > 0) {
    currentIndex.value--
  } else if (props.autoPlay) {
    currentIndex.value = props.banners.length - 1 // 循环播放
  }
  resetAutoPlay()
}

function startAutoPlay() {
  if (!props.autoPlay || props.banners.length <= 1) return
  
  isAutoPlaying.value = true
  autoPlayTimer.value = setInterval(() => {
    nextSlide()
  }, props.autoPlayInterval)
}

function stopAutoPlay() {
  if (autoPlayTimer.value) {
    clearInterval(autoPlayTimer.value)
    autoPlayTimer.value = undefined
  }
  isAutoPlaying.value = false
}

function resetAutoPlay() {
  stopAutoPlay()
  if (props.autoPlay) {
    setTimeout(() => {
      startAutoPlay()
    }, 1000) // 1秒后重新开始自动播放
  }
}

function onImageLoad(index: number) {
  loadedImages.value.add(index)
  
  // 如果是第一张图片加载完成，开始自动播放
  if (index === 0 && props.autoPlay) {
    startAutoPlay()
  }
}

function onImageError(index: number) {
  console.error(`Failed to load banner image at index ${index}`)
}

// 键盘事件处理
function handleKeydown(event: KeyboardEvent) {
  switch (event.key) {
    case 'ArrowLeft':
      prevSlide()
      break
    case 'ArrowRight':
      nextSlide()
      break
  }
}

// 触摸事件处理
let touchStartX = 0
let touchEndX = 0

function handleTouchStart(event: TouchEvent) {
  touchStartX = event.touches[0].clientX
}

function handleTouchEnd(event: TouchEvent) {
  touchEndX = event.changedTouches[0].clientX
  handleSwipe()
}

function handleSwipe() {
  const swipeThreshold = 50
  const diff = touchStartX - touchEndX
  
  if (Math.abs(diff) > swipeThreshold) {
    if (diff > 0) {
      nextSlide() // 向左滑动，下一张
    } else {
      prevSlide() // 向右滑动，上一张
    }
  }
}

// 监听banners变化
watch(() => props.banners, (newBanners) => {
  if (newBanners.length === 0) {
    stopAutoPlay()
    currentIndex.value = 0
  } else if (currentIndex.value >= newBanners.length) {
    currentIndex.value = newBanners.length - 1
  }
  loadedImages.value.clear()
}, { immediate: true })

// 生命周期
onMounted(() => {
  // 添加键盘事件监听
  document.addEventListener('keydown', handleKeydown)
  
  // 添加触摸事件监听
  const carousel = document.querySelector('.banner-carousel')
  if (carousel) {
    carousel.addEventListener('touchstart', handleTouchStart, { passive: true })
    carousel.addEventListener('touchend', handleTouchEnd, { passive: true })
  }
})

onUnmounted(() => {
  stopAutoPlay()
  document.removeEventListener('keydown', handleKeydown)
  
  const carousel = document.querySelector('.banner-carousel')
  if (carousel) {
    carousel.removeEventListener('touchstart', handleTouchStart)
    carousel.removeEventListener('touchend', handleTouchEnd)
  }
})
</script>

<style scoped>
.banner-carousel {
  position: relative;
  width: 100%;
  margin-bottom: 20px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.carousel-container {
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.carousel-track {
  display: flex;
  width: 100%;
  height: 100%;
  transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.banner-item {
  flex: 0 0 100%;
  position: relative;
  cursor: pointer;
  overflow: hidden;
}

.banner-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.banner-item:hover .banner-image {
  transform: scale(1.05);
}

.banner-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 20px;
  color: white;
}

.banner-title {
  margin: 0;
  font-size: 18px;
  font-weight: bold;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.pagination-dots {
  position: absolute;
  bottom: 16px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 8px;
  z-index: 2;
}

.dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  border: none;
  background: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: all 0.3s ease;
}

.dot.active {
  background: white;
  transform: scale(1.2);
}

.dot:hover {
  background: rgba(255, 255, 255, 0.8);
}

.nav-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.5);
  color: white;
  font-size: 18px;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-btn:hover:not(:disabled) {
  background: rgba(0, 0, 0, 0.7);
  transform: translateY(-50%) scale(1.1);
}

.nav-btn:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.prev-btn {
  left: 16px;
}

.next-btn {
  right: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .carousel-container {
    height: 150px;
  }
  
  .banner-title {
    font-size: 16px;
  }
  
  .nav-btn {
    width: 32px;
    height: 32px;
    font-size: 14px;
  }
  
  .prev-btn {
    left: 8px;
  }
  
  .next-btn {
    right: 8px;
  }
}

/* 加载状态 */
.banner-image {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
</style>
