import { UIComponent } from "../customComponent/UIComponent";
import { DEEP_INDEXZ, E_PAGE_TYPE, EVENT, GOLDANI_TYPE, JUMP_TYPE, UI_PATH_DIC, WALLETTASK_STATUS } from "../GlobalConstant";
import Global from "../GlobalScript";
import Banners from "../hall/Banners";
import { AutoPopMgr } from "../mgr/AutoPopMgr";
import { uiManager } from "../mgr/UIManager";
import { NetRspMsg, NetRspObject } from "../net/Http";
import utils from "../utils/utils";
import { WalletTaskInfo } from "./WalletTaskConst";

const { ccclass, property } = cc._decorator;

export function showWalletTaskPop(args?: any) {
    let parent = Global.getInstance().popNode
    if (!parent) return
    let gameNd = parent.getChildByName("WalletTask")
    if (gameNd) {
        return
    }
    uiManager.instance.loadPrefabByLoading(UI_PATH_DIC.WalletTask, cc.Prefab, () => {
    }, (err, prefab: cc.Prefab) => {
        if (!prefab) return
        let gameNd = parent.getChildByName("WalletTask")
        if (gameNd) {
            return
        }
        if (err) return
        if (Global.getInstance().getPageId() != E_PAGE_TYPE.HALL) {
            AutoPopMgr.set_isshowing();//没弹出来后卡住问题
            return
        }//控制必须在hall 页面弹出
        let ndPop = cc.instantiate(prefab)
        ndPop.name = "WalletTask"
        // ndPop.zIndex = 999
        ndPop.parent = parent

        if (!args) {
            args = [];
        }
        const script = ndPop.getComponent(WalletTask);
        if (script && script.show) {
            script.show.apply(script, args);
        }

        WalletTask.bAutoPop = true;
    })
}

@ccclass
export default class WalletTask extends UIComponent {
    static bAutoPop: boolean = false;

    private _bShowGuide: boolean = false;

    onLoad() {
        super.onLoad();

        this._bShowGuide = Global.instance.getStoreageData(Global.GLOBAL_STORAGE_KEY.WALLET_TASK, false) == false && this.gameData.walletTaskData.length > 0;
        this._bShowGuide ? this.initGuideUI() : this.initUI();
    }

    onDestroy(): void {
        super.onDestroy();

        if (WalletTask.bAutoPop) {
            WalletTask.bAutoPop = false;
            cc.director.emit("DestroyQueuePopup");
        }
    }

    start() {
        let page = this.node.getChildByName("page").getComponent(cc.PageView);
        page.node.on('page-turning', () => {
            let curIndex = page.getCurrentPageIndex();
            if (curIndex != 1) {
                this.node.destroy();
            }
        }, self);
        let tempnode = this.node.getChildByName("temp");
        page.insertPage(cc.instantiate(tempnode), 0);
        page.scrollToPage(1, 0.01);
    }

    show(args) {
        Global.instance.hide_spin_btn();
        Global.instance.hideEnvelopeBtn();
        cc.director.emit("Hide_LiveVideo");

        if (!this._bShowGuide) this.updateBanner();
    }

    addEvents(): void {
        this.addEvent(EVENT.UPDATE_WALLETTASKINFO, this.refreshUI);
    }

    onClick(name: string, btn: cc.Node): void {
        if (name == "btn_close") {
            this.node.destroy();
        }
        else if (name == "btn_record") {
            uiManager.instance.showDialog(UI_PATH_DIC.Transations, ["award"],null,DEEP_INDEXZ.TRANSATIONS);
        }
        else if (name == "btn_guide") {
            cc.sys.localStorage.setItem(Global.GLOBAL_STORAGE_KEY.WALLET_TASK, true);
            this.initUI();
            this.updateBanner();
        }
        else if (name == "btn_more") {
            this.node.destroy();

            utils.linkJump({ jumpType: JUMP_TYPE.PROMO });
        }
    }

    initGuideUI() {
        this.nodeAtNode("guide").active = true;

        this.setLabel("txt_balance", "0.00");

    }

    initUI() {
        this.nodeAtNode("guide").destroy();

        const balance = Global.getInstance().balanceTotal;
        this.setLabel("txt_balance", utils.formatNumberWithCommas(balance));

        this.refreshUI();
    }

    refreshUI() {
        this.nodeAtNode("noresult").active = this.gameData.walletTaskData.length == 0;
        this.nodeAtNode("listview").active = this.gameData.walletTaskData.length > 0;

        this.initList();
    };

    updateBanner() {
        const bannerData = this.gameData.bannerData;
        if (bannerData && bannerData.length > 0) {
            this.nodeAtNode("banners").getComponent(Banners).set_banner(bannerData);
        }
        else {
            this.nodeAtNode("banners").active = false;
        }
    }

    initList() {
        const count = Math.max(this.nodeAtNode("uiContent").childrenCount, this.gameData.walletTaskData.length);
        for (let i = 0; i < count; i++) {
            const data = this.gameData.walletTaskData[i];
            let item = this.nodeAtNode("uiContent").children[i];

            if (data) {
                if (!item) {
                    item = cc.instantiate(this.nodeAtNode("item_model"));
                    item.parent = this.nodeAtNode("uiContent");
                }

                item.active = true;

                this.initItem(item, data, i);
            }
            else {
                if (item) item.active = false;
            }
        }
    }

    initItem(parent: cc.Node, data: WalletTaskInfo, itemIndex: number) {
        const taskId = data.id;
        const status = data.task_status;
        const bonus = Number(data.bonus);
        const title = data.task_name;
        const betCur = Number(data.bet_num);
        const betTotal = data.bet_target_value;
        const recharge_done = data.recharge_done;
        const rechargeTotal = Number(data.recharge_target_value);
        const expireTime = Number(data.expire_time);
        const createdTime = Number(data.created_at);
        const updatedTime = data.updated_at;
        const gameType = data.game_type;
        const providerList = data.provider_list;

        this.setLabel("txt_title", this._formatTitle(title), parent);
        this.setLabel("txt_reward", utils.formatNumberWithCommas(bonus), parent);

        this.nodeAtSpriteSwitcher("mask", parent).selectedIndex = WALLETTASK_STATUS.LOCK ? 0 : 1;
        this.nodeAtColorSwitcher("txt_title", parent).selectedIndex = WALLETTASK_STATUS.LOCK ? 0 : 1;

        this.nodeAtNode("btn_unlock", parent).active = status == WALLETTASK_STATUS.LOCK;
        this.nodeAtNode("btn_ongoing", parent).active = status == WALLETTASK_STATUS.ONGOING;
        this.nodeAtNode("btn_collect", parent).active = status == WALLETTASK_STATUS.FINISHED;
        this.nodeAtSpriteSwitcher("btn_unlock", parent).selectedIndex = this.gameData.existOngoingTask() ? 1 : 0;

        const bShowBetBar = betTotal > 0 && status != WALLETTASK_STATUS.FINISHED;
        this.nodeAtNode("bg_progress_bet", parent).active = bShowBetBar;
        if (bShowBetBar) {
            let betCurForamt = utils.formatNumberWithCommas(betCur, 2, false);
            const betTotalFormat = utils.formatNumberWithCommas(betTotal, 2, false);
            const betRichtextFormat = betCur == 0 ? `<color=#999999>${betCurForamt}</c><color=#999999>/${betTotalFormat}</color>` : `<color=#222222>${betCurForamt}</c><color=#999999>/${betTotalFormat}</color>`;

            this.setProgress("progressBar_bet", betCur / betTotal, parent);
            this.setRichLabel("txt_progress_bet", betRichtextFormat, parent);
        }

        const bShowRechargeBar = rechargeTotal > 0 && status != WALLETTASK_STATUS.FINISHED;
        this.nodeAtNode("bg_progress_deposit", parent).active = bShowRechargeBar;
        if (bShowRechargeBar) {
            const rechargeTotalFormat = utils.formatLocalNum(rechargeTotal);
            const rechargeRichtextFormat = recharge_done ? `<color=#222222>${rechargeTotalFormat}</c><color=#999999>/${rechargeTotalFormat}</color>` : `<color=#999999>${0}</c><color=#999999>/${rechargeTotalFormat}</color>`;

            this.setProgress("progressBar_deposit", recharge_done ? 1 : 0, parent);
            this.setRichLabel("txt_progress_deposit", rechargeRichtextFormat, parent);
        }

        const bTimeLimited = expireTime != 0;
        this.nodeAtNode("ic_time", parent).active = bTimeLimited && status != WALLETTASK_STATUS.FINISHED;
        if (bTimeLimited) {
            const now = Global.getInstance().now() / 1000;
            let diffTime = expireTime * 3600 + createdTime - now;
            const updateTime = () => {
                if (diffTime < 0) {
                    this.nodeAtNode("ic_time", parent).active = false;
                    this.nodeAtSprite("ic_time", parent).unscheduleAllCallbacks();

                    this.gameData.reduceWalletTask(taskId);
                    return;
                }
                const hour = Math.floor((diffTime / 3600) % 24).toString().padStart(2, '0');
                const minute = Math.floor((diffTime / 60) % 60).toString().padStart(2, '0');
                const second = Math.floor(diffTime % 60).toString().padStart(2, '0');
                this.setLabel("txt_time", `${hour}:${minute}:${second}`, parent);

                diffTime--;
            }
            updateTime();
            this.nodeAtSprite("ic_time", parent).unscheduleAllCallbacks();
            this.nodeAtSprite("ic_time", parent).schedule(updateTime, 1);
        }

        this.addButtonClick(this.nodeAtNode("btn_collect", parent), () => {
            this.httpProxy.receiveWalletTaskReward(taskId).then((rsp: NetRspObject) => {
                if (rsp.msg == NetRspMsg.Success && cc.isValid(this.node)) {
                    Global.getInstance().updateBalanceAfterGame(() => {
                        if (!cc.isValid(this.node)) return;

                        cc.director.emit("showGoldAni", { type: GOLDANI_TYPE.TOTARGET, target: this.nodeAtNode("node_balance") });
                        this.refreshUI();
                    });
                }
            });
        });

        this.addButtonClick(this.nodeAtNode("btn_unlock", parent), () => {
            if (this.gameData.existOngoingTask()) {
                const str = "Please complete the ongoing task first";
                Global.getInstance().showSimpleTip(str)
            }
            else {
                this.httpProxy.receiveWalletTask(taskId);
            }
        });

        this.addButtonClick(this.nodeAtNode("btn_ongoing", parent), () => {
            utils.linkJump({ jumpType: JUMP_TYPE.GAME, gameType: gameType, providerList: providerList });
        });

        this.addButtonClick(parent, () => {
            uiManager.instance.showDialog(UI_PATH_DIC.WalletTaskRule, [data.task_rule],null,DEEP_INDEXZ.ACTIVITY_CONTENT);
        });

    }

    private _formatTitle(str: string) {
        let formatStr = str;
        if (str.length > 30) {
            formatStr = str.substring(0, 27) + "***";
        }

        return formatStr;
    }
}