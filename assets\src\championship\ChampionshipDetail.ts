import Pie<PERSON>hart from "../component/PieChart";
import Radar from "../component/Radar";
import { UIComponent } from "../customComponent/UIComponent";
import { DEEP_INDEXZ, EVENT, UI_PATH_DIC } from "../GlobalConstant";
import { uiManager } from "../mgr/UIManager";
import utils from "../utils/utils";

const { ccclass, property } = cc._decorator;

const TAB = ['bet', 'win', 'rate'];

@ccclass
export default class ChampionshipDetail extends UIComponent {
    private _isSliding: boolean = false;
    private _curTab: string = null;
    private _params: any = null;

    onLoad() {
        super.onLoad();

        this.initDefaultUI();
    }

    show(args) {
        this._params = args;

        this.changeTab(0);

        this.httpProxy.getRadarData(this._params);
    }

    addEvents(): void {
        this.addEvent(EVENT.PLAYER_GAME_DISTRIBUTION, this.initChampionshipList);
        this.addEvent(EVENT.PLAYER_BETTING_SUMMARY, this.initPlayBettingSummary);
    }

    onClick(name: string, btn: cc.Node): void {
        if (name == "btn_game") {
            this.nodeAtNode("bg1").active = false;
            this.nodeAtNode("bg2").active = true;
        }
        else if (name == "btn_bet") {
            this.nodeAtNode("bg1").active = true;
            this.nodeAtNode("bg2").active = false;
        }
        else if (name.startsWith("btn_close")) {
            this.node.destroy();
        }
        else if (name.startsWith("btn_tab")) {
            const index = Number(name[name.length - 1]);
            this.changeTab(index);
        }
    }

    initDefaultUI() {
        for (let i = 0; i < 5; i++) {
            this.nodeAtNode("line" + i).active = false;
        }

        this.setLabel("txt_amount", "--");
        this.setLabel("txt_times", "--");
        this.setLabel("txt_win", "--");
        this.setLabel("txt_rate", "--");
        this.setLabel("txt_num", "--");
    }

    initChampionshipList(data) {
        const piechartData = data.pie_chart_data;
        const listData = data.list_data;

        if (piechartData == null || listData == null) return;

        for (let i = 0; i < 5; i++) {
            const info = listData[i];

            this.nodeAtNode("line" + i).active = info;

            if (!info) continue;

            this.setLabel("txt_name" + i, info.game_name);
            this.setLabel("txt_balance" + i, info.value);

            this.nodeAtNode("line" + i).getChildByName("ic_jinbi").active = this._curTab != TAB[2];

            this.nodeAtNode("btn_go" + i).active = info.game_name != "Others";
            this.addButtonClick(this.nodeAtNode("btn_go" + i), () => {
                uiManager.instance.showDialog(UI_PATH_DIC.ChampionshipBoardTip, [info],null,DEEP_INDEXZ.ACTIVITYS);
            });
        }

        const formatPiechartData = piechartData.map(item => Number(item.percent));
        this.nodeAtNode("graphics_piechart").getComponent(PieChart).initData(formatPiechartData);
    }

    initPlayBettingSummary(data) {
        const radarData = data.radar_data;
        const listData = data.list_data;

        this.setLabel("txt_amount", utils.formatNumberWithCommas(listData.bet_amount, 0));
        this.setLabel("txt_times", listData.bet_times);
        this.setLabel("txt_win", utils.formatNumberWithCommas(listData.net_win, 0));
        this.setLabel("txt_rate", listData.win_rate);
        this.setLabel("txt_num", utils.formatNumberWithCommas(listData.game_numbers, 0));

        this.nodeAtNode("graphics_fivestarchart").getComponent(Radar).initData([10 * radarData.bet_amount, 10 * radarData.win_rate, 10 * radarData.game_numbers, 10 * radarData.net_win, 10 * radarData.bet_times]);
    }

    changeTab(index: number) {
        if (this._curTab == TAB[index]) return;

        if (this._isSliding) return;

        this._isSliding = true;

        this.nodeAtColorSwitcher("txt_label0").selectedIndex = index == 0 ? 1 : 0;
        this.nodeAtColorSwitcher("txt_label1").selectedIndex = index == 1 ? 1 : 0;
        this.nodeAtColorSwitcher("txt_label2").selectedIndex = index == 2 ? 1 : 0;

        const xPos = [-283, 0, 283];
        const displayName = ['Valid Bet', 'Net Win', 'Win Rate'];
        cc.tween(this.nodeAtNode("ic_slide")).to(0.1, { x: xPos[index] }).call(() => {
            this._isSliding = false;
            this._curTab = TAB[index];

            this.setLabel("txt_display", displayName[index]);

            let params = {};
            utils.DeepClone(this._params, params);

            const gameTypeStr = ["BetAmount", "NetWin", "WinRate"];
            params["data_type"] = gameTypeStr[index];

            this.httpProxy.getPiechartData(params);
        }).start();
    }
}

