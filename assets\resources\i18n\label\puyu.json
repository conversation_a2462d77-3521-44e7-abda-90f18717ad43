{"loginword1": "Ao Fazer Login Você Aceita que É", "loginword2": "E Concorda com Nossa", "loginword3": "*T&C", "loginword4": "e", "loginword5": "*Política de Privacidade", "loginword6": "Carregando...Aguarde.", "loginword7": "Atualizando...Aguarde...", "loginword8": "Login no <PERSON>, Aguarde...", "loginword9": "<PERSON><PERSON><PERSON> no <PERSON>gin", "loginword10": "<PERSON><PERSON>", "loginword11": "<PERSON><PERSON><PERSON> <PERSON>, <PERSON><PERSON> <PERSON>, Tente Novamente.", "loginword12": "Falha ao Verificar as Atualizações. Por favor, Tente Novamente.", "loginword13": "Baixando...", "loginword14": "Falha na Atualização, Por favor, Tente Novamente.", "loginword15": "O Servidor Está em Manutenção, Aguarde.", "loginword16": "Por favor, Digite a Conta ou Senha Correta.", "loginword17": "mais de 18 anos", "loginword18": "<PERSON><PERSON><PERSON>", "logintip1": "Jogue a", "logintip2": "qualquer hora", "logintip3": "e em", "logintip4": "qualquer lugar!", "logintip5": "Socialize", "logintip6": "enquanto cria", "logintip7": "estratégias!", "logintip8": "<PERSON><PERSON><PERSON><PERSON>", "logintip9": "e", "logintip10": "apreciado", "logintip11": "em todo o Brasil!", "logintip12": "Junte-se para chegar até ", "logintip13": " <PERSON><PERSON><PERSON>", "logintip14": "Conecte-se", "logintip15": "Ou", "weekendword1": "<PERSON><PERSON><PERSON> <PERSON> Adici<PERSON>, Você Tem 3 dias Para Jogar e Liberar o Bônus.", "workdayword1": "Aproveite Cada Negócio Para Revelar <PERSON>.", "workdayword2": "Validade do Bônus:", "workdayword3": "dia", "workdayword4": "Rodada Gratuita Diária", "workdayword5": "A Roda da Sorte foi atualizada", "bankbindword1": "Tipo de Conta:", "bankbindword2": "Nome:", "bankbindword3": "Insira Seu Nome", "bankbindword4": "Email:", "bankbindword5": "<PERSON><PERSON><PERSON>", "bankbindword6": "Endereço:", "bankbindword7": "Insira Seu Endereço", "bankbindword8": "Conta Bancária:", "bankbindword9": "Conta Bancária", "bankbindword10": "Cartão Bancário", "bankbindword11": "PAN NO.:", "bankbindword12": "Insira Seu PAN NO.", "bankbindword13": "UPI", "bankbindword14": "UPI ID:", "bankbindword15": "Insira Seu UPI ID", "bankbindword16": "Código IFSC:", "bankbindword17": "Insira Seu Código IFSC", "mailword1": "Mails", "mailword2": "Sugestões", "mailword3": "Vazio...", "mailword4": "Insira Suas Sugestões...", "mailword5": "Coletar", "mailword6": "Mandar", "phonebindword1": "Telefone Celular:", "phonebindword2": "Insira Seu Número de Telefone(11 digitos)", "phonebindword3": "Senha:", "phonebindword4": "<PERSON><PERSON><PERSON> Sua Sen<PERSON>", "phonebindword5": "OTP:", "phonebindword6": "Insira o OTP", "phonebindword7": "Registre-se", "phonebindword8": "<PERSON><PERSON> sua senha", "phonebindword9": "<PERSON><PERSON>", "phonebindword10": "OTP", "gamelist1": "3 Cartas", "gamelist2": "Rummy", "gamelist3": "Poker", "gamelist4": "Palhaço", "gamelist5": "AK47", "gamelist13": "Carrom", "gamelist14": "Disco Pool", "gamelist15": "GRATUITAMENTE", "gamelist18": "Ciência", "gamelist19": "Cultura", "gamelist20": "Aleatória", "gamelist25": "Paulista", "gamelist27": "<PERSON><PERSON><PERSON>", "roomlistword1": "<PERSON><PERSON><PERSON>", "roomlistword2": "Prática", "roomlistword3": "Nome", "roomlistword4": "Máximo de Jogadores", "roomlistword5": "Quantidade de Inicialização", "roomlistword6": "Entrada Mínima", "roomlistword7": "Online", "roomlistword8": "Disco", "roomlistword9": "WinBet", "joinroom_text_1": "JOGAR COM AMIGOS", "joinroom_text_2": "JUNTAR-SE À MESA", "joinroom_text_3": "Redefinir", "joinroom_text_4": "CRIAR MESA", "joinroom_text_5": "Inválido Número da Mesa", "joinroom_text_6": "A mesa está cheia", "createroom_text_1": "CRIAR", "createroom_text_2": "<PERSON><PERSON>ogo", "createroom_text_3": "Número de jogadores", "createroom_text_4": "<PERSON><PERSON><PERSON>", "createroom_text_5": "Apostas", "createroom_text_6": "Paulista", "createroom_text_7": "Mineiro", "createroom_text_8": "<PERSON><PERSON><PERSON>", "createroom_text_9": "<PERSON><PERSON><PERSON>", "createroom_text_10": "Entrada mínima:", "createroom_text_11": "<PERSON><PERSON><PERSON>", "invitation_text_1": "<PERSON><PERSON><PERSON>, n<PERSON>mer<PERSON> da mesa:", "invitation_text_2": "venha ganhar dinheiro comigo! !", "invitation_text_3": "Copie o seguinte e abra-o no seu navegador:\n", "invitation_text_4": "Você está aceitando o convite de um amigo", "invitation_text_5": "Número da Mesa: ", "invitation_text_6": "Entrar", "serviceword1": "Se Você Tiver Alguma Dúvida, Sinta-se <PERSON> Vontade para Entrar em Contato Conosco Todos os Dias das 8h às 21h, Exceto Feriados nacionais. \nForneça seu ID de Jogo e as Capturas de Tela Necessárias Para que Possamos Ajudá-lo Melhor!", "settingword1": "Música", "settingword2": "Som", "settingword3": "Vibração", "settingword4": "Linguagem", "settingword5": "Telefone", "settingword6": "Google", "settingword7": "Vers<PERSON>", "settingword8": "Vinculado", "settingword9": "Unir", "shareword1": "Compartilhe Este Link Com Seus Amigos.", "shopword1": "Reembolso", "shopword2": "<PERSON><PERSON><PERSON>", "shopword3": "Total", "shopword4": "Você Encontrará Seus Bônus Na Sua Conta de Bônus. Após Cada Jogo, 10% de Sua Perda (Máximo de um valor de inicialização) Será Coberto Pelo Bônus em Sua Conta de Bônus", "shopword5": "Você encontrará seu reembolso instantâneo no e-mail do aplicativo e bônus em sua conta de bônus após cada depósito bem-sucedido.", "shopword6": "10% de sua perda (máximo de um valor de inicialização) será coberto pelo bônus em sua conta de bônus, adicionado diretamente ao seu \"Saldo em dinheiro\" após cada rodada do jogo.", "shopword7": "Dicas de recarga:", "withdrawword1": "<PERSON><PERSON><PERSON>", "withdrawword2": "<PERSON><PERSON>", "withdrawword3": "<PERSON><PERSON><PERSON>", "withdrawword4": "<PERSON><PERSON><PERSON>", "withdrawword5": "<PERSON><PERSON>", "withdrawword6": "Antes de Retirar da Sua Conta de Jogo, Certifique-se de Que Suas Informações Foram Inseridas Corretamente.", "withdrawword7": "CONTA", "withdrawword8": "ID", "withdrawword9": "<PERSON><PERSON>", "withdrawword10": "Valor", "withdrawword11": "Status", "withdrawword12": "Saldo em Sua Conta de Dinheiro", "withdrawword13": "Saldo em Sua Conta de Saque em Dinheiro, <PERSON><PERSON>do o Valor do Seu Prêmio", "withdrawword14": "<PERSON>do que Pode Ser Usado Para Jogar", "withdrawword15": "Detalhes da Transação", "withdrawword16": "Data", "withdrawword17": "Tipo", "withdrawword18": "Transações", "withdrawword19": "Bônus Disponível:", "withdrawword20": "Você não Tem Nenhum Bônus. Adicione Dinheiro Agora para Pegar Seu Bônus.", "withdrawtipword0": "Dicas de Retirada:", "withdrawtipword1": "Por favor, Verifique Sua Conta Bancária:", "withdrawtipword2": "1.O valor mínimo de saque é de R$.100 e pode levar de 2 a 24 horas para chegar à sua conta pessoal.", "withdrawtipword3": "2.Se o seu pedido de retirada falhou, o dinheiro será devolvido a você via correio no aplicativo, verifique suas informações bancárias cuidadosamente e envie sua solicitação novamente.", "withdrawtipword4": "3.Se você encontrar mais problemas, entre em contato com nosso atendimento ao cliente e envie-nos seu ID do jogo (não seu apelido) e capturas de tela de seus problemas para que possamos ajudá-lo melhor.", "withdrawtipword5": "Entre em contato com o atendimento ao cliente sempre que encontrar algum problema de retirada.", "withdrawtipword6": "1.Solicite a qualquer momento, e o pagamento será processado em 24 horas entre 9h00 e 18h00 em dias úteis", "withdrawtipword7": "2.O valor mínimo deve ser superior a %d e ser múltiplo de %d", "withdrawtipword8": "4.<PERSON><PERSON><PERSON> pode sacar  até %d por transação por no máximo %d vezes ao dia", "withdrawtipword9": "3.<PERSON><PERSON><PERSON>se <PERSON> vontade para entrar em contato com nosso serviço de atendimento ao cliente a qualquer momento!", "withdrawtipword10": "Dicas de Retirada:", "withdrawtipword11": "Como funciona o bônus?", "withdrawword21": "Saldo de ca<PERSON>a", "withdrawword22": "<PERSON><PERSON>", "withdrawword23": "Fluxo do Jogo", "withdrawword24": "<PERSON><PERSON><PERSON><PERSON>", "withdrawword25": "Reembolso de Retirada", "withdrawword26": "<PERSON><PERSON><PERSON>", "withdrawword27": "Transferir", "withdrawword28": "Coleta de Correio", "withdrawword29": "Presente de Evento", "phoneloginword1": "Cadastre-se Agora", "phoneloginword2": "Esqueceu a Senha?", "phoneloginword3": "<PERSON><PERSON><PERSON>", "phonezhuceword1": "Apelido:", "phonezhuceword2": "Insira Seu Apelido", "ClassicLayword1": "Quantidade de Inicialização:", "ClassicLayword2": "Máxi<PERSON>s:", "ClassicLayword3": "Limite de Apostas:", "ClassicLayword4": "Limite do Pote:", "helpword1": "<PERSON><PERSON><PERSON> <PERSON>jun<PERSON> (Três Cartas do Mesmo Ranque)", "helpword2": "<PERSON><PERSON>", "helpword3": "<PERSON><PERSON>", "helpword4": "Sequência Pura (Cartas do Mesmo Naipe)", "helpword5": "Sequência (Cartas de Naipe Diferente)", "helpword6": "Cor", "helpword7": "Par (Duas Cartas do Mesmo Ranque)", "helpword8": "Carta Alta", "tipword1": "Pagamento Bem-Sucedido", "tipword2": "Falha no Pagamento", "tipword3": "Enviado com Sucesso", "tipword4": "Por Favor Insira um Endereço de E-mail Correto.", "tipword5": "Por Favor In<PERSON> um Apelido.", "tipword6": "Por F<PERSON>or <PERSON><PERSON>a Seu E-mail.", "tipword7": "Por Favor Insira Seu Endereço.", "tipword8": "Por Favor Insira Seu PAN NO.", "tipword9": "Por Favor Insira Seu Código IFSC.", "tipword10": "Por Favor Insira sua ID UPI.", "tipword11": "Insira Sua Conta Bancária", "tipword12": "<PERSON> Continuar, Você Precisa Vincular um Número de Telefone Celular.", "tipword13": "O Número de Celular Que Você Vinculou é:\n", "tipword14": "Vinculação Bem-Sucedida", "tipword15": "Falha na Vinculação", "tipword16": "<PERSON><PERSON> insuficiente, por favor, adicione fichas primeiro.", "tipword17": "<PERSON><PERSON><PERSON>, Para Garantir a Segurança de Sua Conta, Ligue Sua Conta de Jogo a Um Número de Celular. Boas Partidas e Muitas Vitórias!", "tipword18": "Em Breve...", "tipword19": "Este Número Já Foi Vinculado a Uma Conta Existente, <PERSON><PERSON>, <PERSON><PERSON> Com Outro Número", "tipword20": "Por Favor <PERSON> a Senha Correta.", "tipword21": "Erro de OTP,Por Favor insira o OTP Correto.", "tipword22": "Por Favor Insira Seu Número de Telefone.", "tipword23": "O Número Está Vinculado a Uma Conta Existente.", "tipword24": "Falha ao Obter a Opção.", "tipword25": "Por <PERSON><PERSON>or <PERSON>.", "tipword26": "Por Favor Insira o OTP.", "tipword27": "Apelido Modificado com Sucesso", "tipword28": "Avatar Modificado com Sucesso", "tipword29": "Falha na Modificação", "tipword30": "Você Vinculou Sua do Facebook Com Sucesso.", "tipword31": "Cópia <PERSON>-Sucedida", "tipword32": "Não Foi Possível Compartilhar, Instale o WhatsApp e Tente Novamente..", "tipword33": "Você Pode Depositar No Mínimo @#0 em Uma Única Transação.", "tipword34": "Você Pode Depositar até @100.000 em Uma Única Transação.", "tipword35": "Retirada Com Sucesso", "tipword36": "Falha na Retirada. Tente novamente Mais Tarde.", "tipword37": "O Valor <PERSON> que ", "tipword38": "Você está sacando <PERSON> ", "tipword39": ",Esta Ação Resultará em Uma Taxa de Transferência de 4%. O Valor Real Recebido em Sua Conta Será de ₹ ", "tipword40": ".(<PERSON><PERSON> <PERSON>avor <PERSON>-se de Ter digitado a Conta Bancária Correta Para Garantir Uma retirada Bem-sucedida.)", "tipword41": "Por Favor Insira o Número de Telefone Correto.", "tipword42": "Por Favor Obtenha o OTP Primeiro.", "tipword43": "OTP Inválido", "tipword44": "<PERSON>r <PERSON><PERSON>or <PERSON><PERSON> Sen<PERSON>álid<PERSON>.", "tipword45": "Você Está Conectado em Outro Dispositivo.", "tipword46": "<PERSON><PERSON> Acesso <PERSON> Restrito, Entre em Contato Com o Atendimento ao Cliente.", "tipword47": "Senha incorreta", "tipword48": "O Número do Celular é Inconsistente Com o OTP.", "tipword49": "Por favor, Digite o Número de Telefone Celular Correto.", "tipword50": "O Número já Foi Usado.", "tipword51": "A Conta Fez Login em Outro Lugar", "tipword52": "<PERSON><PERSON><PERSON>, Faça Login Novamente.", "tipword53": "<PERSON><PERSON><PERSON>", "tipword54": "<PERSON><PERSON>", "tipword55": "Aguarde Sua Vez", "tipword56": "Iniciando a Próxima Rodada em", "tipword57": "<PERSON><PERSON><PERSON>, Por <PERSON><PERSON>", "tipword58": "<PERSON>ocê Saiu da Mesa Devido à Inatividade, Tente Novamente Entrar na Mesa.", "tipword59": "Falha ao enviar.", "tipword60": "Intervalo de 3 Segundos", "tipword61": "<PERSON><PERSON>, Tente Novamente Mais Tarde.", "tipword62": "Nenhum Sala Adequada Disponível!", "tipword63": "O Número de Telefone Não Está Registrado.", "tipword64": "Confirme Para Usar Jokers/Wild Cards.", "tipword65": "Esperando que Outros Declarem...", "tipword66": "Agrupe Suas Cartas em Conjuntos/Sequências e Declare!", "tipword67": "Falha ao Concluir.", "tipword68": "<PERSON><PERSON><PERSON>", "tipword69": "<PERSON><PERSON><PERSON> <PERSON>.", "tipword70": "<PERSON>ite de Contagem.", "tipword71": "Em um Esforço para Evitar Trapaças e Comportamentos Fraudulentos, Novos Jogadores que Estão Esperando na Sala não Podem Ver os Avatares e os Apelidos de Outros Jogadores Até Que o Jogo Comece.", "tipword72": "Espera.  É necessário um saldo mínimo de %d reais para apostar!", "tipword73": "Jogo na Aposta %d Segundo(s)", "tipword74": "Iniciando o Jogo em %d Segundo(s)", "tipword75": "Jogo no Faturamento %d <PERSON><PERSON><PERSON>(s)", "tipword76": "Limite de Apostas Atingido!", "tipword77": "<PERSON>ocê Perderá Sua Aposta Se <PERSON>, Confirme <PERSON>?", "tipword78": "Aguardando a Próxima Rodada em %d Segundo(s)", "tipword79": "O Disco Vermelho Deve Ser Coberto", "tipword80": "Acerte Seus Discos Para Ganhar o Jogo", "tipword81": "PONTO DE COMBINAÇÃO", "tipword82": "Falta: O Atacante Não Deve Ser Envasado", "tipword83": "Você Perderá Se Sair Agora!", "tipword84": "Sua Aposta Foi Feita. Aguarde Até Que A Aposta Termine Aara Evitar <PERSON>.", "tipword85": "Limite de Apostas Atingido Nesta Área!", "tipword86": "A Próxima Rodada Começará Em #0 Segundo(s)", "tipword87": "Você Fez Login Via Celular, Gostaria de Mudar Para o Login do Facebook?", "tipword88": "Talvez Você Não Consiga Salvar Suas Informações Atuais Se Fizer Login Via Facebook. Continuar a Fazer Login?", "tipword89": "<PERSON>ando <PERSON>...", "tipword90": "Pagamento Realizado Com Sucesso!", "tipword91": "Erro de <PERSON>nto, Tente Novamente.", "tipword92": "Erro do formato do telefone", "tipword93": "Erro do formato de e-mail", "scriptword1": "Tempo restante: ", "scriptword2": "Tempo restante: fim", "scriptword3": "E<PERSON>rda", "scriptword4": "Ganhar", "scriptword5": "<PERSON><PERSON><PERSON>", "scriptword6": "<PERSON><PERSON><PERSON><PERSON>", "scriptword7": "Registro", "scriptword8": "Lançado", "scriptword9": "E-mail", "scriptword10": "Pacote de presente", "scriptword11": "<PERSON><PERSON><PERSON>", "scriptword12": "Presente do sistema", "scriptword13": "Sucesso", "scriptword14": "Em revisão", "scriptword15": "<PERSON><PERSON><PERSON>", "scriptword16": "Visto", "scriptword17": "<PERSON><PERSON>", "scriptword18": "<PERSON><PERSON>", "scriptword19": "Blind", "scriptword20": "Blindx2", "scriptword21": "Apostas", "scriptword22": "Apostasx2", "scriptword23": "Comparar", "scriptword24": "Mostrar", "scriptword25": "Peça um Espetáculo Lateral", "scriptword26": "Quantidade de Inicialização:", "scriptword27": "Negou o Espetáculo Lateral!", "scriptword28": "Pendente", "scriptword29": "Pontuação:", "scriptword30": "Valor do Ponto ", "scriptword31": "<PERSON>v<PERSON><PERSON><PERSON>", "scriptword32": "Sequência Pura", "scriptword33": "Sequência Impura", "scriptword34": "Definir", "scriptword35": "Lobby", "scriptword36": "Alternar <PERSON>", "scriptword37": "<PERSON>er", "roomword1": "<PERSON><PERSON><PERSON>í<PERSON>", "code_204": "A Operação Não Pode Ser Executada Neste Momento", "code_206": "A ação Não Pode Ser Executada Neste Momento", "php_code_400": "<PERSON>, Por Favor F<PERSON> Login Novamente.", "php_code_500": "Please refresh or try again shortly（500）", "php_code_600": "O Número de Telefone Foi Usado.", "php_code_700": "A Conta do Facebook Foi Usada.", "php_code_101001": "O Número do Celular Não Pode Estar Vazio.", "php_code_101002": "O ID do Dispositivo Não Pode Estar Vazio.", "php_code_101003": "O Código de Verificação Não Pode Estar Vazio.", "php_code_101004": "Seu Código de Verificação Foi Enviado, Verifique.", "php_code_101005": "Falha ao Enviar o Código de Verificação.", "php_code_102001": "Por Favor <PERSON><PERSON>.", "php_code_102002": "Formato Errado. Digite 4-12 <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> ou <PERSON>.", "php_code_102003": "<PERSON>ta ou Senha Incorreta.", "php_code_102004": "<PERSON>ta ou Senha Incorreta.", "php_code_102005": "O Número de Telefone Foi Usado.", "php_code_102006": "Você Registrou Muitas Contas Neste Dispositivo.", "php_code_102007": "Código de Verificação Incorreto.", "php_code_102008": "O ID do Dispositivo Não Pode Estar Vazio.", "php_code_102009": "AppPackageName Não Pode Estar Vazio.", "php_code_102010": "A Versão do Aplicativo Não Pode Estar Vazia.", "php_code_102011": "Você Só Tem Permissão Para Alterar Seu Apelido Uma Vez Por Mês.", "php_code_102012": "Falha no Login do Facebook.", "php_code_102013": "O Número de Telefone Foi Usado.", "php_code_102014": "Você Só Pode Vincular Uma Conta do Facebook.", "php_code_102015": "<PERSON>al<PERSON> no <PERSON>gin, Entre em Contato Com o Atendimento ao Cliente.", "php_code_102016": "A pessoa Que Te Convidou Não Existe.", "php_code_102017": "A pessoa Que Te Convidou Não Existe.", "php_code_102018": "A pessoa Que Te Convidou Não Existe.", "php_code_103001": "O E-mail Não Pode Ficar Vazio. ", "php_code_103002": "O Código IFSC Não Pode Estar Vazio.", "php_code_103003": "A Conta Bancária Não Pode Estar Vazia.", "php_code_103004": "O Nome Não Pode Estar Vazio.", "php_code_103005": "O cartão PAN Não Pode Estar Vazio.", "php_code_103006": "O Número do Celular Não Pode Estar Vazio.", "php_code_103007": "Informações de Conta Erradas, Confirme e Digite Novamente.", "php_code_103008": "Este Cartão PAN Foi Usado por Outra Conta.", "php_code_103009": "Esta Conta Bancária já Foi Usada, Verifique Novamente.", "php_code_103010": "O AppPackageName Não Pode Estar Vazio.", "php_code_103011": "A AppVersion Não Pode Estar Vazia.", "php_code_103012": "Valor de Recarga Incorreto, Verifique.", "php_code_103013": "<PERSON><PERSON>, Tente Novamente.", "php_code_103014": "<PERSON><PERSON>, Tente Novamente.", "php_code_103017": "Por favor, Adicione Sua Conta Bancária Primeiro.", "php_code_103019": "O Número do Celular Não Pode Estar Vazio.", "php_code_103020": "O E-mail Não Pode Estar Vazio.", "php_code_103021": "PayChannel Não Pode Estar Vazio.", "php_code_103022": "Por Favor <PERSON> No<PERSON>.", "php_code_103023": "A Sacola de Presentes Expirou.", "php_code_103024": "<PERSON><PERSON> Sacola de Presente.", "php_code_103025": "Compre Para Atingir o Limite Superior.", "php_code_103026": "As Sacolas de Presente Estão Esgotadas.", "php_code_103028": "Você Solicitou Muitas Retiradas Ho<PERSON>, Tente Novamente Amanhã.", "php_code_103029": "O Evento Terminou ou a Compra Excedeu o Limite, <PERSON><PERSON> <PERSON>avor <PERSON>.", "php_code_104001": "O MailID Não Pode Estar Vazio.", "php_code_104002": "Solicitação Inválida.", "php_code_104003": "Reivindica<PERSON>hou, Por Favor Tente Novamente.", "php_code_104004": "Reivindica<PERSON>hou, Por Favor Tente Novamente.", "php_code_105001": "O Código de Convite Não Pode Estar Vazio.", "php_code_105002": "Você Adicionou um Convidado.", "php_code_105003": "O Convidador Não Existe.", "php_code_105004": "Solicitação Inválida.", "php_code_105005": "Você Não Pode Se Convidar.", "php_code_105006": "Verifique Seu Número de Telefone", "php_code_103030": "Valor de Transferência Incorreto, Tente novamente", "php_code_103031": "Por favor, <PERSON><PERSON> Número de Celular", "php_code_103032": "O destinatário <PERSON> Existe. Verifique as Informações e Tente Novamente", "php_code_103033": "Falha na Transferência, Verifique o Saldo e Tente Novamente", "php_code_104006": "Por favor, Insira o ID do Destinatário Correto", "php_code_104007": "Não Há Chips Suficientes Para Enviar E-mails, Clique em \"OK\" para Comprar Mai<PERSON>.", "php_code_109001": "<PERSON><PERSON>nus <PERSON>, Reivindique Novamente Amanhã!", "andar_text_1": "Aposte quantas cartas serão distribuídas nesta rodada", "andar_text_2": "ANDAR", "andar_text_3": "PALHAÇO", "andar_text_4": "BAHAR", "andar_text_5": "APOSTAS SECUNDÁRIAS", "andar_text_6": "História", "andar_text_7": "Regras", "andarhelp_text_1": "Como jogar", "andarhelp_text_2": "     O dealer embaralha um baralho de 52 cartas, escolhe aleatoriamente uma carta do baralho disponível e coloca-a virada para cima, que seria a Carta de Jogo, contra a qual as apostas podem ser feitas para esse jogo.", "andarhelp_text_3": "     Os jogadores podem optar por apostar em pontos de aposta ANDAR ou Bahar para a mão.  É necessário colocar um valor mínimo de aposta, que seria a seleção padrão quando clicado no local da aposta.", "andarhelp_text_4": " Resultado do jogo", "andarhelp_text_5": "     Se o número na carta distribuída corresponder ao valor da carta de jogo, o jogo termina.", "andarhelp_text_6": " Tipos de aposta", "andarhelp_text_7": "     Os jogadores podem optar por apostar em pontos ANDAR ou BAHAR.  Uma vez que as apostas são feitas, o jogador deve clicar no botão confirmar aposta para postar as apostas.", "andarhelp_text_8": " Estatisticas", "andarhelp_text_9": "     O resumo do resultado dos últimos 100 jogos pode ser visto aqui junto com a porcentagem de vitórias em ambos os lados ANDAR ou BAHAR.  Além disso, a porcentagem de vitórias da carta de jogo (carta de dealer) nos últimos 100 jogos no ANDAR e BAHAR também pode ser visualizada no momento da aposta.", "andarhelp_text_10": "Pagamentos", "andarhelp_text_11": "     A vitória no lado do ANDAR obtém 90%;", "andarhelp_text_12": "     Vitória no lado BAHAR recebe 100%;", "andarhelp_text_13": " Opções de apostas paralelas Andar e Bahar", "andarhelp_text_14": "     Para apimentar o jogo, há uma gama de apostas paralelas.  O lado pode ser jogado com ou sem uma aposta da mão principal.  O objetivo é escolher o número de cartas que serão distribuídas antes que uma partida com a carta “Joker” seja feita.  Os pagamentos são mais generosos do que a rodada principal do jogo, sendo o mais alto 120:1, para 41 ou mais cartas distribuídas.", "andarhelp_text_15": "RESULTADO   1-5      6-10   11-15   16-25   26-30   31-35   36-40   40+", "andarhelp_text_16": "PAGAMENTO    x3.5    x4.5    x5.5     x4.5     x15      x25       x50      x120", "dragon_text_1": "Clique para apostar", "dragon_text_2": "História de vitórias", "dragon_text_3": "Dragão", "dragon_text_4": "Tigre", "dragon_text_5": "<PERSON><PERSON><PERSON><PERSON>", "dragon_help_text_2": "Regras dos jogos", "dragon_help_text_3": "O dealer distribui duas cartas em cada jogo, uma para o Dragão e outra para o Tigre.  Simplesmente faz uma aposta no Dragão ou Tigre que poderia tirar a carta mais alta.", "dragon_help_text_4": "Cartão do menor para o maior são (ignorando os símbolos):", "dragon_help_text_5": "A<2<3<4<5<6<7<8<9<10<J<Q<K", "dragon_help_text_6": "Pago por Dragão/Tigre: 1:1", "dragon_help_text_7": "Pago por gravata: 1:8", "dragon_help_text_8": "Para o empate, apostar no Dragão e no Tigre receberá metade do valor da aposta de volta.", "wingo_text_1": "Aguardando a próxima Rodada em {0} <PERSON><PERSON><PERSON>(s)", "wingo_text_2": "Espera.  É necessário um saldo mínimo de {0} reais para apostar!", "wingo_text_3": "JOGADORAS", "wingo_text_4": "Mostrar apenas os 30 melhores", "helpcell_text_1": "Você tem 15 segundos de tempo de aposta em cada rodada.  Durante o tempo de aposta, os jogadores podem escolher diferentes áreas de apostas para apostar (um total de 12 áreas de apostas, cada área de apostas tem odds diferentes e limites de bônus diferentes).", "helpcell_text_2": "Depois que o tempo de aposta terminar (aposta não é mais possível), a roda do meio e o ponteiro começarão a girar.", "helpcell_text_3": "Quando a roda parar, a cor e o animal apontado pelo ponteiro (ambos os critérios devem ser atendidos ao mesmo tempo) são a área vencedora, e a recompensa correspondente será obtida de acordo com a aposta do jogador.", "helpcell_text_4": "<PERSON><PERSON> de apostas", "helpcell_text_5": "Após o início das apostas, os jogadores podem fazer apostas em 12 áreas de apostas, e cada área de apostas tem uma liquidação máxima de aposta.", "helpcell_text_6": "Variedade Especial", "helpcell_text_7": "<PERSON><PERSON><PERSON><PERSON>", "helpcell_text_8": "Depois que o toca-discos parar, todas as três cores do ANIMAL VENCEDOR vencerão (por exemplo, se a aposta vencedora for um leão vermelho, o leão vermelho, o leão verde e o leão amarelo são todas apostas vencedoras, ou seja, os Três Grandes)", "helpcell_text_9": "Quatro Grandes", "helpcell_text_10": "Depois que o toca-discos parar, todos os quatro animais da mesma cor ganham (por exemplo, se a aposta vencedora for um leão vermelho, o leão vermelho, o panda vermelho, o macaco vermelho e o coelho vermelho são todos apostas vencedoras, ou seja, o  Quatro Grandes)", "helpcell_text_11": "Depois que o toca-discos parar, além do ganho atual, outro ponto de ganho será gerado (o novo ponto de ganho não pode ser idêntico ao ponto de ganho anterior)", "helpcell_text_12": "Dobro", "helpcell_text_13": "Depois que o toca-discos parar, as chances serão dobradas na área de apostas vencedora atual (por exemplo: se você ganhar o tigre vermelho 46 vezes e ganhar um duplo ao mesmo tempo, sua vitória real nesta rodada é de 92 vezes)", "helpcell_text_14": "Existem 3 conjuntos de mesas ímpares na Dança da Floresta.  <PERSON><PERSON><PERSON> as tabelas aba<PERSON>:", "helpcell_text_15": "Um conjunto de Tabelas de Odds será selecionado aleatoriamente para apostar antes de cada rodada.", "helpcell_text_16": "Por exemplo: quando a Tabela 1 de probabilidades for selecionada para esta rodada, as probabilidades para o leão vermelho serão 46 vezes e assim por diante.", "helpcell_text_17": "Tabela de probabilidades 1", "helpcell_text_18": "Animal", "helpcell_text_19": "Chances", "helpcell_text_20": "<PERSON><PERSON> ve<PERSON>", "helpcell_text_21": "<PERSON><PERSON> verm<PERSON><PERSON>", "helpcell_text_22": "Macaco vermelho", "helpcell_text_23": "coelho vermelho", "helpcell_text_24": "leão verde", "helpcell_text_25": "panda verde", "helpcell_text_26": "macaco verde", "helpcell_text_27": "coelho verde", "helpcell_text_28": "leão amarelo", "helpcell_text_29": "panda amarelo", "helpcell_text_30": "Macaco amarelo", "helpcell_text_31": "co<PERSON>ho amarelo", "helpcell_text_32": "Tabela de probabilidades 2", "helpcell_text_33": "Tabela de probabilidades 3", "helpcell_text_34": "O seguinte é o valor máximo da aposta para diferentes pontos de aposta em cada área:", "helpcell_text_35": "leão", "helpcell_text_36": "panda", "helpcell_text_37": "<PERSON><PERSON><PERSON>", "helpcell_text_38": "macaco", "guess_text_1": "CORRETO", "guess_text_2": "Procurando por jogadores", "guess_text_3": "Explorar a Índia Quiz", "guess_text_4": "Delhi fica em qual rio?  Quantos países fazem fronteira com a Índia?  Em que estado fica Bangalore?  Explore a vibrante cultura e matemática da Índia com este quiz.", "guess_text_5": "JOGAR DE NOVO", "bankbind_text_1": "<PERSON>al<PERSON>", "bankbind_text_2": "Digite seu nome legal", "bankbind_text_3": "Digite seu telefone(11 digitos)", "bankbind_text_4": "Digite seu CPF", "bankbind_text_5": "PIX DICT:", "bankbind_text_6": "CPF", "bankbind_text_7": "E-mail", "bankbind_text_8": "Telefone(+55)", "bankbind_text_9": "Enviar", "bankbind_text_10": "Confirme", "bankbind_text_11": "Vinculação o Facebook", "bankbind_text_12": "Vinculação Telefone não.", "bankbind_text_13": "Preencha o número do celular", "bankbind_text_14": "Adicionar dados bancá<PERSON>", "phonebing_text_1": "Dicas: Obtenha R$5 vinculando seu celular!", "profile_text_1": "Perfil", "profile_text_2": "Cópia", "profile_text_3": "<PERSON><PERSON><PERSON>", "profile_text_4": "OK", "profile_text_5": "Entrar", "profile_text_6": "ARMAÇÃO", "profile_text_7": "OLTA DE CARTÃO", "profile_text_8": "PERFIL", "profile_text_9": "Armação", "profile_text_10": "escolha um avatar", "sharing": "Compartil<PERSON><PERSON>", "withdraw_text_1": "Tempo reivindicado", "withdraw_text_2": "Bônus creditado", "withdraw_text_3": "<PERSON><PERSON>", "withdraw_text_4": "Data de validade", "withdraw_text_5": "Entre em contato com o atendimento ao cliente sempre que encontrar algum problema de retirada.", "withdraw_text_6": "<PERSON><PERSON><PERSON> se<PERSON>a", "tips": "Pontas", "gift_text_1": "Validade do bônus: 1 dia", "gift_text_2": "Ganhe agora!", "gift_text_3": "Participar de atividades", "gift_text_4": "Por favor, insira seu código promocional", "gift_text_5": "CÓDIGO PROMOCIONAL", "gift_text_6": "Resgatar", "gift_text_7": "Compre", "gift_text_8": "Faça e receba:", "gift_text_9": "Atividades de tempo", "emoji_text_1": "SINAL", "emoji_text_2": "Apenas em 2V2, e visto apenas pelo companheiro de equipe", "truco_help_text_1": "Regras Básicas", "truco_help_text_2": "Vira e Manilha", "truco_help_text_3": "Ligue para Truco", "truco_help_text_4": "Mão de Onze", "truco_help_text_5": "Mão de Ferro", "truco_help_text_6": "Cartão Oculto", "truco_help_text_7": "Truco Paulista é um jogo feito entre duas pessoas ou duas equipes.  A primeira pessoa ou equipe que atingir 12 pontos é a vencedora.  Os concorrentes disputam em melhor de três rodadas chamada Mão.", "truco_help_text_8": "No início de uma Mão, cada jogador recebe três cartas.  Os jogadores são obrigados a jogar uma carta no início de cada rodada.  O jogador com o maior valor da carta ganha a rodada.", "truco_help_text_9": "Qual é a Carta Mais Alta?", "truco_help_text_10": "A sequência de valores das cartas no Truco Paulista, jogando com o Baralho Sujo é:", "truco_help_text_11": "<PERSON><PERSON> baixa", "truco_help_text_12": "<PERSON><PERSON>", "truco_help_text_13": "No entanto, lembre-se de que a carta mais alta de uma mão é sempre a \"MANILHA\".", "truco_help_text_14": "Jogo do <PERSON>ho", "truco_help_text_15": "*Se a 1ª rodada empatar, o vencedor da 2ª rodada ganhou o ponto.", "truco_help_text_16": "*Se a 2ª rodada empatar, o vencedor da 1ª rodada ganhou o ponto.", "truco_help_text_17": "*Se a 3ª rodada empatar, o vencedor da 1ª rodada ganhou o ponto.", "truco_help_text_18": "*Se a 1ª e a 2ª rodadas empatarem, o vencedor da 3ª rodada ganha o ponto.", "truco_help_text_19": "*Se todas as três rodadas empatarem, ning<PERSON>m ganha o ponto.", "truco_help_text_20": "No truco, as \"<PERSON><PERSON><PERSON>\" são as 4 cartas mais valiosas do jogo.  Para definir a \"Manilha\" em Truco Paulista, uma carta é virada na mesa no início de uma Mão.  Este cartão é chamado de \"Vira\".", "truco_help_text_21": "As \"Manilhas\" são as cartas cujo valor é imediatamente superior à carta \"Vira\".  Cada “<PERSON>ilha” tem um nome relacionado ao seu naipe e estão na ordem crescente de valor: Ouros(Picafumo), Espadas(Espadilha), Copas（Escopeta) e Paus(ZAP).  Esses nomes podem variar de acordo com a região onde O truco é executado.", "truco_help_text_22": "*Por exemplo: \"Vira\"=7", "truco_help_text_23": "Como a carta de valor sujo, ma<PERSON>, o valor das cartas, da menor para a maior usando o baralho:", "truco_help_text_24": "seu valor é sempre imediatamente superior ao “vira”.", "truco_help_text_25": "Como chamar Truco?", "truco_help_text_26": "*Truco é uma aposta que pode ser feita a qualquer momento do jogo.", "truco_help_text_27": "*A dupla oponente deve responder com uma das seguintes opções:", "truco_help_text_28": "Correr: <PERSON><PERSON><PERSON> o truco e a dupla adversária vence a rodada.", "truco_help_text_29": "Aceitar: <PERSON><PERSON> a aposta que será decidida no final da rodada.", "truco_help_text_30": "Truco 09/06/12: Aumenta a aposta e passa a vez para o decidir.", "truco_help_text_31": "Se a equipe adversária passar, a equipe que chamou o truco ganha os pontos.", "truco_help_text_32": "Aposta", "truco_help_text_33": "Pontos", "truco_help_text_34": "Se não houver truco", "truco_help_text_35": "o vencedor da rodada ganhou 1 ponto", "truco_help_text_36": "Truco", "truco_help_text_37": "Aumentar para 09/06/12", "truco_help_text_38": "3 pontos", "truco_help_text_39": "09/06/12 pontos", "truco_help_text_40": "O que é \"Mão de Onze\"?", "truco_help_text_41": "*Diz-se \"<PERSON>ão de Onze\" quando uma das duplas já tem 11 pontos.  A dupla que teve 11 pontos pode ver as cartas do parceiro e decidir se joga ou não a rodada.", "truco_help_text_42": "*Se não jogar a rodada, os aparelhos vencem a rodada e marcam 1 ponto.", "truco_help_text_43": "Quando uma equipe atinge 11 pontos", "truco_help_text_44": "NóS", "truco_help_text_45": "ELE", "truco_help_text_46": "Como \"Mão de Ferro\"?", "truco_help_text_47": "*Diz-se \"<PERSON>ão de Ferro\" se ambas as duplas têm 11 pontos.  É normal jogar uma carta.  as cartas da mesa viradas Todas para baixo e você não pode chamar Truco.  Os derrotados derrotados os vencedores do jogo.", "truco_help_text_48": "<PERSON><PERSON><PERSON> ambas as equipes tiverem 11 pontos,", "truco_help_text_49": "<PERSON><PERSON> as cartas da mesa permanecem viradas para baixo e você não pode chamar Truco.", "truco_help_text_50": "CORRER", "truco_help_text_51": "*Os jogadores podem optar por não revelar uma carta, portanto ela é jogada com uma face para baixo.  Esta carta tem valor e qualquer carta jogada pelo oponente não vencerá.", "truco_help_text_52": "*Para jogar uma carta virada para baixo, basta pressionar e segurar a carta por um momento e ela virará.  Qualquer carta pode ser jogada como carta oculta, mas apenas durante a 2ª ou 3ª rodadas, nunca na primeira.", "truco_help_text_53": "DICA:Toque e segure para pegar o cartão da segunda rodada em diante", "hall_text_1": "Indique e ganhe", "hall_text_2": "Tiro de sorte", "hall_text_3": "VIP", "hall_text_4": "Vermelho preto", "hall_text_5": "<PERSON><PERSON> da Sorte", "hall_text_6": "<PERSON><PERSON>", "shop_text_1": "Obter fi<PERSON>s", "shop_text_2": "Sair do jogo", "shop_text_3": "Log Out", "reward_text_1": "Bônus de login diário", "reward_text_2": "1º dia", "reward_text_3": "2º dia", "reward_text_4": "3º dia", "reward_text_5": "7º+ dia", "reward_text_6": "4º dia", "reward_text_7": "5º dia", "reward_text_8": "6º dia", "reward_text_9": "<PERSON><PERSON>ue mais", "reward_text_10": "<PERSON><PERSON><PERSON>", "text_title_1": "Serviço", "text_title_2": "Contexto", "text_title_3": "<PERSON><PERSON>", "rummy_text_1": "Seqüência", "rummy_text_2": "Pontuação = 0", "rummy_text_3": "<PERSON><PERSON>", "rummy_text_4": "<PERSON><PERSON><PERSON>", "rummy_text_5": "Terminar", "rummy_text_6": "<PERSON><PERSON><PERSON>", "rummy_text_7": "Grupo", "rummy_text_8": "Descar<PERSON>", "rummy_text_9": "desconhecida", "rummy_text_10": "TERMINAR\nESPAÇO", "rummy_help_text_1": "Regras básicas", "rummy_help_text_2": "O jogo é jogado com 2 baralhos de cartas. Organize todas as 13 cartas em conjuntos de 2 e sequências de 3 ou mais cartas", "rummy_help_text_3": "No caso acima, todos os 10s funcionam como curingas e podem ser usados ​​como quaisquer cartas", "rummy_help_text_4": "Conjuntos", "rummy_help_text_5": "Conjunto/sequência inválido", "rummy_help_text_6": "Como agrupar", "rummy_help_text_7": "Toque nos cartões que você deseja agrupar", "rummy_help_text_8": "Toque no botão 'Grupo'!", "rummy_help_text_9": "Veja-os agrupados", "rummy_help_text_10": "Como Descartar", "rummy_help_text_11": "Toque no cartão que você deseja descartar!  Toque no botão 'DISCAD'!", "rummy_help_text_12": "Como terminar", "rummy_help_text_13": "Toque na carta que deseja descartar para terminar o jogo!", "rummy_help_text_14": "Toque no botão 'FINALIZAR'!  O jogo acabou!  A<PERSON>a você ganha!", "rummy_help_text_15": "Como declarar", "rummy_help_text_16": "Certifique-se de que todas as suas sequências/conjuntos possíveis estejam agrupadas!", "rummy_help_text_17": "Jogador", "rummy_help_text_18": "<PERSON><PERSON><PERSON><PERSON>", "rummy_help_text_19": "Pontuação", "rummy_help_text_20": "<PERSON><PERSON><PERSON><PERSON>", "rummy_help_text_21": "Nova função antifraude", "rummy_help_text_22": "Se<PERSON><PERSON><PERSON>ncia Pura (sem coringa)", "loadin_text_1": "Telefone não.", "loadin_text_2": "Facebook", "loadin_text_3": "Jogue como convidado", "loadin_text_4": "Clássico", "tp_text_1": "Recusar", "tp_text_2": "Concordo", "tp_help_1": "Os jogadores receberão duas cartas comuns e um coringa.  Todos receberão um curinga obrigatório.", "tp_help_2": "A classificação das mãos ainda está de acordo com as regras padr<PERSON>tti, onde o Coringa é substituído por uma carta melhor.", "tp_help_3": "Nesta variação de 3 patti - A(♠♥♦♣), K(♠♥♦♣), 4(♠♥♦♣), 7(♠♥♦♣) de todos os naipes são coringas ou curinga", "tp_help_4": "Um jogador com a mão de 3 Patti mais forte vencerá o jogo.  <PERSON><PERSON> as outras regras e apostas permanecem as mesmas", "tp_help_5": "Exemplo: – vamos supor que temos A♠-K♦-4♠, isso se tornará A-A-A (j<PERSON> que todas as três cartas Ás, <PERSON><PERSON> e 4 são coringas)", "tp_help_6": "Exemplo: – Vamos supor que temos A♠-2♠-2♥ e o oponente tem Q♥-4♥-7♣, então o vencedor será o oponente.  (Q-4- 7 = Q-Q- Q como 4 e 7 são coringas, enquanto temos 2-2- 2)", "truco_text_1": "Mensagem", "truco_text_2": "VITÓRIA", "truco_text_3": "Rodada", "truco_text_4": "Voltar à mesa", "truco_text_5": "<PERSON>ós", "truco_text_6": "Eles", "truco_text_7": "Rodada", "truco_text_8": "Pontos", "truco_text_9": "Mostra cartas", "truco_text_10": "Aumentar", "truco_text_11": "Aceitar", "truco_text_12": "<PERSON><PERSON>", "truco_text_13": "Informações da Tabela", "truco_text_14": "Apostas", "truco_text_15": "Modo", "truco_text_16": "Numéro de jogadores", "truco_text_17": "<PERSON><PERSON><PERSON>", "truco_text_18": "Paulista", "truco_text_19": "Mineiro", "truco_text_20": "<PERSON><PERSON>", "truco_text_21": "<PERSON><PERSON>", "truco_text_22": "1 VS 1", "truco_text_23": "2 VS 2", "truco_text_24": "Por favor, clique no botão 'Pronto' para jogar.", "truco_text_25": "Pronto", "truco_text_26": "<PERSON><PERSON><PERSON>", "truco_text_27": "Jo<PERSON><PERSON> correspondentes, por favor aguarde...", "updown7_text_1": "7UP Down é um jogo de dados simples", "updown7_text_2": "Abaixo de 7", "updown7_text_3": "Igual a 7", "updown7_text_4": "Acima de 7", "updown7_text_5": "APOSTA", "updown7_text_6": "Se você gastar 100 para apostar:", "updown7_text_7": "1. Aposte no VERDE:", "updown7_text_8": "Se o resultado mostrar 1,3,7,9, voc<PERSON> obterá(100*2)=200", "updown7_text_9": "Se o resultado mostrar 5, voc<PERSON> obter<PERSON> (100*1,5)=150", "updown7_text_10": "2. Aposte no VERMELHO:", "updown7_text_11": "Se o resultado mostrar 2,4,6,8, voc<PERSON> obterá(100*2)=200", "updown7_text_12": "Se o resultado mostrar 0, voc<PERSON> obter<PERSON> (100*1,5)=150", "updown7_text_13": "3. Aposte em VIOLETA:", "updown7_text_14": "se o resultado mostrar 0 ou 5, voc<PERSON> obter<PERSON> (100*4,5)450", "updown7_text_15": "4. Aposte em NÚMEROS:", "updown7_text_16": "se o resultado for o mesmo que o número que você selecionou, você obterá (100*9)=900", "updown7_text_17": "Quando o resultado do dado for a<PERSON>, o bônus de dados de ouro aparecerá aleatoriamente, e todos os jogadores apostados nesta rodada ganharão (dinheiro de ganho = o valor que ele apostou * soma dos pontos dos dois dados)", "updown7_text_18": "Dados de ouro", "bonustipword1": "1.V<PERSON><PERSON> pode obter bônus comprando promoções ou participando de todos os tipos de eventos por tempo limitado.", "bonustipword2": "2.Preste atenção que cada bônus que você recebeu tem uma data de validade, por favor, use-o a tempo.", "bonustipword3": "3.10% de sua perda (no máximo um valor de inicialização) será coberto pelo bônus em sua conta de bônus, adicionado ao seu \"Saldo em dinheiro\" após cada rodada do jogo.", "carrom_text_1": "1.O objetivo do jogo é colocar todos os seus discos nos buracos.", "carrom_text_2": "2.O jogo só terminará depois que o disco vermelho estiver no buraco.", "carrom_text_3": "3.O disco vermelho precisa ser co<PERSON> depois que estiver no buraco, senão ele reaparecerá na mesa.", "carrom_text_4": "4.<PERSON><PERSON><PERSON> pode embolsar o disco vermelho a qualquer momento depois de acertar o primeiro disco e antes que todos os outros discos acabem.", "carrom_text_5": "5.O número de disquetes que você embolsou será exibido ao lado da sua foto de perfil no jogo.", "carrom_text_6": "JOGAR DE NOVO", "carrom_text_7": "TOTAL", "carrom_text_8": "O jogador perderá sua vez quando o tempo limite for esgotado.  Se o tempo limite for mais de 3 vezes, o jogador perderá o jogo.", "carrom_text_9": "1.O objetivo do jogo é colocar todos os seus discos nos buracos.", "carrom_text_10": "2.O número de disquetes que você embolsou será exibido ao lado da sua foto de perfil no jogo.", "carrom_text_11": "O jogador perderá sua vez quando o tempo limite for esgotado.  Se o tempo limite for mais de 3 vezes, o jogador perderá o jogo.", "carrom_text_12": "1.O primeiro jogador a atingir a pontuação do gol vencerá o jogo.", "carrom_text_13": "2.Discos de bolso de cores diferentes gerarão pontuações diferentes.", "carrom_text_14": "3.O disco vermelho precisa ser co<PERSON> depois que estiver no buraco, senão ele reaparecerá na mesa.", "carrom_text_15": "4.<PERSON> disco verm<PERSON>ho (<PERSON> <PERSON>ha) deve bater em um disco de cor, caso contrário será considerado jogo sujo.", "carrom_text_16": "5.<PERSON><PERSON> as regras por mais de 5 vezes será considerado como \"Falta Perda\".", "carrom_text_17": "6.As pontuações serão calculadas com base em: a diferença de pontuação entre duas partes * boot.  O rendimento resultará na dedução máxima do valor da inicialização.", "carrom_text_18": "7.<PERSON><PERSON>ós completar o jogo, a parte perdedora pontuará 0 e a parte vencedora ganhará o dobro do valor.", "carrom_text_19": "Horas extras ou violação das regras serão consideradas como faltas, 5 faltas levarão à perda do jogo.", "carrom_text_20": "Arraste para a posição", "texas_text_0": "R$ #0", "texas_text_1": "Verificar", "texas_text_2": "Aposta", "texas_text_3": "<PERSON><PERSON>", "texas_text_4": "Levantar", "texas_text_5": "Fold", "texas_text_6": "<PERSON><PERSON> em", "texas_text_7": "Cartão alto", "texas_text_8": "Um par", "texas_text_9": "<PERSON><PERSON> pares", "texas_text_10": "<PERSON>r<PERSON><PERSON> de um tipo", "texas_text_11": "Direto", "texas_text_12": "<PERSON><PERSON><PERSON>", "texas_text_13": "Casa cheia", "texas_text_14": "Quatro de um tipo", "texas_text_15": "Straight flush", "texas_text_16": "Rubor Real", "texas_text_18": "Apostas: #0/#1", "texas_text_19": "Não.#0", "texas_text_20": "Compra mínima: R$ #0", "texas_text_21": "PACOTE", "texas_text_22": "Panela: ", "texas_text_23": "Panela", "texas_text_24": "<PERSON><PERSON>", "texas_text_25": "3/4 Pote", "texas_text_26": "1/2 Pote", "texas_text_27": "+1BB", "texas_text_28": "Fold", "texas_text_29": "Verificar/Desistir", "texas_text_30": "Ligue para qualquer", "texas_text_31": "Na próxima mão, suas fichas serão reabastecidas", "texas_text_32": "confirme", "texas_text_33": "VPIP", "texas_text_34": "AF", "texas_text_35": "<PERSON><PERSON>", "texas_help_1": "<PERSON><PERSON>", "texas_help_2": "Após um embaralhamento das cartas, o jogo começa com cada jogador recebendo duas cartas viradas para baixo, com o jogador no small blind recebendo a primeira carta e o jogador na mesa do botão recebendo a última carta distribuída. (Como na maioria dos jogos de pôquer, o baralho é um baralho padrão de 52 cartas que não contém coringas.) Essas cartas são as cartas da mão ou da mão dos jogadores. E<PERSON>s são as únicas cartas que cada jogador receberá individualmente, e elas (possivelmente) serão reveladas apenas no showdown, tornando o Texas Hold 'em um jogo de pôquer fechado.", "texas_help_3": "A mão começa com uma rodada de apostas \"pré-flop\", começando com o jogador à esquerda do big blind (ou o jogador à esquerda do dealer, se nenhum blind for usado) e continua no sentido horário. Uma rodada de apostas continua até que todos os jogadores tenham desistido, colocado todas as suas fichas ou igualado o valor colocado por todos os outros jogadores ativos. Veja apostas para uma conta detalhada. Observe que os blinds são considerados \"ao vivo\" na rodada de apostas pré-flop, o que significa que eles são contados para a quantia que o jogador cego deve contribuir. Se todos os jogadores derem call ao jogador na posição de big blind, esse jogador pode pedir mesa ou aumentar.", "texas_help_4": "Após a rodada de apostas pré-flop, supondo que restem pelo menos dois jogadores participando da mão, o dealer dá um flop: três cartas comunitárias viradas para cima. O flop é seguido por uma segunda rodada de apostas. Esta e todas as rodadas de apostas subsequentes começam com o jogador à esquerda do dealer e continuam no sentido horário.", "texas_help_4-1": "Depois que a rodada de apostas no flop termina, uma única carta comunitária (chamada turn ou quarta street) é distribuída, seguida por uma terceira rodada de apostas. Uma última carta comunitária (chamada de river ou five street) é então distribuída, seguida por uma quarta rodada de apostas e o showdown, se necessário. Na terceira e quarta rodadas de apostas, as apostas dobram.", "texas_help_5": "Em todos os cassinos, o dealer queima uma carta antes do flop, turn e river. Por causa dessa queima, os jogadores que estão apostando não podem ver o verso da próxima carta comunitária que está por vir. Isso é feito por razões tradicionais, para evitar qualquer possibilidade de um jogador saber antecipadamente a próxima carta a ser distribuída devido à sua marcação.", "texas_help_6": "O confronto", "texas_help_7": "Se um jogador apostar e todos os outros jogadores desistirem, o jogador restante recebe o pote e não é obrigado a mostrar suas cartas fechadas. Se dois ou mais jogadores permanecerem após a rodada final de apostas, ocorre um showdown. No showdown, cada jogador joga a melhor mão de pôquer que pode fazer das sete cartas que compõem suas cartas de duas mãos e as cinco cartas comunitárias. Um jogador pode usar ambas as suas próprias duas cartas, apenas uma, ou nenhuma, para formar sua mão final de cinco cartas. Se as cinco cartas comunitárias formam a melhor mão do jogador, diz-se que o jogador está jogando na mesa e só pode esperar dividir o pote, porque cada outro jogador também pode usar as mesmas cinco cartas para construir a mesma mão.", "texas_help_8": "Se a melhor mão for compartilhada por mais de um jogador, o pote é dividido igualmente entre eles, com quaisquer fichas extras indo para os primeiros jogadores após o botão no sentido horário. É comum que os jogadores tenham mãos de valor próximo, mas não de mesma classificação. No entanto, deve-se ter cuidado ao determinar a melhor mão; se a mão envolver menos de cinco cartas (como dois pares ou três do mesmo tipo), então os kickers são usados ​​para resolver os empates (veja o segundo exemplo abaixo). A classificação numérica da carta é de única", "texas_help_9": "import<PERSON><PERSON>; os valores dos naipes são irrelevantes no hold 'em.", "texas_help_10": "<PERSON><PERSON> da <PERSON>", "texas_help_11": "A tabela a seguir mostra os valores de mão possíveis em ordem crescente.", "texas_help_12": "Cartão alto", "texas_help_13": "Valor simples do cartão. Mais baixo: 2 – <PERSON><PERSON> alto: <PERSON><PERSON>", "texas_help_14": "Par", "texas_help_15": "Duas cartas com o mesmo valor", "texas_help_16": "<PERSON><PERSON> pares", "texas_help_17": "Duas vezes duas cartas com o mesmo valor", "texas_help_18": "<PERSON>r<PERSON><PERSON> de um tipo", "texas_help_19": "Três cartas com o mesmo valor", "texas_help_20": "Direto", "texas_help_21": "Sequência de 5 cartas em valor crescente (Ás pode preceder 2 e seguir Rei)", "texas_help_22": "<PERSON><PERSON><PERSON>", "texas_help_23": "5 cartas do mesmo naipe", "texas_help_24": "Casa cheia", "texas_help_25": "Combinação de três de um tipo e um par", "texas_help_26": "Quatro de um tipo", "texas_help_27": "Quatro cartas do mesmo valor", "texas_help_28": "Straight flush", "texas_help_29": "Em linha reta do mesmo terno", "texas_help_30": "Rubor Real", "texas_help_31": "Straight flush de dez a ás", "texas_help_32": "Ações", "texas_help_33": "Em uma rodada de apostas, o jogador pode realizar uma das seguintes ações", "texas_help_34": "<PERSON><PERSON>", "texas_help_35": "Se já houve uma aposta, pagar significa que um jogador concorda em igualar a aposta. Em outras palavras, se o jogador A apostar 10 e o jogador B pagar, o jogador B terá que igualar a aposta de 10 e colocar 10 no pote.", "texas_help_36": "Verificar", "texas_help_37": "Se não houver apostas na rodada atual, um jogador pode fazer check. A ação então passa para o próximo jogador à esquerda. O check pode ser interpretado como uma espécie de passe: você permanece no jogo, mas decide não apostar a rodada atual.", "texas_help_38": "Levantar", "texas_help_39": "Um jogador pode aumentar o tamanho atual da aposta. Para fazer isso, a aposta deve exceder a aposta do último jogador em pelo menos o dobro.", "texas_help_40": "Fold", "texas_help_41": "Fold quer dizer se recusa a competir no pote atual. Quando um jogador fold , as cartas do jogador não vão participar mais nessa rodada e não pode vencer na ronda atual."}