// 统一导出所有类型定义

// Hall模块类型
export * from './hall'

// Wallet模块类型  
export * from './wallet'

// 通用类型
export * from './common'

// API响应类型
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  timestamp?: number
}

// 分页响应类型
export interface PaginatedResponse<T = any> {
  code: number
  message: string
  data: {
    items: T[]
    total: number
    page: number
    pageSize: number
    totalPages: number
  }
}

// 用户信息类型
export interface UserInfo {
  id: string
  username: string
  email?: string
  phone?: string
  avatar?: string
  balance: number
  vip_level: number
  created_at: string
  updated_at: string
}

// 登录响应类型
export interface LoginResponse {
  token: string
  user: UserInfo
  expires_in: number
}

// 错误类型
export interface AppError {
  code: string
  message: string
  details?: any
}

// 路由元信息类型
export interface RouteMeta {
  title?: string
  requiresAuth?: boolean
  transition?: string
  keepAlive?: boolean
  showTabbar?: boolean
}

// 全局配置类型
export interface AppConfig {
  apiBaseUrl: string
  wsUrl: string
  version: string
  environment: 'development' | 'staging' | 'production'
  features: {
    wallet: boolean
    activities: boolean
    leaderboard: boolean
    notifications: boolean
  }
}

// 通知类型
export interface Notification {
  id: string
  type: 'info' | 'success' | 'warning' | 'error'
  title: string
  message: string
  duration?: number
  actions?: Array<{
    label: string
    action: () => void
  }>
}

// Toast类型
export interface Toast {
  id: string
  type: 'info' | 'success' | 'warning' | 'error'
  message: string
  duration?: number
}

// 模态框类型
export interface Modal {
  id: string
  component: any
  props?: Record<string, any>
  options?: {
    closable?: boolean
    maskClosable?: boolean
    width?: string | number
    height?: string | number
  }
}

// 加载状态类型
export interface LoadingState {
  global: boolean
  [key: string]: boolean
}

// 网络状态类型
export interface NetworkState {
  online: boolean
  effectiveType?: string
  downlink?: number
  rtt?: number
}

// 设备信息类型
export interface DeviceInfo {
  isMobile: boolean
  isTablet: boolean
  isDesktop: boolean
  platform: string
  userAgent: string
  screenWidth: number
  screenHeight: number
}

// 主题类型
export interface Theme {
  name: string
  colors: {
    primary: string
    secondary: string
    success: string
    warning: string
    error: string
    info: string
    background: string
    surface: string
    text: string
  }
  fonts: {
    primary: string
    secondary: string
  }
}

// 语言类型
export interface Language {
  code: string
  name: string
  flag: string
}

// 统计数据类型
export interface Statistics {
  totalUsers: number
  activeUsers: number
  totalGames: number
  totalTransactions: number
  totalRewards: number
}

// 游戏历史记录类型
export interface GameHistory {
  id: string
  game_id: string
  game_name: string
  game_type: string
  provider: string
  bet_amount: number
  win_amount: number
  profit: number
  played_at: string
  duration: number
}

// 支付方式类型
export interface PaymentMethod {
  id: string
  name: string
  type: 'bank' | 'ewallet' | 'crypto' | 'card'
  icon: string
  min_amount: number
  max_amount: number
  fee_rate: number
  processing_time: string
  is_available: boolean
}

// 银行信息类型
export interface BankInfo {
  id: string
  name: string
  code: string
  icon: string
  account_number?: string
  account_name?: string
}

// 地址信息类型
export interface AddressInfo {
  country: string
  state?: string
  city?: string
  address: string
  postal_code?: string
}

// 验证规则类型
export interface ValidationRule {
  required?: boolean
  min?: number
  max?: number
  pattern?: RegExp
  validator?: (value: any) => boolean | string
  message?: string
}

// 表单字段类型
export interface FormField {
  name: string
  label: string
  type: 'text' | 'email' | 'password' | 'number' | 'select' | 'textarea'
  placeholder?: string
  rules?: ValidationRule[]
  options?: Array<{ label: string; value: any }>
  disabled?: boolean
  readonly?: boolean
}

// 表格列类型
export interface TableColumn {
  key: string
  title: string
  width?: string | number
  align?: 'left' | 'center' | 'right'
  sortable?: boolean
  filterable?: boolean
  render?: (value: any, record: any, index: number) => any
}

// 分页配置类型
export interface PaginationConfig {
  page: number
  pageSize: number
  total: number
  showSizeChanger?: boolean
  showQuickJumper?: boolean
  showTotal?: boolean
}

// 搜索配置类型
export interface SearchConfig {
  placeholder?: string
  fields?: string[]
  filters?: Array<{
    key: string
    label: string
    type: 'select' | 'date' | 'range'
    options?: Array<{ label: string; value: any }>
  }>
}

// 导出配置类型
export interface ExportConfig {
  filename?: string
  format: 'csv' | 'excel' | 'pdf'
  columns?: string[]
  filters?: Record<string, any>
}

// 上传文件类型
export interface UploadFile {
  id: string
  name: string
  size: number
  type: string
  url?: string
  status: 'uploading' | 'success' | 'error'
  progress?: number
  error?: string
}

// 权限类型
export interface Permission {
  id: string
  name: string
  code: string
  description?: string
  module: string
}

// 角色类型
export interface Role {
  id: string
  name: string
  code: string
  description?: string
  permissions: Permission[]
}

// 菜单项类型
export interface MenuItem {
  id: string
  title: string
  icon?: string
  path?: string
  children?: MenuItem[]
  permission?: string
  hidden?: boolean
  disabled?: boolean
}

// 面包屑类型
export interface Breadcrumb {
  title: string
  path?: string
  icon?: string
}

// 标签页类型
export interface Tab {
  key: string
  title: string
  closable?: boolean
  content?: any
}

// 步骤类型
export interface Step {
  title: string
  description?: string
  icon?: string
  status?: 'wait' | 'process' | 'finish' | 'error'
}

// 时间线项类型
export interface TimelineItem {
  time: string
  title: string
  description?: string
  type?: 'primary' | 'success' | 'warning' | 'error'
  icon?: string
}

// 图表数据类型
export interface ChartData {
  labels: string[]
  datasets: Array<{
    label: string
    data: number[]
    backgroundColor?: string | string[]
    borderColor?: string | string[]
    borderWidth?: number
  }>
}

// 仪表板卡片类型
export interface DashboardCard {
  id: string
  title: string
  value: string | number
  change?: {
    value: number
    type: 'increase' | 'decrease'
  }
  icon?: string
  color?: string
}

// WebSocket消息类型
export interface WebSocketMessage {
  type: string
  data: any
  timestamp: number
}

// 事件类型
export interface AppEvent {
  type: string
  payload?: any
  timestamp: number
}

// 缓存配置类型
export interface CacheConfig {
  key: string
  ttl?: number // 生存时间（秒）
  storage?: 'memory' | 'localStorage' | 'sessionStorage'
}

// 日志级别类型
export type LogLevel = 'debug' | 'info' | 'warn' | 'error'

// 日志条目类型
export interface LogEntry {
  level: LogLevel
  message: string
  timestamp: number
  data?: any
  source?: string
}
