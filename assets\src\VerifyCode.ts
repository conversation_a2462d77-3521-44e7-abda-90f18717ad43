import { ALL_APP_SOURCE_CONFIG } from "./Config";
import { DEEP_INDEXZ, UI_PATH_DIC, VERIFY_CODE_TYPE } from "./GlobalConstant";
import Global from "./GlobalScript";
import { PN_VERIFY_TYPE } from "./SetPhoneNumber";
import <PERSON>ICom<PERSON> from "./component/UICommon";
import { GEETEST_TYPE, GeetestMgr } from "./geetest/GeetestMgr";
import { uiManager } from "./mgr/UIManager";
import HttpUtils from "./net/HttpUtils";

const { ccclass, property } = cc._decorator;
export function showVerifyCode(data: any) {
    uiManager.instance.showDialog(UI_PATH_DIC.VerifyCode, [{ data }],null,DEEP_INDEXZ.VERIFY_CODE)
}
@ccclass
export default class VerifyCode extends UICommon {

    @property(cc.Node)
    uiBox: cc.Node = null;

    @property(cc.Label)
    lbTips: cc.Label = null;

    //两个label
    @property(cc.Label)
    lb1: cc.Label = null;
    @property(cc.Label)
    lb2: cc.Label = null;

    @property(cc.Label)
    phone: cc.Label = null;
    code: string = ""
    @property(cc.Node)
    m_get_btn: cc.Node = null;
    @property(cc.Node)
    red: cc.Node = null;
    @property(cc.Label)
    m_time: cc.Label = null;
    @property(cc.Label)
    lbError: cc.Label = null;
    m_t: number = 0;
    verifyType: number = 0;
    sucCb: Function = null;
    verifyStr:string = '';

    smsType = 1;

    init(data: any) {
        this.phone.string = Global.getInstance().userdata.phone.substring(0, 2) + "*****" + Global.getInstance().userdata.phone.substring(6);
        this.phone.node.active = false;
        //this.lbTips.string = data.data.type;
        this.lbTips.string = "Verification";
        this.verifyType = data.data.verifyType;
        this.verifyStr = data.data.type;
        if (data.data.cb) this.sucCb = data.data.cb;
        let mainNode = this.node.getChildByName("UiBox");
        mainNode.position = new cc.Vec3(0,-1200);
        //cc.tween(mainNode).to(0.1,{position:new cc.Vec3(0,-600)}).start();
    }
    //验证过之后 重新获取code
    reGetcode(ret?){
        let gee_guard = ret?.geetest_guard || '';
        let uInfo = ret?.userInfo || '';
        let gee_captcha = ret?.geetest_captcha || '';
        let ctype = this.verifyType;
        this.smsType = ctype;
        HttpUtils.getInstance().post(3, 3, this, "/common/api/sms/send/short/msg", {
            phone: Global.getInstance().userdata.phone,
            telephoneCode: "+63",
            type: ctype,
            geetest_guard:gee_guard,
            userInfo:uInfo,
            geetest_captcha:gee_captcha,
            buds:ret?.buds || '64',
        }, () => {
            Global.getInstance().showSimpleTip("Verification code sent successfully")
            //这里 显示对应的 phone和 信息
            this.phone.node.active = true;
            this.lb1.node.active = true;
            this.lb2.node.active = false;
        }, (response) => {
            if (response["msg"] && response["msg"] != "") {
                Global.getInstance().showSimpleTip(response["msg"]);
            } else if (response["code"]) {
                Global.getInstance().showSimpleTip(response.msg);
            }
        });
        this.m_get_btn.active = false;
        this.m_time.node.parent.active = true;
        this.m_t = 60;
        this.m_time.string = this.m_t + " s";
        this.schedule(this.timer, 1);
    }
    onClickSend() {
        let gtype = ''
        switch (this.verifyStr) {
            case VERIFY_CODE_TYPE.ChangePhoneNum:
                gtype = GEETEST_TYPE.change_pt_phone_code;
                break;
            case VERIFY_CODE_TYPE.AddWithdrawAccount:
                gtype = GEETEST_TYPE.bind_withdraw_account_code;
                break;
            case VERIFY_CODE_TYPE.ChangePaymentPwd:
                gtype = GEETEST_TYPE.change_pay_password_code;
                break;
            case VERIFY_CODE_TYPE.ChangeLoginPwd:
                gtype = GEETEST_TYPE.forget_password_code;
                break;
            case VERIFY_CODE_TYPE.SetLoginPwd:
                gtype = GEETEST_TYPE.first_password;
                break;
            case VERIFY_CODE_TYPE.SetPaymentPwd:
                gtype = GEETEST_TYPE.first_pay_password;
                break;
            default:
                break;
        }
        GeetestMgr.instance.geetest_device(gtype,(succ)=>{
            if(succ){
                let ret = {}
                if(Object.getPrototypeOf(succ) !== Boolean.prototype){
                    ret = succ
                }
                this.reGetcode(ret);
            }
        })
    }
    reSetCode() {
        this.unschedule(this.timer);
        this.m_get_btn.active = true;
        this.m_time.node.parent.active = false;
    }
    timer() {
        this.m_t--;
        this.m_time.string = this.m_t + " s";
        if (this.m_t <= 0) {
            this.reSetCode();
        }
    }
    onClickConfirm() {
        if (!this.code) return Global.getInstance().showSimpleTip("Code Error,Please Try Again");
        HttpUtils.getInstance().post(3, 3, this, "/common/api/sms/verify/short/msg/code", {
            token: Global.getInstance().token,
            phone: Global.getInstance().userdata.phone,
            telephoneCode: "+63",
            code: this.code,
            type: this.smsType
        }, (response) => {
            // this.setData(response.data);
            this.onClose();
            if (this.sucCb) this.sucCb()
        }, () => {
            Global.getInstance().showSimpleTip("Verification Error,Please Try Again");
        });
    }
    onEditBegin(editbox: cc.EditBox) {
        Global.getInstance().editParentMove(editbox, this.uiBox,800);
        editbox.placeholderLabel.node.name = editbox.placeholder
        editbox.placeholder = ""
    }

    onEditEnd(editbox: cc.EditBox) {
        if (Global.getInstance().needScreenUp()) {
            const widght = this.uiBox.getComponent(cc.Widget)
            widght.bottom = -74;
            widght.updateAlignment();
        }
        editbox.placeholder = editbox.placeholderLabel.node.name
    }
    onEditCode(text) {
        this.code = text;
    }
    onClose() {
        let mainNode = this.node.getChildByName("UiBox");
        cc.tween(mainNode).to(0.1,{position:new cc.Vec3(0,-1200)}).call(()=>{
            this.node.destroy();
        }).start();
    }
}
