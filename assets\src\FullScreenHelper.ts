import Global from "./GlobalScript";
import MoreGameManager from "./MoreGameManager";

const { ccclass, property } = cc._decorator;

@ccclass
export default class FullScreenHelper extends cc.Component {
    onLoad() {
        if(cc.sys.isNative)return;
        this.node.on("touchstart", (event) => {
            if (!MoreGameManager.instance().isBrowserDevices()) {
                return
            }
            console.log('------------浏览器类型----------',cc.sys.browserType);
            if(Global.getInstance().isNative){return}
            if (!document.fullscreenElement) {
                cc.view.enableAutoFullScreen(false)
                cc.director.emit('reWidgetScene');
            }
            console.log('------------浏览器类型',cc.sys.browserType);
            if(cc.sys.browserType == cc.sys.BROWSER_TYPE_MIUI)return;
            // if(cc.sys.browserType == 'chrome')return;//依然会有bug 旋转 360 还有一些内核是chrome的浏览器都布恩那个 全屏模式
            if(!document.fullscreenElement){
                document.documentElement.requestFullscreen({ navigationUI: "hide" });
                // this.lockPortrait();
            }else{
                document.exitFullscreen()
            }
        });
    }
    async lockPortrait(){
        if(cc.sys.isNative)return;
        await document.documentElement.requestFullscreen();
        // await screen.orientation.lock('portrait').catch(e=>{})
    }
}
